@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 145, 27%, 51%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.3rem;

    --brand-primary: hsl(145, 27%, 45%);
    --brand-primary-dark: hsl(145, 27%, 30%);
    --brand-contrast: hsl(4, 27%, 51%);

  }

  @media (prefers-color-scheme: dark) {
    :root {
      --background: 20 14.3% 4.1%;
      --foreground: 0 0% 90%;
      --card: 24 9.8% 10%;
      --card-foreground: 0 0% 95%;
      --popover: 0 0% 9%;
      --popover-foreground: 0 0% 95%;
      --primary: 142.1 70.6% 45.3%;
      --primary-foreground: 144.9 80.4% 10%;
      --secondary: 240 3.7% 15.9%;
      --secondary-foreground: 0 0% 98%;
      --muted: 0 0% 15%;
      --muted-foreground: 240 5% 64.9%;
      --accent: 12 6.5% 15.1%;
      --accent-foreground: 0 0% 98%;
      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 0 85.7% 97.3%;
      --border: 240 3.7% 15.9%;
      --input: 240 3.7% 15.9%;
      --ring: 142.4 71.8% 29.2%;
    }
  }
}

.bg-brand {
    /*background-color: var(--brand-primary);*/
  background: linear-gradient(197deg, rgba(91,162,121,1) 0%, rgba(82,144,108,1) 35%, rgba(63,110,82,1) 100%);
}

.bg-brand-dark {
  /*background-color: var(--brand-primary-dark);*/
  background: linear-gradient(197deg, rgba(91,162,121,1) 0%, rgba(82,144,108,1) 35%, rgba(63,110,82,1) 100%);
}


.bg-brand-contrast {
  background-color: var(--brand-contrast);
}


@layer base {
  * {
    @apply border-foreground/20;
  }
}

.animate-in {
  animation: animateIn 0.3s ease 0.15s both;
}

@keyframes animateIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
