import { GeistSans } from 'geist/font/sans'
import './globals.css'
import { Inter } from 'next/font/google'
import { Suspense } from 'react'

const inter = Inter({
    subsets: ['latin'],
    display: 'swap',
})
const defaultUrl = process.env.VERCEL_URL
    ? `https://${process.env.VERCEL_URL}`
    : "http://localhost:3000";

export const metadata = {
    metadataBase: new URL(defaultUrl),
    title: "ekoIntelligence - shining a light on investments",
    description: "ekoIntelligence provides actionable analytics of companies and funds against Kate Raworth's Doughnut Model. A more nuanced and transparent way to help you assess their sustainability and fairness.",
};

export default async function RootLayout({
                                             children,
                                         }: {
    children:any;
}) {

    return (
        <html lang="en" className={GeistSans.className}>
        <Suspense>
        {children}
        </Suspense>
        </html>
    );
}
