import Link from 'next/link'
import './globals.css'
import { EkoLogoText, EkoSymbolBrand } from '@utils/images'
import React, { Suspense } from 'react'
import { MeetTheTeam } from '@ui/components/meet-the-team'
import { Features } from '@ui/components/front-page/features'
import { Benefits } from '@ui/components/front-page/benefits'
import { Newsletter } from '@ui/components/front-page/newsletter'
import { Pricing } from '@ui/components/front-page/pricing'
import { Hero } from '@ui/components/front-page/hero'
import { Footer } from '@/app/components/footer'
import { BackgroundReading } from '@ui/components/front-page/background'

export default async function Index() {


    return (<>
            <body className="bg-background text-foreground">
            <div className="flex min-h-screen flex-col">
                <div className="flex flex-col min-h-[100dvh]">

                    <header className="px-4 lg:px-6 h-14 flex items-center">
                        <Link href="#" className="flex items-center justify-center" prefetch={false}>
                            <EkoSymbolBrand height={28} className=""/>
                            <EkoLogoText height={28} className="ml-4 dark:hidden" ekoColor="#1f1f1f"
                                         intelligenceColor="#505050"/>
                            <EkoLogoText height={28} className="ml-4 hidden dark:block" ekoColor="#f0f0f0"
                                         intelligenceColor="#a0a0a0"/>
                        </Link>
                        <nav className="ml-auto hidden md:flex gap-4 sm:gap-6">
                            <Link href="#features" className="text-sm font-medium hover:underline underline-offset-4"
                                  prefetch={false}>
                                Features
                            </Link>
                            <Link href="#pricing" className="text-sm font-medium hover:underline underline-offset-4"
                                  prefetch={false}>
                                Pricing
                            </Link>
                            <Link href="#team" className="text-sm font-medium hover:underline underline-offset-4"
                                  prefetch={false}>
                                About
                            </Link>
                            <Link href="mailto:<EMAIL>"
                                  className="text-sm font-medium hover:underline underline-offset-4"
                                  prefetch={false}>
                                Contact
                            </Link>
                            <Link href="https://appsmith.ekointelligence.eco/applications"
                                  className="text-sm font-medium hover:underline underline-offset-4"
                                  prefetch={false}>
                                Admin
                            </Link>
                        </nav>
                    </header>
                    <main className="flex-1">
                        <Suspense>
                            <Hero/>
                            <Features/>
                            <MeetTheTeam></MeetTheTeam>
                            <Benefits/>
                            <Newsletter/>
                            <Pricing/>
                            <BackgroundReading/>
                        </Suspense>
                    </main>
                </div>
            </div>
            <Footer></Footer>
            </body>
        </>
    );
}
