/** @type {import('tailwindcss').Config} */
const {
    default: flattenColorPalette,
} = require("tailwindcss/lib/util/flattenColorPalette");

const colors = require('tailwindcss/colors')
module.exports = {
    content: [
        "./app/**/*.{js,ts,jsx,tsx,mdx}",
        "./components/**/*.{js,ts,jsx,tsx,mdx}",
        "../../packages/ui/src/**/*{.js,.ts,.jsx,.tsx}",
    ],
    theme: {
        screens: {
            'print': { 'raw': 'print' },
        },
        extend: {
            animation: {
                aurora: "aurora 60s linear infinite",
                wiggle: 'wiggle 0.4s ease-in-out 3'
            },
            keyframes: {
                aurora: {
                    from: {
                        backgroundPosition: "50% 50%, 50% 50%",
                    },
                    to: {
                        backgroundPosition: "350% 50%, 350% 50%",
                    },
                },
                wiggle: {
                    '0%, 100%': {transform: 'rotate(-1deg)'},
                    '50%': {transform: 'rotate(1deg)'},
                }
            },
            colors: {
                background: "hsl(var(--background))",
                foreground: "hsl(var(--foreground))",
                btn: {
                    background: "hsl(var(--btn-background))",
                    "background-hover": "hsl(var(--btn-background-hover))",
                }
            },
        },
    },
    variants: {
        extend: {
            animation: ['hover', 'focus'],
        }
    },
    plugins: [require("tailwindcss-animate"),  require('@tailwindcss/typography'),addVariablesForColors],
};

// This plugin adds each Tailwind color as a global CSS variable, e.g. var(--gray-200).
function addVariablesForColors({addBase, theme}) {
    let allColors = flattenColorPalette(theme("colors"));
    let newVars = Object.fromEntries(
        Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
    );

    addBase({
        ":root": newVars,
    });
}
