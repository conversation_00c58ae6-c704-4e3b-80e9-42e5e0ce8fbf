{"name": "web", "private": true, "scripts": {"dev": "next dev --turbopack --port 3002", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.1", "@repo/ui": "workspace:*", "@repo/utils": "workspace:*", "@tailwindcss/typography": "^0.5.13", "@types/d3": "^7.4.3", "autoprefixer": "10.4.17", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "d3": "^7.9.0", "d3plus-react": "^1.3.3", "d3plus-text": "^1.2.5", "framer-motion": "^12.0.0-alpha.2", "geist": "^1.2.1", "lucide-react": "^0.395.0", "next": "^15.0.3", "openai": "^4.51.0", "postcss": "8.4.33", "prop-types": "^15.8.1", "react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106", "react-dropzone": "^14.2.3", "react-force-graph-3d": "^1.24.3", "react-hook-form": "^7.52.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "remark-gfm": "^4.0.0", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.3", "usehooks-ts": "^3.1.0", "uuid": "^10.0.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "20.11.5", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "@types/react-syntax-highlighter": "^15.5.13", "encoding": "^0.1.13", "next": "15.0.3", "turbo": "latest"}, "pnpm": {"overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}}