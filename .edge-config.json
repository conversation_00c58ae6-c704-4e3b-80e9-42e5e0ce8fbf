{"repositories": [{"id": "fc8f0315-ac18-44cb-95aa-fe0130c4478e-1750165354199", "name": "mono-repo", "repositoryPath": "/Users/<USER>/IdeaProjects/mono-repo", "baseBranch": "main", "linearWorkspaceId": "fc8f0315-ac18-44cb-95aa-fe0130c4478e", "linearWorkspaceName": "Ekointelligence", "linearToken": "lin_oauth_3df3bf3938ff5b448ffabb6a62aeb7c541708196805d5ffb619e997b52fd0237", "workspaceBaseDir": "/Users/<USER>/IdeaProjects/mono-repo/workspaces", "isActive": true, "mcpConfigPath": "/Users/<USER>/IdeaProjects/mono-repo/.mcp.json", "teamKeys": ["EKO"]}]}