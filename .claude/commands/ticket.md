Ticket EKO-$ARGUMENTS

Please can you work on the ticket EKO-$ARGUMENTS in Linear.

Firstly please move the ticket into 'In Progress'.

Firstly create a new branch called feature/eko-$ARGUMENTS and use that for the development.

Then create a env file in apps/customer/.env.development.local containing

```
AWS_ACCESS_KEY_ID="********************"
ANTHROPIC_API_KEY=************************************************************************************************************
AWS_SECRET_ACCESS_KEY="3X952B4sa+S6ZuaEa5Gx94p8zeBFYfO97Ue5GzjB"
#KV_REST_API_READ_ONLY_TOKEN="ArUSAAIgcDEpW80o6wGeDlV1n5OGyrptocnE1GgNdZLjC9kxGtOwdg"
#KV_REST_API_TOKEN="AbUSAAIncDFlMjU2YmE4YzIwMzk0OWY0YWZmOTdhNWE5NTk0ZmJlNHAxNDYzNTQ"
#KV_REST_API_URL="https://careful-tick-46354.upstash.io"
#KV_URL="redis://default:<EMAIL>:6379"
GOOGLE_API_KEY=AIzaSyCffot1AZ2_Jgbrjbo60kLQvFnpKbK5_vE
GOOGLE_GENERATIVE_AI_API_KEY=AIzaSyCffot1AZ2_Jgbrjbo60kLQvFnpKbK5_vE
NEXT_PUBLIC_SUPABASE_URL=https://dfohljvyhcbtctejukvj.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRmb2hsanZ5aGNidGN0ZWp1a3ZqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUzNDIyNTAsImV4cCI6MjA2MDkxODI1MH0.m68ilgt1TQL43irYuARAMJZEQOmRYNBwJAR-Fvrat44
VERCEL_URL="eko-admin-app.vercel.app"
NEXT_PUBLIC_TIPTAP_APP_ID=xk2o7gwm
TIPTAP_SECRET=35670655370344dcd69bd2beba5c3ee3de232b206127a99b292da7349606f9ef
VECTOR_STORE="vs_Fhs0y3a0KQWELE1Rb05IKUCE"
VERCEL="1"
VERCEL_ENV="development"
```

Reminders:

- Use tailwind plugins and tailwind not custom CSS, check what plugins exist already before adding new.

Please use the following workflow:

Check Linear for details on the issue, including any attachments

- Plan the work before you start in detail with small incremental steps, thinking through step by step.
- Search the web for any details you need on libraries, esepcially ones that change frequently, or use your Context7
  MCP.
- Update the ticket with a comment containing your plan of action.
- Do the actual work, making use of the tools you have, especially Supabase AND Linear.
- For web frequently use `tsc --noEmit`
  Then run playright tests `npx playwright test --reporter=line` at the end.

- For python run the pytests in tests.on_commit and run `uvx ty check` on the changed files.

- For the customer app please add a playwright test if appropriate to reproduce the issue, this test should be in a new
  file called `apps/customer/tests/issues/issue-eko-$ARGUMENTS.spec.ts`
- Commit changes as you need, with a verbose comment including the Linear issue ID
  -Update Linear with a report on what you have done so far.

After you've finished please create a PR to merge feature/eko-$ARGUMENTS and push all changes. Please then move the
ticket into 'In Review' not 'Done'.

You're doing a great job, keep at it, plan this out and ultra think carefully step by and work your way through this
methodically. Be your best self!
