Please refactor $ARGUMENTS to make better use of test utils apps/customer/tests/helpers/tests/test-utils.ts

For example, login:

    let testUtils: TestUtils;
    ...
    test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    await testUtils.login();
    });

Or create a document:

    await testUtils.createDocumentFromTemplate()

Or wait for the editor:

    await testUtils.waitForEditor()
