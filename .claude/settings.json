{"permissions": {"allow": ["Bash(./bin/run_in_db.sh:*)", "Bash(./fix_gemini_command.sh)", "Bash(bin/extract-schema.sh:*)", "Bash(cd /Users/<USER>/IdeaProjects/mono-repo/backoffice && pipenv shell \"cd src && pyright /Users/<USER>/IdeaProjects/mono-repo/backoffice/src/eko/entities/cluster.py\")", "Bash(cd /Users/<USER>/IdeaProjects/mono-repo/backoffice && pipenv shell \"cd src && python -m pytest tests/test_entity_clustering.py -v\")", "Bash(cd backoffice && pipenv shell \"cd src && python cli.py create-effect-flags-viz --entity NojNQDMp2L --start-year=2000 --keep-alive\")", "Bash(cd:*)", "<PERSON><PERSON>(chmod:*)", "Bash(find:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git diff:*)", "<PERSON><PERSON>(git mv:*)", "Bash(git push:*)", "Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pip install:*)", "Bash(pipenv install:*)", "Bash(pipenv run pyright:*)", "Bash(pipenv run python:*)", "Bash(pnpm build)", "<PERSON><PERSON>(popd:*)", "<PERSON><PERSON>(pushd:*)", "Bash(pyright:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(time python:*)", "WebFetchTool(domain:docs.crewai.com)", "WebFetchTool(domain:docs.runpod.io)", "WebFetchTool(domain:github.com)", "WebFetchTool(domain:googleapis.github.io)", "WebFetchTool(domain:supabase.com)", "mcp__browsermcp__browser_navigate", "mcp__browsermcp__browser_screenshot", "mcp__browsermcp__browser_snapshot", "mcp__fetch__fetch", "mcp__filesystem__list_allowed_directories", "mcp__filesystem__read_file", "mcp__filesystem__search_files", "mcp__filesystem__write_file", "mcp__jetbrains__create_new_file_with_text", "mcp__jetbrains__get_file_text_by_path", "mcp__jetbrains__get_open_in_editor_file_path", "mcp__memory__add_observations", "mcp__memory__create_entities", "mcp__memory__create_relations", "mcp__playwright__playwright_click", "mcp__playwright__playwright_evaluate", "mcp__playwright__playwright_navigate", "mcp__playwright__playwright_screenshot", "mcp__postgres__query", "mcp__slack__slack_get_channel_history", "mcp__slack__slack_list_channels", "<PERSON><PERSON>(npx playwright test:*)", "mcp__playwright__playwright_console_logs", "mcp__linear__linear_get_issue", "mcp__linear__linear_get_issue_comments", "mcp__linear__linear_create_comment", "mcp__linear__linear_update_issue", "mcp__linear__linear_create_issue", "mcp__linear__linear_search_issues", "mcp__linear__linear_get_issue_attachments", "mcp__linear__linear_get_issue_labels", "mcp__linear__linear_get_issue_timeline", "mcp__linear__linear_get_issue_workflows", "mcp__linear__linear_get_issue_projects", "mcp__linear__linear_get_issue_sprints", "mcp__linear__linear_get_issue_milestones", "mcp__linear__linear_get_issue_epics", "mcp__linear__linear_get_issue_releases", "mcp__linear__linear_get_issue_iterations", "mcp__linear__linear_get_issue_statuses", "mcp__linear__linear_get_issue_priorities", "mcp__linear__linear_get_issue_types", "mcp__supabase-customer__list_tables", "mcp__supabase-customer__execute_sql", "mcp__supabase-customer__generate_typescript_types", "Bash(tsc --noEmit)", "<PERSON><PERSON>(tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>)", "Bash(npx tsc:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(grep:*)", "Bash(git push:*)", "mcp__linear__update_issue", "mcp__linear__get_team", "mcp__linear__list_issue_statuses"], "deny": []}, "enabledMcpjsonServers": ["supabase-customer", "supabase-cms", "linear"]}