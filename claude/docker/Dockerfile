FROM ubuntu:22.04

# Set environment variables to prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Set up working directory
WORKDIR /workspace

# Install basic system dependencies
RUN apt-get update && apt-get install -y \
    lsof \
    curl \
    wget \
    git \
    build-essential \
    ca-certificates \
    gnupg \
    lsb-release \
    software-properties-common \
    sudo \
    vim \
    nano \
    unzip \
    zip \
    jq \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install Python 3.11
RUN add-apt-repository ppa:deadsnakes/ppa && \
    apt-get update && \
    apt-get install -y \
    python3.11 \
    python3.11-dev \
    python3.11-venv \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Set Python 3.11 as default python3
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1 && \
    update-alternatives --install /usr/bin/python python /usr/bin/python3.11 1

# Update PIP and install pipenv
RUN python3 -m pip install --upgrade pip setuptools wheel uv uvicorn gunicorn

# Pre-install numpy and cython before installing dependencies
RUN uv venv
RUN uv pip install numpy cython
ENV BLIS_ARCH="generic"
RUN uv pip install blis
RUN uv pip install spacy
RUN uv pip install pip

# Download spaCy model inside the virtual environment
RUN  uv run python -m spacy download en_core_web_sm

# Install Node.js 18+ using NodeSource repository
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs

# Tools
RUN apt-get install ripgrep

# Install gh
RUN (type -p wget >/dev/null || (sudo apt update && sudo apt-get install wget -y)) \
	&& sudo mkdir -p -m 755 /etc/apt/keyrings \
        && out=$(mktemp) && wget -nv -O$out https://cli.github.com/packages/githubcli-archive-keyring.gpg \
        && cat $out | sudo tee /etc/apt/keyrings/githubcli-archive-keyring.gpg > /dev/null \
	&& sudo chmod go+r /etc/apt/keyrings/githubcli-archive-keyring.gpg \
	&& echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
	&& sudo apt update \
	&& sudo apt install gh -y

# Install pnpm
RUN npm install -g pnpm

## Install playwright
RUN npx playwright install-deps

# Install Claude Code CLI
RUN npm install -g @anthropic-ai/claude-code

# Create a non-root user
RUN useradd -m -s /bin/bash claude && \
    echo 'claude ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

# Create necessary directories for Claude configuration
RUN mkdir -p /home/<USER>/.claude /home/<USER>/.config /tmp/claude-config && \
    chown -R claude:claude /home/<USER>/.claude  /home/<USER>/.config /tmp/claude-config
# Switch to claude user
USER claude
WORKDIR /workspace



# Copy entrypoint script
COPY --chown=claude:claude entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
