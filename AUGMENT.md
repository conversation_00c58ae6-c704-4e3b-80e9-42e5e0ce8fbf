# Global Preferences

* Please commit changes when finished.
* I use zsh shell.
* I use tailwind and typescript.
* Fail Fast, don't add fallbacks or backwards compatability unless explicitly asked to.

# Coding Philosophy
- Max 300-400 lines per file, please split your output into multiple files, creating packages as you need.
- Defensive validation of function parameters, throw ValueErrors if incorrect. Add explicit error logging and exception propagation.
- Do not put in runtime checks for things you are uncertain of at compile time (such as getattr("xyz")) instead FAIL FAST. Avoid silent degradation, fail fast.
- Fail fast, don't try to work out how to continue raise an Error
- When data and functionality are closely related use classes not functions. No need to be Object Oriented about everything, just when it makes sense.
- If a list can only contain unique values consider using a set instead.
- Don't 'swallow' exceptions, log them and raise

# The project

## Project Structure
This is a mono-repo

```
/backoffice/     # Python backend
  /src/          # Python code
    /eko/        # Core modules
    /tests/      # Unit tests
/apps/           # Frontend apps
  /cms/          # NextJS CMS (Payload)
  /customer/     # Customer app
/packages/       # Shared libraries
/db/             # Database files
/schema/         # Schema dump
```

## Frontend (React/Typescript)

Run `pnpm build` before committing.

### Payload CMS `/apps/cms`
The CMS uses Payload with configurable components:

- Create `config.ts` files to define schemas for CMS configuration
- Use array fields for lists and `link` type for navigation
- Include fallbacks for missing data
- Use Next.js `revalidateTag` for cache refresh

```typescript
// Component config pattern
{
  name: 'sectionName',
  type: 'array',
  label: 'Label',
  fields: [...],
  admin: {
    initCollapsed: true,
    components: { RowLabel: '@/Path/RowLabel#RowLabel' }
  },
}
```

**Key UI Components:**
- **Design System**: Glass-morphism with heavily rounded elements
    - Translucent, frosted glass-like surfaces with backdrop blur
    - Generous border radii (standard: 1.5rem) for a modern, approachable feel
    - Subtle shadows and animations for depth and interactivity
- **Header**: Transparent with backdrop blur (`/Header/Component.client.tsx`)
    - Glass-effect styling with heavily rounded bottom corners
    - Fixed positioning with conditional spacer for non-home pages
    - Transparent to solid background transition on scroll
- **Cards**: Glass-morphism cards with rounded corners
    - `GlassCard` component for image-based content cards
    - `CardGeneric` component for flexible content cards
    - Consistent hover animations with subtle lift effects
- **Footer**: Gradient background with spacing (`/Footer/Component.tsx`)
- **Blocks**: Content, Features, Banner in `/blocks/`
    - Block-based content system with standardized wrappers
    - Uses `BlockWrapper` component for consistent glass-morphism styling
- **Visual**: AuroraBackground for animations, glass effects
- **Design**: Consistent spacing, heavily rounded corners, hover states


## Python Backend


### Common Commands & Style Guidelines
```zsh
# Python commands in backoffice
cd backoffice/src && uv run python
cd backoffice/src && uv run python src/cli.py claude-self-test  # Self-test
cd backoffice/src && uv run python-m pytest src/tests      # Run tests
cd backoffice/src && uvx pyright <filename>              # Typecheck

# Web apps
pnpm build                      # Build frontend
```


### Python Code Style
- I use uv
- Max 300-400 lines per file, please split your output into multiple files, creating packages as you need.
- Type annotations with `Optional[T]` for nullable values
- Defensive validation of function parameters, throw ValueErrors if incorrect. Add explicit error logging and exception propagation.
- Do not put in runtime checks for things you are uncertain of at compile time (such as getattr("xyz")) instead FAIL FAST. Avoid silent degradation, fail fast.
- `snake_case` variables, `PascalCase` classes, `UPPER_SNAKE_CASE` constants
- Sort imports: stdlib → third-party → local
- Use loguru for logging (use **logger.exception** for exception recording not logger.error)
- Document with docstrings including param/return types, only describe the purpose of the code not how it works.
- Prefer non-Optional fields to Optional ones, never make a pydantic field List or Dict Optional, use default_factory=
- Fail fast, don't try to work out how to continue raise an Error
- When data and functionality are closely related use classes not functions. No need to be Object Oriented about everything, just when it makes sense.
- Prefer to use  Pydantic classes over dictionaries, only use non-typed objects such as dicts, dataframes etc. in performance sensitive code (like clustering etc.).  The core models of a process should be **Pydantic** classes.
- You have plenty of memory so all non-trivial data should be stored as a graph of fully realised objects, not referenced by a list of database ids. If a model is excessively memory hungry I will deal with that.
- If a list can only contain unique values consider using a set instead.
- Pass contextual data down through function calls, prefer Pydantic classes rather than large lists of parameters.
- Don't 'swallow' exceptions, log them and raise
- Use eko.settings to save any 'magic' numnbers like hyperparameters etc.
- When faced with circular imports it's usually best to break files down into smaller chunks. 



### Key Packages

Ignore anything in _deprecated_code_, do not use or edit this code.

- `cli`: Command line interface
- `eko.db.data`: DAOs (see statement.py as example)
- `eko.llm`: LLM integration (providers, prompts)
- `eko.models`: Pydantic models (not ORM)
- `eko.models.vector`: Embedding models (DEMISE, TREVR)
- `eko.analysis_v2.effects`: Effect analysis system
- `eko.analysis_v2.responsibility`: Responsibility analysis system
- `eko.nlp`: NLP processing utilities
- `eko.domains`: Web domain management
- `eko.entities`: Named entity management

### Core Patterns

**DAOs**: 

Each DAO is tied to a Pydantic model class.

Create methods return the model class with `id` set
Retrieve methods return the model class tied to the DAO
Find/select methods return a list of the model class tied to the DAO
Update methods take a model, return the same.
Delete methods return the id of the deleted model.

**Effect Analysis**:
- Identifies effects from entity statements
- Clusters statements and generates flags
- Effect flags are evaluated against the DEMISE model to calculate domain vectors
- Effect flags are deduplicated using DBSCAN clustering on the Domain part of the DEMISE model
- Claims and promises analysis system:
    - `claims_and_promises.py`: Shared code for both systems
    - `claim_evidence.py`: Analyzes claims vs historical evidence
    - `promise_analysis.py`: Analyzes promises vs future evidence
  ```bash
  # Run claims analysis
  cd backoffice/src && uv run cli.py analyze-claims --entity "EntityShortId" --start-year 2019 --end-year 2025

  # Run promises analysis
  cd backoffice/src && uv run cli.py analyze-promises --entity "EntityShortId" --start-year 2019 --end-year 2025
  ```

**DEMISE Model for Effect Flags**:
- Each effect flag has a DEMISE model calculated from its title, summary and analysis text
- The specialized `extract_effect_flag_demise()` function is used (not the same as for statements)
- Effect flags use a different prompt that acknowledges they are analyses, not raw statements
- The DEMISE model is stored in the `full_demise_centroid` field of the `EffectFlagModel`
- The Domain part of the DEMISE model is used for clustering similar flags
- DBSCAN clustering with minimum cluster size of 1 is used for deduplication
- Flags are only merged if they have similar years (within 2 years)

**PipelineTracker**: Located in `pipeline_tracker.py`
- Records metrics about pipeline stages in 'dash' schema
- Provides tracking methods for each stage


**CLI commands**:
- Use hyphenated names like `create-effect-flags-viz`
- Add to cli package in dedicated files (not cli.py)
- Implement with named parameters



### File Storage

- **Always use `eko_var_path`**:
  ```python
  from eko import eko_var_path
  os.path.join(eko_var_path, "cache/entity_cache")
  ```
- **Never use `../var/` paths**
- **Common directories**: `cache/`, `files/`, `embed/`, `models/`

### Best Practices
- Use loguru (logger.exception, not logger.error)
- Store temp files in `tmp` at root
- Git: Use `git mv` and `git rm` (not plain mv/rm)
- Run pyright before committing
