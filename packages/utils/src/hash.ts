export function hash64(obj: any) {
  let hash1 = 0xdeadbeef // First seed
  let hash2 = 0x41c6ce57 // Second seed
  const str = JSON.stringify(obj)
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash1 = Math.imul(hash1 ^ char, 2654435761)
    hash2 = Math.imul(hash2 ^ char, 1597334677)
  }

  hash1 = Math.imul(hash1 ^ (hash1 >>> 16), 2246822507)
  hash1 = Math.imul(hash1 ^ (hash1 >>> 13), 3266489909)
  hash1 = (hash1 ^ (hash1 >>> 16)) >>> 0

  hash2 = Math.imul(hash2 ^ (hash2 >>> 15), 3951481745)
  hash2 = Math.imul(hash2 ^ (hash2 >>> 13), 2938465626)
  hash2 = (hash2 ^ (hash2 >>> 16)) >>> 0

  // Generate 64-character string (16 hex chars from each of 4 hashes)
  let h1 = hash1.toString(16).padStart(8, '0')
  let h2 = hash2.toString(16).padStart(8, '0')
  let h3 = (hash1 ^ hash2).toString(16).padStart(8, '0')
  let h4 = (hash1 + hash2).toString(16).padStart(8, '0')

  // Spread the impact of input bits
  for (let i = 0; i < 3; i++) {
    h1 = (parseInt(h1, 16) ^ parseInt(h4, 16)).toString(16).padStart(8, '0')
    h2 = (parseInt(h2, 16) ^ parseInt(h1, 16)).toString(16).padStart(8, '0')
    h3 = (parseInt(h3, 16) ^ parseInt(h2, 16)).toString(16).padStart(8, '0')
    h4 = (parseInt(h4, 16) ^ parseInt(h3, 16)).toString(16).padStart(8, '0')
  }

  // Combine all parts to create a 64-character string
  return (h1 + h2 + h3 + h4).padStart(64, '0')
}
