#!/bin/bash
set -ex

## Colors for output
#RED='\033[0;31m'
#GREEN='\033[0;32m'
#YELLOW='\033[1;33m'
#NC='\033[0m' # No Color

RED='==!!!!!!=='
GREEN='--------'
YELLOW='==????=='
NC='======='


echo -e "${GREEN} Setting up mono-repo development environment ${NC}"

# Check if running on Ubuntu/Debian
if ! command -v apt-get &> /dev/null; then
    echo -e "${RED}Error: This script requires apt-get (Ubuntu/Debian) ${NC}"
    exit 1
fi

# Update system packages
sudo apt-get update -qq
sudo apt-get install -y software-properties-common

# Add deadsnakes PPA for Python 3.11
sudo add-apt-repository -y ppa:deadsnakes/ppa
sudo apt-get update -qq

# Install system dependencies exactly as in Dockerfile
echo "Installing system dependencies..."
sudo apt-get install -y curl wget postgresql-client > /dev/null 2>&1

# Install Node.js 18+ for frontend
echo "Installing Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - > /dev/null 2>&1
sudo apt-get install -y nodejs > /dev/null 2>&1


# Install pnpm using corepack
echo "Installing pnpm..."
sudo corepack enable > /dev/null 2>&1
corepack prepare pnpm@9.0.0 --activate > /dev/null 2>&1

# Go back to root and install frontend dependencies
cd /mnt/persist/workspace

echo -e "${GREEN} Setup completed successfully ${NC}"

# Test basic functionality
echo -e "${YELLOW} Running basic tests... ${NC}"

cd /mnt/persist/workspace
pnpm --version

#Install playwright
echo -e "${YELLOW} Installing Playwright browsers (this may take several minutes)... ${NC}"
sudo npm install -g playwright
sudo npx playwright install
sudo npx playwright install-deps
cd -
echo -e "${GREEN} Setup completed successfully ${NC}"
