#!/usr/bin/env python3
"""Test script to verify that the EffectFlagData.get_by_id fix works correctly."""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backoffice', 'src'))

from eko.db import get_bo_conn
from eko.db.data.effect_flag import EffectFlagData

def test_effect_flag_retrieval():
    """Test that effect flag 18062 now has impact_measurement and impact_evaluation populated."""
    
    conn = get_bo_conn()
    try:
        # Get the effect flag
        effect_flag = EffectFlagData.get_by_id(conn, 18062)
        
        if effect_flag is None:
            print("❌ Effect flag 18062 not found")
            return False
            
        print(f"✅ Effect flag {effect_flag.id} retrieved successfully")
        print(f"   Title: {effect_flag.title}")
        
        # Check if impact_measurement exists
        if hasattr(effect_flag, 'impact_measurement') and effect_flag.impact_measurement:
            print(f"✅ impact_measurement populated: {type(effect_flag.impact_measurement).__name__}")
        else:
            print("❌ impact_measurement missing or None")
            
        # Check if impact_evaluation exists  
        if hasattr(effect_flag, 'impact_evaluation') and effect_flag.impact_evaluation:
            print(f"✅ impact_evaluation populated: {type(effect_flag.impact_evaluation).__name__}")
        else:
            print("❌ impact_evaluation missing or None")
            
        return True
        
    finally:
        conn.close()

if __name__ == "__main__":
    test_effect_flag_retrieval()