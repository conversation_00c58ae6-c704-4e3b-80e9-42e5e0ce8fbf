-- Add is_public field to collaborative_documents table for public sharing
-- EKO-89: Document Sharing

-- Add the is_public column if it doesn't exist
ALTER TABLE collaborative_documents 
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT FALSE;

-- Update RLS policy to allow public access to public documents
-- First drop the existing policy if it exists
DROP POLICY IF EXISTS "Users can view documents in their organisation or public documents" ON collaborative_documents;

-- Create new policy that allows public access to public documents
CREATE POLICY "Users can view documents in their organisation or public documents" 
ON collaborative_documents FOR SELECT 
USING (
  -- Allow access if user is authenticated and document is in their organisation
  (auth.uid() IS NOT NULL AND created_by = auth.uid()) OR
  -- Allow access to public documents (no authentication required)
  is_public = TRUE
);

-- Create policy for updating is_public field (only document owners can change this)
CREATE POLICY "Document owners can update public status" 
ON collaborative_documents FOR UPDATE 
USING (created_by = auth.uid())
WITH CHECK (created_by = auth.uid());

-- Add comment to document the purpose
COMMENT ON COLUMN collaborative_documents.is_public IS 'When true, document can be accessed publicly without authentication via /share/public/documents/[id]';
