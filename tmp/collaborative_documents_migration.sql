-- Create collaborative_documents table for storing TipTap documents
CREATE TABLE IF NOT EXISTS collaborative_documents (
  id TEXT PRIMARY KEY,
  content TEXT,
  title TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  metadata JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_collaborative_documents_created_by ON collaborative_documents(created_by);
CREATE INDEX IF NOT EXISTS idx_collaborative_documents_updated_by ON collaborative_documents(updated_by);
CREATE INDEX IF NOT EXISTS idx_collaborative_documents_updated_at ON collaborative_documents(updated_at);

-- Enable Row Level Security
ALTER TABLE collaborative_documents ENABLE ROW LEVEL SECURITY;

-- Create policies for collaborative_documents
-- Users can read documents they created or that are shared with them
DROP POLICY IF EXISTS "Users can read their own documents" ON collaborative_documents;
CREATE POLICY "Users can read their own documents" ON collaborative_documents
  FOR SELECT USING (
    auth.uid() = created_by OR
    auth.uid() = updated_by OR
    -- Allow reading if user has access (you can customize this logic)
    true -- For now, allow all authenticated users to read
  );

-- Users can insert new documents
DROP POLICY IF EXISTS "Users can create documents" ON collaborative_documents;
CREATE POLICY "Users can create documents" ON collaborative_documents
  FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Users can update documents they have access to
DROP POLICY IF EXISTS "Users can update accessible documents" ON collaborative_documents;
CREATE POLICY "Users can update accessible documents" ON collaborative_documents
  FOR UPDATE USING (
    auth.uid() = created_by OR
    auth.uid() = updated_by OR
    -- Allow updating if user has access (you can customize this logic)
    true -- For now, allow all authenticated users to update
  );

-- Users can delete documents they created
DROP POLICY IF EXISTS "Users can delete their own documents" ON collaborative_documents;
CREATE POLICY "Users can delete their own documents" ON collaborative_documents
  FOR DELETE USING (auth.uid() = created_by);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_collaborative_documents_updated_at ON collaborative_documents;
CREATE TRIGGER update_collaborative_documents_updated_at
  BEFORE UPDATE ON collaborative_documents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create document_permissions table for fine-grained access control (optional)
CREATE TABLE IF NOT EXISTS document_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id TEXT REFERENCES collaborative_documents(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  permission_type TEXT CHECK (permission_type IN ('read', 'write', 'admin')) DEFAULT 'read',
  granted_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  granted_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(document_id, user_id)
);

-- Create indexes for document_permissions
CREATE INDEX IF NOT EXISTS idx_document_permissions_document_id ON document_permissions(document_id);
CREATE INDEX IF NOT EXISTS idx_document_permissions_user_id ON document_permissions(user_id);

-- Enable RLS for document_permissions
ALTER TABLE document_permissions ENABLE ROW LEVEL SECURITY;

-- Policies for document_permissions
DROP POLICY IF EXISTS "Users can read permissions for their documents" ON document_permissions;
CREATE POLICY "Users can read permissions for their documents" ON document_permissions
  FOR SELECT USING (
    user_id = auth.uid() OR
    document_id IN (
      SELECT id FROM collaborative_documents
      WHERE created_by = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Document owners can manage permissions" ON document_permissions;
CREATE POLICY "Document owners can manage permissions" ON document_permissions
  FOR ALL USING (
    document_id IN (
      SELECT id FROM collaborative_documents
      WHERE created_by = auth.uid()
    )
  );

-- Create document_comments table for storing comments
CREATE TABLE IF NOT EXISTS document_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id TEXT REFERENCES collaborative_documents(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  position_from INTEGER,
  position_to INTEGER,
  parent_comment_id UUID REFERENCES document_comments(id) ON DELETE CASCADE,
  resolved BOOLEAN DEFAULT FALSE,
  resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  resolved_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for document_comments
CREATE INDEX IF NOT EXISTS idx_document_comments_document_id ON document_comments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_comments_user_id ON document_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_document_comments_parent_id ON document_comments(parent_comment_id);
CREATE INDEX IF NOT EXISTS idx_document_comments_created_at ON document_comments(created_at);

-- Enable RLS for document_comments
ALTER TABLE document_comments ENABLE ROW LEVEL SECURITY;

-- Policies for document_comments
DROP POLICY IF EXISTS "Users can read comments on accessible documents" ON document_comments;
CREATE POLICY "Users can read comments on accessible documents" ON document_comments
  FOR SELECT USING (
    document_id IN (
      SELECT id FROM collaborative_documents
      WHERE created_by = auth.uid() OR updated_by = auth.uid() OR true -- For now, allow all
    )
  );

DROP POLICY IF EXISTS "Users can create comments on accessible documents" ON document_comments;
CREATE POLICY "Users can create comments on accessible documents" ON document_comments
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    document_id IN (
      SELECT id FROM collaborative_documents
      WHERE created_by = auth.uid() OR updated_by = auth.uid() OR true -- For now, allow all
    )
  );

DROP POLICY IF EXISTS "Users can update their own comments" ON document_comments;
CREATE POLICY "Users can update their own comments" ON document_comments
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own comments" ON document_comments;
CREATE POLICY "Users can delete their own comments" ON document_comments
  FOR DELETE USING (auth.uid() = user_id);

-- Create document_versions table for version history
CREATE TABLE IF NOT EXISTS document_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id TEXT REFERENCES collaborative_documents(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  title TEXT,
  content TEXT,
  data JSONB,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  change_summary TEXT,
  is_auto_save BOOLEAN DEFAULT FALSE,
  UNIQUE(document_id, version_number)
);

-- Create indexes for document_versions
CREATE INDEX IF NOT EXISTS idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX IF NOT EXISTS idx_document_versions_created_by ON document_versions(created_by);
CREATE INDEX IF NOT EXISTS idx_document_versions_created_at ON document_versions(created_at);
CREATE INDEX IF NOT EXISTS idx_document_versions_version_number ON document_versions(document_id, version_number);

-- Enable RLS for document_versions
ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;

-- Policies for document_versions
DROP POLICY IF EXISTS "Users can read versions of accessible documents" ON document_versions;
CREATE POLICY "Users can read versions of accessible documents" ON document_versions
  FOR SELECT USING (
    document_id IN (
      SELECT id FROM collaborative_documents
      WHERE created_by = auth.uid() OR updated_by = auth.uid() OR true -- For now, allow all
    )
  );

DROP POLICY IF EXISTS "Users can create versions for accessible documents" ON document_versions;
CREATE POLICY "Users can create versions for accessible documents" ON document_versions
  FOR INSERT WITH CHECK (
    auth.uid() = created_by AND
    document_id IN (
      SELECT id FROM collaborative_documents
      WHERE created_by = auth.uid() OR updated_by = auth.uid() OR true -- For now, allow all
    )
  );

-- Create document_presence table for real-time user presence
CREATE TABLE IF NOT EXISTS document_presence (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id TEXT REFERENCES collaborative_documents(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  user_name TEXT NOT NULL,
  user_email TEXT,
  user_avatar TEXT,
  user_color TEXT DEFAULT '#3B82F6',
  cursor_position JSONB,
  selection JSONB,
  last_seen TIMESTAMPTZ DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE,
  UNIQUE(document_id, user_id)
);

-- Create indexes for document_presence
CREATE INDEX IF NOT EXISTS idx_document_presence_document_id ON document_presence(document_id);
CREATE INDEX IF NOT EXISTS idx_document_presence_user_id ON document_presence(user_id);
CREATE INDEX IF NOT EXISTS idx_document_presence_last_seen ON document_presence(last_seen);
CREATE INDEX IF NOT EXISTS idx_document_presence_active ON document_presence(document_id, is_active);

-- Enable RLS for document_presence
ALTER TABLE document_presence ENABLE ROW LEVEL SECURITY;

-- Policies for document_presence
DROP POLICY IF EXISTS "Users can read presence on accessible documents" ON document_presence;
CREATE POLICY "Users can read presence on accessible documents" ON document_presence
  FOR SELECT USING (
    document_id IN (
      SELECT id FROM collaborative_documents
      WHERE created_by = auth.uid() OR updated_by = auth.uid() OR true -- For now, allow all
    )
  );

DROP POLICY IF EXISTS "Users can manage their own presence" ON document_presence;
CREATE POLICY "Users can manage their own presence" ON document_presence
  FOR ALL USING (auth.uid() = user_id);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON collaborative_documents TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON document_permissions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON document_comments TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON document_versions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON document_presence TO authenticated;
