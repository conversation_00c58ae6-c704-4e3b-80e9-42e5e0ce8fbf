-- Final UUID fix - drop the remaining specific policies and fix column types

-- Drop the specific remaining policies
DROP POLICY IF EXISTS "Users can delete their own documents" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can read their own documents" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can update accessible documents" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can read versions of accessible documents" ON document_versions;
DROP POLICY IF EXISTS "Users can read permissions for their documents" ON document_permissions;

-- Disable R<PERSON> temporarily
ALTER TABLE collaborative_documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_versions DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_permissions DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_presence DISABLE ROW LEVEL SECURITY;

-- Drop any remaining foreign key constraints that might prevent column type changes
ALTER TABLE document_comments DROP CONSTRAINT IF EXISTS fk_document_comments_document_id;
ALTER TABLE document_versions DROP CONSTRAINT IF EXISTS fk_document_versions_document_id;
ALTER TABLE document_permissions DROP CONSTRAINT IF EXISTS fk_document_permissions_document_id;
ALTER TABLE document_presence DROP CONSTRAINT IF EXISTS fk_document_presence_document_id;

-- Now fix the collaborative_documents.id column type
ALTER TABLE collaborative_documents 
    ALTER COLUMN id TYPE UUID USING id::UUID;

-- Fix document_comments.document_id to match
ALTER TABLE document_comments 
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID;

-- Fix document_versions.document_id to match
ALTER TABLE document_versions 
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID;

-- Fix document_permissions.document_id to match
ALTER TABLE document_permissions 
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID;

-- Fix document_presence.document_id to match
ALTER TABLE document_presence 
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID;

-- Now add back the foreign key constraints
ALTER TABLE document_comments 
    ADD CONSTRAINT fk_document_comments_document_id 
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

ALTER TABLE document_versions 
    ADD CONSTRAINT fk_document_versions_document_id 
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

ALTER TABLE document_permissions 
    ADD CONSTRAINT fk_document_permissions_document_id 
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

ALTER TABLE document_presence 
    ADD CONSTRAINT fk_document_presence_document_id 
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

-- Re-enable RLS
ALTER TABLE collaborative_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_presence ENABLE ROW LEVEL SECURITY;
