-- Add overall_impact field to ana_predict_component_analysis table
ALTER TABLE ana_predict_component_analysis 
ADD COLUMN IF NOT EXISTS overall_impact FLOAT;

-- Update existing records to set default value for overall_impact
UPDATE ana_predict_component_analysis
SET overall_impact = 0.0
WHERE overall_impact IS NULL;

-- Add comment to the new column
COMMENT ON COLUMN ana_predict_component_analysis.overall_impact IS 'Overall impact value from the component prediction, positive values indicate beneficial impact, negative values indicate harmful impact';

-- Add overall_impact field to xfer_predictive_component_analysis table
ALTER TABLE xfer_predictive_component_analysis 
ADD COLUMN IF NOT EXISTS overall_impact FLOAT;

-- Update existing records to set default value for overall_impact
UPDATE xfer_predictive_component_analysis
SET overall_impact = 0.0
WHERE overall_impact IS NULL;

-- Add comment to the new column
COMMENT ON COLUMN xfer_predictive_component_analysis.overall_impact IS 'Overall impact value from the component prediction, positive values indicate beneficial impact, negative values indicate harmful impact';
