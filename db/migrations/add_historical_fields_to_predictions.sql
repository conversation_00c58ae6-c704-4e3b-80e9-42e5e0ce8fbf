-- Add new columns to ana_predict_cluster_predictions table
ALTER TABLE ana_predict_cluster_predictions 
ADD COLUMN IF NOT EXISTS final_historical_year INTEGER,
ADD COLUMN IF NOT EXISTS historical_vector JSONB;

-- Rename predicted_demise to predicted_vector for consistency
ALTER TABLE ana_predict_cluster_predictions 
RENAME COLUMN predicted_demise TO predicted_vector;

-- Update existing records to set default values
UPDATE ana_predict_cluster_predictions
SET final_historical_year = EXTRACT(YEAR FROM created_at)::INTEGER - 1,
    historical_vector = '[]'::JSONB
WHERE final_historical_year IS NULL OR historical_vector IS NULL;

-- Add a comment to explain the purpose of these fields
COMMENT ON COLUMN ana_predict_cluster_predictions.final_historical_year IS 'The final year of historical data used for prediction';
COMMENT ON COLUMN ana_predict_cluster_predictions.historical_vector IS 'Vector from the final historical year';
COMMENT ON COLUMN ana_predict_cluster_predictions.predicted_vector IS 'Predicted vector for the future year (renamed from predicted_demise)';
