-- Create tables for prediction_v2 package

-- Create enum for component types
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'component_type') THEN
        CREATE TYPE component_type AS ENUM (
            'motivation',
            'statement_type',
            'engagement',
            'impact'
        );
    END IF;
END$$;

-- Create table for DSO clusters
CREATE TABLE IF NOT EXISTS ana_predict_dso_clusters (
    id SERIAL PRIMARY KEY,
    run_id INTEGER NOT NULL REFERENCES ana_runs(id) ON DELETE CASCADE,
    virtual_entity_id INTEGER NOT NULL REFERENCES kg_virtual_entities(id) ON DELETE CASCADE,
    year INTEGER NOT NULL,
    domain_centroid FLOAT[] NOT NULL,
    subject_centroid FLOAT[] NOT NULL,
    object_centroid FLOAT[] NOT NULL,
    statement_ids INTEGER[] NOT NULL,
    size INTEGER NOT NULL,
    coherence FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on virtual_entity_id and run_id
CREATE INDEX IF NOT EXISTS ana_predict_dso_clusters_virtual_entity_id_run_id_idx
ON ana_predict_dso_clusters(virtual_entity_id, run_id);

-- Create index on year
CREATE INDEX IF NOT EXISTS ana_predict_dso_clusters_year_idx
ON ana_predict_dso_clusters(year);

-- Create table for component predictions
CREATE TABLE IF NOT EXISTS ana_predict_component_predictions (
    id SERIAL PRIMARY KEY,
    run_id INTEGER NOT NULL REFERENCES ana_runs(id) ON DELETE CASCADE,
    virtual_entity_id INTEGER NOT NULL REFERENCES kg_virtual_entities(id) ON DELETE CASCADE,
    cluster_id INTEGER NOT NULL REFERENCES ana_predict_dso_clusters(id) ON DELETE CASCADE,
    component_type component_type NOT NULL,
    predicted_year INTEGER NOT NULL,
    final_historical_year INTEGER NOT NULL,
    predicted_vector FLOAT[] NOT NULL,
    historical_vector FLOAT[] NOT NULL,
    confidence FLOAT NOT NULL,
    model_type regression_model_type NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on virtual_entity_id and run_id
CREATE INDEX IF NOT EXISTS ana_predict_component_predictions_virtual_entity_id_run_id_idx
ON ana_predict_component_predictions(virtual_entity_id, run_id);

-- Create index on cluster_id
CREATE INDEX IF NOT EXISTS ana_predict_component_predictions_cluster_id_idx
ON ana_predict_component_predictions(cluster_id);

-- Create index on component_type
CREATE INDEX IF NOT EXISTS ana_predict_component_predictions_component_type_idx
ON ana_predict_component_predictions(component_type);

-- Create index on predicted_year
CREATE INDEX IF NOT EXISTS ana_predict_component_predictions_predicted_year_idx
ON ana_predict_component_predictions(predicted_year);

-- Create table for predictive component analysis
CREATE TABLE IF NOT EXISTS ana_predict_component_analysis (
    id SERIAL PRIMARY KEY,
    run_id INTEGER NOT NULL REFERENCES ana_runs(id) ON DELETE CASCADE,
    prediction_id INTEGER NOT NULL REFERENCES ana_predict_component_predictions(id) ON DELETE CASCADE,
    virtual_entity_id INTEGER NOT NULL REFERENCES kg_virtual_entities(id) ON DELETE CASCADE,
    cluster_id INTEGER NOT NULL REFERENCES ana_predict_dso_clusters(id) ON DELETE CASCADE,
    component_type component_type NOT NULL,
    year INTEGER NOT NULL,
    summary TEXT NOT NULL,
    detailed_analysis TEXT NOT NULL,
    potential_risks TEXT[] NOT NULL,
    potential_opportunities TEXT[] NOT NULL,
    confidence FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on virtual_entity_id and run_id
CREATE INDEX IF NOT EXISTS ana_predict_component_analysis_virtual_entity_id_run_id_idx
ON ana_predict_component_analysis(virtual_entity_id, run_id);

-- Create index on prediction_id
CREATE INDEX IF NOT EXISTS ana_predict_component_analysis_prediction_id_idx
ON ana_predict_component_analysis(prediction_id);

-- Create index on cluster_id
CREATE INDEX IF NOT EXISTS ana_predict_component_analysis_cluster_id_idx
ON ana_predict_component_analysis(cluster_id);

-- Create index on component_type
CREATE INDEX IF NOT EXISTS ana_predict_component_analysis_component_type_idx
ON ana_predict_component_analysis(component_type);

-- Create index on year
CREATE INDEX IF NOT EXISTS ana_predict_component_analysis_year_idx
ON ana_predict_component_analysis(year);

-- Create transfer table for predictive component analysis
CREATE TABLE IF NOT EXISTS xfer_predictive_component_analysis (
    id SERIAL PRIMARY KEY,
    run_id INTEGER NOT NULL,
    prediction_id INTEGER NOT NULL,
    virtual_entity_id INTEGER NOT NULL,
    cluster_id INTEGER NOT NULL,
    component_type TEXT NOT NULL,
    year INTEGER NOT NULL,
    summary TEXT NOT NULL,
    detailed_analysis TEXT NOT NULL,
    potential_risks TEXT[] NOT NULL,
    potential_opportunities TEXT[] NOT NULL,
    confidence FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on virtual_entity_id and year
CREATE INDEX IF NOT EXISTS xfer_predictive_component_analysis_virtual_entity_id_year_idx
ON xfer_predictive_component_analysis(virtual_entity_id, year);

-- Create index on cluster_id
CREATE INDEX IF NOT EXISTS xfer_predictive_component_analysis_cluster_id_idx
ON xfer_predictive_component_analysis(cluster_id);

-- Create index on component_type
CREATE INDEX IF NOT EXISTS xfer_predictive_component_analysis_component_type_idx
ON xfer_predictive_component_analysis(component_type);

-- Add comments to tables
COMMENT ON TABLE ana_predict_dso_clusters IS 'Clusters based on Domain+Subject+Object components of DEMISE model';
COMMENT ON TABLE ana_predict_component_predictions IS 'Predictions for individual DEMISE components (Motivation, Statement Types, Engagement, Impact)';
COMMENT ON TABLE ana_predict_component_analysis IS 'Human-readable analysis of predicted component trends';
COMMENT ON TABLE xfer_predictive_component_analysis IS 'Transfer table for predictive component analysis';
