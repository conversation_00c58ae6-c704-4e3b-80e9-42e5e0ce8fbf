-- Add missing indexes to kg_statements_v2 table for improved performance
-- This migration adds indexes to improve the performance of the search_for_statements function

-- Index for company_id (used in joins with kg_virt_entity_map)
CREATE INDEX IF NOT EXISTS idx_statements_v2_company_id ON kg_statements_v2(company_id);

-- Index for doc_id (used in joins with kg_documents)
CREATE INDEX IF NOT EXISTS idx_statements_v2_doc_id ON kg_statements_v2(doc_id);

-- Composite index for common filtering conditions
CREATE INDEX IF NOT EXISTS idx_statements_v2_esg_flags_company ON kg_statements_v2(company_id, is_environmental, is_social, is_governance, is_animal_welfare);

-- Create a GIN index on the tsvector of statement_text for text search
CREATE INDEX IF NOT EXISTS idx_statements_v2_statement_text_search ON kg_statements_v2 USING GIN (to_tsvector('english', statement_text));

-- Check if pg_vector extension is installed
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_extension WHERE extname = 'vector'
    ) THEN
        -- Create vector indexes for the embedding columns if pg_vector is installed
        -- Index for domain_embedding
        IF NOT EXISTS (
            SELECT 1 FROM pg_indexes WHERE indexname = 'idx_statements_v2_domain_embedding'
        ) THEN
            EXECUTE 'CREATE INDEX idx_statements_v2_domain_embedding ON kg_statements_v2 USING ivfflat (domain_embedding vector_cosine_ops)';
        END IF;

        -- Index for text_embedding
        IF NOT EXISTS (
            SELECT 1 FROM pg_indexes WHERE indexname = 'idx_statements_v2_text_embedding'
        ) THEN
            EXECUTE 'CREATE INDEX idx_statements_v2_text_embedding ON kg_statements_v2 USING ivfflat (text_embedding vector_cosine_ops)';
        END IF;
    ELSE
        RAISE NOTICE 'pg_vector extension is not installed. Vector indexes were not created.';
    END IF;
END
$$;
