# Pipeline Stages for Claims and Promises Analysis

This document explains the changes made to add dedicated pipeline stages for claims and promises analysis.

## Problem

The claims and promises analysis code was reusing pipeline stages from the effect processing pipeline, which made it difficult to track and analyze the performance of these specific processes. For example:

- `PipelineStage.DOCUMENT_RETRIEVED` was used for both effect processing and claims/promises analysis
- `PipelineStage.STATEMENT_EXTRACTED` was used for both effect processing and claims/promises analysis
- `PipelineStage.EFFECT_CREATED` was used for effect processing but also for tracking claim/promise evidence
- `PipelineStage.EFFECT_FLAG_CREATED` was used for effect flags but also for tracking claim/promise verdicts

This made it difficult to:
1. Distinguish between different types of processing in the pipeline tracking data
2. Analyze the performance of claims and promises analysis separately from effect processing
3. Understand where bottlenecks might be occurring in specific processes

## Solution

The solution consists of two parts:

### 1. New Pipeline Stages

Added the following new pipeline stages to the `PipelineStage` enum:

**For Claims Analysis:**
- `CLAIM_IDENTIFIED`: When a claim is identified in a statement
- `CLAIM_EVIDENCE_FOUND`: When evidence for a claim is found
- `CLAIM_VERDICT_CREATED`: When a verdict for a claim is created
- `CLAIM_VERDICT_STORED`: When a verdict for a claim is stored in the database

**For Promises Analysis:**
- `PROMISE_IDENTIFIED`: When a promise is identified in a statement
- `PROMISE_EVIDENCE_FOUND`: When evidence for a promise is found
- `PROMISE_VERDICT_CREATED`: When a verdict for a promise is created
- `PROMISE_VERDICT_STORED`: When a verdict for a promise is stored in the database

### 2. Code Changes

Modified the claims and promises analysis code to:

1. Use the new pipeline stages instead of reusing stages from effect processing
2. Properly handle the `Entity` type requirements of the `TraceabilityTracker` methods
3. Extract the first base entity from the virtual entity to use as the entity parameter for tracking

## How to Apply

1. Run the SQL migration script to add the new pipeline stages:
   ```bash
   ./bin/run_in_db.sh < db/migrations/20240625_add_pipeline_stages.sql
   ```

2. The code changes have been applied to:
   - `eko/analysis_v2/heart/trust_and_reliability/claims_and_promises.py`
   - `eko/analysis_v2/heart/trust_and_reliability/claim_evidence.py`
   - `eko/analysis_v2/heart/trust_and_reliability/promise_analysis.py`

## Expected Benefits

The changes will provide:

1. Better tracking of claims and promises analysis processes
2. More accurate performance metrics for each stage of the pipeline
3. Clearer distinction between different types of processing in the tracking data
4. Ability to identify bottlenecks specific to claims or promises analysis
