-- Add new column to ana_predict_component_predictions table
ALTER TABLE ana_predict_component_predictions
ADD COLUMN IF NOT EXISTS historical_vectors_by_year JSONB DEFAULT '{}'::JSONB;

-- Update existing records to set default values
-- Convert the final_historical_year and historical_vector to a JSONB object
UPDATE ana_predict_component_predictions
SET historical_vectors_by_year = jsonb_build_object(final_historical_year::TEXT, historical_vector)
WHERE historical_vectors_by_year = '{}'::JSONB;

-- Add comment to explain the new column
COMMENT ON COLUMN ana_predict_component_predictions.historical_vectors_by_year IS 'Dictionary mapping historical years to their corresponding vectors';
