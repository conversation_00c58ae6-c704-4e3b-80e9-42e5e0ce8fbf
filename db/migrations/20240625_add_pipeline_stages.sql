-- Add new pipeline stages for claims and promises analysis
-- This migration adds new values to the pipeline_stage enum type

-- Check if the enum type exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_type WHERE typname = 'pipeline_stage'
    ) THEN
        -- Add new values to the enum type if they don't exist
        -- For claims analysis
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'claim_identified'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'claim_identified';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'claim_evidence_found'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'claim_evidence_found';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'claim_verdict_created'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'claim_verdict_created';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'claim_verdict_stored'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'claim_verdict_stored';
        END IF;
        
        -- For promises analysis
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'promise_identified'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'promise_identified';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'promise_evidence_found'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'promise_evidence_found';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'promise_verdict_created'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'promise_verdict_created';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'promise_verdict_stored'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'promise_verdict_stored';
        END IF;
    ELSE
        RAISE NOTICE 'pipeline_stage enum type does not exist. No changes made.';
    END IF;
END
$$;
