-- Drop the unique constraint on virtual_entity_id and year
ALTER TABLE xfer_predictive_analysis 
DROP CONSTRAINT IF EXISTS xfer_predictive_analysis_virtual_entity_id_year_key;

-- Add a comment explaining why this constraint was dropped
COMMENT ON TABLE xfer_predictive_analysis IS 'Transfer table for predictive analysis. The unique constraint on (virtual_entity_id, year) was dropped to allow multiple analyses per entity per year, representing different cluster predictions.';
