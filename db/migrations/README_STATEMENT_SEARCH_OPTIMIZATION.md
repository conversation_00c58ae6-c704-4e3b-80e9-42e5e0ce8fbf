# Statement Search Optimization

This document explains the optimizations made to improve the performance of the `search_for_statements` function.

## Problem

The `search_for_statements` function in `eko/analysis_v2/heart/trust_and_reliability/claims_and_promises.py` was slow because:

1. It fetched all potential statements from the database
2. It deserialized each statement's model_json into a DEMISE model
3. It calculated vector similarity in Python
4. It filtered based on similarity threshold in Python

This approach was inefficient because:
- It transferred a lot of data from the database to the application
- It performed vector calculations in Python rather than using database capabilities
- It didn't leverage PostgreSQL's vector operations

## Update (2024-06-25)

Fixed an issue with the pg_vector implementation where PostgreSQL couldn't determine the data type of the vector parameter. The error was:

```
psycopg.errors.IndeterminateDatatype: could not determine data type of parameter $1
```

The solution was to embed the vector literal directly in the SQL query using a CTE (Common Table Expression) instead of passing it as a parameter. This avoids the parameter type determination issue.

## Solution

The solution consists of two parts:

### 1. Database Indexes

Added the following indexes to improve query performance:

- `idx_statements_v2_company_id`: Index on company_id for faster joins with kg_virt_entity_map
- `idx_statements_v2_doc_id`: Index on doc_id for faster joins with kg_documents
- `idx_statements_v2_esg_flags_company`: Composite index for common filtering conditions
- `idx_statements_v2_statement_text_search`: GIN index for text search on statement_text
- `idx_statements_v2_domain_embedding`: Vector index for domain_embedding (if pg_vector is installed)
- `idx_statements_v2_text_embedding`: Vector index for text_embedding (if pg_vector is installed)

### 2. Code Improvements

Modified the `search_for_statements` function to:

1. Check if the pg_vector extension is available
2. If available, use PostgreSQL's vector operations to calculate similarity directly in the database
3. If not available, fall back to the original implementation
4. Made the code more robust by handling different types of VirtualEntityModel objects
5. Improved error handling for DEMISE model vector extraction

## How to Apply

1. Run the SQL migration script to add the indexes:
   ```bash
   ./bin/run_in_db.sh < db/migrations/20240625_add_statement_indexes_for_search.sql
   ```

2. The code changes have been applied to:
   - `eko/analysis_v2/heart/trust_and_reliability/claims_and_promises.py`

## Expected Performance Improvement

The optimized version should be significantly faster because:

1. It uses database indexes for faster filtering
2. It calculates vector similarity directly in the database when possible
3. It reduces the amount of data transferred between the database and application
4. It filters based on similarity threshold in the database

## Fallback Mechanism

If the pg_vector extension is not available or if there's an error with the vector operations, the function will automatically fall back to the original implementation. This ensures backward compatibility while still providing the performance benefits when possible.
