-- Add source_text column to kg_statements_v2 table
-- This migration adds the source_text column to store the original unaltered text of statements

-- Add source_text column if it doesn't exist
ALTER TABLE kg_statements_v2 
ADD COLUMN IF NOT EXISTS source_text TEXT;

-- Add comment to explain the purpose of this field
COMMENT ON COLUMN kg_statements_v2.source_text IS 'The complete and unaltered text of the statement, as opposed to statement_text which may be expanded or contextualised.';

-- Create a GIN index on the tsvector of source_text for text search
CREATE INDEX IF NOT EXISTS idx_statements_v2_source_text_search ON kg_statements_v2 USING GIN (to_tsvector('english', source_text));
