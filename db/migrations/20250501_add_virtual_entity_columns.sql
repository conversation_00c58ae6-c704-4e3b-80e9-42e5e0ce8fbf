-- Add virtual_entity_id and virtual_entity_short_id columns to ana_promises_v2 table
ALTER TABLE ana_promises_v2 
ADD COLUMN IF NOT EXISTS virtual_entity_id INTEGER,
ADD COLUMN IF NOT EXISTS virtual_entity_short_id TEXT;

-- Add virtual_entity_id and virtual_entity_short_id columns to ana_claims_v2 table
ALTER TABLE ana_claims_v2 
ADD COLUMN IF NOT EXISTS virtual_entity_id INTEGER,
ADD COLUMN IF NOT EXISTS virtual_entity_short_id TEXT;

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_ana_promises_v2_virtual_entity_id ON ana_promises_v2(virtual_entity_id);
CREATE INDEX IF NOT EXISTS idx_ana_claims_v2_virtual_entity_id ON ana_claims_v2(virtual_entity_id);

-- Add comment to explain the purpose of these columns
COMMENT ON COLUMN ana_promises_v2.virtual_entity_id IS 'ID of the virtual entity associated with this promise';
COMMENT ON COLUMN ana_promises_v2.virtual_entity_short_id IS 'Short ID of the virtual entity associated with this promise';
COMMENT ON COLUMN ana_claims_v2.virtual_entity_id IS 'ID of the virtual entity associated with this claim';
COMMENT ON COLUMN ana_claims_v2.virtual_entity_short_id IS 'Short ID of the virtual entity associated with this claim';
