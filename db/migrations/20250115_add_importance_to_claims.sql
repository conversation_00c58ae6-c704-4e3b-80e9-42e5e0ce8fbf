-- Add importance column to ana_claims_v2 table
-- This migration adds the importance column to store the importance score of claims

-- Add importance column if it doesn't exist
ALTER TABLE ana_claims_v2 
ADD COLUMN IF NOT EXISTS importance INTEGER DEFAULT 0;

-- Add comment to explain the purpose of this field
COMMENT ON COLUMN ana_claims_v2.importance IS 'Importance score of the claim (0-100), where higher scores indicate more newsworthy and impactful claims';

-- Create an index on importance for efficient filtering and sorting
CREATE INDEX IF NOT EXISTS idx_ana_claims_v2_importance ON ana_claims_v2(importance);

-- Add importance column to xfer_gw_claims_v2 table in customer database
-- Note: This will be handled by the sync process, but we document it here for completeness
-- The xfer table structure will be updated when claims are synced
