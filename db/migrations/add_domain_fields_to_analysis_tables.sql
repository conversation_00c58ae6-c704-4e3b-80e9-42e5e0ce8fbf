-- Add core_domain_keys and core_domain_descriptions to ana_predict_cluster_analysis table
ALTER TABLE ana_predict_cluster_analysis
ADD COLUMN IF NOT EXISTS core_domain_keys TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS core_domain_descriptions TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS overall_impact FLOAT DEFAULT 0.0;

-- Add comments to the new columns
COMMENT ON COLUMN ana_predict_cluster_analysis.core_domain_keys IS 'Core domain keys with top values (>0.7)';
COMMENT ON COLUMN ana_predict_cluster_analysis.core_domain_descriptions IS 'Descriptions of core domain keys';
COMMENT ON COLUMN ana_predict_cluster_analysis.overall_impact IS 'Overall impact value, positive values indicate beneficial impact, negative values indicate harmful impact';

-- Add core_domain_keys and core_domain_descriptions to ana_predict_entity_year_analysis table
ALTER TABLE ana_predict_entity_year_analysis
ADD COLUMN IF NOT EXISTS core_domain_keys TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS core_domain_descriptions TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS overall_impact FLOAT DEFAULT 0.0;

-- Add comments to the new columns
COMMENT ON COLUMN ana_predict_entity_year_analysis.core_domain_keys IS 'Core domain keys with top values (>0.7)';
COMMENT ON COLUMN ana_predict_entity_year_analysis.core_domain_descriptions IS 'Descriptions of core domain keys';
COMMENT ON COLUMN ana_predict_entity_year_analysis.overall_impact IS 'Overall impact value, positive values indicate beneficial impact, negative values indicate harmful impact';

-- Add core_domain_keys and core_domain_descriptions to xfer_predict_cluster_analysis table
ALTER TABLE xfer_predict_cluster_analysis
ADD COLUMN IF NOT EXISTS core_domain_keys TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS core_domain_descriptions TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS overall_impact FLOAT DEFAULT 0.0;

-- Add comments to the new columns
COMMENT ON COLUMN xfer_predict_cluster_analysis.core_domain_keys IS 'Core domain keys with top values (>0.7)';
COMMENT ON COLUMN xfer_predict_cluster_analysis.core_domain_descriptions IS 'Descriptions of core domain keys';
COMMENT ON COLUMN xfer_predict_cluster_analysis.overall_impact IS 'Overall impact value, positive values indicate beneficial impact, negative values indicate harmful impact';

-- Add core_domain_keys and core_domain_descriptions to xfer_predict_entity_year_analysis table
ALTER TABLE xfer_predict_entity_year_analysis
ADD COLUMN IF NOT EXISTS core_domain_keys TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS core_domain_descriptions TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS overall_impact FLOAT DEFAULT 0.0;

-- Add comments to the new columns
COMMENT ON COLUMN xfer_predict_entity_year_analysis.core_domain_keys IS 'Core domain keys with top values (>0.7)';
COMMENT ON COLUMN xfer_predict_entity_year_analysis.core_domain_descriptions IS 'Descriptions of core domain keys';
COMMENT ON COLUMN xfer_predict_entity_year_analysis.overall_impact IS 'Overall impact value, positive values indicate beneficial impact, negative values indicate harmful impact';
