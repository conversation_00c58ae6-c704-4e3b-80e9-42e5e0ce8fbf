-- Add new pipeline stages for predictive analytics
-- This migration adds new values to the pipeline_stage enum type

-- Check if the enum type exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_type WHERE typname = 'pipeline_stage'
    ) THEN
        -- Add new values to the enum type if they don't exist
        -- For predictive analytics
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'predictive_analytics'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'predictive_analytics';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'temporal_clustering'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'temporal_clustering';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'predictive_regression'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'predictive_regression';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'pipeline_stage')
            AND enumlabel = 'predictive_analysis'
        ) THEN
            ALTER TYPE pipeline_stage ADD VALUE 'predictive_analysis';
        END IF;
    ELSE
        RAISE NOTICE 'pipeline_stage enum type does not exist. No changes made.';
    END IF;
END
$$;
