-- Complete UUID fix - drop all policies, constraints, and fix all column types

-- Disable <PERSON><PERSON> temporarily to avoid policy conflicts
ALTER TABLE collaborative_documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_versions DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_permissions DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_presence DISABLE ROW LEVEL SECURITY;

-- Drop all existing foreign key constraints
ALTER TABLE document_comments DROP CONSTRAINT IF EXISTS document_comments_document_id_fkey;
ALTER TABLE document_comments DROP CONSTRAINT IF EXISTS fk_document_comments_user_id;
ALTER TABLE document_comments DROP CONSTRAINT IF EXISTS fk_document_comments_resolved_by;
ALTER TABLE document_comments DROP CONSTRAINT IF EXISTS fk_document_comments_document_id;

ALTER TABLE document_versions DROP CONSTRAINT IF EXISTS document_versions_document_id_fkey;
ALTER TABLE document_versions DROP CONSTRAINT IF EXISTS fk_document_versions_created_by;
ALTER TABLE document_versions DROP CONSTRAINT IF EXISTS fk_document_versions_document_id;

ALTER TABLE document_permissions DROP CONSTRAINT IF EXISTS document_permissions_document_id_fkey;
ALTER TABLE document_permissions DROP CONSTRAINT IF EXISTS fk_document_permissions_user_id;
ALTER TABLE document_permissions DROP CONSTRAINT IF EXISTS fk_document_permissions_granted_by;
ALTER TABLE document_permissions DROP CONSTRAINT IF EXISTS fk_document_permissions_document_id;

ALTER TABLE document_presence DROP CONSTRAINT IF EXISTS document_presence_document_id_fkey;
ALTER TABLE document_presence DROP CONSTRAINT IF EXISTS fk_document_presence_user_id;
ALTER TABLE document_presence DROP CONSTRAINT IF EXISTS fk_document_presence_document_id;

ALTER TABLE collaborative_documents DROP CONSTRAINT IF EXISTS fk_collaborative_documents_created_by;
ALTER TABLE collaborative_documents DROP CONSTRAINT IF EXISTS fk_collaborative_documents_updated_by;

-- Now fix all column types
-- Fix collaborative_documents first (this is the parent table)
ALTER TABLE collaborative_documents 
    ALTER COLUMN id TYPE UUID USING id::UUID,
    ALTER COLUMN created_by TYPE UUID USING created_by::UUID,
    ALTER COLUMN updated_by TYPE UUID USING updated_by::UUID;

-- Fix document_comments
ALTER TABLE document_comments 
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID,
    ALTER COLUMN user_id TYPE UUID USING user_id::UUID,
    ALTER COLUMN resolved_by TYPE UUID USING resolved_by::UUID;

-- Fix document_versions
ALTER TABLE document_versions 
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID,
    ALTER COLUMN created_by TYPE UUID USING created_by::UUID;

-- Fix document_permissions
ALTER TABLE document_permissions 
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID,
    ALTER COLUMN user_id TYPE UUID USING user_id::UUID,
    ALTER COLUMN granted_by TYPE UUID USING granted_by::UUID;

-- Fix document_presence
ALTER TABLE document_presence 
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID,
    ALTER COLUMN user_id TYPE UUID USING user_id::UUID;

-- Now add back the foreign key constraints
ALTER TABLE collaborative_documents 
    ADD CONSTRAINT fk_collaborative_documents_created_by 
    FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE collaborative_documents 
    ADD CONSTRAINT fk_collaborative_documents_updated_by 
    FOREIGN KEY (updated_by) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE document_comments 
    ADD CONSTRAINT fk_document_comments_user_id 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE document_comments 
    ADD CONSTRAINT fk_document_comments_resolved_by 
    FOREIGN KEY (resolved_by) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE document_comments 
    ADD CONSTRAINT fk_document_comments_document_id 
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

ALTER TABLE document_versions 
    ADD CONSTRAINT fk_document_versions_created_by 
    FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE document_versions 
    ADD CONSTRAINT fk_document_versions_document_id 
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

ALTER TABLE document_permissions 
    ADD CONSTRAINT fk_document_permissions_user_id 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE document_permissions 
    ADD CONSTRAINT fk_document_permissions_granted_by 
    FOREIGN KEY (granted_by) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE document_permissions 
    ADD CONSTRAINT fk_document_permissions_document_id 
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

ALTER TABLE document_presence 
    ADD CONSTRAINT fk_document_presence_user_id 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE document_presence 
    ADD CONSTRAINT fk_document_presence_document_id 
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

-- Re-enable RLS
ALTER TABLE collaborative_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_presence ENABLE ROW LEVEL SECURITY;
