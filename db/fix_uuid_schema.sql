-- Fix UUID types and foreign key relationships for document tables

-- First, drop all existing RLS policies that depend on the columns we're changing
DROP POLICY IF EXISTS "Users can view documents they own or have permission to" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can create documents" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can update documents they own" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can delete documents they own" ON collaborative_documents;
DROP POLICY IF EXISTS "Users can view comments on accessible documents" ON document_comments;
DROP POLICY IF EXISTS "Users can create comments on accessible documents" ON document_comments;
DROP POLICY IF EXISTS "Users can update their own comments" ON document_comments;
DROP POLICY IF EXISTS "Users can delete their own comments" ON document_comments;
DROP POLICY IF EXISTS "Users can view versions of accessible documents" ON document_versions;
DROP POLICY IF EXISTS "Users can create versions of accessible documents" ON document_versions;
DROP POLICY IF EXISTS "Document owners can manage permissions" ON document_permissions;
DROP POLICY IF EXISTS "Users can view permissions for documents they have access to" ON document_permissions;
DROP POLICY IF EXISTS "Users can manage presence for accessible documents" ON document_presence;

-- Drop any existing policies with different names
DROP POLICY IF EXISTS "Users can read comments on accessible documents" ON document_comments;
DROP POLICY IF EXISTS "Users can create versions for accessible documents" ON document_versions;
DROP POLICY IF EXISTS "Users can manage their own presence" ON document_presence;

-- First, let's check if we have a profiles table, if not create it
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT,
    name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for profiles
DROP POLICY IF EXISTS "Users can view and update their own profile" ON profiles;
CREATE POLICY "Users can view and update their own profile" ON profiles
    FOR ALL USING (auth.uid() = id);

-- Grant permissions on profiles
GRANT SELECT, INSERT, UPDATE ON profiles TO authenticated;

-- Now fix the collaborative_documents table
-- First change the id column to UUID if it's not already
ALTER TABLE collaborative_documents
    ALTER COLUMN id TYPE UUID USING id::UUID,
    ALTER COLUMN created_by TYPE UUID USING created_by::UUID,
    ALTER COLUMN updated_by TYPE UUID USING updated_by::UUID;

-- Add foreign key constraints to collaborative_documents
ALTER TABLE collaborative_documents
    ADD CONSTRAINT fk_collaborative_documents_created_by
    FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE collaborative_documents
    ADD CONSTRAINT fk_collaborative_documents_updated_by
    FOREIGN KEY (updated_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Fix document_comments table
ALTER TABLE document_comments
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID,
    ALTER COLUMN user_id TYPE UUID USING user_id::UUID,
    ALTER COLUMN resolved_by TYPE UUID USING resolved_by::UUID;

-- Add foreign key constraints to document_comments
ALTER TABLE document_comments
    ADD CONSTRAINT fk_document_comments_user_id
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE document_comments
    ADD CONSTRAINT fk_document_comments_resolved_by
    FOREIGN KEY (resolved_by) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE document_comments
    ADD CONSTRAINT fk_document_comments_document_id
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

-- Fix document_versions table
ALTER TABLE document_versions
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID,
    ALTER COLUMN created_by TYPE UUID USING created_by::UUID;

-- Add foreign key constraints to document_versions
ALTER TABLE document_versions
    ADD CONSTRAINT fk_document_versions_created_by
    FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE document_versions
    ADD CONSTRAINT fk_document_versions_document_id
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

-- Fix document_permissions table
ALTER TABLE document_permissions
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID,
    ALTER COLUMN user_id TYPE UUID USING user_id::UUID,
    ALTER COLUMN granted_by TYPE UUID USING granted_by::UUID;

-- Add foreign key constraints to document_permissions
ALTER TABLE document_permissions
    ADD CONSTRAINT fk_document_permissions_user_id
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE document_permissions
    ADD CONSTRAINT fk_document_permissions_granted_by
    FOREIGN KEY (granted_by) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE document_permissions
    ADD CONSTRAINT fk_document_permissions_document_id
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

-- Fix document_presence table
ALTER TABLE document_presence
    ALTER COLUMN document_id TYPE UUID USING document_id::UUID,
    ALTER COLUMN user_id TYPE UUID USING user_id::UUID;

-- Add foreign key constraints to document_presence
ALTER TABLE document_presence
    ADD CONSTRAINT fk_document_presence_user_id
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE document_presence
    ADD CONSTRAINT fk_document_presence_document_id
    FOREIGN KEY (document_id) REFERENCES collaborative_documents(id) ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_collaborative_documents_created_by ON collaborative_documents(created_by);
CREATE INDEX IF NOT EXISTS idx_collaborative_documents_updated_by ON collaborative_documents(updated_by);
CREATE INDEX IF NOT EXISTS idx_document_comments_user_id ON document_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_document_comments_document_id ON document_comments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_versions_created_by ON document_versions(created_by);
CREATE INDEX IF NOT EXISTS idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX IF NOT EXISTS idx_document_permissions_user_id ON document_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_document_permissions_document_id ON document_permissions(document_id);
CREATE INDEX IF NOT EXISTS idx_document_presence_user_id ON document_presence(user_id);
CREATE INDEX IF NOT EXISTS idx_document_presence_document_id ON document_presence(document_id);

-- Create a function to automatically create profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
