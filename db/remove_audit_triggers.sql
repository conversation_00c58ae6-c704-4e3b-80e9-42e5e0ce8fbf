-- <PERSON><PERSON>t to remove audit triggers from all tables
DO $$
DECLARE
    trigger_record RECORD;
    drop_command TEXT;
BEGIN
    -- Loop through all triggers that start with 'audit_trigger_'
    FOR trigger_record IN 
        SELECT trigger_name, event_object_table 
        FROM information_schema.triggers 
        WHERE trigger_name LIKE 'audit_trigger_%'
          AND trigger_schema = 'public'
    LOOP
        drop_command := format('DROP TRIGGER IF EXISTS %I ON %I CASCADE;', 
                             trigger_record.trigger_name, 
                             trigger_record.event_object_table);
        EXECUTE drop_command;
        RAISE NOTICE 'Dropped trigger % on table %', 
                     trigger_record.trigger_name, 
                     trigger_record.event_object_table;
    END LOOP;
    
    -- Also drop the audit trigger function
    DROP FUNCTION IF EXISTS audit_trigger_func() CASCADE;
    RAISE NOTICE 'Dropped function audit_trigger_func()';
    
    -- Drop the get_latest_run_id function if it exists
    DROP FUNCTION IF EXISTS get_latest_run_id() CASCADE;
    RAISE NOTICE 'Dropped function get_latest_run_id()';
    
    -- Note: The audit_log table is not dropped by default
    -- Uncomment the following line if you also want to drop the audit_log table
    -- DROP TABLE IF EXISTS audit_log CASCADE;
    -- RAISE NOTICE 'Dropped table audit_log';
END $$;
