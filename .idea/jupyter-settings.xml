<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="JupyterPersistentConnectionParameters">
    <option name="knownRemoteServers">
      <list>
        <JupyterConnectionParameters>
          <option name="authType" value="notebook" />
          <option name="urlString" value="http://localhost:8888/" />
        </JupyterConnectionParameters>
      </list>
    </option>
    <option name="moduleParameters">
      <map>
        <entry key="$PROJECT_DIR$/apps/customer/customer.iml">
          <value>
            <JupyterConnectionParameters>
              <option name="managed" value="true" />
              <option name="sdkName" value="" />
            </JupyterConnectionParameters>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/backoffice/notebooks/notebooks.iml">
          <value>
            <JupyterConnectionParameters>
              <option name="managed" value="true" />
              <option name="sdkName" value="Pipenv (services)" />
            </JupyterConnectionParameters>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>