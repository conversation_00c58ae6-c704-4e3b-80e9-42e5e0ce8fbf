<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="SqlDialectMappings">
    <file url="file://$PROJECT_DIR$/backoffice/src/eko/cms/content/create_profile.py" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/db/cus/views.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/db/functions/eko_update_normalized_entity_name.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/db/migrations/20240625_add_pipeline_stages.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/db/migrations/20240625_add_statement_indexes.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/db/migrations/20240625_add_statement_indexes_for_search.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/db/views/views2.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/supabase/cms/supabase/migrations/20250115000000_collaborative_documents.sql" dialect="PostgreSQL" />
    <file url="PROJECT" dialect="PostgreSQL" />
  </component>
</project>