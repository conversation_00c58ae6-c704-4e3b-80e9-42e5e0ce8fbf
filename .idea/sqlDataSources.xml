<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="SqlDataSourceStorage">
    <option name="dataSources">
      <list>
        <State>
          <option name="id" value="c19b7fa2-8d45-43fd-8025-bc59e07fc9d4" />
          <option name="name" value="Supabase (DDL)" />
          <option name="dbmsName" value="POSTGRES" />
          <option name="urls">
            <array />
          </option>
          <option name="outLayout" value="File per object by schema.groovy" />
        </State>
      </list>
    </option>
  </component>
</project>