name: Packages CI

on:
  push:
    paths:
      - 'packages/**'
      - 'pnpm-lock.yaml'
  pull_request:
    paths:
      - 'packages/**'
      - 'pnpm-lock.yaml'

jobs:
  build:
    name: Build Packages
    runs-on: ubuntu-latest
    strategy:
      matrix:
        package:
          - packages/ui
          - packages/utils

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 'latest'

      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: TypeScript check
        working-directory: ${{ matrix.package }}
        run: npx tsc --noEmit

      - name: Build package
        working-directory: ${{ matrix.package }}
        run: pnpm build

#      - name: Lint package
#        working-directory: ${{ matrix.package }}
#        run: pnpm lint
