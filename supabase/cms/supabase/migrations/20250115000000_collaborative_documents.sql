-- Migration for collaborative document editing with Supa<PERSON>
-- This creates the necessary tables for document storage, collaboration, and presence

-- Enable realtime for collaboration
ALTER PUBLICATION supabase_realtime ADD TABLE IF NOT EXISTS public.collaborative_documents;
ALTER PUBLICATION supabase_realtime ADD TABLE IF NOT EXISTS public.document_presence;
ALTER PUBLICATION supabase_realtime ADD TABLE IF NOT EXISTS public.document_operations;

-- Table for storing collaborative documents
CREATE TABLE IF NOT EXISTS public.collaborative_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content JSONB DEFAULT '{}',
    yjs_state BYTEA, -- Y.js document state
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    organisation_id INTEGER REFERENCES public.acc_organisations(id) ON DELETE CASCADE,
    is_public BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'
);

-- Table for tracking user presence in documents
CREATE TABLE IF NOT EXISTS public.document_presence (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES public.collaborative_documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    user_name TEXT,
    user_avatar TEXT,
    user_color TEXT DEFAULT '#3B82F6',
    cursor_position JSONB,
    selection JSONB,
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(document_id, user_id)
);

-- Table for storing Y.js operations for conflict resolution
CREATE TABLE IF NOT EXISTS public.document_operations (
    id BIGSERIAL PRIMARY KEY,
    document_id UUID REFERENCES public.collaborative_documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    operation BYTEA NOT NULL, -- Y.js update
    clock INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_collaborative_documents_org ON public.collaborative_documents(organisation_id);
CREATE INDEX IF NOT EXISTS idx_collaborative_documents_created_by ON public.collaborative_documents(created_by);
CREATE INDEX IF NOT EXISTS idx_document_presence_document ON public.document_presence(document_id);
CREATE INDEX IF NOT EXISTS idx_document_presence_active ON public.document_presence(document_id, is_active);
CREATE INDEX IF NOT EXISTS idx_document_operations_document ON public.document_operations(document_id);
CREATE INDEX IF NOT EXISTS idx_document_operations_clock ON public.document_operations(document_id, clock);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_collaborative_documents_updated_at
    BEFORE UPDATE ON public.collaborative_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up inactive presence
CREATE OR REPLACE FUNCTION cleanup_inactive_presence()
RETURNS void AS $$
BEGIN
    UPDATE public.document_presence
    SET is_active = FALSE
    WHERE last_seen < NOW() - INTERVAL '5 minutes';
END;
$$ language 'plpgsql';

-- RLS Policies
ALTER TABLE public.collaborative_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_presence ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_operations ENABLE ROW LEVEL SECURITY;

-- Policy for collaborative_documents
CREATE POLICY "Users can view documents in their organisation" ON public.collaborative_documents
    FOR SELECT USING (
        organisation_id IN (
            SELECT organisation FROM public.profiles WHERE id = auth.uid()
        ) OR is_public = TRUE
    );

CREATE POLICY "Users can create documents in their organisation" ON public.collaborative_documents
    FOR INSERT WITH CHECK (
        organisation_id IN (
            SELECT organisation FROM public.profiles WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can update documents in their organisation" ON public.collaborative_documents
    FOR UPDATE USING (
        organisation_id IN (
            SELECT organisation FROM public.profiles WHERE id = auth.uid()
        )
    );

-- Policy for document_presence
CREATE POLICY "Users can view presence in accessible documents" ON public.document_presence
    FOR SELECT USING (
        document_id IN (
            SELECT id FROM public.collaborative_documents
            WHERE organisation_id IN (
                SELECT organisation FROM public.profiles WHERE id = auth.uid()
            ) OR is_public = TRUE
        )
    );

CREATE POLICY "Users can manage their own presence" ON public.document_presence
    FOR ALL USING (user_id = auth.uid());

-- Policy for document_operations
CREATE POLICY "Users can view operations for accessible documents" ON public.document_operations
    FOR SELECT USING (
        document_id IN (
            SELECT id FROM public.collaborative_documents
            WHERE organisation_id IN (
                SELECT organisation FROM public.profiles WHERE id = auth.uid()
            ) OR is_public = TRUE
        )
    );

CREATE POLICY "Users can insert operations for accessible documents" ON public.document_operations
    FOR INSERT WITH CHECK (
        document_id IN (
            SELECT id FROM public.collaborative_documents
            WHERE organisation_id IN (
                SELECT organisation FROM public.profiles WHERE id = auth.uid()
            ) OR is_public = TRUE
        )
    );