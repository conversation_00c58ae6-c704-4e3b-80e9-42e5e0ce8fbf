import { createClient } from '@/app/supabase/client'
import {
  ClusterAnalysisResponse,
  ComponentType,
  EntityYearAnalysisResponse,
  PredictiveComponentResponse,
} from '@/types/prediction'

const supabase = createClient();

/**
 * Fetch component-level analyses for an entity
 * @param entityXid Entity XID
 * @param year Optional year filter
 * @param clusterId Optional cluster ID filter
 * @param componentType Optional component type filter
 * @returns Array of component analyses
 */
export async function fetchComponentAnalyses(
  entityXid: string,
  year?: number,
  clusterId?: number,
  componentType?: ComponentType
): Promise<PredictiveComponentResponse[]> {
  let query = supabase
    .from('xfer_predict_component')
    .select('*')
    .eq('entity_xid', entityXid);

  if (year) {
    query = query.eq('year', year);
  }

  if (clusterId) {
    query = query.eq('cluster_id', clusterId);
  }

  if (componentType) {
    query = query.eq('component_type', componentType);
  }

  // Get the latest run_id for this entity
  const { data: runData } = await supabase
    .from('xfer_predict_component')
    .select('run_id')
    .eq('entity_xid', entityXid)
    .order('run_id', { ascending: false })
    .limit(1);

  if (runData && runData.length > 0) {
    const latestRunId = runData[0].run_id as number;
    query = query.eq('run_id', latestRunId);
  }

  const { data, error } = await query.order('year', { ascending: true });

  if (error) {
    console.error('Error fetching component analyses:', error);
    throw error;
  }

  return (data || []) as unknown as PredictiveComponentResponse[];
}

/**
 * Fetch cluster-level analyses for an entity
 * @param entityXid Entity XID
 * @param year Optional year filter
 * @param clusterId Optional cluster ID filter
 * @returns Array of cluster analyses
 */
export async function fetchClusterAnalyses(
  entityXid: string,
  year?: number,
  clusterId?: number
): Promise<ClusterAnalysisResponse[]> {
  let query = supabase
    .from('xfer_predict_cluster')
    .select('*')
    .eq('entity_xid', entityXid);

  if (year) {
    query = query.eq('year', year);
  }

  if (clusterId) {
    query = query.eq('cluster_id', clusterId);
  }

  // Get the latest run_id for this entity
  const { data: runData } = await supabase
    .from('xfer_predict_cluster')
    .select('run_id')
    .eq('entity_xid', entityXid)
    .order('run_id', { ascending: false })
    .limit(1);

  if (runData && runData.length > 0) {
    const latestRunId = runData[0].run_id as number;
    query = query.eq('run_id', latestRunId);
  }

  const { data, error } = await query.order('year', { ascending: true });

  if (error) {
    console.error('Error fetching cluster analyses:', error);
    throw error;
  }

  return (data || []) as unknown as ClusterAnalysisResponse[];
}

/**
 * Fetch entity-year analyses for an entity
 * @param entityXid Entity XID
 * @param year Optional year filter
 * @returns Array of entity-year analyses
 */
export async function fetchEntityYearAnalyses(
  entityXid: string,
  year?: number
): Promise<EntityYearAnalysisResponse[]> {
  let query = supabase
    .from('xfer_predict_entity_year')
    .select('*')
    .eq('entity_xid', entityXid);

  if (year) {
    query = query.eq('year', year);
  }

  // Get the latest run_id for this entity
  const { data: runData } = await supabase
    .from('xfer_predict_entity_year')
    .select('run_id')
    .eq('entity_xid', entityXid)
    .order('run_id', { ascending: false })
    .limit(1);

  if (runData && runData.length > 0) {
    const latestRunId = runData[0].run_id as number;
    query = query.eq('run_id', latestRunId);
  }

  const { data, error } = await query.order('year', { ascending: true });

  if (error) {
    console.error('Error fetching entity-year analyses:', error);
    throw error;
  }

  return (data || []) as unknown as EntityYearAnalysisResponse[];
}
