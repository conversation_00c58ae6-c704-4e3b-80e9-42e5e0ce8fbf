'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import {
  fetchEntityYearAnalyses,
  fetchClusterAnalyses,
  fetchComponentAnalyses
} from '@/services/prediction';
import {
  EntityYearAnalysisResponse,
  ClusterAnalysisResponse,
  PredictiveComponentResponse,
  ComponentType
} from '@/types/prediction';
import { EntityYearAnalysisCard } from '@/components/prediction/EntityYearAnalysis';
import { ClusterAnalysisCard } from '@/components/prediction/ClusterAnalysis';
import { ComponentAnalysisModal } from '@/components/prediction/ComponentAnalysis';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@ui/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@ui/components/ui/tabs';
import { Skeleton } from '@ui/components/ui/skeleton';
import { ChartLine, AlertTriangle } from 'lucide-react';

export default function PredictionPage() {
  const { entity } = useParams() as { entity: string };

  const [entityYearAnalyses, setEntityYearAnalyses] = useState<EntityYearAnalysisResponse[]>([]);
  const [clusterAnalyses, setClusterAnalyses] = useState<ClusterAnalysisResponse[]>([]);
  const [componentAnalyses, setComponentAnalyses] = useState<PredictiveComponentResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // For the modal
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [selectedClusterId, setSelectedClusterId] = useState<string | null>(null);
  const [selectedComponentType, setSelectedComponentType] = useState<ComponentType | null>(null);
  const [showComponentModal, setShowComponentModal] = useState(false);

  // Fetch data
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        // Fetch entity-year analyses
        const entityYearData = await fetchEntityYearAnalyses(entity);
        setEntityYearAnalyses(entityYearData);

        // If we have entity-year analyses, set the selected year to the latest one
        if (entityYearData.length > 0) {
          const latestYear = Math.max(...entityYearData.map(a => a.year));
          setSelectedYear(latestYear);

          // Fetch cluster analyses for the latest year
          const clusterData = await fetchClusterAnalyses(entity, latestYear);
          setClusterAnalyses(clusterData);

          // Fetch component analyses for the latest year
          const componentData = await fetchComponentAnalyses(entity, latestYear);
          setComponentAnalyses(componentData);
        }
      } catch (err) {
        console.error('Error fetching prediction data:', err);
        setError('Failed to load prediction data. Please try again later.');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [entity]);

  // Handle year change
  const handleYearChange = async (year: number) => {
    try {
      setSelectedYear(year);
      setSelectedClusterId(null);
      setSelectedComponentType(null);

      // Fetch cluster analyses for the selected year
      const clusterData = await fetchClusterAnalyses(entity, year);
      setClusterAnalyses(clusterData);

      // Fetch component analyses for the selected year
      const componentData = await fetchComponentAnalyses(entity, year);
      setComponentAnalyses(componentData);
    } catch (err) {
      console.error('Error fetching data for year:', year, err);
      setError(`Failed to load data for year ${year}. Please try again later.`);
    }
  };

  // Handle cluster selection
  const handleClusterSelect = (clusterId: string) => {
    setSelectedClusterId(clusterId);
  };

  // Handle component selection
  const handleComponentSelect = (componentType: ComponentType) => {
    setSelectedComponentType(componentType);
    setShowComponentModal(true);
  };

  // Get the selected component analysis
  const getSelectedComponentAnalysis = () => {
    if (!selectedYear || !selectedClusterId || !selectedComponentType) return null;

    return componentAnalyses.find(
      a => a.year === selectedYear &&
           a.cluster_id.toString() === selectedClusterId &&
           a.component_type === selectedComponentType
    );
  };

  // Get years from entity-year analyses
  const yearsArray = entityYearAnalyses.map(a => a.year);
  const uniqueYears = Array.from(new Set(yearsArray));
  const years = uniqueYears.sort((a, b) => a - b);

  // Get clusters for the selected year
  const yearClusters = clusterAnalyses
    .filter(a => a.year === selectedYear)
    .sort((a, b) => a.cluster_id - b.cluster_id);

  // Get the selected entity-year analysis
  const selectedEntityYearAnalysis = entityYearAnalyses.find(a => a.year === selectedYear);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center mb-8">
        <ChartLine className="h-8 w-8 mr-3 text-brand-500" />
        <h1 className="text-3xl font-bold text-white">Predictive Analysis</h1>
      </div>

      {loading ? (
        <div className="space-y-6">
          <Skeleton className="h-12 w-48 bg-slate-800/50" />
          <Skeleton className="h-64 w-full bg-slate-800/50" />
          <Skeleton className="h-64 w-full bg-slate-800/50" />
        </div>
      ) : error ? (
        <div className="bg-red-900/20 border border-red-800 rounded-lg p-6 flex items-center">
          <AlertTriangle className="h-6 w-6 text-red-400 mr-3" />
          <p className="text-red-200">{error}</p>
        </div>
      ) : entityYearAnalyses.length === 0 ? (
        <div className="bg-slate-800/30 border border-slate-700 rounded-lg p-6 text-center">
          <p className="text-slate-300">No predictive analysis data available for this entity.</p>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Year selector */}
          {years.length > 1 && (
            <Tabs
              value={selectedYear?.toString()}
              onValueChange={(value) => handleYearChange(parseInt(value))}
              className="w-full"
            >
              <TabsList className="bg-slate-800/50 text-slate-300">
                {years.map(year => (
                  <TabsTrigger key={year} value={year.toString()}>
                    {year}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          )}

          {/* Entity-year analysis */}
          {selectedEntityYearAnalysis && (
            <div className="mb-10">
              <h2 className="text-2xl font-bold text-white mb-4">Entity Overview</h2>
              <EntityYearAnalysisCard
                analysis={selectedEntityYearAnalysis.model}
                onViewCluster={handleClusterSelect}
              />
            </div>
          )}

          {/* Cluster analyses */}
          {yearClusters.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-white mb-4">Cluster Analysis</h2>
              <div className="space-y-6">
                {yearClusters.map(cluster => (
                  <ClusterAnalysisCard
                    key={`${cluster.year}-${cluster.cluster_id}`}
                    analysis={cluster.model}
                    onViewComponent={handleComponentSelect}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Component analysis modal */}
          <Dialog open={showComponentModal} onOpenChange={setShowComponentModal}>
            <DialogContent className="sm:max-w-[800px] bg-slate-900 border-slate-800">
              <DialogHeader>
                <DialogTitle className="text-white">Component Analysis</DialogTitle>
              </DialogHeader>
              {getSelectedComponentAnalysis() && (
                <ComponentAnalysisModal analysis={getSelectedComponentAnalysis()!.model} />
              )}
            </DialogContent>
          </Dialog>
        </div>
      )}
    </div>
  );
}
