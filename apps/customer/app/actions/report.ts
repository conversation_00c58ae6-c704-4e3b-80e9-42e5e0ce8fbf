'use server'


export type CategorySummary = {
  summary: string;
  positives: string;
  negatives: string;
  risks: string;
  opportunities: string;
  model_sections: Record<string, string>; // This was added to the result after AI call
};

export type ReliabilitySummary = {
  summary: string;
  claims: {
    summary: string;
    examples: string[];
  };
  promises: {
    summary: string;
    examples: string[];
  };
};

export type TransparencySummary = {
  summary: string;
  cherry_picking: { // Note: key was 'cherry_picking' in original JSON structure
    summary: string;
    examples: string[];
  };
};
