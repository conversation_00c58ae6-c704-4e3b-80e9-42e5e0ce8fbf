import {<PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title} from "@ui/components/ui/card";
import {AlertCircle} from "lucide-react";
import Link from "next/link";
import {PageHeader} from "@/components/page-header";

export default async function BillingEnquiriesPage(props: {
    searchParams: Promise<Record<string, string | string[]>>;
}) {
    const sp = await props.searchParams; // Resolve the Promise

    return (
        <>
            <PageHeader />
            <main className="flex items-center justify-center min-h-screen bg-background" data-testid="billing-page">
                <Card className="w-full max-w-md">
                    <CardHeader className="flex flex-row items-center gap-2">
                        <AlertCircle className="h-6 w-6 text-blue-500" />
                        <CardTitle>Billing Enquiries</CardTitle>
                    </CardHeader>
                    <CardContent data-testid="billing-contact-info">
                        <p className="text-center text-muted-foreground">
                            <Link
                                href={`/customer/account/contact/billing?${new URLSearchParams(sp as Record<string, string>).toString()}`}
                                className="bolder text-foreground"
                                data-testid="billing-contact-link"
                            >
                                Contact us
                            </Link>{" "}
                            for billing enquiries
                        </p>
                    </CardContent>
                </Card>
            </main>
        </>
    );
}
