'use client';

import React, { useState, useEffect } from 'react';
import {
  fetchEntityYearAnalyses,
  fetchClusterAnalyses,
  fetchComponentAnalyses
} from '@/services/prediction';
import {
  EntityYearAnalysisResponse,
  ClusterAnalysisResponse,
  PredictiveComponentResponse,
  ComponentType
} from '@/types/prediction';
import { EntityYearAnalysisCard } from '@/components/prediction/EntityYearAnalysis';
import { ClusterAnalysisCard } from '@/components/prediction/ClusterAnalysis';
import { ComponentAnalysisModal } from '@/components/prediction/ComponentAnalysis';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@ui/components/ui/dialog';
import { Tabs, TabsList, TabsTrigger } from '@ui/components/ui/tabs';
import { Skeleton } from '@ui/components/ui/skeleton';
import { AlertTriangle } from 'lucide-react';
import { useEntity } from '@/components/context/entity/entity-context';
import { Headline } from '@ui/components/front-page/headline';
import PredictionClient from './client';

export default function PredictionV2Page() {
  const entityContext = useEntity();
  const entity = entityContext.entity;

  const [entityYearAnalyses, setEntityYearAnalyses] = useState<EntityYearAnalysisResponse[]>([]);
  const [clusterAnalyses, setClusterAnalyses] = useState<ClusterAnalysisResponse[]>([]);
  const [componentAnalyses, setComponentAnalyses] = useState<PredictiveComponentResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // For the modal
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [selectedClusterId, setSelectedClusterId] = useState<string | null>(null);
  const [selectedComponentType, setSelectedComponentType] = useState<ComponentType | null>(null);
  const [showComponentModal, setShowComponentModal] = useState(false);

  // Fetch data
  useEffect(() => {
    if (!entity) return;

    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        // Fetch entity-year analyses
        const entityYearData = await fetchEntityYearAnalyses(entity as string);
        setEntityYearAnalyses(entityYearData);

        // If we have entity-year analyses, set the selected year to the latest one
        if (entityYearData.length > 0) {
          const latestYear = Math.max(...entityYearData.map(a => a.year));
          setSelectedYear(latestYear);

          // Fetch cluster analyses for the latest year
          const clusterData = await fetchClusterAnalyses(entity as string, latestYear);
          setClusterAnalyses(clusterData);

          // Fetch component analyses for the latest year
          const componentData = await fetchComponentAnalyses(entity as string, latestYear);
          setComponentAnalyses(componentData);
        }
      } catch (err) {
        console.error('Error fetching prediction data:', err);
        setError('Failed to load prediction data. Please try again later.');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [entity]);

  // Handle year change
  const handleYearChange = async (year: number) => {
    if (!entity) return;

    try {
      setSelectedYear(year);
      setSelectedClusterId(null);
      setSelectedComponentType(null);

      // Fetch cluster analyses for the selected year
      const clusterData = await fetchClusterAnalyses(entity as string, year);
      setClusterAnalyses(clusterData);

      // Fetch component analyses for the selected year
      const componentData = await fetchComponentAnalyses(entity as string, year);
      setComponentAnalyses(componentData);
    } catch (err) {
      console.error('Error fetching data for year:', year, err);
      setError(`Failed to load data for year ${year}. Please try again later.`);
    }
  };

  // Handle cluster selection
  const handleClusterSelect = (clusterId: string) => {
    setSelectedClusterId(clusterId);
  };

  // Handle component selection
  const handleComponentSelect = (componentType: ComponentType) => {
    setSelectedComponentType(componentType);
    setShowComponentModal(true);
  };

  // Get the selected component analysis
  const getSelectedComponentAnalysis = () => {
    if (!selectedYear || !selectedClusterId || !selectedComponentType) return null;

    return componentAnalyses.find(
      a => a.year === selectedYear &&
           a.cluster_id.toString() === selectedClusterId &&
           a.component_type === selectedComponentType
    );
  };

  // Get years from entity-year analyses
  const yearsArray = entityYearAnalyses.map(a => a.year);
  const uniqueYears = Array.from(new Set(yearsArray));
  const years = uniqueYears.sort((a, b) => a - b);

  // Get clusters for the selected year
  const yearClusters = clusterAnalyses
    .filter(a => a.year === selectedYear)
    .sort((a, b) => a.cluster_id - b.cluster_id);

  // Get the selected entity-year analysis
  const selectedEntityYearAnalysis = entityYearAnalyses.find(a => a.year === selectedYear);

  if (!entity) {
    return (
      <div className="p-6 space-y-6">
        <Headline>Predictive Analysis V2</Headline>
        <div className="glass-effect-lit rounded-2xl p-6 text-center">
          <p className="text-foreground/70">Please select an entity to view prediction data.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Client component to set navigation path */}
      <PredictionClient />

      <Headline>Predictive Analysis V2</Headline>

      {loading ? (
        <div className="space-y-6">
          <Skeleton className="h-12 w-48 glass-effect-subtle rounded-2xl" />
          <Skeleton className="h-64 w-full glass-effect-subtle rounded-2xl" />
          <Skeleton className="h-64 w-full glass-effect-subtle rounded-2xl" />
        </div>
      ) : error ? (
        <div className="glass-effect-brand-compliment-lit rounded-2xl p-6 flex items-center border-l-4 border-red-500">
          <AlertTriangle className="h-6 w-6 text-red-400 mr-3" />
          <p className="text-foreground">{error}</p>
        </div>
      ) : entityYearAnalyses.length === 0 ? (
        <div className="glass-effect-lit rounded-2xl p-6 text-center">
          <p className="text-foreground/70">No predictive analysis data available for this entity. Run a predictive analysis first.</p>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Year selector */}
          {years.length > 1 && (
            <Tabs
              value={selectedYear?.toString()}
              onValueChange={(value) => handleYearChange(parseInt(value))}
              className="w-full"
            >
              <TabsList className="glass-effect-subtle rounded-xl p-1 space-x-1">
                {years.map(year => (
                  <TabsTrigger
                    key={year}
                    value={year.toString()}
                    className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                  >
                    {year}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          )}

          {/* Entity-year analysis */}
          {selectedEntityYearAnalysis && (
            <div className="mb-10">
              <h2 className="text-2xl font-bold text-white mb-4">Entity Overview</h2>
              <EntityYearAnalysisCard
                analysis={selectedEntityYearAnalysis.model}
                onViewCluster={handleClusterSelect}
              />
            </div>
          )}

          {/* Cluster analyses */}
          {yearClusters.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold text-white mb-4">Cluster Analysis</h2>
              <div className="space-y-6">
                {yearClusters.map(cluster => (
                  <ClusterAnalysisCard
                    key={`${cluster.year}-${cluster.cluster_id}`}
                    analysis={cluster.model}
                    onViewComponent={handleComponentSelect}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Component analysis modal */}
          <Dialog open={showComponentModal} onOpenChange={setShowComponentModal}>
            <DialogContent className="sm:max-w-[800px] glass-effect-strong-lit rounded-2xl border-border/20">
              <DialogHeader>
                <DialogTitle className="text-foreground">Component Analysis</DialogTitle>
              </DialogHeader>
              {getSelectedComponentAnalysis() && (
                <ComponentAnalysisModal analysis={getSelectedComponentAnalysis()!.model} />
              )}
            </DialogContent>
          </Dialog>
        </div>
      )}
    </div>
  );
}
