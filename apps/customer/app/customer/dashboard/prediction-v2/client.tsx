'use client';

import { useEffect } from 'react';
import { useNav } from '@/components/context/nav/nav-context';

export default function PredictionClient() {
  const nav = useNav();

  useEffect(() => {
    // Set the navigation path for Prediction page
    nav.changeNavPath([
      { label: "Dashboard", href: "/customer/dashboard" },
      { label: "Predictions", href: "/customer/dashboard/prediction-v2" }
    ]);
  }, []);

  return null;
}
