"use client";

import React, { useEffect, useMemo } from 'react'
import { ClaimTypeV2 } from '@/types/claim'
import NoData from '@/components/no-data'
import { useEntity } from '@/components/context/entity/entity-context'
import { useNav } from '@/components/context/nav/nav-context'
import { useAuth } from '@/components/context/auth/auth-context'
import { ClaimsListV2 } from './claims-list-v2'
import { EkoPageTitle } from '@/components/page-title'

export default function Page() {
    const entityContext = useEntity();
    const auth = useAuth();
    const nav = useNav();

    useEffect(() => {
        nav.changeNavPath([
            { label: "Dashboard", href: "/customer/dashboard" },
            { label: "Claims", href: "/customer/dashboard/gw/claims" }
        ]);
    }, []);

    // Filter and process claims data from EntityContext
    const filteredClaimsData = useMemo(() => {
        if (!entityContext.claimsData) return [];

        // Filter claims with ESG claims, confidence > 50, and importance >= 30
        const filteredClaims = entityContext.claimsData
            .filter(claim => {
                // Safely access esg_claim, confidence, and importance with type checking
                const model = claim.model as any;
                return model && typeof model === 'object' &&
                  'esg_claim' in model && model.esg_claim &&
                  'confidence' in model && model.confidence > 50 &&
                  'importance' in model && model.importance >= 30;
            });

        // Sort by importance first (descending), then by confidence (descending)
        filteredClaims.sort((a, b) => {
            // Safely access importance and confidence with type checking
            const modelA = a.model as any;
            const modelB = b.model as any;
            const importanceA = modelA && typeof modelA === 'object' && 'importance' in modelA ? modelA.importance : 0;
            const importanceB = modelB && typeof modelB === 'object' && 'importance' in modelB ? modelB.importance : 0;
            const confA = modelA && typeof modelA === 'object' && 'confidence' in modelA ? modelA.confidence : 0;
            const confB = modelB && typeof modelB === 'object' && 'confidence' in modelB ? modelB.confidence : 0;

            // Sort by importance first
            if (importanceB !== importanceA) {
                return importanceB - importanceA;
            }
            // If importance is equal, sort by confidence
            return confB - confA;
        });

        // Limit to 100 claims
        return filteredClaims.slice(0, 100);
    }, [entityContext.claimsData]);

    // Show loading state if claims are still loading
    if (entityContext.isLoadingClaims) {
        return (
            <div className="container mx-auto p-4" data-testid="loading-claims">
                <EkoPageTitle title="Claims" />
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg">Loading claims...</div>
                </div>
            </div>
        );
    }

    return filteredClaimsData && filteredClaimsData.length > 0 ? (
        <div className="container mx-auto p-4" data-testid="claims-page">
            <EkoPageTitle title="Claims" />
            <ClaimsListV2 claimsData={filteredClaimsData} admin={auth.admin} />
        </div>
    ) : (
        <NoData title="No Claims Found" description="No claims found for this entity" />
    );
}
