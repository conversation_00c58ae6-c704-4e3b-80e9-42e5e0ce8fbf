import { Badge } from '@ui/components/ui/badge'
import { Separator } from '@ui/components/ui/separator'
import { AlertCircle, CheckCircle2, Info } from 'lucide-react'
import { CitationType, CompactCitation, reduceCitations } from '@/components/citation'
import React from 'react'
import { ClaimTypeV2 } from '@/types/claim'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'

const greenwashingTypes: {[key:string]:string} = {
    "vague": "Vague",
    "misleading": "Misleading",
    "offsetting": "Includes Offsetting",
    "intentions": "Describes Intentions",
    "distracting": "Distracting",
    "org": "Possible Greenwashing",
    "fund": "Possible Greenwashing",
    "product": "Possible Greenwashing"
}

function accuracy(valid: boolean, greenwashing: boolean, greenwashingType?: string) {
    if (greenwashing) {
        if (valid) {
            return "Accurate but " + (greenwashingTypes[greenwashingType || ""] || "Greenwashing");
        } else {
            return "Inaccurate and " + (greenwashingTypes[greenwashingType || ""] || "Greenwashing");
        }
    } else {
        return valid ? "Accurate Claim" : "Inaccurate";
    }
}

export default function ClaimDetailV2({claim, admin}: {claim: ClaimTypeV2, admin: boolean}) {
    const model = claim.model;

    // Get all citations from the model
    const citations = model.citations || [];
    // Cast to unknown first to avoid TypeScript errors
    const allCitationsSet = reduceCitations(citations as unknown as CitationType[]);

    // Get the first citation if available for document info
    const firstCitation = citations && citations.length > 0 ? citations[0] : null;

    // Get document info from the model or first citation
    const docAuthors = firstCitation?.authors || [];
    const docTitle = model.claim_doc || firstCitation?.title || '';
    const docYear = model.claim_doc_year || firstCitation?.year || new Date().getFullYear();

    // Determine if the claim is valid and if it's greenwashing
    const isValid = claim.verified !== null ? claim.verified : model.valid_claim;
    const isGreenwashing = model.greenwashing || false;
    // Get greenwashing type from the model if available
    let greenwashingType = '';
    if (typeof model.greenwashing === 'object' && model.greenwashing !== null) {
        // Use type assertion to access the type property
        const gwObj = model.greenwashing as any;
        greenwashingType = gwObj.type || '';
    }

    return <>
        <section>
            <h2 className="text-xl font-semibold mb-3">
                <Badge
                    variant={isValid ? "outline" : "destructive"}
                    className="text-sm"
                >
                    {isValid ? <CheckCircle2 className="w-4 h-4 mr-1"/> : <AlertCircle className="w-4 h-4 mr-1"/>}
                    {accuracy(isValid, isGreenwashing, greenwashingType)}
                </Badge>
            </h2>
            <p className="mb-4 text-lg 2xl:text-xl">{model.statement_text || model.text}</p>
            <p>
                <span className="italic ml-4">
                    {docAuthors.map((author: any) => author.name || author.cn).join(", ")}.
                    ({docYear}) <strong>{docTitle}</strong>
                    {firstCitation?.doc_page_id !== undefined && <>, p.{firstCitation.doc_page_id + 1}</>}
                </span>
            </p>

            <h3 className="font-semibold text-lg mb-2">Verdict</h3>
            <p className="mb-4">{model.conclusion}</p>
        </section>

        <Separator/>

        <section className="text-left w-full mt-6">
            <h2 className="text-xl font-semibold mb-3">Analysis</h2>
            <div className="flex flex-wrap gap-2 mb-4">
                <Badge variant={isValid ? "default" : "destructive"} className="text-sm">
                    {isValid ? <CheckCircle2 className="w-4 h-4 mr-1"/> : <AlertCircle className="w-4 h-4 mr-1"/>}
                    {isValid ? "Valid Claim" : "Invalid Claim"}
                </Badge>
                <Badge variant={isGreenwashing ? "destructive" : "default"} className="text-sm">
                    {isGreenwashing ? <AlertCircle className="w-4 h-4 mr-1"/> : <CheckCircle2 className="w-4 h-4 mr-1"/>}
                    {(isGreenwashing  && isValid) ? "Greenwashing" : "No Greenwashing"}
                </Badge>
                {isGreenwashing && greenwashingType && (
                    <Badge variant="default" className="text-sm">
                        {greenwashingType}
                    </Badge>
                )}
                <Badge variant="default" className="text-sm">
                    <Info className="w-4 h-4 mr-1"/>
                    Confidence: {model.confidence}%
                </Badge>
                <Badge variant={model.esg_claim ? "default" : "outline"} className="text-sm">
                    {model.esg_claim ? "ESG Claim" : "Not ESG Claim"}
                </Badge>
            </div>

            <p className="mb-4"><EkoMarkdown citations={citations as unknown as CitationType[]}
                                             admin={admin}>{model.summary}</EkoMarkdown></p>
            <EkoMarkdown
                citations={citations as unknown as CitationType[]}
                admin={admin}
            >
                {model.verdict?.replaceAll(/\\n/g, "\n\n")}
            </EkoMarkdown>
        </section>

        <Separator/>

        <section className="text-left w-full mt-6">
            <h2 className="text-xl font-semibold mb-3">References</h2>
            {allCitationsSet
                .sort((a, b) => (b.score || 0) - (a.score || 0))
                .map((data, j) => (
                    <CompactCitation key={j} data={data} admin={admin}/>
                ))}
        </section>

        <Separator/>
    </>;
}
