'use client'
import React from 'react'
import { CherryTypeV2 } from '@/types'
import SelectiveHighlightingCard from './selective-highlighting-card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@ui/components/ui/tabs'
import { <PERSON><PERSON><PERSON>riangle, Cherry, Waves } from 'lucide-react'
import { GlassCard } from '@/components/ui/glass-card'
import { Badge } from '@ui/components/ui/badge'

interface SelectiveHighlightingListProps {
  data: CherryTypeV2[];
  admin: boolean;
}

export default function SelectiveHighlightingList({ data, admin }: SelectiveHighlightingListProps) {
  // Add debugging to check data structure
  console.log("SelectiveHighlightingList received data:", data);

  // Validate data structure before processing
  const validData = data.filter(item => {
    // Check if item has the expected structure
    const isValid = item &&
                   typeof item === 'object' &&
                   item.model &&
                   typeof item.model === 'object' &&
                   item.model.model;

    if (!isValid) {
      console.warn("Invalid item structure:", item);
    }

    return isValid;
  });

  console.log("Valid data items:", validData.length);

  // Separate cherry picking and flooding instances
  const cherryPickingInstances = validData.filter(item => item.model.model === 'cherry_picking');
  const floodingInstances = validData.filter(item => item.model.model === 'flooding');

  // Sort instances by severity (highest first)
  const sortedCherryPicking = [...cherryPickingInstances].sort((a, b) =>
    (b.model.severity || 0) - (a.model.severity || 0)
  );
  const sortedFlooding = [...floodingInstances].sort((a, b) =>
    (b.model.severity || 0) - (a.model.severity || 0)
  );

  // Count instances
  const cherryPickingCount = cherryPickingInstances.length;
  const floodingCount = floodingInstances.length;
  const totalCount = validData.length;

  if (totalCount === 0) {
    return (
      <GlassCard variant="subtle" className="text-center">
        <div className="flex flex-col items-center space-y-4">
          <AlertTriangle className="h-12 w-12 text-muted-foreground/50" />
          <p className="text-muted-foreground">No selective highlighting instances found for this entity.</p>
        </div>
      </GlassCard>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with stats */}
      <GlassCard variant="brand" className="border-l-4 border-l-brand">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-6 w-6 text-brand" />
              <h2 className="text-xl font-semibold">Selective Highlighting Analysis</h2>
            </div>
            <Badge variant="secondary" className="text-sm">
              {totalCount} instance{totalCount !== 1 ? 's' : ''} found
            </Badge>
          </div>
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Cherry className="h-4 w-4 text-rose-500" />
              <span>{cherryPickingCount} cherry picking</span>
            </div>
            <div className="flex items-center space-x-1">
              <Waves className="h-4 w-4 text-amber-500" />
              <span>{floodingCount} flooding</span>
            </div>
          </div>
        </div>
      </GlassCard>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3 glass-effect-subtle rounded-2xl">
          <TabsTrigger value="all" className="rounded-xl">
            All ({totalCount})
          </TabsTrigger>
          <TabsTrigger value="cherry-picking" className="rounded-xl">
            <Cherry className="h-4 w-4 mr-2" />
            Cherry Picking ({cherryPickingCount})
          </TabsTrigger>
          <TabsTrigger value="flooding" className="rounded-xl">
            <Waves className="h-4 w-4 mr-2" />
            Flooding ({floodingCount})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6 space-y-6">
          {validData.length === 0 ? (
            <GlassCard variant="subtle" className="text-center">
              <p className="text-muted-foreground">No instances found</p>
            </GlassCard>
          ) : (
            validData.map(instance => (
              <SelectiveHighlightingCard
                key={instance.id}
                data={instance}
                admin={admin}
              />
            ))
          )}
        </TabsContent>

        <TabsContent value="cherry-picking" className="mt-6 space-y-6">
          {sortedCherryPicking.length === 0 ? (
            <GlassCard variant="subtle" className="text-center">
              <div className="flex flex-col items-center space-y-3">
                <Cherry className="h-8 w-8 text-muted-foreground/50" />
                <p className="text-muted-foreground">No cherry picking instances found</p>
              </div>
            </GlassCard>
          ) : (
            sortedCherryPicking.map(instance => (
              <SelectiveHighlightingCard
                key={instance.id}
                data={instance}
                admin={admin}
              />
            ))
          )}
        </TabsContent>

        <TabsContent value="flooding" className="mt-6 space-y-6">
          {sortedFlooding.length === 0 ? (
            <GlassCard variant="subtle" className="text-center">
              <div className="flex flex-col items-center space-y-3">
                <Waves className="h-8 w-8 text-muted-foreground/50" />
                <p className="text-muted-foreground">No flooding instances found</p>
              </div>
            </GlassCard>
          ) : (
            sortedFlooding.map(instance => (
              <SelectiveHighlightingCard
                key={instance.id}
                data={instance}
                admin={admin}
              />
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* Information card */}
      <GlassCard variant="subtle" className="border-l-4 border-l-amber-500">
        <div className="flex items-start gap-4">
          <AlertTriangle className="h-6 w-6 text-amber-500 mt-1 flex-shrink-0" />
          <div className="space-y-2">
            <h3 className="font-semibold text-amber-700 dark:text-amber-300">About Selective Highlighting</h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              Selective highlighting refers to practices where an entity emphasizes positive aspects while downplaying negative ones.
              <span className="font-medium text-rose-600 dark:text-rose-400"> Cherry picking</span> involves selectively presenting favorable data while ignoring unfavorable information.
              <span className="font-medium text-amber-600 dark:text-amber-400"> Flooding</span> involves overwhelming audiences with minor positive claims to distract from more significant negative issues.
            </p>
          </div>
        </div>
      </GlassCard>
    </div>
  );
}
