'use client'

import React, { useEffect, useState } from 'react'
import { createClient } from '@/app/supabase/client'
import { useEntity } from '@/components/context/entity/entity-context'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, TabsList, TabsTrigger } from '@ui/components/ui/tabs'
import { Badge } from '@ui/components/ui/badge'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { Headline } from '@ui/components/front-page/headline'
import { runAsync } from '@utils/react-utils'
import { Skeleton } from '@ui/components/ui/skeleton'
import { cn } from '@utils/lib/utils'

// Define the interface for predictive analysis data
interface PredictiveAnalysis {
  id: number
  virtual_entity_id: number
  year: number
  summary: string
  detailed_analysis: string
  potential_risks: string[]
  potential_opportunities: string[]
  confidence: number
  prediction_id: number
  source_cluster_id: number
}

export default function PredictivePage() {
  const entityContext = useEntity()
  const supabase = createClient()
  const [analyses, setAnalyses] = useState<Record<number, PredictiveAnalysis[]>>({})
  const [years, setYears] = useState<number[]>([])
  const [loading, setLoading] = useState(true)
  const [activeYear, setActiveYear] = useState<number | null>(null)

  useEffect(() => {
    if (!entityContext.entity) return

    runAsync(async () => {
      setLoading(true)

      // Fetch predictive analyses for this entity
      const { data, error } = await supabase
        .from('xfer_predictive_analysis')
        .select('*')
        .eq('virtual_entity_id', entityContext.entity as string)
        .order('year', { ascending: true })
        .order('confidence', { ascending: false })

      if (error) {
        console.error('Error fetching predictive analyses:', error)
        setLoading(false)
        return
      }

      if (!data || data.length === 0) {
        console.log('No predictive analyses found for this entity')
        setLoading(false)
        return
      }

      // Process the data
      const analysesByYear: Record<number, PredictiveAnalysis[]> = {}
      const yearsList: number[] = []

      data.forEach((analysis: any) => {
        // Parse JSON fields
        const processedAnalysis: PredictiveAnalysis = {
          ...analysis,
          potential_risks: Array.isArray(analysis.potential_risks)
            ? analysis.potential_risks
            : (typeof analysis.potential_risks === 'object'
                ? Object.values(analysis.potential_risks)
                : []),
          potential_opportunities: Array.isArray(analysis.potential_opportunities)
            ? analysis.potential_opportunities
            : (typeof analysis.potential_opportunities === 'object'
                ? Object.values(analysis.potential_opportunities)
                : [])
        }

        if (!analysesByYear[analysis.year]) {
          analysesByYear[analysis.year] = []
          yearsList.push(analysis.year)
        }

        analysesByYear[analysis.year].push(processedAnalysis)
      })

      // Sort years
      yearsList.sort((a, b) => a - b)

      setAnalyses(analysesByYear)
      setYears(yearsList)
      setActiveYear(yearsList[0] || null)
      setLoading(false)
    })
  }, [entityContext.entity])

  // Helper function to get confidence class
  const getConfidenceClass = (confidence: number) => {
    if (confidence >= 0.7) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    if (confidence >= 0.4) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <Headline>Predictive Analysis</Headline>
        <div className="grid grid-cols-1 gap-6">
          <Card className="glass-effect-lit rounded-3xl overflow-hidden">
            <CardHeader>
              <Skeleton className="h-8 w-64" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (years.length === 0) {
    return (
      <div className="p-6 space-y-6">
        <Headline>Predictive Analysis</Headline>
        <Card className="glass-effect-lit rounded-3xl overflow-hidden">
          <CardContent className="p-6">
            <p className="text-center text-slate-600 dark:text-slate-300">
              No predictive analyses available for this entity. Run a predictive analysis first.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <Headline>Predictive Analysis</Headline>

      {/* Year tabs */}
      <Tabs
        value={activeYear?.toString() || ''}
        onValueChange={(value) => setActiveYear(parseInt(value))}
        className="w-full"
      >
        <TabsList className="mb-4">
          {years.map(year => (
            <TabsTrigger key={year} value={year.toString()}>
              {year}
            </TabsTrigger>
          ))}
        </TabsList>

        {years.map(year => (
          <TabsContent key={year} value={year.toString()} className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              {analyses[year]?.map((analysis, index) => (
                <Card key={index} className="glass-effect-lit rounded-3xl overflow-hidden">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="text-xl">
                      {analysis.year} Prediction {index + 1}
                    </CardTitle>
                    <Badge className={cn("ml-2", getConfidenceClass(analysis.confidence))}>
                      Confidence: {Math.round(analysis.confidence * 100)}%
                    </Badge>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Summary</h3>
                      <p className="text-slate-700 dark:text-slate-300">{analysis.summary}</p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-2">Detailed Analysis</h3>
                      <div className="prose dark:prose-invert max-w-none">
                        <EkoMarkdown citations={null} admin={false}>{analysis.detailed_analysis}</EkoMarkdown>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-lg font-semibold mb-2">Potential Risks</h3>
                        <ul className="list-disc pl-5 space-y-1">
                          {analysis.potential_risks.map((risk, i) => (
                            <li key={i} className="text-red-700 dark:text-red-400">{risk}</li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold mb-2">Potential Opportunities</h3>
                        <ul className="list-disc pl-5 space-y-1">
                          {analysis.potential_opportunities.map((opportunity, i) => (
                            <li key={i} className="text-green-700 dark:text-green-400">{opportunity}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
