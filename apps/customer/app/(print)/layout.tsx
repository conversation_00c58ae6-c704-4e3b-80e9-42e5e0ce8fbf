import '../globals.css'
import { Inter } from 'next/font/google'
import { createClient } from '@/app/supabase/server'
import { redirect } from 'next/navigation'

const inter = Inter({
    subsets: ['latin'],
    display: 'swap',
})

export default async function PrintLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const supabase = await createClient();

    const user = (await supabase.auth.getUser()).data.user;
    if (!user) {
        return redirect("/login?next=/customer");
    }

    return (
        <div className="min-h-screen w-full bg-white text-black print:bg-white print:text-black">
            {children}
        </div>
    );
}
