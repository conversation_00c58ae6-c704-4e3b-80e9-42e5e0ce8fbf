import { GoogleGenerativeA<PERSON> } from '@google/generative-ai'
import { kv } from '@vercel/kv'
import crypto from 'crypto'
import { 
  callValidatedLLMs, 
  callLLM, 
  LLMModel, 
  LLMOptions, 
  Message,
  createJSONValidator,
  createTextValidator 
} from './validated-llms'

// Initialize the Google Generative AI client
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENERATIVE_AI_API_KEY || '')

// Model names
export const AI_MODEL_NAME = 'gemini-2.5-flash-preview-05-20'
// export const FANCY_AI_MODEL_NAME = 'gemini-2.5-pro-preview-05-06';
export const FANCY_AI_MODEL_NAME = 'gemini-2.5-flash-preview-05-20'

// Export LLM models for external use
export { LLMModel }

const caching = true
/**
 * Common handler for generating content by calling Gemini API directly
 * @param options Options for generating content
 * @returns The generated content as a string
 */
export async function generateReportContent(options: {
  modelName: string,
  prompt: string,
  endpoint: string,
  entityName: string,
}): Promise<string> {
  const { modelName, prompt, endpoint, entityName } = options;
  
  console.log(`[API] ${endpoint} called for ${entityName} using model ${modelName}`);
  const cacheKey = `v35-${endpoint}-${crypto.createHash('md5').update(JSON.stringify(prompt)).digest('hex')}`
  if (caching && await kv.hgetall(cacheKey)) {
    return (await kv.hgetall(cacheKey))?.response as string
  }
  try {
    // Get the model
    const model = genAI.getGenerativeModel({
      model: modelName,
      generationConfig: {
        maxOutputTokens: 16000,
      },

      // tools: [
      //   {
      //     googleSearchRetrieval: {
      //       // Optional: Configure search parameters
      //       dynamicRetrievalConfig: {
      //         mode: DynamicRetrievalMode.MODE_DYNAMIC,
      //         dynamicThreshold: 0.3, // Threshold for when to use search (0.0 to 1.0)
      //       },
      //     },
      //   },
      // ],
    })
    
    // Generate content using direct API call
    const result = await model.generateContent(prompt)
    const response = result.response;
    const text = response.text();
    
    console.log(`[API] ${endpoint} completed for ${entityName}, length: ${text.length} chars`);
    if (caching) {
      await kv.hset(cacheKey, { response: text })
    }
    return text;
  } catch (error) {
    console.error(`[API] ${endpoint} error:`, error);
    throw error;
  }
}

/**
 * Generate validated report content with retry and evaluation
 * This is an example of how to use the validated LLMs
 */
export async function generateValidatedReportContent(options: {
  prompt: string,
  endpoint: string,
  entityName: string,
  validator?: (response: string | null) => boolean | string | Promise<boolean | string>,
  retries?: number
}): Promise<string> {
  const { prompt, endpoint, entityName, validator, retries = 4 } = options;
  
  console.log(`[API] ${endpoint} called for ${entityName} with validation`);
  
  const messages: Message[] = [{ role: 'user', content: prompt }]
  
  const llmOptions: LLMOptions = {
    eval: validator,
    evalRetry: retries,
    cacheKey: `${endpoint}-${entityName}`,
    cachePrefix: 'validated-report',
    maxOutputTokens: 16000,
    escalateTo: [LLMModel.GEMINI_PRO], // Fallback to pro model if flash fails
    appendOnEvalFail: true,
    evalRetryMessage: 'The response did not meet our quality standards. Please provide a more comprehensive and well-structured response.'
  }
  
  const result = await callValidatedLLMs([LLMModel.GEMINI_FLASH], messages, llmOptions)
  
  if (!result) {
    throw new Error(`Failed to generate content for ${endpoint}`)
  }
  
  console.log(`[API] ${endpoint} completed for ${entityName}, length: ${result.length} chars`);
  return result
}

/**
 * Example: Generate report content that must contain citations
 */
export async function generateCitedReportContent(options: {
  prompt: string,
  endpoint: string,
  entityName: string,
  minimumCitations?: number
}): Promise<string> {
  const { minimumCitations = 1 } = options
  
  const citationValidator = createTextValidator(
    (text: string) => {
      const citationMatches = text.match(/\[\^(\d+)]/g)
      return citationMatches ? citationMatches.length >= minimumCitations : false
    },
    `Response must contain at least ${minimumCitations} citation(s) in the format [^1234]`
  )
  
  return generateValidatedReportContent({
    ...options,
    validator: citationValidator
  })
}

/**
 * Example: Generate JSON content with schema validation
 */
export async function generateValidatedJSON<T>(options: {
  prompt: string,
  endpoint: string,
  entityName: string,
  schema: (obj: any) => obj is T,
  retries?: number
}): Promise<T> {
  const { schema } = options
  
  const jsonValidator = createJSONValidator(schema, 'Response must be valid JSON matching the expected schema')
  
  const result = await generateValidatedReportContent({
    ...options,
    validator: jsonValidator
  })
  
  return JSON.parse(result) as T
}
