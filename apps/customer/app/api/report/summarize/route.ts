import { NextRequest } from 'next/server'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { FANCY_AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'
import { AI_MODEL_NAME } from '@/utils/cache-example'

export const maxDuration = 180;

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { content, prompt, title } = body

    if (!content) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameter: content' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Validate content is a string
    if (typeof content !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Content must be a string' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Truncate content if too long (to prevent token limit issues)
    const maxContentLength = 200000 // Adjust based on your needs
    const truncatedContent = content.length > maxContentLength 
      ? content.substring(0, maxContentLength) + '...[truncated]'
      : content

    console.log(`[API] /api/report/summarize called with:`, {
      contentLength: content.length,
      truncatedLength: truncatedContent.length,
      title: title || 'No title',
      hasPrompt: !!prompt
    })

    // Build the prompt for summarization
    const summaryPrompt = `
      <instructions>
      You are tasked with creating a comprehensive summary of the provided content.
      
      ${title ? `The summary is for: "${title}"` : ''}
      ${prompt ? `Additional context: ${prompt}` : ''}
      
      Please provide a well-structured summary that:
      1. Captures the key points and main themes
      2. Maintains important details and context
      3. Preserves any citations in the format [^citation_id]
      4. Is written in clear, professional language
      5. Has no headings
      6. Is no longer than 500 words
      
      The summary should be comprehensive but concise, focusing on the most important information.
      
      ${COMMON_INSTRUCTIONS}
      
      Content to summarize:
      ${truncatedContent}

      DO NOT INCLUDE TABLES OR GRAPHS IN SUMMARIES
      </instructions>
    `;

    // Generate summary using our Gemini client
    const summaryText = await generateReportContent({
      modelName: AI_MODEL_NAME,
      prompt: summaryPrompt,
      endpoint: '/api/report/summarize',
      entityName: title || 'Summary',
    });

    // Return the summary (citations are managed by EkoDocumentEditor)
    const response = {
      text: summaryText,
      metadata: {
        type: 'summary',
        originalContentLength: content.length,
        summaryLength: summaryText.length,
        title: title || null,
        prompt: prompt || null,
        generatedAt: new Date().toISOString(),
      }
    };

    return new Response(JSON.stringify(response), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=1800, stale-while-revalidate=3600'
      }
    });

  } catch (error) {
    console.error('[API] /api/report/summarize error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate summary', 
        message: error instanceof Error ? error.message : String(error) 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
