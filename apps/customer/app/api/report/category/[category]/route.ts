import { NextRequest } from 'next/server'
import { truncate } from '@/utils/text-utils'
import { create<PERSON><PERSON><PERSON><PERSON> } from '@/utils/cache-utils'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { FANCY_AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'
import { ONTOLOGY } from '@/app/api/report/ontology'
import { fetchEntityData, fetchRunData } from '../../data-fetchers'

export const maxDuration = 180;

// Define valid categories
const VALID_CATEGORIES = ['environmental', 'social', 'governance'];

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ category: string }> }
) {
  try {
    // Get the category from the route parameter
    const params = await context.params;
    const { category } = params;

    // Validate the category
    if (!VALID_CATEGORIES.includes(category)) {
      return new Response(
        JSON.stringify({ error: `Invalid category: ${category}. Must be one of: ${VALID_CATEGORIES.join(', ')}` }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const { searchParams } = new URL(request.url)
    const entity = searchParams.get('entity')
    const run = searchParams.get('run') || 'latest'
    const model = searchParams.get('model') || 'ekoIntelligence'

    // Get section summaries from query params (they should be passed as JSON string)
    const sectionSummariesParam = searchParams.get('sectionSummaries')
    let sectionSummaries = {}
    if (sectionSummariesParam) {
      try {
        sectionSummaries = JSON.parse(sectionSummariesParam)
      } catch (e) {
        console.error('Error parsing sectionSummaries:', e)
      }
    }

    if (!entity) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameter: entity' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch entity data
    const entityData = await fetchEntityData(entity)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(run, entity)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const entityName = entityData.name
    const modelName = model

    console.log(`[API] /api/report/category/${category} called with:`, {
      entityName,
      modelName,
      sectionCount: Object.keys(sectionSummaries).length
    })

    // Create a cache key for logging purposes
    const sectionKeys = Object.keys(sectionSummaries).sort().join(',');
    const cacheKey = createCacheKey(category, {
      sectionKeys,
      entityName,
      modelName
    });

    // Prepare section content for the prompt
    const sectionContent = Object.entries(sectionSummaries)
      .map(([sectionId, content]) => {
        return `
Section: ${sectionId}
Content: ${content}
        `.trim();
      }).join('\n\n');


    // Create a combined prompt for all parts
    const promptText = `

<ontology>
${ONTOLOGY}
</ontology>

<instructions>
${COMMON_INSTRUCTIONS}

You are analyzing the ${category.charAt(0).toUpperCase() + category.slice(1)} category of an ESG report about ${entityName} using the ${modelName} model.

Here are the section summaries for this category:

${sectionContent}

Please provide the following in your response, using the exact formatting specified::

### Summary
A comprehensive summary that identifies key themes and patterns across all sections, highlights the most significant findings, provides context and implications, and maintains a balanced, objective tone. This should be 2-3 paragraphs and include citations in the format [^1234] where appropriate.

### Key Positives
A bullet-point list of 3-5 key POSITIVE aspects identified in these sections. Include citations in the format [^1234] where appropriate.

### Key Negatives
A bullet-point list of 3-5 key NEGATIVE aspects identified in these sections. Include citations in the format [^1234] where appropriate.

### Key Risks
A bullet-point list of 3-5 key RISKS (potential future negative outcomes or challenges) identified in these sections. Include citations in the format [^1234] where appropriate.

### Key Opportunities
A bullet-point list of 3-5 key OPPORTUNITIES (potential future positive developments or areas for improvement) identified in these sections. Include citations in the format [^1234] where appropriate.

### Impact of Actions
Using the ontology, evaluate the impact of the actions described in these sections. Provide a bullet-point list of 3-5 key findings. Include citations in the format [^1234] where appropriate.
DO NOT REFER TO THE ONTOLOGY. This is internal and the user does not need to understand it.

### Recommendations
Using the ontology, provide a bullet-point list of 3-5 key recommendations. Include citations in the format [^1234] where appropriate.
DO NOT REFER TO THE ONTOLOGY. This is internal and the user does not need to understand it.

IMPORTANT: Use exactly "###" (three hash symbols) for all section headings, not "##" (two hash symbols).

DO NOT ADD ANY OTHER HEADINGS, PLEASE, PLEASE I BEG YOU!
</instructions>
    `;

    // Truncate if needed
    const truncatedPrompt = truncate(promptText, 100000)!;

    // Generate content using our Gemini client
    const text = await generateReportContent({
      modelName: FANCY_AI_MODEL_NAME,
      prompt: `You are a professional ESG report writer for a leading auditing firm specializing in environmental, social, and governance matters. You are objective and factual but diplomatic when criticizing companies. You are not conversational.\n\n${truncatedPrompt}`,
      endpoint: `/api/report/category/${category}`,
      entityName,
    });

    // Return the text response
    return new Response(text, {
      headers: { 'Content-Type': 'text/plain' }
    });
  } catch (error) {
    // Get the category from the route parameter for error reporting
    let categoryValue = 'unknown';
    try {
      const params = await context.params;
      categoryValue = params.category || 'unknown';
    } catch (e) {
      console.error('Error resolving params:', e);
    }
    
    console.error(`[API] /api/report/category/${categoryValue} error:`, error);
    return new Response(
      JSON.stringify({ error: `Failed to summarize ${categoryValue} category`, message: error instanceof Error ? error.message : String(error) }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
