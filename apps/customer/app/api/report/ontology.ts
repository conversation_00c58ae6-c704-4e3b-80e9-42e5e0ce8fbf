export const ONTOLOGY = `
  ################################################################
# COMBINED ONTOLOGY OF SUSTAINABILITY & COLONIALITY (v10.2)
# ----------------------------------------------------------
# A single-file consolidation of ecological, socio-economic,
# colonial-power, justice and mitigation vocabularies.
################################################################


@prefix ex: <http://example.org/ontology#> .
@prefix exqm: <http://example.org/ontology/quantitative#> .
@prefix exgf: <http://example.org/ontology/ghostfishing#> .
@prefix eco: <http://example.org/ecology#> .
@prefix species: <http://example.org/species#> .
@prefix sdg: <http://example.org/sdg#> .
@prefix doughnut: <http://example.org/doughnut#> .
@prefix pbt: <http://example.org/plantbasedtreaty#> .
@prefix axioms: <http://example.org/ontology/axioms#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix skos: <http://www.w3.org/2004/02/skos/core#> .
@prefix excol: <http://example.org/ontology/colonial#> .
@prefix exvio: <http://example.org/ontology/violence#> .
@prefix exind: <http://example.org/ontology/indigenous#> .
@prefix exepi: <http://example.org/ontology/epistemic#> .
@prefix exhis: <http://example.org/ontology/history#> .
@prefix time: <http://www.w3.org/2006/time#> .


<http://example.org/combinedOntologyCausal>
 a owl:Ontology ;
 owl:versionInfo "10.1" ;
 rdfs:comment """
 Fully integrated ontology covering:


 • Planetary Boundaries (incl. Land-System Change) 
 • The complete UN-SDG set 
 • Doughnut-Economics social & ecological segments 
 • Drawdown mitigation solutions 
 • Plant-Based Treaty categories 
 • Colonial-power structures & violence typologies 
 • Indigenous sustainabilities 
 • Green-washing mechanics 
 • Ghost-fishing, keystone species, regenerative agriculture, modern slavery 
 • Quantitative sustainability metrics, economic & demographic data 
 • Cultural & Economic Paradigms — now extended with Degrowth, Well-being-Economy and Super-organism frames **(v10.1)** 
 • Growth, Decoupling & Energy-Economics module **(NEW in v10.1)** 
 · decoupling taxonomy (relative / absolute) 
 · rebound & carbon-leakage drivers 
 · system-dynamics models (World3) and collapse scenarios 
 · EROI / net-energy metrics, decent-living energy threshold 
 · behavioural crisis & advertising-pressure classes 
 · finance & IAM critique classes (ESG-metric, discount-rate) 
 • Unified SWRL-like reasoning axioms and cross-module integration properties.


 All classes and properties are DL-compliant; multi-domain collisions fixed, missing
 predicates declared, and class/property punning removed.
 """ .














################################################################
# 0. SHARED DATATYPE PROPERTIES (previously undeclared)
################################################################
ex:hasYear a owl:DatatypeProperty ;
 rdfs:label "has year" ;
 rdfs:range xsd:gYear .


ex:hasValue a owl:DatatypeProperty ;
 rdfs:label "has scalar value" ;
 rdfs:range xsd:float .


ex:hasPHLevel a owl:DatatypeProperty ;
 rdfs:label "has pH level" ;
 rdfs:domain ex:OceanChemistryIndicator ;
 rdfs:range xsd:float .


# consolidate carbon‑sequestration datatype into the quantitative namespace
exqm:hasCarbonSequestrationPotential a owl:DatatypeProperty ;
 rdfs:label "has carbon sequestration potential" ;
 rdfs:domain ex:SustainabilityInitiative ;
 rdfs:range xsd:float .




################################################################
# 2. LEGAL DOCTRINES — NEW CLASS TO AVOID PUNNING
################################################################
excol:PapalBull a owl:Class ;
 rdfs:subClassOf excol:LegalDoctrine ;
 rdfs:label "Papal Bull" ;
 skos:definition "Formal papal decree functioning as a legal doctrine." .


################################################################
# 3. CORE FIXED AXIOMS & BRIDGES
################################################################
# Bridge to remove unsatisfiable class chain (domain mismatch)
ex:DriverOfOvershoot rdfs:subClassOf ex:CulturalOrEconomicParadigm .




################################################################
# MODULE: CORE – PLANETARY BOUNDARIES & FOUNDATIONAL RELATIONSHIPS
################################################################


### Planetary Boundaries
ex:PlanetaryBoundary a owl:Class ;
 rdfs:label "Planetary Boundary" ;
 skos:definition "A safe operating space for humanity as defined by ecological limits." ;
 rdfs:comment "Core class representing environmental limits of Earth’s systems." .


ex:ClimateChangeBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Climate Change Boundary" ;
 skos:definition "A threshold for the impacts of anthropogenic climate change." ;
 rdfs:comment "Represents limits to climate change impacts." .


ex:OceanAcidificationBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Ocean Acidification Boundary" ;
 skos:definition "A threshold for ocean acidification." ;
 rdfs:comment "Represents limits on changes in ocean pH." .


ex:TerrestrialBiodiversityBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Terrestrial Biodiversity Boundary" ;
 skos:definition "A threshold for biodiversity loss on land." ;
 rdfs:comment "Defines safe limits for terrestrial ecosystems." .


ex:FreshwaterUseBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Freshwater Use Boundary" ;
 skos:definition "A threshold for the sustainable use of freshwater resources." ;
 rdfs:comment "Represents safe limits on water consumption and ecosystem alteration." .


ex:NovelEntitiesBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Novel Entities Boundary" ;
 skos:definition "A threshold for the introduction of novel substances (chemicals, plastics) that could disrupt ecosystems." ;
 rdfs:comment "Captures safe limits for novel entities in the environment." .


ex:StratosphericOzoneBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Stratospheric Ozone Boundary" ;
 skos:definition "A threshold for depletion of the stratospheric ozone layer." ;
 rdfs:comment "Represents safe limits for ozone layer depletion." .


ex:NitrogenCycleBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Nitrogen Cycle Boundary" ;
 skos:definition "A threshold for disruptions to the global nitrogen cycle." ;
 rdfs:comment "Represents safe operating limits for nitrogen flows." .


ex:PhosphorusCycleBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Phosphorus Cycle Boundary" ;
 skos:definition "A threshold for disruptions to the global phosphorus cycle." ;
 rdfs:comment "Represents safe operating limits for phosphorus flows." .


ex:AerosolLoadingBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Aerosol Loading Boundary" ;
 skos:definition "A threshold for atmospheric aerosol concentrations." ;
 rdfs:comment "Defines safe limits for aerosol loadings affecting climate and air quality." .


ex:LandSystemChangeBoundary a owl:Class ;
 rdfs:subClassOf ex:PlanetaryBoundary ;
 rdfs:label "Land System Change Boundary" ;
 skos:definition "A threshold for the extent of land system transformation." ;
 rdfs:comment "Represents limits on deforestation, land conversion, etc." .


### Foundational Relationships
ex:ViableBiosphere a owl:Class ;
 rdfs:label "Viable Biosphere" ;
 skos:definition "A diverse, sustainable, healthy biosphere providing essential ecosystem services." ;
 rdfs:comment "Without a healthy biosphere, human civilisation cannot exist." .


ex:HumanCivilisation a owl:Class ;
 rdfs:label "Human Civilisation" ;
 skos:definition "The complex socio-cultural, political, and economic structures forming human society." ;
 rdfs:comment "Develops upon and depends on a functioning biosphere." .


ex:EconomicActivity a owl:Class ;
 rdfs:label "Economic Activity" ;
 skos:definition "Production, distribution, and consumption of goods/services essential for livelihoods." ;
 rdfs:comment "Enabled by and emerges from human civilisation." .


ex:providesFoundationFor a owl:ObjectProperty ;
 rdfs:label "provides foundation for" ;
 skos:definition "Indicates that a viable biosphere is the essential natural basis for human civilisation." ;
 rdfs:domain ex:ViableBiosphere ;
 rdfs:range ex:HumanCivilisation ;
 rdfs:comment "Healthy, diverse biosphere → emergent human civilisation." .


ex:isPrerequisiteFor a owl:ObjectProperty ;
 rdfs:label "is prerequisite for" ;
 skos:definition "Indicates that human civilisation is a necessary precursor to economic activity." ;
 rdfs:domain ex:HumanCivilisation ;
 rdfs:range ex:EconomicActivity ;
 rdfs:comment "Human civilisation → structured economic activity." .


### Causal Relationships among Planetary Boundaries
ex:causallyInfluences a owl:ObjectProperty ;
 rdfs:label "causally influences" ;
 skos:definition "One planetary boundary exerts a causal influence on another." ;
 rdfs:comment "Explicit causal link: e.g., Climate Change can drive Ocean Acidification." ;
 rdfs:domain ex:PlanetaryBoundary ;
 rdfs:range ex:PlanetaryBoundary ;
 owl:inverseOf ex:isCausallyInfluencedBy .


ex:isCausallyInfluencedBy a owl:ObjectProperty ;
 rdfs:label "is causally influenced by" ;
 skos:definition "Inverse: a planetary boundary is influenced by another boundary’s state." ;
 rdfs:comment "Used for bidirectional causal links among boundaries." ;
 rdfs:domain ex:PlanetaryBoundary ;
 rdfs:range ex:PlanetaryBoundary .


# Example causal linkages
ex:ClimateChangeBoundary ex:causallyInfluences ex:OceanAcidificationBoundary .
ex:OceanAcidificationBoundary ex:causallyInfluences ex:ClimateChangeBoundary .
ex:LandSystemChangeBoundary ex:causallyInfluences ex:TerrestrialBiodiversityBoundary .
ex:AerosolLoadingBoundary ex:causallyInfluences ex:ClimateChangeBoundary .
ex:NitrogenCycleBoundary ex:causallyInfluences ex:FreshwaterUseBoundary .
ex:PhosphorusCycleBoundary ex:causallyInfluences ex:FreshwaterUseBoundary .
ex:LandSystemChangeBoundary ex:causallyInfluences ex:NitrogenCycleBoundary .
ex:LandSystemChangeBoundary ex:causallyInfluences ex:PhosphorusCycleBoundary .
ex:ClimateChangeBoundary ex:causallyInfluences ex:TerrestrialBiodiversityBoundary .
ex:NitrogenCycleBoundary ex:causallyInfluences ex:OceanAcidificationBoundary .
ex:PhosphorusCycleBoundary ex:causallyInfluences ex:OceanAcidificationBoundary .
ex:NovelEntitiesBoundary ex:causallyInfluences ex:OceanAcidificationBoundary .
ex:NovelEntitiesBoundary ex:causallyInfluences ex:TerrestrialBiodiversityBoundary .
ex:ClimateChangeBoundary ex:causallyInfluences ex:LandSystemChangeBoundary .
ex:AerosolLoadingBoundary ex:causallyInfluences ex:TerrestrialBiodiversityBoundary .
ex:ClimateChangeBoundary ex:cascadesInto ex:TerrestrialBiodiversityBoundary .






################################################################
# MODULE 1: DOUGHNUT ECONOMICS (ECOLOGICAL & SOCIAL SEGMENTS)
################################################################


ex:DoughnutEconomics a owl:Class ;
 rdfs:label "Doughnut Economics" ;
 skos:definition "A model balancing ecological ceilings and social foundations." ;
 rdfs:comment "Captures the Doughnut approach to sustainability." .


ex:DoughnutSegment a owl:Class ;
 rdfs:label "Doughnut Segment" ;
 skos:definition "A segment representing a specific ecological or social threshold in the Doughnut model." ;
 rdfs:comment "Generic parent class for either an ecological ceiling or a social foundation." .


ex:EcologicalCeilingSegment a owl:Class ;
 rdfs:subClassOf ex:DoughnutSegment ;
 rdfs:label "Ecological Ceiling Segment" ;
 skos:definition "A segment representing one of the planetary boundaries in Doughnut Economics." ;
 rdfs:comment "Forms the outer ring of the Doughnut." .


ex:SocialFoundationSegment a owl:Class ;
 rdfs:subClassOf ex:DoughnutSegment ;
 rdfs:label "Social Foundation Segment" ;
 skos:definition "A segment representing a social foundation necessary for human well-being." ;
 rdfs:comment "Forms the inner ring of the Doughnut." .


ex:hasSegment a owl:ObjectProperty ;
 rdfs:domain ex:DoughnutEconomics ;
 rdfs:range ex:DoughnutSegment ;
 rdfs:label "has segment" ;
 skos:definition "Associates a Doughnut Economics instance with its constituent segments." ;
 rdfs:comment "Links ecological ceilings and social foundations to the Doughnut model." .


### Ecological Ceiling Segments
ex:climateChangeSegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Climate Change Segment" ;
 skos:definition "Ecological ceiling corresponding to anthropogenic climate change." ;
 ex:refersToBoundary ex:ClimateChangeBoundary .


ex:oceanAcidificationSegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Ocean Acidification Segment" ;
 skos:definition "Ecological ceiling corresponding to ocean acidification." ;
 ex:refersToBoundary ex:OceanAcidificationBoundary .


ex:terrestrialBiodiversitySegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Terrestrial Biodiversity Segment" ;
 skos:definition "Ecological ceiling corresponding to terrestrial biodiversity loss." ;
 ex:refersToBoundary ex:TerrestrialBiodiversityBoundary .


ex:freshwaterUseSegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Freshwater Use Segment" ;
 skos:definition "Ecological ceiling corresponding to sustainable freshwater use." ;
 ex:refersToBoundary ex:FreshwaterUseBoundary .


ex:novelEntitiesSegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Novel Entities Segment" ;
 skos:definition "Ecological ceiling corresponding to the impacts of novel entities." ;
 ex:refersToBoundary ex:NovelEntitiesBoundary .


ex:stratosphericOzoneSegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Stratospheric Ozone Segment" ;
 skos:definition "Ecological ceiling corresponding to depletion of the stratospheric ozone layer." ;
 ex:refersToBoundary ex:StratosphericOzoneBoundary .


ex:nitrogenCycleSegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Nitrogen Cycle Segment" ;
 skos:definition "Ecological ceiling corresponding to disruptions in the nitrogen cycle." ;
 ex:refersToBoundary ex:NitrogenCycleBoundary .


ex:phosphorusCycleSegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Phosphorus Cycle Segment" ;
 skos:definition "Ecological ceiling corresponding to disruptions in the phosphorus cycle." ;
 ex:refersToBoundary ex:PhosphorusCycleBoundary .


ex:aerosolLoadingSegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Aerosol Loading Segment" ;
 skos:definition "Ecological ceiling corresponding to aerosol loadings." ;
 ex:refersToBoundary ex:AerosolLoadingBoundary .


ex:landSystemChangeSegment a ex:EcologicalCeilingSegment ;
 rdfs:label "Land System Change Segment" ;
 skos:definition "Ecological ceiling corresponding to land system transformations." ;
 ex:refersToBoundary ex:LandSystemChangeBoundary .


### Social Foundation Segments
ex:waterSegment a ex:SocialFoundationSegment ;
 rdfs:label "Water Segment" ;
 skos:definition "Social foundation for access to clean water." .


ex:foodSegment a ex:SocialFoundationSegment ;
 rdfs:label "Food Segment" ;
 skos:definition "Social foundation for food security." .


ex:healthSegment a ex:SocialFoundationSegment ;
 rdfs:label "Health Segment" ;
 skos:definition "Social foundation for health and well-being." .


ex:educationSegment a ex:SocialFoundationSegment ;
 rdfs:label "Education Segment" ;
 skos:definition "Social foundation for quality education." .


ex:incomeWorkSegment a ex:SocialFoundationSegment ;
 rdfs:label "Income & Work Segment" ;
 skos:definition "Social foundation for decent income and employment." .


ex:peaceJusticeSegment a ex:SocialFoundationSegment ;
 rdfs:label "Peace & Justice Segment" ;
 skos:definition "Social foundation for peaceful societies and justice." .


ex:politicalVoiceSegment a ex:SocialFoundationSegment ;
 rdfs:label "Political Voice Segment" ;
 skos:definition "Social foundation for effective political participation." .


ex:socialEquitySegment a ex:SocialFoundationSegment ;
 rdfs:label "Social Equity Segment" ;
 skos:definition "Social foundation for equity among individuals." .


ex:genderEqualitySegment a ex:SocialFoundationSegment ;
 rdfs:label "Gender Equality Segment" ;
 skos:definition "Social foundation for gender equality." .


ex:housingSegment a ex:SocialFoundationSegment ;
 rdfs:label "Housing Segment" ;
 skos:definition "Social foundation for adequate housing." .


ex:networksSegment a ex:SocialFoundationSegment ;
 rdfs:label "Networks Segment" ;
 skos:definition "Social foundation for effective social and economic networks." .


ex:energySegment a ex:SocialFoundationSegment ;
 rdfs:label "Energy Segment" ;
 skos:definition "Social foundation for access to affordable and sustainable energy." .


ex:doughnutEconomicsInstance a ex:DoughnutEconomics ;
 rdfs:label "Doughnut Economics Model Instance" ;
 skos:definition "An instance linking all ecological ceilings and social foundations of the Doughnut model." ;
 ex:hasSegment 
 ex:climateChangeSegment, ex:oceanAcidificationSegment, ex:terrestrialBiodiversitySegment, ex:freshwaterUseSegment,
 ex:novelEntitiesSegment, ex:stratosphericOzoneSegment, ex:nitrogenCycleSegment, ex:phosphorusCycleSegment,
 ex:aerosolLoadingSegment, ex:landSystemChangeSegment,
 ex:waterSegment, ex:foodSegment, ex:healthSegment, ex:educationSegment, ex:incomeWorkSegment,
 ex:peaceJusticeSegment, ex:politicalVoiceSegment, ex:socialEquitySegment, ex:genderEqualitySegment,
 ex:housingSegment, ex:networksSegment, ex:energySegment .


################################################################
# MODULE: UN SUSTAINABLE DEVELOPMENT GOALS (ALL 17)
################################################################


ex:SDG a owl:Class ;
 rdfs:label "Sustainable Development Goal" ;
 skos:definition "A high-level global goal from the UN for sustainable development." .


ex:sdg1 a ex:SDG ;
 rdfs:label "SDG 1: No Poverty" ;
 skos:definition "End poverty in all its forms everywhere." ;
 rdfs:comment "Focuses on reducing poverty globally." .


ex:sdg2 a ex:SDG ;
 rdfs:label "SDG 2: Zero Hunger" ;
 skos:definition "End hunger, achieve food security, improved nutrition, and promote sustainable agriculture." .


ex:sdg3 a ex:SDG ;
 rdfs:label "SDG 3: Good Health and Well-being" ;
 skos:definition "Ensure healthy lives and promote well-being for all at all ages." .


ex:sdg4 a ex:SDG ;
 rdfs:label "SDG 4: Quality Education" ;
 skos:definition "Ensure inclusive and equitable quality education and promote lifelong learning opportunities for all." .


ex:sdg5 a ex:SDG ;
 rdfs:label "SDG 5: Gender Equality" ;
 skos:definition "Achieve gender equality and empower all women and girls." .


ex:sdg6 a ex:SDG ;
 rdfs:label "SDG 6: Clean Water and Sanitation" ;
 skos:definition "Ensure availability and sustainable management of water and sanitation for all." ;
 ex:refersToBoundary ex:FreshwaterUseBoundary .


ex:sdg7 a ex:SDG ;
 rdfs:label "SDG 7: Affordable and Clean Energy" ;
 skos:definition "Ensure access to affordable, reliable, sustainable, and modern energy for all." .


ex:sdg8 a ex:SDG ;
 rdfs:label "SDG 8: Decent Work and Economic Growth" ;
 skos:definition "Promote sustained, inclusive, and sustainable economic growth, full and productive employment, and decent work for all." .


ex:sdg9 a ex:SDG ;
 rdfs:label "SDG 9: Industry, Innovation and Infrastructure" ;
 skos:definition "Build resilient infrastructure, promote inclusive and sustainable industrialization, and foster innovation." .


ex:sdg10 a ex:SDG ;
 rdfs:label "SDG 10: Reduced Inequalities" ;
 skos:definition "Reduce inequality within and among countries." .


ex:sdg11 a ex:SDG ;
 rdfs:label "SDG 11: Sustainable Cities and Communities" ;
 skos:definition "Make cities and human settlements inclusive, safe, resilient, and sustainable." .


ex:sdg12 a ex:SDG ;
 rdfs:label "SDG 12: Responsible Consumption and Production" ;
 skos:definition "Ensure sustainable consumption and production patterns." .


ex:sdg13 a ex:SDG ;
 rdfs:label "SDG 13: Climate Action" ;
 skos:definition "Take urgent action to combat climate change and its impacts." ;
 ex:refersToBoundary ex:ClimateChangeBoundary .


ex:sdg14 a ex:SDG ;
 rdfs:label "SDG 14: Life Below Water" ;
 skos:definition "Conserve and sustainably use the oceans, seas, and marine resources for sustainable development." ;
 ex:refersToBoundary ex:OceanAcidificationBoundary .


ex:sdg15 a ex:SDG ;
 rdfs:label "SDG 15: Life on Land" ;
 skos:definition "Protect, restore, and promote sustainable use of terrestrial ecosystems, manage forests sustainably, combat desertification, and halt biodiversity loss." ;
 ex:refersToBoundary ex:TerrestrialBiodiversityBoundary, ex:LandSystemChangeBoundary .


ex:sdg16 a ex:SDG ;
 rdfs:label "SDG 16: Peace, Justice and Strong Institutions" ;
 skos:definition "Promote peaceful and inclusive societies, provide access to justice for all, build effective, accountable, and inclusive institutions at all levels." .


ex:sdg17 a ex:SDG ;
 rdfs:label "SDG 17: Partnerships for the Goals" ;
 skos:definition "Strengthen the means of implementation and revitalize the global partnership for sustainable development." .


################################################################
# MODULE: PLANT-BASED TREATY (COMPLETE CATEGORIES)
################################################################


ex:PlantBasedTreaty a owl:Class ;
 rdfs:label "Plant Based Treaty" ;
 skos:definition "A treaty framework promoting plant-based diets and sustainable agriculture to reduce environmental impacts." ;
 rdfs:comment "Focuses on sustainability in food systems." .


ex:PlantBasedTreatyCategory a owl:Class ;
 rdfs:label "Plant Based Treaty Category" ;
 skos:definition "A category within the Plant Based Treaty addressing specific sustainability or environmental areas." .


ex:climateChangeCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Climate Change" ;
 skos:definition "A category addressing climate change impacts within the treaty framework." ;
 ex:refersToBoundary ex:ClimateChangeBoundary .


ex:oceanAcidificationCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Ocean Acidification" ;
 skos:definition "A category addressing ocean acidification within the treaty." ;
 ex:refersToBoundary ex:OceanAcidificationBoundary .


ex:terrestrialBiodiversityCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Terrestrial Biodiversity" ;
 skos:definition "A category addressing terrestrial biodiversity loss within the treaty." ;
 ex:refersToBoundary ex:TerrestrialBiodiversityBoundary .


ex:freshwaterUseCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Freshwater Use" ;
 skos:definition "A category addressing sustainable freshwater usage within the treaty." ;
 ex:refersToBoundary ex:FreshwaterUseBoundary .


ex:novelEntitiesCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Novel Entities" ;
 skos:definition "A category addressing novel chemicals or plastics within the treaty." ;
 ex:refersToBoundary ex:NovelEntitiesBoundary .


ex:stratosphericOzoneCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Stratospheric Ozone Depletion" ;
 skos:definition "A category addressing depletion of the stratospheric ozone layer within the treaty." ;
 ex:refersToBoundary ex:StratosphericOzoneBoundary .


ex:nitrogenCycleCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Nitrogen Cycle Disruption" ;
 skos:definition "A category addressing excessive nitrogen loading from agriculture within the treaty." ;
 ex:refersToBoundary ex:NitrogenCycleBoundary .


ex:phosphorusCycleCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Phosphorus Cycle Disruption" ;
 skos:definition "A category addressing excessive phosphorus loading from agriculture within the treaty." ;
 ex:refersToBoundary ex:PhosphorusCycleBoundary .


ex:aerosolLoadingCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Atmospheric Aerosol Loadings" ;
 skos:definition "A category addressing aerosol concentrations within the treaty." ;
 ex:refersToBoundary ex:AerosolLoadingBoundary .


ex:landSystemChangeCategory a ex:PlantBasedTreatyCategory ;
 rdfs:label "Land System Change" ;
 skos:definition "A category addressing transformations in land use within the treaty." ;
 ex:refersToBoundary ex:LandSystemChangeBoundary .


ex:plantBasedTreatyInstance a ex:PlantBasedTreaty ;
 rdfs:label "Plant Based Treaty Instance" ;
 skos:definition "An instance referencing all relevant planetary boundary categories under the Plant Based Treaty." ;
 ex:hasCategory 
 ex:climateChangeCategory, ex:oceanAcidificationCategory, ex:terrestrialBiodiversityCategory, ex:freshwaterUseCategory,
 ex:novelEntitiesCategory, ex:stratosphericOzoneCategory, ex:nitrogenCycleCategory, ex:phosphorusCycleCategory,
 ex:aerosolLoadingCategory, ex:landSystemChangeCategory .


################################################################
# MODULE: GHOST FISHING (SPECIALIST)
################################################################


@prefix exgf: <http://example.org/ontology/ghostfishing#> .


exgf:GhostFishingGear a owl:Class ;
 rdfs:label "Ghost Fishing Gear" ;
 skos:definition "Lost or abandoned fishing gear contributing to marine plastic pollution." .


exgf:impactsBiodiversity a owl:ObjectProperty ;
 rdfs:domain exgf:GhostFishingGear ;
 rdfs:range ex:biodiversityLoss ;
 rdfs:label "impacts biodiversity" ;
 skos:definition "Indicates that ghost fishing gear contributes to biodiversity loss by capturing marine organisms." .


exgf:contributesToMarinePlasticPollution a owl:ObjectProperty ;
 rdfs:domain exgf:GhostFishingGear ;
 rdfs:range ex:NovelEntitiesBoundary ;
 rdfs:label "contributes to marine plastic pollution" ;
 skos:definition "Links ghost fishing gear to the Novel Entities Boundary for plastic pollution." .


################################################################
# MODULE: KEYSTONE SPECIES (SPECIALIST)
################################################################


@prefix eco: <http://example.org/ecology#> .
@prefix species: <http://example.org/species#> .


eco:KeystoneSpecies a owl:Class ;
 rdfs:label "Keystone Species" ;
 skos:definition "Species with a disproportionately large effect on ecosystem function relative to their abundance." .


eco:EcologicalProcess a owl:Class ;
 rdfs:label "Ecological Process" ;
 skos:definition "Processes that occur within ecosystems, such as CO2 absorption or biodiversity maintenance." .


eco:affects a owl:ObjectProperty ;
 rdfs:domain eco:KeystoneSpecies ;
 rdfs:range eco:EcologicalProcess ;
 rdfs:label "affects" ;
 skos:definition "Indicates that a keystone species influences a particular ecological process." .


species:BlueWhale a eco:KeystoneSpecies ;
 rdfs:label "Blue Whale" ;
 skos:definition "A keystone species in marine ecosystems, critical for nutrient cycling." ;
 eco:affects eco:CO2Absorption .


species:Wolf a eco:KeystoneSpecies ;
 rdfs:label "Wolf" ;
 skos:definition "A keystone species in terrestrial ecosystems, affecting vegetation and prey populations." ;
 eco:affects eco:Biodiversity .


eco:CO2Absorption a eco:EcologicalProcess ;
 rdfs:label "CO2 Absorption" ;
 skos:definition "The process of removing CO2 from the atmosphere." .


eco:Biodiversity a eco:EcologicalProcess ;
 rdfs:label "Biodiversity Maintenance" ;
 skos:definition "The variety of life in an ecosystem, critical for resilience." .


### Cross-module link: Linking keystone species to planetary boundaries
ex:criticalToBoundary a owl:ObjectProperty ;
 rdfs:label "critical to boundary" ;
 skos:definition "Indicates that an entity is essential to maintaining a specific planetary boundary." ;
 rdfs:comment "Used to explicitly link keystone species or processes to the stability of a planetary boundary." .


species:Wolf ex:criticalToBoundary ex:TerrestrialBiodiversityBoundary .


################################################################
# MODULE: REGENERATIVE AGRICULTURE (SPECIALIST)
################################################################


ex:RegenerativeAgriculture a owl:Class ;
 rdfs:label "Regenerative Agriculture" ;
 skos:definition "Agricultural practices that restore soil health, enhance biodiversity, and sequester carbon." .


ex:hasCarbonSequestrationPotential a owl:DatatypeProperty ;
 rdfs:domain ex:RegenerativeAgriculture ;
 rdfs:range xsd:float ;
 rdfs:label "has carbon sequestration potential" ;
 skos:definition "Quantifies carbon sequestration potential in regenerative agriculture systems." .


ex:regenerativeAgricultureInstance a ex:RegenerativeAgriculture ;
 rdfs:label "Regenerative Agriculture Example" ;
 skos:definition "An instance of regenerative agriculture." ;
 ex:hasCarbonSequestrationPotential "0.5"^^xsd:float .


### Cross-module link: Regenerative Agriculture reducing pressure on boundaries
ex:supportsBoundary a owl:ObjectProperty ;
 rdfs:label "supports boundary" ;
 skos:definition "Indicates that a sustainability practice helps keep a planetary boundary within safe limits." ;
 rdfs:domain ex:RegenerativeAgriculture ;
 rdfs:range ex:PlanetaryBoundary ;
 rdfs:comment "Used to model how regenerative agriculture can reduce pressure on boundaries such as Land System Change, Nitrogen Cycle, and Phosphorus Cycle." .


ex:regenerativeAgricultureInstance ex:supportsBoundary 
 ex:LandSystemChangeBoundary, ex:NitrogenCycleBoundary, ex:PhosphorusCycleBoundary .


################################################################
# MODULE: LABOUR EXPLOITATION & MODERN SLAVERY (SPECIALIST)
################################################################


ex:LabourExploitation a owl:Class ;
 rdfs:label "Labour Exploitation" ;
 skos:definition "Exploitative labor practices, including forced labor and human trafficking." .


ex:modernSlavery a owl:Class ;
 rdfs:subClassOf ex:LabourExploitation ;
 rdfs:label "Modern Slavery" ;
 skos:definition "Forced labor and human trafficking within supply chains." .


ex:involvesModernSlavery a owl:ObjectProperty ;
 rdfs:domain ex:CommodityProduction ;
 rdfs:range ex:modernSlavery ;
 rdfs:label "involves modern slavery" ;
 skos:definition "Indicates that a commodity production process involves modern slavery." .


ex:drivenByInequity a owl:ObjectProperty ;
 rdfs:domain ex:modernSlavery ;
 rdfs:range ex:socioEconomicInequity ;
 rdfs:label "driven by inequity" ;
 skos:definition "Indicates that modern slavery is driven by underlying socio-economic inequities." .


ex:isAddressedByPolicy a owl:ObjectProperty ;
 rdfs:label "is addressed by policy" ;
 skos:definition "Indicates that modern slavery practices are addressed by a policy instrument." ;
 rdfs:domain ex:modernSlavery ;
 rdfs:range ex:PolicyInstrument .


ex:telecouplesModernSlavery a owl:ObjectProperty ;
 rdfs:label "telecouples modern slavery" ;
 skos:definition "Links global supply chain telecoupling to modern slavery." ;
 rdfs:domain ex:telecoupling ;
 rdfs:range ex:modernSlavery .


################################################################
# MODULE: SOCIO-ENVIRONMENTAL DRIVERS OF BIODIVERSITY LOSS (SPECIALIST)
################################################################


ex:UnsustainableConsumption a owl:Class ;
 rdfs:label "Unsustainable Consumption" ;
 skos:definition "Consumption patterns that exceed ecological limits." .


ex:CommodityProduction a owl:Class ;
 rdfs:label "Commodity Production" ;
 skos:definition "Production of commodities, often linked to export-driven agriculture." .


ex:soyProduction a owl:Class ;
 rdfs:subClassOf ex:CommodityProduction ;
 rdfs:label "Soy Production" ;
 skos:definition "Production of soybeans, often associated with deforestation." .


ex:palmOilProduction a owl:Class ;
 rdfs:subClassOf ex:CommodityProduction ;
 rdfs:label "Palm Oil Production" ;
 skos:definition "Production of palm oil, often linked to deforestation and biodiversity loss." .


ex:landGrabbing a owl:Class ;
 rdfs:label "Land Grabbing" ;
 skos:definition "Acquisition of land without proper consent, often leading to habitat destruction." .


ex:telecoupling a owl:Class ;
 rdfs:label "Telecoupling" ;
 skos:definition "Environmental or socio-economic interactions between distant regions (production-consumption linkages)." .


ex:socioEconomicInequity a owl:Class ;
 rdfs:label "Socio-Economic Inequity" ;
 skos:definition "Disparities in socio-economic status that drive unsustainable practices." .


ex:biodiversityLoss a owl:Class ;
 rdfs:label "Biodiversity Loss" ;
 skos:definition "The decline in biological diversity at genetic, species, and ecosystem levels." .


ex:habitatLoss a owl:Class ;
 rdfs:label "Habitat Loss" ;
 skos:definition "The degradation or destruction of natural habitats." .


ex:drivesDeforestation a owl:ObjectProperty ;
 rdfs:label "drives deforestation" ;
 skos:definition "Commodity production leads to deforestation and habitat loss." ;
 rdfs:domain ex:CommodityProduction ;
 rdfs:range ex:habitatLoss .


ex:contributesToHabitatDegradation a owl:ObjectProperty ;
 rdfs:label "contributes to habitat degradation" ;
 skos:definition "Land grabbing contributes to the degradation of natural habitats." ;
 rdfs:domain ex:landGrabbing ;
 rdfs:range ex:habitatLoss .


ex:exacerbatesInequity a owl:ObjectProperty ;
 rdfs:label "exacerbates inequity" ;
 skos:definition "Unsustainable consumption worsens socio-economic disparities." ;
 rdfs:domain ex:UnsustainableConsumption ;
 rdfs:range ex:socioEconomicInequity .


ex:telecouplesImpacts a owl:ObjectProperty ;
 rdfs:label "telecouples impacts" ;
 skos:definition "Commodity production links distant regions via telecoupling, causing cross-border impacts." ;
 rdfs:domain ex:CommodityProduction ;
 rdfs:range ex:telecoupling .


ex:affectedByPopulationPressure a owl:ObjectProperty ;
 rdfs:label "affected by population pressure" ;
 skos:definition "Habitat loss can be influenced by human population pressure." ;
 rdfs:domain ex:habitatLoss ;
 rdfs:range ex:HumanPopulation .


# Drivers carried over from MODULE Y
ex:AdvertisingPressure rdfs:subClassOf ex:UnsustainableConsumption .




################################################################
# MODULE: OCEAN CHEMISTRY & PLANKTON HEALTH (SPECIALIST)
################################################################


ex:OceanChemistryIndicator a owl:Class ;
 rdfs:label "Ocean Chemistry Indicator" ;
 skos:definition "Measures aspects of ocean chemistry (pH, alkalinity, etc.)." .


ex:PlanktonHealth a owl:Class ;
 rdfs:label "Plankton Health" ;
 skos:definition "Indicator of the health of marine plankton populations." .


ex:hasPHLevel a owl:DatatypeProperty ;
 rdfs:domain ex:OceanChemistryIndicator ;
 rdfs:range xsd:float ;
 rdfs:label "has pH level" ;
 skos:definition "Associates a measurement with an ocean's pH value." .


ex:contributesToOceanAcidification a owl:ObjectProperty ;
 rdfs:label "contributes to ocean acidification" ;
 skos:definition "Indicates that certain chemical pollutants contribute to ocean acidification." ;
 rdfs:domain ex:novelEntitiesSegment ;
 rdfs:range ex:oceanAcidificationSegment .


ex:inhibitsPlanktonHealth a owl:ObjectProperty ;
 rdfs:label "inhibits plankton health" ;
 skos:definition "Indicates that pollutants or chemicals negatively affect plankton health." ;
 rdfs:domain ex:novelEntitiesSegment ;
 rdfs:range ex:PlanktonHealth .


ex:affectsOxygenProduction a owl:ObjectProperty ;
 rdfs:label "affects oxygen production" ;
 skos:definition "Indicates that changes in ocean chemistry affect plankton’s oxygen production capacity." ;
 rdfs:domain ex:OceanChemistryIndicator ;
 rdfs:range ex:PlanktonHealth .


ex:oceanChemistryInstance a ex:OceanChemistryIndicator ;
 rdfs:label "Ocean Chemistry Measurement" ;
 skos:definition "An example measurement (e.g., pH) taken in the year 2020." ;
 ex:hasPHLevel "8.04"^^xsd:float ;
 ex:hasYear "2020"^^xsd:gYear .


ex:planktonHealthInstance a ex:PlanktonHealth ;
 rdfs:label "Plankton Health Indicator" ;
 skos:definition "An instance representing the health of marine plankton." .






################################################################
# MODULE: SUSTAINABILITY INITIATIVES
################################################################


ex:SustainabilityInitiative a owl:Class ;
 rdfs:label "Sustainability Initiative" ;
 skos:definition "An abstract class for strategies or initiatives aimed at mitigating environmental impacts." ;
 rdfs:comment "All concrete mitigation solutions (e.g., plant‑based diets, regenerative agriculture, rewilding) are subclasses of this class." .


### Quantitative Metrics for Initiatives
exqm:hasLandUseEfficiency a owl:DatatypeProperty ;
 rdfs:domain ex:SustainabilityInitiative ;
 rdfs:range xsd:float ;
 rdfs:label "has land use efficiency" ;
 skos:definition "Quantifies the ratio of output to land use for an initiative." .


exqm:hasCarbonSequestrationPotential a owl:DatatypeProperty ;
 rdfs:domain ex:SustainabilityInitiative ;
 rdfs:range xsd:float ;
 rdfs:label "has carbon sequestration potential" ;
 skos:definition "Quantifies the potential for carbon sequestration of an initiative." .


exqm:hasBiodiversityImpact a owl:DatatypeProperty ;
 rdfs:domain ex:SustainabilityInitiative ;
 rdfs:range xsd:float ;
 rdfs:label "has biodiversity impact" ;
 skos:definition "Quantifies the net impact (positive or negative) on biodiversity." .


### Standardized Cross-Module Properties
ex:mitigatesImpactOn a owl:ObjectProperty ;
 rdfs:label "mitigates impact on" ;
 skos:definition "Links a sustainability initiative to the planetary boundary whose impact it helps reduce." ;
 rdfs:domain ex:SustainabilityInitiative ;
 rdfs:range ex:PlanetaryBoundary ;
 rdfs:comment "This property standardizes how solution initiatives are linked to environmental limits." .


ex:reducesGhostFishingImpact a owl:ObjectProperty ;
 rdfs:label "reduces ghost fishing impact" ;
 skos:definition "Indicates that adoption of a given strategy reduces the incidence or impact of ghost fishing gear, thereby lowering marine plastic pollution." ;
 rdfs:domain ex:SustainabilityInitiative ;
 rdfs:range exgf:GhostFishingGear ;
 rdfs:comment "For example, a plant‑based diet reduces seafood demand, leading to fewer ghost nets lost at sea and less microplastic generation." .


exqm:isPreferredOver a owl:ObjectProperty ;
 rdfs:domain ex:SustainabilityInitiative ;
 rdfs:range ex:SustainabilityInitiative ;
 rdfs:label "is preferred over" ;
 skos:definition "Indicates that one sustainability initiative is preferred over another based on specific criteria." .


### Concrete Mitigation Initiatives


# Plant-Based Diet
exqm:PlantBasedDiet a owl:Class ;
 rdfs:subClassOf ex:SustainabilityInitiative ;
 rdfs:label "Plant-Based Diet" ;
 skos:definition "A diet centered on plant-based foods with minimal or no animal products. This diet reduces greenhouse gas emissions, lowers land use and water consumption, and importantly, reduces pressure on marine ecosystems by decreasing seafood demand—thereby mitigating ghost fishing and reducing plastic pollution." ;
 exqm:hasLandUseEfficiency "0.8"^^xsd:float ;
 exqm:hasCarbonSequestrationPotential "0.7"^^xsd:float ;
 exqm:hasBiodiversityImpact "0.6"^^xsd:float ;
 rdfs:comment "By reducing the need for intensive animal agriculture and seafood production, a plant-based diet benefits both terrestrial and marine ecosystems." .


# Regenerative Grazing
exqm:RegenerativeGrazing a owl:Class ;
 rdfs:subClassOf ex:SustainabilityInitiative , ex:CommodityProduction ;
 rdfs:label "Regenerative Grazing" ;
 skos:definition "A livestock system aiming to regenerate soil and sequester carbon, with specific performance constraints." ;
 rdfs:comment "Includes restrictions on land use efficiency, carbon sequestration potential, and biodiversity impact." ;
 rdfs:subClassOf [
 a owl:Restriction ;
 owl:onProperty exqm:hasLandUseEfficiency ;
 owl:allValuesFrom [ a rdfs:Datatype ; owl:onDatatype xsd:float ; owl:withRestrictions ( [ xsd:maxExclusive \"0.5\"^^xsd:float ] ) ]
 ] ,
 [
 a owl:Restriction ;
 owl:onProperty exqm:hasCarbonSequestrationPotential ;
 owl:allValuesFrom [ a rdfs:Datatype ; owl:onDatatype xsd:float ; owl:withRestrictions ( [ xsd:maxExclusive \"0.3\"^^xsd:float ] ) ]
 ] ,
 [
 a owl:Restriction ;
 owl:onProperty exqm:hasBiodiversityImpact ;
 owl:allValuesFrom [ a rdfs:Datatype ; owl:onDatatype xsd:float ; owl:withRestrictions ( [ xsd:maxExclusive \"0.4\"^^xsd:float ] ) ]
 ] .


# Rewilding
exqm:Rewilding a owl:Class ;
 rdfs:subClassOf ex:SustainabilityInitiative ;
 rdfs:label "Rewilding" ;
 skos:definition "The process of restoring natural ecosystems by reintroducing native species and processes." ;
 exqm:hasLandUseEfficiency "0.9"^^xsd:float ;
 exqm:hasCarbonSequestrationPotential "0.9"^^xsd:float ;
 exqm:hasBiodiversityImpact "0.9"^^xsd:float .


### Explicit Cross-Module Assertions for Plant-Based Diet
exqm:PlantBasedDiet ex:mitigatesImpactOn ex:NovelEntitiesBoundary ;
exqm:PlantBasedDiet ex:reducesGhostFishingImpact exgf:GhostFishingGear .


### Prioritization Assertions
exqm:PlantBasedDiet exqm:isPreferredOver exqm:RegenerativeGrazing ;
exqm:Rewilding exqm:isPreferredOver exqm:RegenerativeGrazing .






################################################################
# MODULE: REASONING RULES (SWRL-like Axioms) & CONSTRAINTS
################################################################


[ rdf:type owl:Axiom ;
 owl:annotatedSource exqm:RegenerativeGrazing ;
 owl:annotatedProperty rdfs:comment ;
 owl:annotatedTarget "Regenerative grazing cannot scale to meet global food demand due to high land use requirements." ] .


[ rdf:type owl:Axiom ;
 owl:annotatedSource exqm:RegenerativeGrazing ;
 owl:annotatedProperty rdfs:comment ;
 owl:annotatedTarget "Regenerative grazing cannot fully restore biodiversity compared to rewilding." ] .


[ rdf:type owl:Axiom ;
 owl:annotatedSource exqm:RegenerativeGrazing ;
 owl:annotatedProperty rdfs:comment ;
 owl:annotatedTarget "Regenerative grazing cannot fully mitigate climate change due to methane emissions." ] .


[ rdf:type owl:Axiom ;
 owl:annotatedSource exqm:PlantBasedDiet ;
 owl:annotatedProperty rdfs:comment ;
 owl:annotatedTarget "A plant-based diet is more scalable and sustainable than regenerative grazing." ] .


[ rdf:type owl:Axiom ;
 owl:annotatedSource exqm:Rewilding ;
 owl:annotatedProperty rdfs:comment ;
 owl:annotatedTarget "Rewilding is more effective than regenerative grazing for biodiversity and carbon sequestration." ] .


################################################################
# MODULE: POLICY INSTRUMENTS (SPECIALIST)
################################################################


ex:PolicyInstrument a owl:Class ;
 rdfs:label "Policy Instrument" ;
 skos:definition "A regulatory or policy tool designed to promote sustainability." .


ex:policyInstrumentExample a ex:PolicyInstrument ;
 rdfs:label "Modern Slavery Prevention Act" ;
 skos:definition "A policy instrument aimed at preventing modern slavery in supply chains." .


# ---- Sufficiency & Degrowth instruments ------------------------
ex:SufficiencyPolicy a owl:Class ; rdfs:subClassOf ex:PolicyInstrument ;
 rdfs:label "Sufficiency Policy" ;
 skos:definition "A policy that sets ceilings on energy/material throughput rather than mere efficiency targets." .




################################################################
# MODULE: ECONOMIC & DEMOGRAPHIC DATA (SPECIALIST)
################################################################


ex:EconomicGrowth a owl:Class ;
 rdfs:label "Economic Growth" ;
 skos:definition "A measurement of economic growth (e.g., GDP growth rate)." .


ex:HumanPopulation a owl:Class ;
 rdfs:label "Human Population" ;
 skos:definition "A measurement of human population levels." .


ex:economicGrowthUSA a ex:EconomicGrowth ;
 rdfs:label "USA Economic Growth Measure" ;
 skos:definition "An indicator of the GDP growth rate for the USA." ;
 ex:hasValue "2.3"^^xsd:float ;
 ex:hasYear "2022"^^xsd:gYear .


ex:humanPopulationUSA a ex:HumanPopulation ;
 rdfs:label "USA Population" ;
 skos:definition "The human population of the USA (in millions)." ;
 ex:hasValue "331"^^xsd:float ;
 ex:hasYear "2022"^^xsd:gYear .


ex:economyHasDecouplingStatus a owl:ObjectProperty ;
 rdfs:domain ex:EconomicGrowth ; rdfs:range ex:Decoupling ;
 rdfs:label "has decoupling status" .


################################################################
# MODULE: CULTURAL & ECONOMIC PARADIGMS
################################################################


ex:CulturalOrEconomicParadigm a owl:Class ;
 rdfs:label "Cultural or Economic Paradigm" ;
 skos:definition "A broad system of beliefs, practices, or norms shaping how society organizes production, consumption, and values." ;
 rdfs:comment "Groups various cultural or economic models that impact planetary boundaries." .


ex:InfiniteGrowthModel a owl:Class ;
 rdfs:subClassOf ex:CulturalOrEconomicParadigm ;
 rdfs:label "Infinite Growth Model" ;
 skos:definition "An economic paradigm predicated on perpetual GDP expansion, ignoring biophysical limits." ;
 rdfs:comment "Often leads to overshoot of multiple planetary boundaries." .


ex:ConsumeristCulture a owl:Class ;
 rdfs:subClassOf ex:CulturalOrEconomicParadigm ;
 rdfs:label "Consumerist Culture" ;
 skos:definition "A cultural norm that emphasizes high material consumption and accumulation as markers of success." ;
 rdfs:comment "Drives unsustainable consumption patterns, risking boundary exceedance." .


ex:ShortTermProfitCycle a owl:Class ;
 rdfs:subClassOf ex:CulturalOrEconomicParadigm ;
 rdfs:label "Short-Term Profit Cycle" ;
 skos:definition "A system focused on immediate returns, often at the expense of long-term sustainability." ;
 rdfs:comment "May ignore ecological consequences due to near-term pressures." .


ex:FossilFuelDependentEconomy a owl:Class ;
 rdfs:subClassOf ex:CulturalOrEconomicParadigm ;
 rdfs:label "Fossil Fuel Dependent Economy" ;
 skos:definition "An economic system heavily reliant on fossil fuels for energy and growth." ;
 rdfs:comment "Leads to sustained high emissions, risking climate boundary exceedance." .


ex:drivesBoundaryExceedance a owl:ObjectProperty ;
 rdfs:domain ex:CulturalOrEconomicParadigm ;
 rdfs:range ex:PlanetaryBoundary ;
 rdfs:label "drives boundary exceedance" ;
 skos:definition "Indicates that a particular cultural or economic paradigm leads to exceeding a planetary boundary." ;
 rdfs:comment "Explicitly links unsustainable paradigms to boundary overshoot." .


ex:infiniteGrowthModelExample a ex:InfiniteGrowthModel ;
 rdfs:label "Infinite Growth Model Example" ;
 skos:definition "An instance of an economy based on limitless resource extraction and GDP growth." ;
 ex:drivesBoundaryExceedance ex:ClimateChangeBoundary, ex:LandSystemChangeBoundary, ex:NovelEntitiesBoundary, ex:NitrogenCycleBoundary, ex:PhosphorusCycleBoundary .


ex:consumeristCultureExample a ex:ConsumeristCulture ;
 rdfs:label "Consumerist Culture Example" ;
 skos:definition "An instance illustrating a culture of continuous material acquisition." ;
 ex:drivesBoundaryExceedance ex:ClimateChangeBoundary, ex:TerrestrialBiodiversityBoundary .


ex:shortTermProfitCycleExample a ex:ShortTermProfitCycle ;
 rdfs:label "Short-Term Profit Cycle Example" ;
 skos:definition "An instance where the focus on immediate returns undermines long-term sustainability." ;
 ex:drivesBoundaryExceedance ex:ClimateChangeBoundary, ex:OceanAcidificationBoundary .


ex:fossilFuelEconomyExample a ex:FossilFuelDependentEconomy ;
 rdfs:label "Fossil Fuel Dependent Economy Example" ;
 skos:definition "An instance of an economy heavily reliant on fossil fuels, causing high greenhouse gas emissions." ;
 ex:drivesBoundaryExceedance ex:ClimateChangeBoundary, ex:AerosolLoadingBoundary .


# --- Additional paradigms from MODULE Y (cross-link) ------------
ex:DegrowthParadigm ex:drivesBoundaryExceedance ex:ClimateChangeBoundary ;
 ex:externalises exvio:Ecocide .


ex:WellbeingEconomy ex:externalises exvio:Greenwashing .


ex:SuperOrganismEconomy ex:drivesBoundaryExceedance ex:ClimateChangeBoundary ;
 ex:externalises exvio:Ecocide .




################################################################
# MODULE X: DRAWDOWN SUSTAINABILITY SOLUTIONS (FULL)
################################################################


# General class for all Drawdown solutions
ex:DrawdownStrategy a owl:Class ;
 rdfs:label "Drawdown Strategy" ;
 skos:definition "A mitigation solution defined in the Drawdown project, targeting climate change through various interventions." ;
 rdfs:comment "Includes solutions from electricity generation, food & agriculture, transport, buildings, industry, waste, and other sectors." .


# Define subclasses for solution categories


ex:ElectricitySolution a owl:Class ;
 rdfs:subClassOf ex:DrawdownStrategy ;
 rdfs:label "Electricity Solution" ;
 skos:definition "A solution focused on clean and renewable electricity generation." .


ex:FoodAndAgricultureSolution a owl:Class ;
 rdfs:subClassOf ex:DrawdownStrategy ;
 rdfs:label "Food and Agriculture Solution" ;
 skos:definition "A solution focused on reducing emissions from food systems, agriculture, and land use." .


ex:TransportSolution a owl:Class ;
 rdfs:subClassOf ex:DrawdownStrategy ;
 rdfs:label "Transport Solution" ;
 skos:definition "A solution aimed at reducing emissions from the transportation sector." .


ex:BuildingSolution a owl:Class ;
 rdfs:subClassOf ex:DrawdownStrategy ;
 rdfs:label "Building Solution" ;
 skos:definition "A solution targeting energy efficiency and emissions reductions in buildings." .


ex:IndustrySolution a owl:Class ;
 rdfs:subClassOf ex:DrawdownStrategy ;
 rdfs:label "Industry Solution" ;
 skos:definition "A solution aimed at improving industrial processes and reducing materials‐related emissions." .


ex:WasteSolution a owl:Class ;
 rdfs:subClassOf ex:DrawdownStrategy ;
 rdfs:label "Waste Solution" ;
 skos:definition "A solution that reduces emissions through improved waste management, recycling, and composting." .


ex:OtherSolution a owl:Class ;
 rdfs:subClassOf ex:DrawdownStrategy ;
 rdfs:label "Other Solution" ;
 skos:definition "Additional Drawdown solutions that do not neatly fall into the above categories (e.g., social interventions)." .


################################################################
# Instances of Electricity Solutions
################################################################


ex:OnshoreWind a ex:ElectricitySolution ;
 rdfs:label "Onshore Wind" ;
 skos:definition "Installation of wind turbines on land to generate renewable electricity." .


ex:OffshoreWind a ex:ElectricitySolution ;
 rdfs:label "Offshore Wind" ;
 skos:definition "Installation of wind turbines in ocean waters to harness stronger and more consistent winds for power generation." .


ex:UtilityScaleSolarPV a ex:ElectricitySolution ;
 rdfs:label "Utility-Scale Solar PV" ;
 skos:definition "Large-scale photovoltaic solar installations that convert sunlight into electricity." .


ex:RooftopSolarPV a ex:ElectricitySolution ;
 rdfs:label "Rooftop Solar PV" ;
 skos:definition "Installation of solar panels on rooftops to generate distributed renewable electricity." .


ex:ConcentratedSolarPower a ex:ElectricitySolution ;
 rdfs:label "Concentrated Solar Power (CSP)" ;
 skos:definition "Systems that concentrate sunlight to generate high-temperature heat for power generation." .


ex:GeothermalEnergy a ex:ElectricitySolution ;
 rdfs:label "Geothermal Energy" ;
 skos:definition "Utilizing the Earth’s natural heat to produce electricity." .


ex:SmallScaleHydropower a ex:ElectricitySolution ;
 rdfs:label "Small-Scale Hydropower" ;
 skos:definition "Local hydropower installations that generate renewable electricity without large-scale damming." .


ex:MarineEnergy a ex:ElectricitySolution ;
 rdfs:label "Marine Energy" ;
 skos:definition "Harnessing tidal, wave, or ocean current energy to produce electricity." .


################################################################
# Instances of Food and Agriculture Solutions
################################################################


ex:PlantRichDietStrategy a ex:FoodAndAgricultureSolution ;
 rdfs:label "Plant-Rich Diet" ;
 skos:definition "Adoption of predominantly plant-based diets to reduce greenhouse gas emissions from food production." .


ex:ReducedFoodWaste a ex:FoodAndAgricultureSolution ;
 rdfs:label "Reduced Food Waste" ;
 skos:definition "Implementing practices to minimize food waste throughout the supply chain." .


ex:RegenerativeAgricultureStrategy a ex:FoodAndAgricultureSolution ;
 rdfs:label "Regenerative Agriculture" ;
 skos:definition "Adopting agricultural practices that restore soil health, increase biodiversity, and sequester carbon." .


ex:Silvopasture a ex:FoodAndAgricultureSolution ;
 rdfs:label "Silvopasture" ;
 skos:definition "Integrating trees into pasture systems to improve animal welfare and ecosystem services." .


ex:ImprovedRiceCultivation a ex:FoodAndAgricultureSolution ;
 rdfs:label "Improved Rice Cultivation" ;
 skos:definition "Employing techniques such as alternate wetting and drying to reduce methane emissions in rice farming." .


ex:BiocharProduction a ex:FoodAndAgricultureSolution ;
 rdfs:label "Biochar Production" ;
 skos:definition "Producing biochar from biomass to enhance soil fertility and sequester carbon." .


ex:ManureManagement a ex:FoodAndAgricultureSolution ;
 rdfs:label "Manure Management" ;
 skos:definition "Optimizing the treatment and use of animal manure to reduce emissions and produce biogas." .


ex:LivestockFeedAdditives a ex:FoodAndAgricultureSolution ;
 rdfs:label "Livestock Feed Additives" ;
 skos:definition "Using additives in animal feed to lower methane emissions from enteric fermentation." .


ex:Afforestation a ex:FoodAndAgricultureSolution ;
 rdfs:label "Afforestation" ;
 skos:definition "Planting trees in areas that have not been recently forested to increase carbon sequestration." .


ex:Reforestation a ex:FoodAndAgricultureSolution ;
 rdfs:label "Reforestation" ;
 skos:definition "Restoring forests on previously deforested land to capture carbon and restore ecosystems." .


ex:AvoidedDeforestation a ex:FoodAndAgricultureSolution ;
 rdfs:label "Avoided Deforestation" ;
 skos:definition "Protecting existing forests from deforestation to prevent carbon emissions." .


ex:Agroforestry a ex:FoodAndAgricultureSolution ;
 rdfs:label "Agroforestry" ;
 skos:definition "Integrating trees and shrubs into agricultural landscapes to improve productivity and carbon storage." .


ex:CoverCrops a ex:FoodAndAgricultureSolution ;
 rdfs:label "Cover Crops" ;
 skos:definition "Using cover crops to improve soil structure, reduce erosion, and sequester carbon." .


ex:ReducedFertilizerUse a ex:FoodAndAgricultureSolution ;
 rdfs:label "Reduced Fertilizer Use" ;
 skos:definition "Adopting practices that lower the use of synthetic fertilizers to cut nitrous oxide emissions." .


ex:SoilCarbonSequestration a ex:FoodAndAgricultureSolution ;
 rdfs:label "Soil Carbon Sequestration" ;
 skos:definition "Improving soil management techniques to capture and store atmospheric carbon in soils." .


################################################################
# Instances of Transport Solutions
################################################################


ex:ElectricVehicles a ex:TransportSolution ;
 rdfs:label "Electric Vehicles" ;
 skos:definition "Transitioning from internal combustion engines to electric vehicles to reduce transportation emissions." .


ex:PublicTransitExpansion a ex:TransportSolution ;
 rdfs:label "Public Transit Expansion" ;
 skos:definition "Enhancing and expanding public transportation networks to lower per-capita emissions." .


ex:HighSpeedRail a ex:TransportSolution ;
 rdfs:label "High-Speed Rail" ;
 skos:definition "Investing in high-speed rail systems as a low-emission alternative to air and road travel." .


ex:BicycleInfrastructure a ex:TransportSolution ;
 rdfs:label "Bicycle Infrastructure" ;
 skos:definition "Developing dedicated cycling lanes and bike-sharing programs to promote sustainable mobility." .


ex:RideSharing a ex:TransportSolution ;
 rdfs:label "Ride-Sharing" ;
 skos:definition "Encouraging ride-sharing and carpooling to reduce the number of vehicles on the road." .


################################################################
# Instances of Building Solutions
################################################################


ex:BuildingRetrofitting a ex:BuildingSolution ;
 rdfs:label "Building Retrofitting" ;
 skos:definition "Upgrading existing buildings to improve energy efficiency and reduce energy consumption." .


ex:LEDLighting a ex:BuildingSolution ;
 rdfs:label "LED Lighting" ;
 skos:definition "Replacing conventional lighting with energy-efficient LED technology." .


ex:ImprovedInsulation a ex:BuildingSolution ;
 rdfs:label "Improved Insulation" ;
 skos:definition "Enhancing building envelopes to reduce heating and cooling energy requirements." .


ex:DistrictHeatingCooling a ex:BuildingSolution ;
 rdfs:label "District Heating and Cooling" ;
 skos:definition "Implementing centralized systems for heating and cooling to improve overall energy efficiency." .


################################################################
# Instances of Industry Solutions
################################################################


ex:IndustrialProcessImprovements a ex:IndustrySolution ;
 rdfs:label "Industrial Process Improvements" ;
 skos:definition "Optimizing industrial operations to reduce energy use and emissions." .


ex:MaterialSubstitution a ex:IndustrySolution ;
 rdfs:label "Material Substitution" ;
 skos:definition "Replacing high-emission materials (e.g., cement, steel) with alternatives that have a lower carbon footprint." .


ex:RecyclingAndCircularEconomy a ex:IndustrySolution ;
 rdfs:label "Recycling and Circular Economy" ;
 skos:definition "Implementing practices that promote recycling, reuse, and reduced material extraction." .


ex:CarbonCaptureAndStorage a ex:IndustrySolution ;
 rdfs:label "Carbon Capture and Storage (CCS)" ;
 skos:definition "Capturing carbon dioxide emissions from industrial processes and storing them underground." .


################################################################
# Instances of Waste Solutions
################################################################


ex:WasteManagementImprovement a ex:WasteSolution ;
 rdfs:label "Waste Management Improvement" ;
 skos:definition "Enhancing waste management (e.g., recycling, composting, landfill gas capture) to reduce emissions." .


ex:Composting a ex:WasteSolution ;
 rdfs:label "Composting" ;
 skos:definition "Transforming organic waste into compost to lower methane emissions and improve soil quality." .


################################################################
# Instances of Other Solutions
################################################################


ex:EducatingGirls a ex:OtherSolution ;
 rdfs:label "Educating Girls" ;
 skos:definition "Investing in girls’ education to empower communities and drive social change that supports lower emissions." .


ex:FamilyPlanning a ex:OtherSolution ;
 rdfs:label "Family Planning" ;
 skos:definition "Providing reproductive health services to help stabilize population growth, indirectly reducing emissions." .


ex:WaterEfficiency a ex:OtherSolution ;
 rdfs:label "Water Efficiency" ;
 skos:definition "Improving water use efficiency to reduce the energy required for water treatment and distribution." .


################################################################
# END OF MODULE X: DRAWDOWN SUSTAINABILITY SOLUTIONS
################################################################




################################################################
# MODULE A – COLONIAL-POWER
################################################################


excol:ColonialEmpire a owl:Class ; rdfs:label "Colonial Empire" .
excol:SettlerColony a owl:Class ; rdfs:subClassOf excol:ColonialEmpire .
excol:CorporateActor a owl:Class ; rdfs:label "Corporate Actor" .
excol:DoctrineOfDiscovery a owl:Class ; rdfs:label "Doctrine of Discovery" ; rdfs:subClassOf excol:LegalDoctrine .
excol:LegalDoctrine a owl:Class ; rdfs:label "Legal Doctrine" .
excol:FortressConservation rdfs:subClassOf exvio:DevelopmentAggression .
# excol:FortressConservation a owl:Class ; rdfs:label "Fortress Conservation Scheme" .


# power relations -------------------------------------------------
excol:assertsDominionOver a owl:ObjectProperty ;
 rdfs:domain excol:LegalDoctrine ; rdfs:range exind:IndigenousTerritory ;
 rdfs:label "asserts dominion over" .


excol:extractsFrom a owl:ObjectProperty ;
 rdfs:domain excol:ColonialEmpire , excol:CorporateActor ;
 rdfs:range exind:IndigenousTerritory ; rdfs:label "extracts resources from" .


excol:greenwashesWith a owl:ObjectProperty ;
 rdfs:domain excol:CorporateActor ; rdfs:range ex:SustainabilityInitiative ;
 rdfs:label "green-washes with" .


################################################################
# MODULE B – COLONIAL VIOLENCE
################################################################


exvio:ColonialViolence a owl:Class ; rdfs:label "Colonial Violence" .
exvio:Ecocide a owl:Class ; rdfs:subClassOf exvio:ColonialViolence .
exvio:Ethnocide a owl:Class ; rdfs:subClassOf exvio:ColonialViolence .
exvio:Epistemicide a owl:Class ; rdfs:subClassOf exvio:ColonialViolence .
exvio:DevelopmentAggression a owl:Class ; rdfs:subClassOf exvio:ColonialViolence .
exvio:Greenwashing a owl:Class ; rdfs:subClassOf exvio:ColonialViolence .
exvio:WhiteSupremacy a owl:Class ; rdfs:label "White-Supremacist System" .


# violence → boundaries
exvio:Ecocide ex:causallyInfluences ex:TerrestrialBiodiversityBoundary ,
 ex:LandSystemChangeBoundary ,
 ex:ClimateChangeBoundary .


exvio:provokesDevelopmentAggression a owl:ObjectProperty ;
 rdfs:label "provokes development aggression" ;
 rdfs:domain excol:CorporateActor ;
 rdfs:range exvio:DevelopmentAggression ;
 skos:definition "Links a corporate actor to acts of development aggression it provokes." .






################################################################
# PROPERTY THAT LINKS PARADIGMS TO VIOLENCE (declared here)
################################################################
ex:externalises a owl:ObjectProperty ;
 rdfs:label "externalises (violence)" ;
 rdfs:domain ex:CulturalOrEconomicParadigm ;
 rdfs:range exvio:ColonialViolence .


ex:InfiniteGrowthModel ex:externalises exvio:Ecocide ;
 ex:drivesBoundaryExceedance ex:ClimateChangeBoundary .






################################################################
# MODULE C – INDIGENOUS & TRADITIONAL SUSTAINABILITIES
################################################################


exind:IndigenousCommunity a owl:Class ; rdfs:label "Indigenous Community" .
exind:IndigenousTerritory a owl:Class ; rdfs:label "Indigenous Territory" .
exind:TraditionalSustainability a owl:Class ; rdfs:label "Traditional Sustainability Practice" .
exind:AmazonTerritory a exind:IndigenousTerritory ;
 rdfs:label "Amazonia (generic demo)" .


exind:practicesCustodianshipOf a owl:ObjectProperty ;
 rdfs:domain exind:IndigenousCommunity ; rdfs:range exind:IndigenousTerritory ;
 rdfs:label "practices custodianship of" .


exind:safeguardsBoundary a owl:ObjectProperty ;
 rdfs:domain exind:TraditionalSustainability ; rdfs:range ex:PlanetaryBoundary ;
 rdfs:label "safeguards boundary" .


# Example instance – the Bishnoi eco-religion
exind:BishnoiCommunity a exind:IndigenousCommunity ; rdfs:label "Bishnoi Community" .
exind:BishnoiEcoReligion a exind:TraditionalSustainability ;
 rdfs:label "Bishnoi Eco-religion" ;
 exind:safeguardsBoundary ex:TerrestrialBiodiversityBoundary ,
 ex:LandSystemChangeBoundary .


################################################################
# MODULE D – EPISTEMIC ORDERS
################################################################


exepi:EpistemicOrder a owl:Class ; rdfs:label "Epistemic Order" .
exepi:WesternScientific a owl:Class ; rdfs:subClassOf exepi:EpistemicOrder ; rdfs:label "Western Scientific Epistemology" .
exepi:IndigenousKnowledge a owl:Class ; rdfs:subClassOf exepi:EpistemicOrder ; rdfs:label "Indigenous Knowledge System" .


exepi:suppresses a owl:ObjectProperty ;
 rdfs:domain exepi:WesternScientific ; rdfs:range exepi:IndigenousKnowledge ;
 rdfs:label "suppresses" .




################################################################
# MODULE E – HISTORICAL CAUSALITY
################################################################


exhis:HistoricalEvent a owl:Class ; rdfs:label "Historical Event" .
exhis:happenedDuring a owl:ObjectProperty ;
 rdfs:domain exhis:HistoricalEvent ; rdfs:range time:Interval ;
 rdfs:label "happened during (interval)" .


exhis:precedes a owl:ObjectProperty , owl:TransitiveProperty ;
 rdfs:domain exhis:HistoricalEvent ; rdfs:range exhis:HistoricalEvent ;
 rdfs:label "precedes" .


# Examples to seed the chain -------------------------------------
exhis:PapalBullRomanusPontifex1455 a exhis:HistoricalEvent , excol:PapalBull;
 rdfs:label "Romanus Pontifex (1455)" ;
 exhis:happenedDuring [ a time:Interval ; time:hasBeginning "1455"^^xsd:gYear ; time:hasEnd "1455"^^xsd:gYear ] ;
 excol:assertsDominionOver exind:IndigenousTerritory .


exhis:ScientificForestryIndia1855 a exhis:HistoricalEvent ;
 rdfs:label "Scientific Forestry Programme – India 1855" .


exhis:StockholmUNCHE1972 a exhis:HistoricalEvent ; rdfs:label "UNCHE Stockholm 1972" .


exhis:PapalBullRomanusPontifex1455 exhis:precedes exhis:ScientificForestryIndia1855 .
exhis:ScientificForestryIndia1855 exhis:precedes exhis:StockholmUNCHE1972 .






















################################################################
# QUICK PATCH – CORE GEO PLACEHOLDER
################################################################


ex:Region a owl:Class ; rdfs:label "Region" .




################################################################
# CROSS-MODULE INTEGRATION
################################################################


# Link paradigms to explicit violence
ex:InfiniteGrowthModel ex:drivesBoundaryExceedance ex:ClimateChangeBoundary ;
 ex:externalises exvio:Ecocide .


# Green-washing – example
excol:SomeESGConsultancy a excol:CorporateActor ;
 excol:greenwashesWith exqm:PlantBasedDiet ;
 excol:extractsFrom exind:IndigenousTerritory .


# Fortess conservation → development aggression
exvio:provokesDevelopmentAggression
 a owl:ObjectProperty ;
 rdfs:label "provokes development aggression" ;
 rdfs:domain excol:CorporateActor ; # or whatever is correct
 rdfs:range exvio:DevelopmentAggression .




################################################################
# EXAMPLE — GREEN-WASHING LINK
################################################################
@prefix excol: <http://example.org/ontology/colonial#> .
@prefix ex: <http://example.org/ontology#> .
@prefix exqm: <http://example.org/ontology/quantitative#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> . 


### 1. The corporate actor
excol:MegaOilCorp
 a excol:CorporateActor ;
 rdfs:label "MegaOilCorp" ;
 excol:extractsFrom exind:AmazonTerritory ; # some territory
 excol:greenwashesWith ex:MOC_NetZero2050 .


### 2. The “sustainability” initiative being marketed
ex:MOC_NetZero2050
 a ex:SustainabilityInitiative ;
 rdfs:label "MegaOilCorp Net-Zero by 2050 Programme" ;
 ex:mitigatesImpactOn ex:ClimateChangeBoundary ; # claimed impact
 rdfs:comment "Offset-heavy net-zero pledge criticised for phantom credits." ;
 exqm:hasCarbonSequestrationPotential "0.05"^^xsd:float .


################################################################
# END EXAMPLE
################################################################




################################################################
# BIODIVERSITY-LOSS DRIVERS (quick-win: single domain fix)
################################################################
ex:DriverOfBiodiversityLoss a owl:Class ; rdfs:label "Driver of Biodiversity Loss" .
ex:CommodityProduction rdfs:subClassOf ex:DriverOfBiodiversityLoss .
ex:UnsustainableConsumption rdfs:subClassOf ex:DriverOfBiodiversityLoss .
ex:landGrabbing rdfs:subClassOf ex:DriverOfBiodiversityLoss .


ex:drivesBiodiversityLoss a owl:ObjectProperty ;
 rdfs:label "drives biodiversity loss" ;
 rdfs:domain ex:DriverOfBiodiversityLoss ;
 rdfs:range ex:biodiversityLoss .




################################################################
# MODULE Y: GROWTH, DECOUPLING & ENERGY ECONOMICS (NEW MODEL)
################################################################


### 1. PARADIGMS & MACRO-FRAMES
ex:DegrowthParadigm a owl:Class ; rdfs:subClassOf ex:CulturalOrEconomicParadigm ;
 rdfs:label "Degrowth Paradigm" ;
 skos:definition "A paradigm that deliberately reduces material and energy throughput to keep humanity within planetary boundaries." .


ex:WellbeingEconomy a owl:Class ; rdfs:subClassOf ex:CulturalOrEconomicParadigm ;
 rdfs:label "Well-being Economy" ;
 skos:definition "An economy organised primarily around human and ecological well-being rather than GDP expansion." .


ex:SuperOrganismEconomy a owl:Class ; rdfs:subClassOf ex:CulturalOrEconomicParadigm ;
 rdfs:label "Super-organism Economy" ;
 skos:definition "A perspective that treats the human economy as an energy-dissipating super-organism subject to biophysical limits." .




### 2. DECOUPLING TAXONOMY
ex:Decoupling a owl:Class ; rdfs:label "Decoupling" ;
 skos:definition "Any claimed separation between economic growth and environmental pressure." .


ex:RelativeDecoupling a owl:Class ; rdfs:subClassOf ex:Decoupling ;
 rdfs:label "Relative Decoupling" ;
 skos:definition "Environmental impact grows more slowly than GDP, but still grows in absolute terms." .


ex:AbsoluteDecoupling a owl:Class ; rdfs:subClassOf ex:Decoupling ;
 rdfs:label "Absolute Decoupling" ;
 skos:definition "Environmental impact declines in absolute terms while GDP grows." .




### 3. OVERSHOOT, REBOUND & LEAKAGE
ex:DriverOfOvershoot a owl:Class ; rdfs:label "Driver of Overshoot" ;
 skos:definition "Processes that push human activity beyond planetary safe limits." .


ex:ReboundEffect a owl:Class ;
 rdfs:subClassOf ex:DriverOfOvershoot , ex:DriverOfBiodiversityLoss ;
 rdfs:label "Rebound (Jevons) Effect" ;
 skos:definition "Efficiency gains lower cost and stimulate extra demand, offsetting environmental savings." .


ex:CarbonLeakage a owl:Class ; rdfs:subClassOf ex:DriverOfOvershoot ;
 rdfs:label "Carbon Leakage" ;
 skos:definition "Shifting emissions from a regulated to an unregulated jurisdiction." .


ex:Overshoot a owl:Class ; rdfs:label "Overshoot State" ;
 skos:definition "A state where resource use exceeds ecological regeneration capacity." .


ex:CollapseScenario a owl:Class ; rdfs:label "Collapse Scenario" ;
 skos:definition "System-dynamics pathway leading from overshoot to rapid decline in social or ecological well-being." .






ex:DriverOfOvershoot rdfs:subClassOf [
 a owl:Restriction ;
 owl:onProperty ex:drivesBoundaryExceedance ;
 owl:someValuesFrom ex:PlanetaryBoundary
 ] .




### 4. SYSTEM-DYNAMICS MODELLING
ex:SystemDynamicsModel a owl:Class ; rdfs:label "System-Dynamics Model" ;
 skos:definition "A formal simulation model representing feedback-rich socio-ecological systems." .


ex:World3 a ex:SystemDynamicsModel ;
 rdfs:label "World3 Model" ;
 skos:definition "Meadows et al. model used in *Limits to Growth*; simulates population, capital, resources, pollution and food." .




### 5. ENERGY & BIOPHYSICAL METRICS
ex:EnergySystem a owl:Class ; rdfs:label "Energy System" .


ex:EnergyReturnOnInvestment a owl:DatatypeProperty ;
 rdfs:domain ex:EnergySystem ; rdfs:range xsd:float ;
 rdfs:label "has EROI" ;
 skos:definition "Ratio of energy delivered to energy invested." .


ex:NetEnergy a owl:Class ; rdfs:label "Net Energy" ;
 skos:definition "Energy available to society after accounting for extraction and conversion costs." .


ex:DecentLivingEnergyThreshold a owl:DatatypeProperty ;
 rdfs:domain ex:HumanCivilisation ; rdfs:range xsd:float ;
 rdfs:label "has decent-living energy threshold (GJ cap⁻¹ yr⁻¹)" ;
 skos:definition "Minimum per-capita final energy required for decent living standards." .


ex:FossilFuelSystem a ex:EnergySystem ;
 ex:EnergyReturnOnInvestment "10"^^xsd:float .




### 6. BEHAVIOURAL & CULTURAL DRIVERS
ex:BehaviouralCrisis a owl:Class ; rdfs:label "Behavioural Crisis" ;
 skos:definition "A mismatch between evolved human behaviour and planetary limits, driving over-consumption." .


ex:AdvertisingPressure a owl:Class ; rdfs:subClassOf ex:DriverOfOvershoot , ex:UnsustainableConsumption ;
 rdfs:label "Advertising-Induced Demand" ;
 skos:definition "Market incentives that artificially inflate consumption expectations." .




### 7. FINANCE & MODELLING CRITIQUES
ex:ESGMetric a owl:Class ; rdfs:label "ESG Metric" ;
 skos:definition "Environmental, social and governance screening criterion used by investors." .


ex:DegrowthInvestment a owl:Class ; rdfs:subClassOf ex:PolicyInstrument ;
 rdfs:label "Degrowth-Compatible Investment" ;
 skos:definition "Capital allocation aligned with throughput reduction and sufficiency principles." .


ex:DegrowthInvestment rdfs:subClassOf ex:SufficiencyPolicy . # (cross-link)


ex:IntegratedAssessmentModel a owl:Class ; rdfs:label "Integrated Assessment Model" ;
 skos:definition "Model that couples climate science with economic optimisation, usually via discounting." .


ex:usesDiscountRate a owl:DatatypeProperty ;
 rdfs:domain ex:IntegratedAssessmentModel ; rdfs:range xsd:float ;
 rdfs:label "uses discount rate" ;
 skos:definition "Discount rate applied to future costs and benefits." .




### 8. CROSS-MODULE OBJECT PROPERTIES
ex:outsourcesEmissionsTo a owl:ObjectProperty ;
 rdfs:domain ex:EconomicActivity ; rdfs:range ex:Region ;
 rdfs:label "outsources emissions to" ;
 skos:definition "Links an economic activity to the jurisdiction where its outsourced emissions occur." .


ex:cascadesInto a owl:ObjectProperty ;
 rdfs:domain ex:PlanetaryBoundary ; rdfs:range ex:PlanetaryBoundary ;
 rdfs:label "cascades into" ;
 skos:definition "Indicates that transgressing one boundary increases pressure on another (tipping-point cascade)." .


################################################################
# END OF MODULE Y
################################################################




################################################################
# END OF ONTOLOGY (v10.2)
################################################################"
 `
