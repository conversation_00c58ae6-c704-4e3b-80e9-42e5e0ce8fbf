import { Tool } from './validated-llms'

// Chart type definitions
export interface ChartData {
  type: 'area' | 'bar' | 'line' | 'pie' | 'radar' | 'radial'
  title?: string
  description?: string
  data: Record<string, any>[]
  config: ChartConfig
}

export interface ChartConfig {
  [key: string]: {
    label?: string
    color?: string
    theme?: {
      light: string
      dark: string
    }
  }
}

// Chart tool implementations
async function createAreaChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  xAxisKey: string
  yAxisKeys: string[]
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, xAxisKey, yAxisKeys, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!xAxisKey) {
    throw new Error('xAxisKey is required')
  }
  if (!yAxisKeys || !Array.isArray(yAxisKeys) || yAxisKeys.length === 0) {
    throw new Error('yAxisKeys must be a non-empty array')
  }
  
  // Auto-generate config for any missing keys
  const chartConfig: ChartConfig = { ...config }
  yAxisKeys.forEach((key, index) => {
    if (!chartConfig[key]) {
      const hue = (index * 60) % 360
      chartConfig[key] = {
        label: key.charAt(0).toUpperCase() + key.slice(1),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'area',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

async function createBarChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  xAxisKey: string
  yAxisKeys: string[]
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, xAxisKey, yAxisKeys, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!xAxisKey) {
    throw new Error('xAxisKey is required')
  }
  if (!yAxisKeys || !Array.isArray(yAxisKeys) || yAxisKeys.length === 0) {
    throw new Error('yAxisKeys must be a non-empty array')
  }
  
  // Auto-generate config for any missing keys
  const chartConfig: ChartConfig = { ...config }
  yAxisKeys.forEach((key, index) => {
    if (!chartConfig[key]) {
      const hue = (index * 60) % 360
      chartConfig[key] = {
        label: key.charAt(0).toUpperCase() + key.slice(1),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'bar',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

async function createLineChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  xAxisKey: string
  yAxisKeys: string[]
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, xAxisKey, yAxisKeys, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!xAxisKey) {
    throw new Error('xAxisKey is required')
  }
  if (!yAxisKeys || !Array.isArray(yAxisKeys) || yAxisKeys.length === 0) {
    throw new Error('yAxisKeys must be a non-empty array')
  }
  
  // Auto-generate config for any missing keys
  const chartConfig: ChartConfig = { ...config }
  yAxisKeys.forEach((key, index) => {
    if (!chartConfig[key]) {
      const hue = (index * 60) % 360
      chartConfig[key] = {
        label: key.charAt(0).toUpperCase() + key.slice(1),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'line',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

async function createPieChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  nameKey: string
  valueKey: string
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, nameKey, valueKey, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!nameKey) {
    throw new Error('nameKey is required')
  }
  if (!valueKey) {
    throw new Error('valueKey is required')
  }
  
  // Auto-generate config for pie chart segments
  const chartConfig: ChartConfig = { ...config }
  data.forEach((item, index) => {
    const name = item[nameKey]
    if (name && !chartConfig[name]) {
      const hue = (index * 40) % 360
      chartConfig[name] = {
        label: String(name),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'pie',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

async function createRadarChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  angleKey: string
  radiusKeys: string[]
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, angleKey, radiusKeys, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!angleKey) {
    throw new Error('angleKey is required')
  }
  if (!radiusKeys || !Array.isArray(radiusKeys) || radiusKeys.length === 0) {
    throw new Error('radiusKeys must be a non-empty array')
  }
  
  // Auto-generate config for any missing keys
  const chartConfig: ChartConfig = { ...config }
  radiusKeys.forEach((key, index) => {
    if (!chartConfig[key]) {
      const hue = (index * 60) % 360
      chartConfig[key] = {
        label: key.charAt(0).toUpperCase() + key.slice(1),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'radar',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

async function createRadialChart(args: {
  title?: string
  description?: string
  data: Array<Record<string, any>>
  nameKey: string
  valueKey: string
  config?: ChartConfig
}): Promise<string> {
  const { title, description, data, nameKey, valueKey, config = {} } = args
  
  // Validate required parameters
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array')
  }
  if (!nameKey) {
    throw new Error('nameKey is required')
  }
  if (!valueKey) {
    throw new Error('valueKey is required')
  }
  
  // Auto-generate config for radial chart segments
  const chartConfig: ChartConfig = { ...config }
  data.forEach((item, index) => {
    const name = item[nameKey]
    if (name && !chartConfig[name]) {
      const hue = (index * 40) % 360
      chartConfig[name] = {
        label: String(name),
        color: `hsl(${hue}, 70%, 50%)`
      }
    }
  })
  
  const chartData: ChartData = {
    type: 'radial',
    title,
    description,
    data,
    config: chartConfig
  }

  const encodedData = Buffer.from(JSON.stringify(chartData)).toString('base64')
  return `<chart data-json="${encodedData}"></chart>`
}

// Export the chart tools
export const chartTools: Tool[] = [
  {
    name: 'create_area_chart',
    description: 'Create an area chart with multiple data series. Best for showing data trends over time or categories with filled areas.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title'
        },
        description: {
          type: 'string',
          description: 'Chart description'
        },
        data: {
          type: 'array',
          description: 'Array of data objects',
          items: {
            type: 'object'
          }
        },
        xAxisKey: {
          type: 'string',
          description: 'Key for x-axis values (e.g., "month", "year", "category")'
        },
        yAxisKeys: {
          type: 'array',
          description: 'Array of keys for y-axis values (e.g., ["revenue", "profit"])',
          items: {
            type: 'string'
          }
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration for colors and labels'
        }
      },
      required: ['data', 'xAxisKey', 'yAxisKeys']
    },
    implementation: createAreaChart
  },
  {
    name: 'create_bar_chart',
    description: 'Create a bar chart with multiple data series. Best for comparing values across categories.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title'
        },
        description: {
          type: 'string',
          description: 'Chart description'
        },
        data: {
          type: 'array',
          description: 'Array of data objects',
          items: {
            type: 'object'
          }
        },
        xAxisKey: {
          type: 'string',
          description: 'Key for x-axis values (e.g., "category", "company", "year")'
        },
        yAxisKeys: {
          type: 'array',
          description: 'Array of keys for y-axis values (e.g., ["sales", "profit"])',
          items: {
            type: 'string'
          }
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration for colors and labels'
        }
      },
      required: ['data', 'xAxisKey', 'yAxisKeys']
    },
    implementation: createBarChart
  },
  {
    name: 'create_line_chart',
    description: 'Create a line chart with multiple data series. Best for showing trends and changes over time.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title'
        },
        description: {
          type: 'string',
          description: 'Chart description'
        },
        data: {
          type: 'array',
          description: 'Array of data objects',
          items: {
            type: 'object'
          }
        },
        xAxisKey: {
          type: 'string',
          description: 'Key for x-axis values (e.g., "date", "month", "quarter")'
        },
        yAxisKeys: {
          type: 'array',
          description: 'Array of keys for y-axis values (e.g., ["temperature", "humidity"])',
          items: {
            type: 'string'
          }
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration for colors and labels'
        }
      },
      required: ['data', 'xAxisKey', 'yAxisKeys']
    },
    implementation: createLineChart
  },
  {
    name: 'create_pie_chart',
    description: 'Create a pie chart to show proportions of a whole. Best for showing percentage breakdowns.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title'
        },
        description: {
          type: 'string',
          description: 'Chart description'
        },
        data: {
          type: 'array',
          description: 'Array of data objects',
          items: {
            type: 'object'
          }
        },
        nameKey: {
          type: 'string',
          description: 'Key for segment names (e.g., "category", "type")'
        },
        valueKey: {
          type: 'string',
          description: 'Key for segment values (e.g., "value", "count", "percentage")'
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration for colors and labels'
        }
      },
      required: ['data', 'nameKey', 'valueKey']
    },
    implementation: createPieChart
  },
  {
    name: 'create_radar_chart',
    description: 'Create a radar chart to show multivariate data. Best for comparing multiple metrics across different entities.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title'
        },
        description: {
          type: 'string',
          description: 'Chart description'
        },
        data: {
          type: 'array',
          description: 'Array of data objects',
          items: {
            type: 'object'
          }
        },
        angleKey: {
          type: 'string',
          description: 'Key for angle/axis labels (e.g., "metric", "dimension")'
        },
        radiusKeys: {
          type: 'array',
          description: 'Array of keys for radius values (e.g., ["company_a", "company_b"])',
          items: {
            type: 'string'
          }
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration for colors and labels'
        }
      },
      required: ['data', 'angleKey', 'radiusKeys']
    },
    implementation: createRadarChart
  },
  {
    name: 'create_radial_chart',
    description: 'Create a radial chart (donut/progress chart). Best for showing progress or completion rates.',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Chart title'
        },
        description: {
          type: 'string',
          description: 'Chart description'
        },
        data: {
          type: 'array',
          description: 'Array of data objects',
          items: {
            type: 'object'
          }
        },
        nameKey: {
          type: 'string',
          description: 'Key for segment names (e.g., "category", "phase")'
        },
        valueKey: {
          type: 'string',
          description: 'Key for segment values (e.g., "progress", "completion")'
        },
        config: {
          type: 'object',
          description: 'Optional chart configuration for colors and labels'
        }
      },
      required: ['data', 'nameKey', 'valueKey']
    },
    implementation: createRadialChart
  }
]
