import { NextRequest } from 'next/server'
import { truncate } from '@/utils/text-utils'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { FANCY_AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'
import { fetchClaimsData, fetchEntityData, fetchPromisesData, fetchRunData } from '../../../../data-fetchers'

export const maxDuration = 180;

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ entityId: string; runId: string }> }
) {
  try {
    // Get the parameters from the route
    const params = await context.params;
    const { entityId, runId } = params;

    const { searchParams } = new URL(request.url)
    const model = searchParams.get('model') || 'ekoIntelligence'
    const includeDisclosures = searchParams.get('includeDisclosures') === 'true'

    if (!entityId || !runId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: entityId, runId' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    console.log(`[API] /api/report/entity/${entityId}/${runId}/reliability called with:`, {
      entityId,
      runId,
      model,
      includeDisclosures
    })

    // Fetch entity data
    const entityData = await fetchEntityData(entityId)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(runId, entityId)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch claims and promises data
    const claimsData = await fetchClaimsData({
      entity: entityId,
      runId: runData.id.toString(),
      model: 'all',
      includeDisclosures
    })
    const promisesData = await fetchPromisesData({
      entity: entityId,
      runId: runData.id.toString(),
      model: 'all',
      includeDisclosures
    })

    const entityName = entityData.name

    console.log(`[API] Processing reliability analysis for:`, {
      entityName,
      claimsCount: claimsData.length,
      promisesCount: promisesData.length
    })

    if (claimsData.length === 0 && promisesData.length === 0) {
      // Return basic response if no claims or promises found
      return new Response(JSON.stringify({
        text: `# Reliability Analysis for ${entityName}\n\nNo claims or promises data available for reliability analysis. This may indicate limited forward-looking statements or commitments in the analyzed materials.`,
        citations: [],
        metadata: {
          entityId,
          entityName,
          analysisType: 'reliability',
          claimsCount: 0,
          promisesCount: 0,
          citationCount: 0,
          generatedAt: new Date().toISOString(),
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Filter and sort claims by importance
    const significantClaims = claimsData
      .filter(claim => claim.model.importance >= 30) // Only include important claims
      .sort((a, b) => b.model.importance - a.model.importance)
      .slice(0, 20);

    // Filter and sort promises by confidence
    const significantPromises = promisesData
      .filter(promise => promise.model.confidence >= 0.3) // Only include confident promises
      .sort((a, b) => b.model.confidence - a.model.confidence)
      .slice(0, 20);

    // Build the prompt for reliability analysis
    const claimsSummaries = significantClaims.map(claim => {
      const summary = truncate(claim.model.summary || '', 400)
      const conclusion = truncate(claim.model.conclusion || '', 300)
      return `**Claim**: ${claim.model.text}\n**Verdict**: ${claim.model.verdict}\n**Summary**: ${summary}\n**Conclusion**: ${conclusion}\n**Confidence**: ${claim.model.confidence}\n**Importance**: ${claim.model.importance}`
    }).join('\n\n')

    const promisesSummaries = significantPromises.map(promise => {
      const summary = truncate(promise.model.summary || '', 400)
      const conclusion = truncate(promise.model.conclusion || '', 300)
      return `**Promise**: ${promise.model.text}\n**Summary**: ${summary}\n**Conclusion**: ${conclusion}\n**Confidence**: ${promise.model.confidence}`
    }).join('\n\n')

    const promptText = `
      <instructions>
      You are analyzing the reliability of statements and commitments made by ${entityName} based on claims analysis and promises tracking.
      
      Please provide a comprehensive reliability analysis that covers:
      
      ## Claims Analysis
      - Evaluate the accuracy and validity of past claims
      - Assess patterns in claim verification outcomes
      - Identify areas where claims were substantiated vs. unsubstantiated
      - Analyze the confidence levels and importance ratings
      
      ## Promises and Commitments
      - Review the tracking of promises and commitments
      - Assess delivery against stated timelines and targets
      - Evaluate the specificity and measurability of commitments
      - Analyze confidence levels in promise fulfillment
      
      ## Reliability Patterns
      - Identify trends in statement accuracy over time
      - Assess consistency between claims and actual performance
      - Evaluate the organization's track record of meeting commitments
      
      ## Risk Assessment
      - Highlight areas of concern regarding statement reliability
      - Identify potential greenwashing or misleading claims
      - Assess the credibility of future commitments based on past performance
      
      Structure your response with clear headings and provide specific examples from the evidence.
      Maintain all citation references in the format [^citation_id].
      Focus on factual analysis based on the evidence provided.
      
      ${significantClaims.length > 0 ? `Claims Analysis Data:\n${claimsSummaries}\n\n` : ''}
      ${significantPromises.length > 0 ? `Promises Analysis Data:\n${promisesSummaries}\n\n` : ''}
      
      ${COMMON_INSTRUCTIONS}
      </instructions>
    `;

    // Generate content using our Gemini client
    const text = await generateReportContent({
      modelName: FANCY_AI_MODEL_NAME,
      prompt: promptText,
      endpoint: `/api/report/entity/${entityId}/${runId}/reliability`,
      entityName: `${entityName} - Reliability Analysis`,
    });

    // Extract citations from claims and promises
    const claimsCitations = significantClaims
      .filter(claim => claim.model.citations && claim.model.citations.length > 0)
      .flatMap(claim => claim.model.citations);

    const promisesCitations = significantPromises
      .filter(promise => promise.model.citations && promise.model.citations.length > 0)
      .flatMap(promise => promise.model.citations);

    // Combine and deduplicate citations
    const allCitations = [...claimsCitations, ...promisesCitations];
    const citations = allCitations.filter((citation: any, index: number, self: any[]) =>
      index === self.findIndex((c: any) => c.doc_page_id === citation.doc_page_id)
    );

    // Return JSON response with text and citations
    const response = {
      text,
      citations,
      metadata: {
        entityId,
        entityName,
        analysisType: 'reliability',
        runId: runData.id,
        claimsCount: significantClaims.length,
        promisesCount: significantPromises.length,
        citationCount: citations.length,
        includeDisclosures,
        generatedAt: new Date().toISOString(),
      }
    };

    return new Response(JSON.stringify(response), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error(`[API] /api/report/entity/reliability error:`, error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate reliability analysis', 
        message: error instanceof Error ? error.message : String(error) 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
