import { NextRequest } from 'next/server'
import { CherryTypeV2 } from '@/types/cherry'
import { truncate } from '@/utils/text-utils'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { FANCY_AI_MODEL_NAME, generateReportContent } from '@/app/api/report/gemini-client'
import { fetchCherryData, fetchEntityData, fetchRunData } from '../../../../data-fetchers'

export const maxDuration = 180;

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ entityId: string; runId: string }> }
) {
  try {
    // Get the parameters from the route
    const params = await context.params;
    const { entityId, runId } = params;

    const { searchParams } = new URL(request.url)
    const model = searchParams.get('model') || 'ekoIntelligence'
    const includeDisclosures = searchParams.get('includeDisclosures') === 'true'

    if (!entityId || !runId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: entityId, runId' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    console.log(`[API] /api/report/entity/${entityId}/${runId}/transparency called with:`, {
      entityId,
      runId,
      model,
      includeDisclosures
    })

    // Fetch entity data
    const entityData = await fetchEntityData(entityId)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(runId, entityId)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch all cherry picking and flooding data for this entity and run
    const allCherryData = await fetchCherryData({
      entity: entityId,
      runId: runData.id.toString(),
      model: 'all', // For transparency analysis, we want all models
      includeDisclosures
    })

    const entityName = entityData.name

    console.log(`[API] Processing transparency analysis for:`, {
      entityName,
      cherryDataCount: allCherryData.length
    })

    // Filter for transparency-related cherry picking and flooding instances
    // Look for instances that involve disclosure, transparency, or communication patterns
    const transparencyRelatedData = allCherryData.filter((cherry: CherryTypeV2) => {
      const analysis = cherry.model?.analysis?.toLowerCase() || '';
      const reason = cherry.model?.reason?.toLowerCase() || '';
      const label = cherry.label?.toLowerCase() || '';

      return analysis.includes('disclosure') ||
             analysis.includes('transparency') ||
             analysis.includes('communication') ||
             analysis.includes('reporting') ||
             analysis.includes('statement') ||
             reason.includes('disclosure') ||
             reason.includes('transparency') ||
             reason.includes('communication') ||
             label.includes('disclosure') ||
             label.includes('transparency') ||
             label.includes('communication');
    })
      .sort((a, b) => (b.model?.severity || 0) - (a.model?.severity || 0))
      .slice(0, 20); // Limit to top 20 most severe instances

    if (transparencyRelatedData.length === 0) {
      // Return basic response if no transparency-related cherry picking/flooding found
      return new Response(JSON.stringify({
        text: `# Transparency Analysis for ${entityName}\n\nNo selective highlighting patterns related to transparency or disclosure were detected in the current analysis. This may indicate either strong transparency practices with consistent communication patterns, or limited disclosure coverage in the analyzed materials.`,

        citations: [],
        metadata: {
          entityId,
          entityName,
          analysisType: 'transparency',
          cherryDataCount: 0,
          citationCount: 0,
          generatedAt: new Date().toISOString(),
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Build the prompt for transparency analysis using cherry picking/flooding data
    const cherryAnalyses = transparencyRelatedData.map((cherry: CherryTypeV2) => {
      const analysis = truncate(cherry.model?.analysis || '', 600)
      const reason = truncate(cherry.model?.reason || '', 300)
      const modelType = cherry.model?.model === 'cherry_picking' ? 'Cherry Picking' : 'Information Flooding'

      return `**${cherry.label}** (${modelType} - Severity: ${cherry.model?.severity || 'N/A'}, Confidence: ${cherry.model?.confidence || 'N/A'}%)\n\n**Analysis:** ${analysis}\n\n**Reason:** ${reason}`
    }).join('\n\n---\n\n')

    const promptText = `
      <instructions>
      You are analyzing the transparency and disclosure practices of ${entityName} based on selective highlighting patterns detected in their communications.

      The evidence below shows instances of cherry picking and information flooding related to transparency, disclosure, and communication practices. These patterns can indicate potential issues with balanced and transparent communication.

      Please provide a comprehensive transparency analysis that covers:

      ## Selective Highlighting Patterns
      - Analyze cherry picking instances where positive information may be emphasized while negative information is downplayed
      - Evaluate information flooding patterns where excessive positive information may obscure important negative disclosures
      - Assess the impact of these patterns on stakeholder understanding

      ## Communication Balance and Transparency
      - Evaluate whether communications provide balanced perspectives
      - Assess the completeness and clarity of disclosures in context of selective highlighting
      - Identify potential gaps in transparent communication

      ## Stakeholder Impact
      - Analyze how selective highlighting patterns may affect stakeholder decision-making
      - Evaluate the potential for misleading or incomplete information presentation
      - Assess the authenticity and reliability of communications

      ## Recommendations for Improvement
      - Suggest ways to improve communication balance and transparency
      - Recommend practices to avoid selective highlighting issues
      - Propose enhanced disclosure practices

      Structure your response with clear headings and provide specific examples from the evidence.
      Maintain all citation references in the format [^citation_id].
      Focus on factual analysis based on the selective highlighting patterns provided.

      Evidence from Selective Highlighting Analysis:
      ${cherryAnalyses}


      ${COMMON_INSTRUCTIONS}
      </instructions>
    `;

    // Generate content using our Gemini client
    const text = await generateReportContent({
      modelName: FANCY_AI_MODEL_NAME,
      prompt: promptText,
      endpoint: `/api/report/entity/${entityId}/${runId}/transparency`,
      entityName: `${entityName} - Transparency Analysis`,
    });

    // Extract citations from the cherry picking/flooding data used in this analysis
    const citations = transparencyRelatedData
      .filter((cherry: CherryTypeV2) => cherry.model?.citations && cherry.model.citations.length > 0)
      .flatMap((cherry: CherryTypeV2) => cherry.model.citations)
      .filter((citation: any, index: number, self: any[]) =>
        index === self.findIndex((c: any) => c.doc_page_id === citation.doc_page_id)
      ); // Remove duplicates

    // Return JSON response with text and citations
    const response = {
      text,
      citations,
      metadata: {
        entityId,
        entityName,
        analysisType: 'transparency',
        runId: runData.id,
        cherryDataCount: transparencyRelatedData.length,
        citationCount: citations.length,
        includeDisclosures,
        generatedAt: new Date().toISOString(),
      }
    };

    return new Response(JSON.stringify(response), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error(`[API] /api/report/entity/transparency error:`, error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate transparency analysis', 
        message: error instanceof Error ? error.message : String(error) 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
