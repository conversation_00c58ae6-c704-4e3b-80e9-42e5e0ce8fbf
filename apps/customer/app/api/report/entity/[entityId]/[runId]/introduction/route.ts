import { NextRequest } from 'next/server'
import { create<PERSON><PERSON><PERSON><PERSON> } from '@/utils/cache-utils'
import { COMMON_INSTRUCTIONS } from '@/app/api/report/report-common'
import { AI_MODEL_NAME } from '@/app/api/report/gemini-client'
import { generateReportContent } from '@/app/api/report/gemini-client'
import { fetchEntityData, fetchRunData } from '../../../../data-fetchers'

export const maxDuration = 180;

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ entityId: string; runId: string }> }
) {
  try {
    // Get the parameters from the route
    const params = await context.params;
    const { entityId, runId } = params;

    const { searchParams } = new URL(request.url)
    const model = searchParams.get('model') || 'ekoIntelligence'
    const includeDisclosures = searchParams.get('includeDisclosures') === 'true'

    if (!entityId || !runId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: entityId, runId' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    console.log(`[API] /api/report/entity/${entityId}/${runId}/introduction called with:`, {
      entityId,
      runId,
      model,
      includeDisclosures
    })

    // Fetch entity data
    const entityData = await fetchEntityData(entityId)
    if (!entityData) {
      return new Response(
        JSON.stringify({ error: 'Entity not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Fetch run data
    const runData = await fetchRunData(runId, entityId)
    if (!runData) {
      return new Response(
        JSON.stringify({ error: 'Run not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const entityName = entityData.name
    const entityDescription = entityData.description
    const modelName = model

    // Create a cache key for logging purposes
    const cacheKey = createCacheKey('intro', {
      entityName,
      entityDescription,
      modelName
    });

    const promptText = `
      <instructions>

      ${COMMON_INSTRUCTIONS}
      
      Write a comprehensive introduction for an ESG report about ${entityName}, a company described as: "${entityDescription}".
      The report uses the ${modelName} model for analysis.

      Your introduction should:
      1. Provide context about ${entityName} and its industry
      2. Explain the purpose of this ESG report
      3. Briefly mention the ${modelName} framework being used
      4. Set expectations for what the report will cover

      Write in a professional, objective tone suitable for investors and stakeholders. The introduction should be 2-3 paragraphs.
      </instructions>
    `;

    // Generate content using our Gemini client
    const text = await generateReportContent({
      modelName: AI_MODEL_NAME,
      prompt: `You are a professional ESG report writer for a leading auditing firm specializing in environmental, social, and governance matters. You are objective and factual but diplomatic when criticizing companies. You are not conversational.\n\n${promptText}`,
      endpoint: `/api/report/entity/${entityId}/${runId}/introduction`,
      entityName,
    });

    // Return the text response
    return new Response(text, {
      headers: { 'Content-Type': 'text/plain' }
    });
  } catch (error) {
    console.error('[API] /api/report/entity/[entityId]/[runId]/introduction error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to generate introduction', message: error instanceof Error ? error.message : String(error) }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
