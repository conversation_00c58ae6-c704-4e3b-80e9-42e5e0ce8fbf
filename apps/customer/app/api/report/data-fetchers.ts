import { createClient } from '@/app/supabase/server'
import { FlagTypeV2, ModelSectionType } from '@/types'
import { ClaimTypeV2 } from '@/types/claim'
import { PromiseTypeV2 } from '@/types/promise'
import { CherryTypeV2 } from '@/types/cherry'

export interface ReportDataParams {
  entity: string
  runId: string
  model: string
  includeDisclosures?: boolean
}

export interface EntityData {
  entity_xid: string
  name: string
  description: string
}

export interface RunData {
  id: number
}

/**
 * Fetch entity data from xfer_entities_v2 table
 */
export async function fetchEntityData(entityXid: string): Promise<EntityData | null> {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('xfer_entities_v2')
    .select('entity_xid, name, model')
    .eq('entity_xid', entityXid)
    .single()

  if (error) {
    console.error('Error fetching entity data:', error)
    return null
  }

  // Extract description from the model JSON
  let description = ''
  if (data.model && typeof data.model === 'object') {
    const model = data.model as any
    // Try to get description from the first base entity
    if (model.base_entities && Array.isArray(model.base_entities) && model.base_entities.length > 0) {
      description = model.base_entities[0].description || ''
    }
    // Fallback to top-level description if it exists
    if (!description && model.description) {
      description = model.description
    }
  }

  return {
    entity_xid: data.entity_xid,
    name: data.name,
    description: description
  } as EntityData
}

/**
 * Fetch run data - if runId is 'latest', get the most recent run
 */
export async function fetchRunData(runId: string, entityXid: string): Promise<RunData | null> {
  const supabase = await createClient()

  if (runId === 'latest') {
    const { data, error } = await supabase
      .from('xfer_runs_v2')
      .select('id')
      .eq('scope', 'entity')
      .eq('target', entityXid)
      .order('id', { ascending: false })
      .limit(1)
      .single()

    if (error) {
      console.error('Error fetching latest run:', error)
      return null
    }

    return data as RunData
  } else {
    // Validate that the specific run exists
    const { data, error } = await supabase
      .from('xfer_runs_v2')
      .select('id')
      .eq('id', parseInt(runId))
      .single()

    if (error) {
      console.error('Error fetching run data:', error)
      return null
    }

    return data as RunData
  }
}

/**
 * Fetch flags data from xfer_flags_v2 table
 */
export async function fetchFlagsData(params: ReportDataParams): Promise<FlagTypeV2[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_flags_v2')
    .select('*')
    .eq('run_id', parseInt(params.runId))
    .eq('entity_xid', params.entity)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching flags data:', error)
    return []
  }

  let flags = data as unknown as FlagTypeV2[]

  // Filter out disclosure-only flags if includeDisclosures is false
  if (!params.includeDisclosures) {
    flags = flags.filter(flag => {
      // Keep all red flags and non-disclosure-only green flags
      return flag.model?.flag_type === 'red' || !flag.model?.is_disclosure_only
    })
  }

  return flags
}

/**
 * Fetch claims data from xfer_gw_claims_v2 table
 */
export async function fetchClaimsData(params: ReportDataParams): Promise<ClaimTypeV2[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_gw_claims_v2')
    .select('*')
    .eq('run_id', parseInt(params.runId))
    .eq('entity_xid', params.entity)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching claims data:', error)
    return []
  }

  return data as unknown as ClaimTypeV2[]
}

/**
 * Fetch promises data from xfer_gw_promises_v2 table
 */
export async function fetchPromisesData(params: ReportDataParams): Promise<PromiseTypeV2[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_gw_promises_v2')
    .select('*')
    .eq('run_id', parseInt(params.runId))
    .eq('entity_xid', params.entity)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching promises data:', error)
    return []
  }

  let promises = data as unknown as PromiseTypeV2[]

  // Sort by confidence if available
  promises.sort((a, b) => {
    const modelA = a.model as any
    const modelB = b.model as any
    const confA = modelA && typeof modelA === 'object' && 'confidence' in modelA ? modelA.confidence : 0
    const confB = modelB && typeof modelB === 'object' && 'confidence' in modelB ? modelB.confidence : 0
    return confB - confA
  })

  return promises
}

/**
 * Fetch cherry data from xfer_gw_cherry_v2 table
 */
export async function fetchCherryData(params: ReportDataParams): Promise<CherryTypeV2[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_gw_cherry_v2')
    .select('*')
    .eq('run_id', parseInt(params.runId))
    .eq('entity_xid', params.entity)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching cherry data:', error)
    return []
  }

  return data as unknown as CherryTypeV2[]
}

/**
 * Fetch model sections data from xfer_model_sections_v2 table
 */
export async function fetchModelSectionsData(model: string): Promise<ModelSectionType[]> {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('xfer_model_sections_v2')
    .select('*')
    .eq('model', model)
    .order('id', { ascending: false })

  if (error) {
    console.error('Error fetching model sections data:', error)
    return []
  }

  return data as unknown as ModelSectionType[]
}
