import { NextRequest, NextResponse } from 'next/server'
import { google } from '@ai-sdk/google'
import { generateText, streamText } from 'ai'

export const runtime = 'edge'

interface GenerateRequest {
  prompt: string
  context?: string
  selectedText?: string
  documentContent?: string
  stream?: boolean
}

export async function POST(request: NextRequest) {
  try {
    const body: GenerateRequest = await request.json()
    const { prompt, context, selectedText, documentContent, stream = false } = body

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      )
    }

    // Build the system prompt for the AI assistant
    const systemPrompt = `You are an AI writing assistant helping with document editing. Follow these guidelines:

1. If working with selected text, provide only the improved version of that text
2. Maintain the original format and structure unless specifically asked to change it
3. Be concise and focused on the specific task
4. Do not add explanations or meta-commentary unless requested
5. For spelling corrections, fix errors while preserving the original meaning and style
6. For grammar improvements, enhance clarity while maintaining the author's voice
7. Return only the corrected/improved text, nothing else`

    // Build the user prompt with context
    let userPrompt = `Task: ${prompt}`

    if (selectedText) {
      userPrompt += `\n\nSelected text to work with: "${selectedText}"`
    }

    if (context) {
      userPrompt += `\n\nAdditional context: ${context}`
    }

    if (documentContent) {
      userPrompt += `\n\nFull document context (for reference): ${documentContent.substring(0, 2000)}${documentContent.length > 2000 ? '...' : ''}`
    }

    // Use Gemini Flash model for fast responses
    const model = google('gemini-2.0-flash-exp')

    if (stream) {
      // Return streaming response
      const result = streamText({
        model,
        system: systemPrompt,
        prompt: userPrompt,
        temperature: 0.3,
        maxTokens: 1000,
      })

      return result.toTextStreamResponse()
    } else {
      // Return non-streaming response
      const result = await generateText({
        model,
        system: systemPrompt,
        prompt: userPrompt,
        temperature: 0.3,
        maxTokens: 1000,
      })

      return NextResponse.json({
        text: result.text,
        success: true
      })
    }
  } catch (error) {
    console.error('AI generation error:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'AI generation failed',
        success: false
      },
      { status: 500 }
    )
  }
}
