import { NextRequest, NextResponse } from 'next/server'
import { generateReportContent, AI_MODEL_NAME } from '../../report/gemini-client'

export async function POST(request: NextRequest) {
  try {
    const { prompt, documentContent } = await request.json()

    if (!prompt || !documentContent) {
      return NextResponse.json(
        { error: 'Missing required fields: prompt and documentContent' },
        { status: 400 }
      )
    }

    // Parse the document content to get the TipTap JSON structure
    let documentJson
    try {
      // If documentContent is HTML, we need to convert it to TipTap JSON
      // For now, assume it's already JSON or can be parsed
      documentJson = typeof documentContent === 'string'
        ? JSON.parse(documentContent)
        : documentContent
    } catch {
      // If parsing fails, create a basic document structure
      documentJson = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [{ type: 'text', text: documentContent }]
          }
        ]
      }
    }

    // Call the AI service to get document edits
    const response = await callAIForDocumentEdit(prompt, documentJson)

    return NextResponse.json({
      patch: response.patch,
      description: response.description,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Document edit API error:', error)
    return NextResponse.json(
      { error: 'Failed to process document edit request' },
      { status: 500 }
    )
  }
}

async function callAIForDocumentEdit(prompt: string, documentJson: any) {
  const systemPrompt = `You are an expert document editor that works with TipTap JSON documents.
Your task is to analyze the current document and the user's request, then return both a user-friendly description and a JSON Patch.

Current document structure (TipTap JSON):
${JSON.stringify(documentJson, null, 2)}

User request: ${prompt}

You must respond with a JSON object containing both a description and a patch:

{
  "description": "A brief, user-friendly description of what changes you're making",
  "patch": [array of JSON Patch operations]
}

Examples of valid JSON Patch operations:
- Add content: {"op": "add", "path": "/content/0", "value": {"type": "paragraph", "content": [{"type": "text", "text": "New text"}]}}
- Replace content: {"op": "replace", "path": "/content/0/content/0/text", "value": "Updated text"}
- Remove content: {"op": "remove", "path": "/content/1"}

Important rules:
1. Return a JSON object with "description" and "patch" fields
2. Description should be 1-2 sentences explaining what you're doing
3. Ensure all patch paths are valid for the current document structure
4. Use proper TipTap node types (paragraph, heading, text, etc.)
5. For headings, include attrs: {"level": 1-6}
6. For text nodes, use {"type": "text", "text": "content"}

Example response:
{
  "description": "I'll add a new paragraph with the requested content at the beginning of the document.",
  "patch": [{"op": "add", "path": "/content/0", "value": {"type": "paragraph", "content": [{"type": "text", "text": "New content"}]}}]
}`

  try {
    // Use your existing Gemini client
    const result = await generateReportContent({
      modelName: AI_MODEL_NAME,
      prompt: systemPrompt,
      endpoint: '/api/ai/edit-document',
      entityName: 'document-edit'
    })

    // Parse the response from the AI
    let response
    try {
      response = JSON.parse(result.trim())
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', result)
      throw new Error('AI returned invalid JSON format')
    }

    // Validate the response structure
    if (!response.description || !response.patch) {
      throw new Error('AI response missing required fields: description and patch')
    }

    // Validate that patch is an array
    if (!Array.isArray(response.patch)) {
      throw new Error('AI response patch is not a valid array')
    }

    return response
  } catch (error) {
    console.error('Gemini API call failed:', error)
    throw error
  }
}


