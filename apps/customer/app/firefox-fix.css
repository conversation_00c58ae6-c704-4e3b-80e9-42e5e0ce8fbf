@-moz-document url-prefix() {
  .card-overlay,
  .glass-effect, .glass-effect-lit,
  .glass-effect-subtle, .glass-effect-subtle-lit,
  .glass-effect-strong, .glass-effect-strong-lit,
  .glass-effect-brand, .glass-effect-brand-lit,
  .glass-effect-brand-strong, .glass-effect-brand-strong-lit,
  .glass-effect-brand-alt-strong, .glass-effect-brand-alt-strong-lit,
  .glass-effect-brand-compliment, .glass-effect-brand-compliment-lit,
  .glass-effect-brand-compliment-strong, .glass-effect-brand-compliment-strong-lit {
    background: rgba(35, 35, 35, 0.85) !important;
    backdrop-filter: none !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25) !important;
  }

  /* Adjust brand-specific glass effects in Firefox */
  .glass-effect-brand-strong, .glass-effect-brand-strong-lit {
    background: linear-gradient(197deg, rgba(46, 125, 50, 0.85), rgba(39, 107, 43, 0.85), rgba(46, 125, 50, 0.85)) !important;
  }

  .glass-effect-brand-alt-strong, .glass-effect-brand-alt-strong-lit {
    background: linear-gradient(197deg, rgba(211, 47, 47, 0.85), rgba(198, 40, 40, 0.85), rgba(211, 47, 47, 0.85)) !important;
  }

  .glass-effect-brand-compliment-strong, .glass-effect-brand-compliment-strong-lit {
    background: linear-gradient(197deg, rgba(204, 51, 51, 0.85), rgba(214, 59, 59, 0.85), rgba(204, 51, 51, 0.85)) !important;
  }

  /* Dark mode adjustments for Firefox */
  [data-theme="dark"] .glass-effect,
  [data-theme="dark"] .glass-effect-lit,
  [data-theme="dark"] .glass-effect-subtle,
  [data-theme="dark"] .glass-effect-strong {
    background: rgba(15, 15, 15, 0.9) !important;
  }

  [data-theme="dark"] .glass-effect-brand-strong,
  [data-theme="dark"] .glass-effect-brand-strong-lit {
    background: linear-gradient(197deg, rgba(39, 107, 43, 0.9), rgba(33, 91, 36, 0.9), rgba(39, 107, 43, 0.9)) !important;
  }

  [data-theme="dark"] .glass-effect-brand-alt-strong,
  [data-theme="dark"] .glass-effect-brand-alt-strong-lit {
    background: linear-gradient(197deg, rgba(183, 28, 28, 0.9), rgba(165, 22, 22, 0.9), rgba(183, 28, 28, 0.9)) !important;
  }

  [data-theme="dark"] .glass-effect-brand-compliment-strong,
  [data-theme="dark"] .glass-effect-brand-compliment-strong-lit {
    background: linear-gradient(197deg, rgba(183, 28, 28, 0.9), rgba(165, 22, 22, 0.9), rgba(183, 28, 28, 0.9)) !important;
  }
}
