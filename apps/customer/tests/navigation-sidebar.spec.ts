import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';

test.describe('Navigation Sidebar', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to customer dashboard to ensure sidebar is visible
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
  });

  test('should display sidebar with main navigation sections', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Check main navigation sections
    await expect(page.locator('[data-testid="nav-dashboard"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-documents"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-analysis"]')).toBeVisible();

    // Check secondary navigation if present
    const accountNav = page.locator('[data-testid="nav-account"]');
    if (await accountNav.isVisible()) {
      await expect(accountNav).toBeVisible();
    }

    // Check user dropdown area
    await expect(page.locator('[data-testid="user-dropdown-trigger"]')).toBeVisible();
  });

  test('should allow collapsing and expanding navigation sections', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Test collapsing dashboard section
    const dashboardSection = page.locator('[data-testid="nav-section-dashboard"]');
    if (await dashboardSection.isVisible()) {
      const collapseButton = dashboardSection.locator('[data-testid="section-collapse-button"]');
      
      if (await collapseButton.isVisible()) {
        // Check initial expanded state
        await expect(dashboardSection.locator('[data-testid="section-content"]')).toBeVisible();
        
        // Collapse section
        await collapseButton.click();
        await expect(dashboardSection.locator('[data-testid="section-content"]')).not.toBeVisible();
        
        // Expand section again
        await collapseButton.click();
        await expect(dashboardSection.locator('[data-testid="section-content"]')).toBeVisible();
      }
    }

    // Test collapsing documents section
    const documentsSection = page.locator('[data-testid="nav-section-documents"]');
    if (await documentsSection.isVisible()) {
      const collapseButton = documentsSection.locator('[data-testid="section-collapse-button"]');
      
      if (await collapseButton.isVisible()) {
        await collapseButton.click();
        await expect(documentsSection.locator('[data-testid="section-content"]')).not.toBeVisible();
        
        await collapseButton.click();
        await expect(documentsSection.locator('[data-testid="section-content"]')).toBeVisible();
      }
    }
  });

  test('should persist collapsed state in localStorage', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Collapse a section
    const dashboardSection = page.locator('[data-testid="nav-section-dashboard"]');
    if (await dashboardSection.isVisible()) {
      const collapseButton = dashboardSection.locator('[data-testid="section-collapse-button"]');
      
      if (await collapseButton.isVisible()) {
        await collapseButton.click();
        await expect(dashboardSection.locator('[data-testid="section-content"]')).not.toBeVisible();
        
        // Refresh page to test persistence
        await page.reload();
        await page.waitForLoadState('networkidle');
        
        // Verify section remains collapsed
        await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
        await expect(dashboardSection.locator('[data-testid="section-content"]')).not.toBeVisible();
      }
    }
  });

  test('should show entity selector dropdown integration', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Check for entity selector in sidebar
    const entitySelector = page.locator('[data-testid="entity-selector"]');
    if (await entitySelector.isVisible()) {
      await expect(entitySelector).toBeVisible();
      
      // Test opening entity dropdown
      await entitySelector.click();
      await expect(page.locator('[data-testid="entity-dropdown-menu"]')).toBeVisible();
      
      // Check for company options
      const companyOptions = page.locator('[data-testid="entity-option"]');
      if (await companyOptions.count() > 0) {
        await expect(companyOptions.first()).toBeVisible();
      }
      
      // Close dropdown
      await page.keyboard.press('Escape');
      await expect(page.locator('[data-testid="entity-dropdown-menu"]')).not.toBeVisible();
    }
  });

  test('should display user dropdown menu with correct options', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Click user dropdown trigger
    const userDropdownTrigger = page.locator('[data-testid="user-dropdown-trigger"]');
    await userDropdownTrigger.click();

    // Verify dropdown menu opens
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();

    // Check for expected menu items
    await expect(page.locator('[data-testid="menu-account"]')).toBeVisible();
    await expect(page.locator('[data-testid="menu-billing"]')).toBeVisible();
    await expect(page.locator('[data-testid="menu-usage"]')).toBeVisible();
    
    // Check for notifications if available
    const notificationsItem = page.locator('[data-testid="menu-notifications"]');
    if (await notificationsItem.isVisible()) {
      await expect(notificationsItem).toBeVisible();
    }

    // Check for sign out option
    await expect(page.locator('[data-testid="menu-sign-out"]')).toBeVisible();

    // Close dropdown by clicking outside
    await page.click('body');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).not.toBeVisible();
  });

  test('should navigate to account settings from user dropdown', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Open user dropdown and click account
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    await page.click('[data-testid="menu-account"]');
    
    // Verify navigation to account page
    await page.waitForURL('**/account**');
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 15000 });
  });

  test('should navigate to billing from user dropdown', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Open user dropdown and click billing
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    await page.click('[data-testid="menu-billing"]');
    
    // Verify navigation to billing page
    await page.waitForURL('**/billing**');
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 15000 });
  });

  test('should navigate to usage from user dropdown', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Open user dropdown and click usage
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    await page.click('[data-testid="menu-usage"]');
    
    // Verify navigation to usage page
    await page.waitForURL('**/usage**');
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 15000 });
  });

  test('should display feature flag-based menu item visibility', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Check for feature-gated navigation items
    const adminNav = page.locator('[data-testid="nav-admin"]');
    const betaFeatureNav = page.locator('[data-testid="nav-beta-feature"]');
    
    // These may or may not be visible depending on user's feature flags
    // Just verify they don't cause errors if present
    if (await adminNav.isVisible()) {
      await expect(adminNav).toBeVisible();
    }
    
    if (await betaFeatureNav.isVisible()) {
      await expect(betaFeatureNav).toBeVisible();
    }

    // Check user dropdown for feature-gated options
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();

    const advancedOptionsItem = page.locator('[data-testid="menu-advanced-options"]');
    if (await advancedOptionsItem.isVisible()) {
      await expect(advancedOptionsItem).toBeVisible();
    }

    // Close dropdown
    await page.keyboard.press('Escape');
  });

  test('should handle real-time notifications via Supabase listeners', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Check for notification indicator
    const notificationBadge = page.locator('[data-testid="notification-badge"]');
    const notificationIcon = page.locator('[data-testid="notification-icon"]');

    // Notifications may or may not be present
    if (await notificationBadge.isVisible()) {
      await expect(notificationBadge).toBeVisible();
      
      // Check badge has count
      const badgeText = await notificationBadge.textContent();
      expect(badgeText).toMatch(/\d+/);
    }

    if (await notificationIcon.isVisible()) {
      await expect(notificationIcon).toBeVisible();
      
      // Test clicking notifications
      await notificationIcon.click();
      
      // Check for notifications panel or navigation
      try {
        await expect(page.locator('[data-testid="notifications-panel"]')).toBeVisible({ timeout: 5000 });
      } catch {
        // Might navigate to notifications page instead
        await page.waitForURL('**/notifications**', { timeout: 5000 });
      }
    }
  });

  test('should maintain navigation state when changing routes', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Collapse a section
    const documentsSection = page.locator('[data-testid="nav-section-documents"]');
    if (await documentsSection.isVisible()) {
      const collapseButton = documentsSection.locator('[data-testid="section-collapse-button"]');
      
      if (await collapseButton.isVisible()) {
        await collapseButton.click();
        await expect(documentsSection.locator('[data-testid="section-content"]')).not.toBeVisible();
      }
    }

    // Navigate to different page
    await page.goto('/customer/dashboard/flags');
    await page.waitForLoadState('networkidle');

    // Verify sidebar state is maintained
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
    if (await documentsSection.isVisible()) {
      await expect(documentsSection.locator('[data-testid="section-content"]')).not.toBeVisible();
    }
  });

  test('should highlight active navigation item', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Navigate to dashboard and check active state
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    
    const dashboardNav = page.locator('[data-testid="nav-dashboard"]');
    await expect(dashboardNav).toHaveClass(/active|current|selected/);

    // Navigate to documents and check active state
    const documentsNav = page.locator('[data-testid="nav-documents"]');
    if (await documentsNav.isVisible()) {
      await documentsNav.click();
      await page.waitForLoadState('networkidle');
      
      await expect(documentsNav).toHaveClass(/active|current|selected/);
      await expect(dashboardNav).not.toHaveClass(/active|current|selected/);
    }
  });

  test('should handle sign out functionality', async ({ page }) => {
    // Wait for sidebar to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Open user dropdown and initiate sign out
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    await page.click('[data-testid="menu-sign-out"]');
    
    // Verify sign out process (might show confirmation or redirect immediately)
    try {
      // Check for sign out confirmation
      await expect(page.locator('[data-testid="sign-out-confirmation"]')).toBeVisible({ timeout: 5000 });
      await page.click('[data-testid="confirm-sign-out"]');
    } catch {
      // Immediate redirect is also valid
    }

    // Verify redirect to login page
    await page.waitForURL('**/login**', { timeout: 15000 });
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
  });

  test('should display responsive sidebar behavior', async ({ page }) => {
    // Wait for sidebar to be visible in desktop view
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });

    // Test mobile view behavior
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500); // Allow for responsive transition

    // Check if sidebar becomes collapsible or hidden
    const mobileMenuButton = page.locator('[data-testid="mobile-menu-button"]');
    if (await mobileMenuButton.isVisible()) {
      // Mobile menu available
      await expect(mobileMenuButton).toBeVisible();
      
      await mobileMenuButton.click();
      await expect(page.locator('[data-testid="mobile-sidebar"]')).toBeVisible();
      
      // Close mobile menu
      const closeMobileMenu = page.locator('[data-testid="close-mobile-menu"]');
      if (await closeMobileMenu.isVisible()) {
        await closeMobileMenu.click();
      }
    }

    // Return to desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(500);
    
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
  });
});