import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('AI Edit Responses', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display edit description instead of raw JSON during streaming', async ({ page }) => {
    // Create a new document using test utils
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Open AI chat panel
    await page.click('button[title*="AI Chat"]')
    await expect(page.locator('[data-testid="ai-chat-panel"]')).toBeVisible()
    
    // Mock the AI response to return an edit response
    await page.route('**/api/ai/chat', async route => {
      const encoder = new TextEncoder()
      const stream = new ReadableStream({
        start(controller) {
          // Simulate streaming JSON edit response
          const editResponse = {
            type: 'edit',
            description: 'Added a test paragraph to the document',
            patch: [
              {
                op: 'add',
                path: '/content/0',
                value: {
                  type: 'paragraph',
                  content: [{ type: 'text', text: 'This is a test paragraph added by AI.' }]
                }
              }
            ]
          }
          
          const jsonString = JSON.stringify(editResponse)
          
          // Stream the JSON character by character to simulate real streaming
          for (let i = 0; i < jsonString.length; i++) {
            const chunk = {
              type: 'stream',
              delta: jsonString[i],
              timestamp: new Date().toISOString()
            }
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunk)}\n\n`))
          }
          
          // Send final response
          const finalResponse = {
            type: 'final',
            aiResponse: {
              type: 'edit',
              message: {
                role: 'assistant',
                content: editResponse.description,
                timestamp: new Date().toISOString()
              },
              patch: editResponse.patch,
              description: editResponse.description,
              success: true
            }
          }
          
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalResponse)}\n\n`))
          controller.enqueue(encoder.encode('data: [DONE]\n\n'))
          controller.close()
        }
      })
      
      // Convert stream to string for Playwright
      const reader = stream.getReader()
      const decoder = new TextDecoder()
      let responseBody = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          responseBody += decoder.decode(value, { stream: true })
        }
      } finally {
        reader.releaseLock()
      }

      await route.fulfill({
        status: 200,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
        body: responseBody
      })
    })
    
    // Send a message that should trigger an edit
    const input = page.locator('input[placeholder*="Ask the AI assistant"]')
    await input.fill('Add a test paragraph to the document')
    await page.click('button:has(.lucide-send)')

    // Wait for response with generous timeout
    await page.waitForTimeout(2000) // Allow time for streaming to start

    // Check that we see the edit description, not raw JSON
    await expect(page.locator('[data-testid="ai-chat-panel"]')).toContainText('✏️ Added a test paragraph to the document', { timeout: 60000 })

    // Ensure we don't see raw JSON
    await expect(page.locator('[data-testid="ai-chat-panel"]')).not.toContainText('"type":"edit"')
    await expect(page.locator('[data-testid="ai-chat-panel"]')).not.toContainText('"patch"')

    // Verify the final message shows the description
    await expect(page.locator('[data-testid="ai-chat-panel"]')).toContainText('✏️ Added a test paragraph to the document', { timeout: 60000 })
  })

  test('should handle regular chat responses normally', async ({ page }) => {
    // Create a new document using test utils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Open AI chat panel
    await page.click('button[title*="AI Chat"]')
    await expect(page.locator('[data-testid="ai-chat-panel"]')).toBeVisible()
    
    // Mock a regular chat response
    await page.route('**/api/ai/chat', async route => {
      const encoder = new TextEncoder()
      const stream = new ReadableStream({
        start(controller) {
          const chatResponse = {
            type: 'chat',
            content: 'This is a regular chat response from the AI assistant.'
          }
          
          const jsonString = JSON.stringify(chatResponse)
          
          // Stream the JSON
          for (let i = 0; i < jsonString.length; i++) {
            const chunk = {
              type: 'stream',
              delta: jsonString[i],
              timestamp: new Date().toISOString()
            }
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunk)}\n\n`))
          }
          
          // Send final response
          const finalResponse = {
            type: 'final',
            aiResponse: {
              type: 'chat',
              message: {
                role: 'assistant',
                content: chatResponse.content,
                timestamp: new Date().toISOString()
              },
              success: true
            }
          }
          
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalResponse)}\n\n`))
          controller.enqueue(encoder.encode('data: [DONE]\n\n'))
          controller.close()
        }
      })
      
      // Convert stream to string for Playwright
      const reader = stream.getReader()
      const decoder = new TextDecoder()
      let responseBody = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          responseBody += decoder.decode(value, { stream: true })
        }
      } finally {
        reader.releaseLock()
      }

      await route.fulfill({
        status: 200,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
        body: responseBody
      })
    })
    
    // Send a regular chat message
    const input = page.locator('input[placeholder*="Ask the AI assistant"]')
    await input.fill('What is ESG reporting?')
    await page.click('button:has(.lucide-send)')

    // Wait for response with generous timeout
    await page.waitForTimeout(2000) // Allow time for streaming

    // Verify we see the chat response
    await expect(page.locator('[data-testid="ai-chat-panel"]')).toContainText('This is a regular chat response from the AI assistant.', { timeout: 60000 })
  })
})
