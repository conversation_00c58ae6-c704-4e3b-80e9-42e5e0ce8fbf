import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report State Management', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should show group loading state when children are loading', async ({ page }) => {
    // Add console logging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    let isFirstCall = true

    // Mock API calls to report endpoints with delay for first call
    page.route('**/api/report/entity/**', route => {
      if (isFirstCall) {
        isFirstCall = false
        // Delay the first response to simulate loading state
        setTimeout(() => {
          route.fulfill({
            status: 200,
            body: JSON.stringify({
              text: '<p>Test report content</p>',
              citations: []
            })
          })
        }, 1500)
      } else {
        // Resolve other calls immediately
        route.fulfill({
          status: 200,
          body: JSON.stringify({
            text: '<p>Test report content</p>',
            citations: []
          })
        })
      }
    })

    // Create a document from template using the correct flow  
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for editor to load
    await testUtils.waitForEditor()

    // Wait for report groups to be visible
    await expect(page.locator('.report-group').first()).toBeVisible({ timeout: 15000 })

    // Check that the group shows loading state initially
    const reportGroup = page.locator('.report-group').first()
    
    // Should show loading indicator (spinning icon in the group header)
    const groupHeader = reportGroup.locator('.absolute.-top-3').first()
    await expect(groupHeader.locator('.animate-spin')).toBeVisible({ timeout: 10000 })

    // Wait for loading to complete
    await page.waitForTimeout(2000)

    // Group should now show loaded state - loading icon should disappear
    await expect(groupHeader.locator('.animate-spin')).not.toBeVisible({ timeout: 10000 })
    
    // Should show success indicator
    await expect(groupHeader.locator('span').filter({ hasText: '✓' })).toBeVisible({ timeout: 5000 })
  })

  test('should show group error state when child fails', async ({ page }) => {
    // Add console logging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    // Mock API to return error for first call
    let callCount = 0
    page.route('**/api/report/entity/**', route => {
      callCount++
      if (callCount === 1) {
        // First call fails
        route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Test error' })
        })
      } else {
        // Other calls succeed
        route.fulfill({
          status: 200,
          body: JSON.stringify({
            text: '<p>Test report content</p>',
            citations: []
          })
        })
      }
    })

    // Create a document from template using the correct flow  
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Wait for editor to load
    await testUtils.waitForEditor()

    // Wait for report groups to be visible
    await expect(page.locator('.report-group').first()).toBeVisible({ timeout: 15000 })

    // Wait for error state to propagate
    await page.waitForTimeout(2000)

    // Check that the group shows error state
    const reportGroup = page.locator('.report-group').first()
    const groupHeader = reportGroup.locator('.absolute.-top-3').first()
    
    // Should show error icon in header
    await expect(groupHeader.locator('.lucide-alert-triangle, span').filter({ hasText: '⚠' })).toBeVisible({ timeout: 10000 })
  })

  test('should wait for dependencies before generating summary', async ({ page }) => {
    // Add console logging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    let sectionLoaded = false
    let summaryRequested = false

    page.route('**/api/report/entity/**', route => {
      // Simulate section loading with delay
      setTimeout(() => {
        sectionLoaded = true
        route.fulfill({
          status: 200,
          body: JSON.stringify({
            text: '<p>Section content loaded</p>',
            citations: []
          })
        })
      }, 1000)
    })

    page.route('**/api/report/summarize', route => {
      summaryRequested = true
      // Summary should only be requested after section is loaded
      expect(sectionLoaded).toBe(true)
      
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Summary of section content</p>',
          citations: []
        })
      })
    })

    // Create a document from template using the correct flow  
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Wait for editor to load
    await testUtils.waitForEditor()
    
    // Wait for report components to be visible
    await expect(page.locator('.report-group, .report-section, .report-summary').first()).toBeVisible({ timeout: 15000 })
    
    // Wait for all loading to complete
    await page.waitForTimeout(3000)

    // Verify that summary was requested after section loaded
    expect(summaryRequested).toBe(true)
    
    // Check that summary exists and shows expected content
    const summaryComponent = page.locator('.report-summary').first()
    if (await summaryComponent.isVisible()) {
      // Summary should show content once dependencies are loaded
      await expect(summaryComponent).toBeVisible({ timeout: 5000 })
    }
  })

  test('should update parent group state when child state changes', async ({ page }) => {
    // Add console logging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    // Mock API responses
    page.route('**/api/report/entity/**', route => {
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content</p>',
          citations: []
        })
      })
    })

    // Create a document from template using the correct flow  
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Wait for editor to load
    await testUtils.waitForEditor()

    // Wait for report components to be visible
    await expect(page.locator('.report-group').first()).toBeVisible({ timeout: 15000 })
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 15000 })

    // Wait for initial loading to complete
    await page.waitForTimeout(2000)
    
    const reportGroup = page.locator('.report-group').first()
    const reportSection = page.locator('.report-section').first()
    
    // Try to manually refresh a section to trigger state change
    const menuTrigger = reportSection.locator('[data-testid="report-section-menu-trigger"]').first()
    
    if (await menuTrigger.isVisible()) {
      await menuTrigger.click()
      
      // Wait for menu to appear and look for refresh option
      const refreshButton = page.locator('text=Refresh').first()
      if (await refreshButton.isVisible({ timeout: 2000 })) {
        await refreshButton.click()
        
        // Check that group state updates during refresh
        const groupHeader = reportGroup.locator('.absolute.-top-3').first()
        
        // Should show loading indicator during refresh
        await expect(groupHeader.locator('.animate-spin')).toBeVisible({ timeout: 5000 })
        
        // Wait for refresh to complete
        await page.waitForTimeout(1500)
        
        // Loading indicator should disappear
        await expect(groupHeader.locator('.animate-spin')).not.toBeVisible({ timeout: 5000 })
      }
    }
  })
})
