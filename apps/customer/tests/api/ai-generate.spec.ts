import { expect, test } from '@playwright/test'
import { login } from '../helpers/auth'

test.describe('AI Generate API (Real)', () => {
  const API_ENDPOINT = '/api/ai/generate'

  // Login user before each test to access the real API
  test.beforeEach(async ({ page }) => {
    await login(page)
  })

  test('should fix spelling errors correctly with real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Fix spelling and grammar errors',
        selectedText: 'The qick brown fox jumps over the lasy dog. This sentance has mispellings.',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()

    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)

    // Real AI should fix spelling errors - check for corrected words
    expect(data.text.toLowerCase()).toContain('quick')
    expect(data.text.toLowerCase()).toContain('lazy')
    expect(data.text.toLowerCase()).not.toContain('qick')
    expect(data.text.toLowerCase()).not.toContain('lasy')
  })

  test('should improve writing clarity with real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Improve writing clarity and flow',
        selectedText: 'The thing is that we need to do the stuff because it is important for the reasons.',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()

    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)

    // Real AI should improve vague language
    expect(data.text.toLowerCase()).not.toContain('the thing is')
    expect(data.text.toLowerCase()).not.toContain('the stuff')
  })

  test('should handle grammar corrections with real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Fix grammar errors',
        selectedText: 'Me and my friend goes to the store yesterday. We was looking for some books.',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()

    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)

    // Real AI should fix grammar - check for improved text
    expect(data.text.toLowerCase()).not.toContain('me and my friend goes')
    expect(data.text.toLowerCase()).not.toContain('we was')
  })

  test('should make text more concise with real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Make this text shorter and more concise',
        selectedText: 'In my personal opinion, I believe that it is absolutely essential and critically important that we should definitely consider the possibility of maybe implementing this solution in the near future.',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()

    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)

    // Real AI should make text more concise
    expect(data.text.length).toBeLessThan(150) // More realistic expectation for real AI
    expect(data.text.toLowerCase()).not.toContain('in my personal opinion')
  })

  test('should handle context from document content with real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Improve this sentence to fit the document context',
        selectedText: 'The company did good.',
        documentContent: 'This is a formal business report analyzing quarterly performance metrics and strategic initiatives.',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()

    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)

    // Real AI should use more formal language
    expect(data.text.toLowerCase()).not.toContain('did good')
  })

  test('should return error for missing prompt with real API', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        selectedText: 'Some text to work with',
        stream: false
      }
    })

    expect(response.status()).toBe(400)
    const data = await response.json()

    expect(data.error).toBe('Prompt is required')
    expect(data.success).toBeFalsy()
  })

  test('should handle empty selected text gracefully with real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Generate a professional introduction',
        selectedText: '',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()

    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)
  })

  test('should work with additional context using real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Rewrite this to be more technical',
        selectedText: 'The computer is broken.',
        context: 'This is for an IT incident report',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()

    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)

    // Real AI should use more technical language
    expect(data.text.toLowerCase()).not.toContain('broken')
  })

  test('should handle streaming responses from real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Fix spelling errors',
        selectedText: 'This is a test sentance with mispellings.',
        stream: true
      }
    })

    expect(response.status()).toBe(200)
    expect(response.headers()['content-type']).toContain('text/plain')

    const text = await response.text()
    expect(text).toBeTruthy()
    expect(text.length).toBeGreaterThan(10)
  })

  test('should preserve formatting in structured text with real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Fix spelling errors only',
        selectedText: '1. First itme\n2. Secnd item\n3. Thrd item',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()

    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)

    // Real AI should maintain list structure and fix spelling
    expect(data.text).toMatch(/1\..*2\..*3\./s) // Check for numbered list structure
    expect(data.text.toLowerCase()).toContain('item')
  })
})
