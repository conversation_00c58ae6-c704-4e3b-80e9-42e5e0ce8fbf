import { expect, test } from '@playwright/test'
import { getTestTemplate } from './test-config'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report System', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });
    
    await testUtils.login();
  });

  test('should display document templates', async ({ page }) => {
    // Navigate to documents page
    await page.goto('/customer/documents');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Look for the "New Document" button
    await expect(page.locator('text=New Document')).toBeVisible();

    // Click to open template selection
    await page.click('text=New Document');

    // Check if template dialog opens
    await expect(page.locator('[role="dialog"]')).toBeVisible();

    // Check if EKO Report template is available (use secondary template which is EKO Report)
    const templateName = getTestTemplate('secondary'); // This returns 'EKO Report'
    await expect(page.getByRole('heading', { name: templateName })).toBeVisible();
  });

  test('should create document from template', async ({ page }) => {
    // Create new document from template using test utils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Check if report components are present
    await expect(page.locator('.report-summary').first()).toBeVisible();
    await expect(page.locator('.report-group').first()).toBeVisible();
    await expect(page.locator('.report-section').first()).toBeVisible();
  });

  test('should display report component controls', async ({ page }) => {
    // Increase test timeout for complex document creation
    test.setTimeout(300000); // 5 minutes

    // Create a document first
    await testUtils.createDocumentFromTemplate(getTestTemplate('secondary'));

    // Wait for report components to load with longer timeout
    await page.waitForSelector('.report-section', { timeout: 30000 });

    // Check if report section has controls
    const reportSection = page.locator('.report-section').first();
    await expect(reportSection).toBeVisible();

    // Check for dropdown menu trigger - look for data-testid first, fallback to generic selector
    const menuTrigger = reportSection.locator('[data-testid="report-section-menu-trigger"], button').first();
    await expect(menuTrigger).toBeVisible({ timeout: 15000 });

    // Click menu to open dropdown
    await menuTrigger.click();

    // Check menu items with timeout
    await expect(page.locator('text=Refresh')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Configure')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Lock')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Preserve')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Delete')).toBeVisible({ timeout: 10000 });
  });

  test('should open configuration dialog', async ({ page }) => {
    // Increase test timeout for complex document creation
    test.setTimeout(300000); // 5 minutes

    // Create a document first
    await testUtils.createDocumentFromTemplate(getTestTemplate('secondary'));

    // Wait for report components to load with longer timeout
    await page.waitForSelector('.report-section', { timeout: 30000 });

    // Open configuration for a report section using TestUtils approach
    await testUtils.openComponentConfig('.report-section');

    // Check for configuration form fields with timeouts
    await expect(page.locator('input[placeholder*="component-id"]')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('input[placeholder*="Component Title"]')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('textarea[placeholder*="Additional instructions"]')).toBeVisible({ timeout: 10000 });

    // Check for dropdowns
    await expect(page.locator('text=Select Entity')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Select Model')).toBeVisible({ timeout: 10000 });
  });

  test('should add new report components from toolbar', async ({ page }) => {
    // Increase test timeout for complex document creation
    test.setTimeout(300000); // 5 minutes

    // Create a document first
    await testUtils.createDocumentFromTemplate(getTestTemplate('secondary'));

    // Wait for editor to load with longer timeout
    await testUtils.waitForEditor(30000);

    // Check if toolbar has report component buttons with timeouts
    await expect(page.locator('button[title*="Insert Report Section"]')).toBeVisible({ timeout: 15000 });
    await expect(page.locator('button[title*="Insert Report Group"]')).toBeVisible({ timeout: 15000 });
    await expect(page.locator('button[title*="Insert Report Summary"]')).toBeVisible({ timeout: 15000 });

    // Test adding a report group using TestUtils
    await testUtils.addReportComponent('group', {
      id: 'test-group',
      title: 'Test Group'
    });

    // Check if new group was added
    await expect(page.locator('.report-group').last()).toBeVisible({ timeout: 20000 });

    // Verify the component has our test ID in its header
    await expect(page.locator('text=test-group')).toBeVisible({ timeout: 15000 });
  });

  test('should handle report summary dependencies', async ({ page }) => {
    // Increase test timeout for complex document creation
    test.setTimeout(300000); // 5 minutes

    // Create a document first
    await testUtils.createDocumentFromTemplate(getTestTemplate('secondary'));

    // Find a report summary component with timeout
    const reportSummary = page.locator('.report-summary').first();
    await expect(reportSummary).toBeVisible({ timeout: 20000 });

    // Check if it shows dependency information
    await expect(reportSummary.locator('text=Summarizes:')).toBeVisible({ timeout: 10000 });

    // Open configuration using TestUtils
    await testUtils.openComponentConfig('.report-summary');

    // Look for component selection interface
    await expect(page.locator('text=Components to Summarize')).toBeVisible({ timeout: 10000 });
  });

  test('should save and load document content', async ({ page }) => {
    // Increase test timeout for complex document creation
    test.setTimeout(300000); // 5 minutes

    // Create a document first
    const documentId = await testUtils.createDocumentFromTemplate(getTestTemplate('secondary'));

    // Add some content to the editor using TestUtils
    await testUtils.typeInEditor('This is test content for the report.');

    // Wait for auto-save with longer timeout
    await page.waitForTimeout(5000);

    // Navigate away and back
    await page.goto('/customer/dashboard');
    await page.goto(`/customer/documents/${documentId}`);

    // Check if content was saved with timeout
    await expect(page.locator('text=This is test content for the report.')).toBeVisible({ timeout: 15000 });
  });

  test('should handle component loading states', async ({ page }) => {
    // Increase test timeout for complex document creation
    test.setTimeout(300000); // 5 minutes

    // Create a document first
    await testUtils.createDocumentFromTemplate(getTestTemplate('secondary'));

    // Use TestUtils to wait for component loading
    await testUtils.waitForComponentLoading('.report-section');
  });

  test('should refresh report component content', async ({ page }) => {
    // Increase test timeout for complex document creation
    test.setTimeout(300000); // 5 minutes

    // Create a document first
    await testUtils.createDocumentFromTemplate(getTestTemplate('secondary'));

    // Use TestUtils to test component refresh
    await testUtils.testComponentRefresh('.report-section');
  });

  test('should lock and preserve components', async ({ page }) => {
    // Increase test timeout for complex document creation
    test.setTimeout(300000); // 5 minutes

    // Create a document first
    await testUtils.createDocumentFromTemplate(getTestTemplate('secondary'));

    // Wait for components to load with longer timeout
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 30000 });

    // Lock a component using TestUtils
    await testUtils.performComponentAction('lock', '.report-section');

    // Check if component is locked
    await testUtils.checkComponentState('locked', '.report-section');

    // Test preserve functionality on a different component
    await testUtils.performComponentAction('preserve', '.report-summary');

    // Check if component is preserved
    await testUtils.checkComponentState('preserved', '.report-summary');
  });
});
