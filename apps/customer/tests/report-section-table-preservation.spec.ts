import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report Section Table Preservation', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
    
    // Create a new document for testing
    await testUtils.createDocumentFromTemplate()
    
    // Wait for editor to load is handled in createDocumentFromTemplate

    // Make sure entity and run are selected (required for report sections to work)
    // The test document already has entity/run context, but let's ensure it's fully loaded
    await page.waitForTimeout(2000)
  })

  test('should preserve table structure when loading content via report section', async ({ page }) => {

    // Set up mock response with table content first - intercept the actual endpoint pattern
    await page.route(/\/api\/report\/entity\/.*\/.*\/test-table-data/, async route => {
      console.log('Mock route intercepted:', route.request().url())
      const tableMarkdown = `
# Test Section with Table

Here's a complex table that should be preserved:

<table class="emissions-table" style="width: 100%; border-collapse: collapse;">
  <thead>
    <tr style="background-color: #f5f5f5;">
      <th style="padding: 8px; border: 1px solid #ddd;">Category</th>
      <th style="padding: 8px; border: 1px solid #ddd;">Emission (KTCO2e)</th>
      <th style="padding: 8px; border: 1px solid #ddd;">% of Total</th>
      <th style="padding: 8px; border: 1px solid #ddd;">Notes</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="padding: 8px; border: 1px solid #ddd;"><strong>Scope 1</strong></td>
      <td style="padding: 8px; border: 1px solid #ddd;">219</td>
      <td style="padding: 8px; border: 1px solid #ddd;">2%</td>
      <td style="padding: 8px; border: 1px solid #ddd;">Direct emissions</td>
    </tr>
    <tr>
      <td style="padding: 8px; border: 1px solid #ddd;"><strong>Scope 2</strong></td>
      <td style="padding: 8px; border: 1px solid #ddd;">370</td>
      <td style="padding: 8px; border: 1px solid #ddd;">4%</td>
      <td style="padding: 8px; border: 1px solid #ddd;">Indirect emissions</td>
    </tr>
    <tr>
      <td style="padding: 8px; border: 1px solid #ddd;"><strong>Scope 3</strong></td>
      <td style="padding: 8px; border: 1px solid #ddd;">8,245</td>
      <td style="padding: 8px; border: 1px solid #ddd;">94%</td>
      <td style="padding: 8px; border: 1px solid #ddd;">Value chain emissions</td>
    </tr>
  </tbody>
</table>

And some text after the table.
`
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          text: tableMarkdown,
          citations: [],
        }),
      })
    })

    // Also log any API requests for debugging
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        console.log('API request:', request.url())
      }
    })

    page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log('API response:', response.url(), response.status())
      }
    })

    // Monitor console errors and logs that might indicate content insertion problems
    page.on('console', msg => {
      if (msg.type() === 'error' || msg.text().includes('Report section') || msg.text().includes('test-table-section')) {
        console.log(`Browser console ${msg.type()}: ${msg.text()}`)
      }
    })

    // Insert a report section using toolbar button
    await page.click('button[title*="Insert Report Section"]')

    // Wait for configuration dialog to appear
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    // Fill in configuration using exact field selectors
    await page.fill('input[placeholder="component-id"]', 'test-table-section')
    await page.fill('input[placeholder="Component Title"]', 'Test Table Section')

    // Try to fill the Generated Endpoint field by its actual placeholder with entity/run placeholders
    await page.fill('input[placeholder="Endpoint will be generated automatically"]', '/api/report/entity/[ENTITY_ID]/[RUN_ID]/test-table-data')

    // Confirm the configuration
    await page.click('button:has-text("Create Component")')

    // Wait for dialog to close and component to be created
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    await page.waitForSelector('.report-section', { timeout: 5000 })

    // Wait for the specific report section component to be properly initialized
    // Target the specific report section with our test ID (be more specific)
    const testReportSection = page.locator('.report-section').filter({ hasText: /test-table-section/ }).first()
    await expect(testReportSection).toBeVisible()

    // Wait a bit for the report manager to initialize
    await page.waitForTimeout(3000)

    // Check if we need to manually trigger the refresh for our specific section
    const refreshButton = testReportSection.getByTestId('report-section-menu-trigger')
    await refreshButton.click()

    // Wait for menu to appear and check if refresh is enabled
    const refreshItem = page.getByRole('menuitem', { name: 'Refresh' })
    const isRefreshDisabled = await refreshItem.getAttribute('data-disabled')
    console.log('Refresh button disabled?', isRefreshDisabled)

    if (isRefreshDisabled !== 'true') {
      await refreshItem.click()
      console.log('Clicked refresh button')
    } else {
      console.log('Refresh button is disabled, content might already be loading')
    }

    // Give more time for the async content loading to complete
    await page.waitForTimeout(5000)

    // First, let's check if our test section has any content at all
    const sectionHTML = await testReportSection.innerHTML()
    console.log('Test section HTML:', sectionHTML.substring(0, 500))

    // Wait for content to load and check various possible selectors
    try {
      await page.waitForSelector('.report-content', { timeout: 5000 })
      console.log('Found .report-content')
    } catch (error) {
      console.log('No .report-content found, checking if content was inserted directly...')

      // Check if the table was inserted directly into our test section
      const tableInOurSection = testReportSection.locator('table.emissions-table')
      const tableCount = await tableInOurSection.count()
      console.log('Found tables with emissions-table class in our test section:', tableCount)

      if (tableCount > 0) {
        console.log('Table found in test section! Testing table preservation...')
        // Continue with the test using the table in our section instead of .report-content
      } else {
        // Check if there are any elements with our test table content
        const testText = testReportSection.locator('text=Here\'s a complex table that should be preserved:')
        const testTextCount = await testText.count()
        console.log('Found test text occurrences in our section:', testTextCount)

        if (testTextCount === 0) {
          console.log('No content found, content loading may have failed')
          throw error
        } else {
          console.log('Content found but table missing, table may have been corrupted')
          throw error
        }
      }
    }

    // Verify table structure is preserved - check both possible locations
    let tableInReport = page.locator('.report-content table.emissions-table')
    const reportContentExists = await page.locator('.report-content').count() > 0

    if (!reportContentExists) {
      // If no .report-content, look for table directly in our test section
      tableInReport = testReportSection.locator('table.emissions-table')
      console.log('Using table from test section directly')
    }
    
    await expect(tableInReport).toBeVisible()

    // Count columns - should have exactly 4 columns
    const headerCells = tableInReport.locator('thead th')
    await expect(headerCells).toHaveCount(4)

    // Verify column headers are correct
    await expect(headerCells.nth(0)).toContainText('Category')
    await expect(headerCells.nth(1)).toContainText('Emission (KTCO2e)')
    await expect(headerCells.nth(2)).toContainText('% of Total')
    await expect(headerCells.nth(3)).toContainText('Notes')

    // Count data rows - should have exactly 3 data rows
    const dataRows = tableInReport.locator('tbody tr')
    await expect(dataRows).toHaveCount(3)

    // Verify data row content
    await expect(dataRows.nth(0).locator('td').nth(0)).toContainText('Scope 1')
    await expect(dataRows.nth(0).locator('td').nth(1)).toContainText('219')
    await expect(dataRows.nth(0).locator('td').nth(2)).toContainText('2%')
    await expect(dataRows.nth(0).locator('td').nth(3)).toContainText('Direct emissions')

    await expect(dataRows.nth(2).locator('td').nth(0)).toContainText('Scope 3')
    await expect(dataRows.nth(2).locator('td').nth(1)).toContainText('8,245')
    await expect(dataRows.nth(2).locator('td').nth(2)).toContainText('94%')

    // Verify table attributes are preserved
    await expect(tableInReport).toHaveAttribute('class', 'emissions-table')
    await expect(tableInReport).toHaveAttribute('style', 'width: 100%; border-collapse: collapse;')

    // Verify cell styling is preserved (check a few sample cells)
    const firstHeaderCell = tableInReport.locator('thead th').first()
    await expect(firstHeaderCell).toHaveAttribute('style', 'padding: 8px; border: 1px solid #ddd;')

    const firstDataCell = tableInReport.locator('tbody td').first()
    await expect(firstDataCell).toHaveAttribute('style', 'padding: 8px; border: 1px solid #ddd;')

    // Verify nested HTML is preserved (bold text)
    await expect(dataRows.nth(0).locator('td strong')).toContainText('Scope 1')
    await expect(dataRows.nth(1).locator('td strong')).toContainText('Scope 2')
    await expect(dataRows.nth(2).locator('td strong')).toContainText('Scope 3')

    // Verify surrounding content is also present - check in both locations
    if (reportContentExists) {
      await expect(page.locator('.report-content')).toContainText('Here\'s a complex table that should be preserved:')
      await expect(page.locator('.report-content')).toContainText('And some text after the table.')
    } else {
      await expect(testReportSection).toContainText('Here\'s a complex table that should be preserved:')
      await expect(testReportSection).toContainText('And some text after the table.')
    }
  })

  test('should preserve multiple tables in same report section', async ({ page }) => {
    // Set up mock response first

    // Mock response with multiple tables
    await page.route('/api/multi-table-data', async route => {
      const multiTableMarkdown = `
# Section with Multiple Tables

## First Table
<table class="table-one">
  <tr><td>Table 1 Cell 1</td><td>Table 1 Cell 2</td></tr>
  <tr><td>Table 1 Cell 3</td><td>Table 1 Cell 4</td></tr>
</table>

Some text between tables.

## Second Table
<table class="table-two" style="border: 2px solid red;">
  <thead>
    <tr><th>Header A</th><th>Header B</th><th>Header C</th></tr>
  </thead>
  <tbody>
    <tr><td>A1</td><td>B1</td><td>C1</td></tr>
    <tr><td>A2</td><td>B2</td><td>C2</td></tr>
  </tbody>
</table>

Final text.
`
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          text: multiTableMarkdown,
          citations: [],
        }),
      })
    })

    // Insert a report section using toolbar button
    await page.click('button[title*="Insert Report Section"]')

    // Wait for configuration dialog to appear
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    // Fill in configuration
    await page.fill('input[placeholder="component-id"]', 'multi-table-section')
    await page.fill('input[placeholder="Component Title"]', 'Multiple Tables Section')
    await page.fill('input[placeholder="Generated Endpoint"]', '/api/multi-table-data')

    // Confirm the configuration
    await page.click('button:has-text("Create Component")')

    // Wait for dialog to close and component to be created
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    await page.waitForSelector('.report-section', { timeout: 5000 })

    // Wait for content to load
    await page.waitForSelector('.report-content', { timeout: 10000 })

    // Verify both tables exist
    const firstTable = page.locator('.report-content table.table-one')
    const secondTable = page.locator('.report-content table.table-two')

    await expect(firstTable).toBeVisible()
    await expect(secondTable).toBeVisible()

    // Verify first table structure
    await expect(firstTable.locator('td')).toHaveCount(4)
    await expect(firstTable.locator('tr').nth(0).locator('td').nth(0)).toContainText('Table 1 Cell 1')
    await expect(firstTable.locator('tr').nth(1).locator('td').nth(1)).toContainText('Table 1 Cell 4')

    // Verify second table structure  
    await expect(secondTable.locator('th')).toHaveCount(3)
    await expect(secondTable.locator('tbody td')).toHaveCount(6)
    await expect(secondTable.locator('th').nth(0)).toContainText('Header A')
    await expect(secondTable.locator('tbody tr').nth(0).locator('td').nth(0)).toContainText('A1')

    // Verify table attributes
    await expect(secondTable).toHaveAttribute('style', 'border: 2px solid red;')

    // Verify surrounding text
    await expect(page.locator('.report-content')).toContainText('Some text between tables.')
    await expect(page.locator('.report-content')).toContainText('Final text.')
  })

  test('should preserve tables with charts together', async ({ page }) => {
    // Set up mock response first

    // Mock response with table and chart
    await page.route('/api/mixed-content', async route => {
      const mixedContent = `
# Mixed Content Test

<table class="data-table">
  <tr><th>Metric</th><th>Value</th></tr>
  <tr><td>Revenue</td><td>$1.2M</td></tr>
  <tr><td>Profit</td><td>$200K</td></tr>
</table>

<chart>
{
  "type": "bar",
  "data": {
    "labels": ["Revenue", "Profit"],
    "datasets": [{
      "label": "Financial Data",
      "data": [1200000, 200000]
    }]
  }
}
</chart>

End of section.
`
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          text: mixedContent,
          citations: [],
        }),
      })
    })

    // Insert a report section using toolbar button
    await page.click('button[title*="Insert Report Section"]')

    // Wait for configuration dialog to appear
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    // Fill in configuration
    await page.fill('input[placeholder="component-id"]', 'mixed-content-section')
    await page.fill('input[placeholder="Component Title"]', 'Mixed Content Section')
    await page.fill('input[placeholder="Generated Endpoint"]', '/api/mixed-content')

    // Confirm the configuration
    await page.click('button:has-text("Create Component")')

    // Wait for dialog to close and component to be created
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    await page.waitForSelector('.report-section', { timeout: 5000 })

    // Wait for content to load
    await page.waitForSelector('.report-content', { timeout: 10000 })

    // Verify table is preserved
    const table = page.locator('.report-content table.data-table')
    await expect(table).toBeVisible()
    await expect(table.locator('th')).toHaveCount(2)
    await expect(table.locator('td')).toHaveCount(4)
    await expect(table.locator('tr').nth(1).locator('td').nth(1)).toContainText('$1.2M')

    // Verify chart is preserved
    const chart = page.locator('.report-content chart')
    await expect(chart).toBeVisible()
    await expect(chart).toContainText('"type": "bar"')
    await expect(chart).toContainText('"Financial Data"')

    // Verify surrounding text
    await expect(page.locator('.report-content')).toContainText('End of section.')
  })
})
