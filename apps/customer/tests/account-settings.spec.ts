import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';

test.describe('Account Settings Page', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to account settings
    await page.goto('/customer/account');
    await page.waitForLoadState('networkidle');
  });

  test('should display account settings form with profile fields', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Check profile form elements
    await expect(page.locator('[data-testid="profile-form"]')).toBeVisible();
    await expect(page.locator('[data-testid="full-name-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="username-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="website-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="organization-input"]')).toBeVisible();

    // Check save button
    await expect(page.locator('[data-testid="save-profile-button"]')).toBeVisible();
  });

  test('should update profile information successfully', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Update profile fields
    await page.fill('[data-testid="full-name-input"]', 'Updated Test User');
    await page.fill('[data-testid="username-input"]', 'updated_test_user');
    await page.fill('[data-testid="website-input"]', 'https://example.com');
    await page.fill('[data-testid="organization-input"]', 'Test Organization');

    // Save changes
    await page.click('[data-testid="save-profile-button"]');

    // Check for success message
    await expect(page.locator('[data-testid="profile-success-message"]')).toBeVisible({ timeout: 10000 });

    // Verify fields retain updated values
    expect(await page.inputValue('[data-testid="full-name-input"]')).toBe('Updated Test User');
    expect(await page.inputValue('[data-testid="username-input"]')).toBe('updated_test_user');
  });

  test('should display avatar upload and editing functionality', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Check avatar section
    await expect(page.locator('[data-testid="avatar-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="current-avatar"]')).toBeVisible();
    await expect(page.locator('[data-testid="upload-avatar-button"]')).toBeVisible();

    // Test avatar upload trigger
    const uploadButton = page.locator('[data-testid="upload-avatar-button"]');
    await uploadButton.click();

    // Check for file input or upload modal
    const fileInput = page.locator('[data-testid="avatar-file-input"]');
    const uploadModal = page.locator('[data-testid="avatar-upload-modal"]');

    if (await fileInput.isVisible()) {
      await expect(fileInput).toBeVisible();
    } else if (await uploadModal.isVisible()) {
      await expect(uploadModal).toBeVisible();
      await expect(uploadModal.locator('[data-testid="file-drop-zone"]')).toBeVisible();
    }
  });

  test('should provide avatar editing with zoom and rotation controls', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Trigger avatar upload/edit
    await page.click('[data-testid="upload-avatar-button"]');

    // Check for avatar editing modal
    const editModal = page.locator('[data-testid="avatar-edit-modal"]');
    if (await editModal.isVisible()) {
      await expect(editModal).toBeVisible();

      // Check editing controls
      const zoomSlider = editModal.locator('[data-testid="avatar-zoom-slider"]');
      const rotateButton = editModal.locator('[data-testid="avatar-rotate-button"]');
      const cropArea = editModal.locator('[data-testid="avatar-crop-area"]');

      if (await zoomSlider.isVisible()) {
        await expect(zoomSlider).toBeVisible();
        
        // Test zoom functionality
        await zoomSlider.fill('1.5');
      }

      if (await rotateButton.isVisible()) {
        await expect(rotateButton).toBeVisible();
        
        // Test rotation
        await rotateButton.click();
      }

      if (await cropArea.isVisible()) {
        await expect(cropArea).toBeVisible();
      }

      // Check save/cancel buttons
      await expect(editModal.locator('[data-testid="save-avatar-button"]')).toBeVisible();
      await expect(editModal.locator('[data-testid="cancel-avatar-button"]')).toBeVisible();

      // Cancel editing
      await editModal.locator('[data-testid="cancel-avatar-button"]').click();
      await expect(editModal).not.toBeVisible();
    }
  });

  test('should handle form validation correctly', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Clear required fields to test validation
    await page.fill('[data-testid="full-name-input"]', '');
    await page.fill('[data-testid="username-input"]', '');

    // Try to save
    await page.click('[data-testid="save-profile-button"]');

    // Check for validation errors
    const nameError = page.locator('[data-testid="full-name-error"]');
    const usernameError = page.locator('[data-testid="username-error"]');

    if (await nameError.isVisible()) {
      await expect(nameError).toBeVisible();
      const errorText = await nameError.textContent();
      expect(errorText?.toLowerCase()).toContain('required');
    }

    if (await usernameError.isVisible()) {
      await expect(usernameError).toBeVisible();
    }

    // Test invalid website URL
    await page.fill('[data-testid="website-input"]', 'invalid-url');
    await page.click('[data-testid="save-profile-button"]');

    const websiteError = page.locator('[data-testid="website-error"]');
    if (await websiteError.isVisible()) {
      await expect(websiteError).toBeVisible();
      const errorText = await websiteError.textContent();
      expect(errorText?.toLowerCase()).toContain('valid');
    }
  });

  test('should handle loading states during updates', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Update a field
    await page.fill('[data-testid="full-name-input"]', 'Loading Test User');

    // Click save and check for loading state
    await page.click('[data-testid="save-profile-button"]');

    // Check for loading indicator
    const loadingIndicator = page.locator('[data-testid="profile-loading"], [data-testid="save-profile-button"][disabled]');
    
    try {
      await expect(loadingIndicator.first()).toBeVisible({ timeout: 5000 });
      await expect(loadingIndicator.first()).not.toBeVisible({ timeout: 15000 });
    } catch {
      // Loading might be too fast to catch
      console.log('Profile update completed quickly');
    }

    // Should show success message eventually
    await expect(page.locator('[data-testid="profile-success-message"]')).toBeVisible({ timeout: 10000 });
  });

  test('should handle avatar storage to Supabase', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Get current avatar src
    const currentAvatar = page.locator('[data-testid="current-avatar"]');
    const initialSrc = await currentAvatar.getAttribute('src');

    // Trigger avatar upload
    await page.click('[data-testid="upload-avatar-button"]');

    // If upload modal appears, check for Supabase integration indicators
    const uploadModal = page.locator('[data-testid="avatar-upload-modal"]');
    if (await uploadModal.isVisible()) {
      // Check for upload progress
      const uploadProgress = uploadModal.locator('[data-testid="upload-progress"]');
      if (await uploadProgress.isVisible()) {
        await expect(uploadProgress).toBeVisible();
      }

      // Check for Supabase storage indicators
      const storageInfo = uploadModal.locator('[data-testid="storage-info"]');
      if (await storageInfo.isVisible()) {
        const storageText = await storageInfo.textContent();
        expect(storageText).toBeTruthy();
      }

      // Cancel upload for test
      const cancelButton = uploadModal.locator('[data-testid="cancel-upload-button"]');
      if (await cancelButton.isVisible()) {
        await cancelButton.click();
      }
    }

    // Verify avatar remains unchanged
    const finalSrc = await currentAvatar.getAttribute('src');
    expect(finalSrc).toBe(initialSrc);
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Test error handling by filling invalid data
    await page.fill('[data-testid="username-input"]', 'invalid@username#with$symbols');
    await page.fill('[data-testid="website-input"]', 'not-a-url');

    // Try to save
    await page.click('[data-testid="save-profile-button"]');

    // Check for error message
    const errorMessage = page.locator('[data-testid="profile-error-message"], [data-testid="form-error"]');
    
    try {
      await expect(errorMessage.first()).toBeVisible({ timeout: 10000 });
      const errorText = await errorMessage.first().textContent();
      expect(errorText?.toLowerCase()).toMatch(/error|invalid|failed/);
    } catch {
      // Individual field errors might be shown instead
      const fieldErrors = page.locator('[data-testid*="error"]');
      if (await fieldErrors.count() > 0) {
        await expect(fieldErrors.first()).toBeVisible();
      }
    }
  });

  test('should persist form data during navigation', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Fill form with data
    await page.fill('[data-testid="full-name-input"]', 'Persistence Test');
    await page.fill('[data-testid="organization-input"]', 'Test Org');

    // Navigate away and back
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    
    await page.goto('/customer/account');
    await page.waitForLoadState('networkidle');
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Check if unsaved changes are handled appropriately
    // (Either restored from cache or reset to saved values)
    const nameValue = await page.inputValue('[data-testid="full-name-input"]');
    expect(nameValue).toBeTruthy(); // Should have some value, whether cached or saved
  });

  test('should display user email and read-only fields', async ({ page }) => {
    // Wait for account page to load
    await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });

    // Check for email display (usually read-only)
    const emailField = page.locator('[data-testid="email-display"], [data-testid="email-input"]');
    if (await emailField.isVisible()) {
      await expect(emailField).toBeVisible();
      
      const emailValue = await emailField.textContent() || await emailField.inputValue();
      expect(emailValue).toContain('@');
      
      // If it's an input, it should be disabled/readonly
      if (await emailField.getAttribute('type') === 'email') {
        const isReadonly = await emailField.isDisabled() || await emailField.getAttribute('readonly') !== null;
        expect(isReadonly).toBe(true);
      }
    }

    // Check for other read-only fields like creation date
    const createdDate = page.locator('[data-testid="account-created-date"]');
    if (await createdDate.isVisible()) {
      await expect(createdDate).toBeVisible();
      const dateText = await createdDate.textContent();
      expect(dateText).toMatch(/\d{4}|\w{3}/); // Should contain year or month
    }
  });
});