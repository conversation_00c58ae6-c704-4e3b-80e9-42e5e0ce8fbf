import { expect, test } from '@playwright/test'
import { buildApiUrl, getTestEntity, getTestModel, getTestRun, getTestSection } from './test-config'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report API Endpoints', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  });

  test('should return JSON format from report endpoints', async ({ page }) => {
    // Use configurable test data
    const entity = getTestEntity('primary');
    const model = getTestModel('primary');
    const section = getTestSection(model, 'primary');
    const run = getTestRun('latest');

    // Test the model section endpoint
    const url = buildApiUrl('/report/model-section', {
      entity,
      model,
      section,
      run
    });

    const response = await page.request.get(url);

    // Accept success, bad request, or not found responses since test data may not exist
    expect([200, 400, 404]).toContain(response.status());

    if (response.status() === 200) {
      const data = await response.json();
      expect(data).toHaveProperty('text');
      expect(data).toHaveProperty('citations');
      expect(data).toHaveProperty('metadata');

      // Check that text is a string
      expect(typeof data.text).toBe('string');

      // Check that citations is an array
      expect(Array.isArray(data.citations)).toBe(true);

      // Check citation structure if any exist
      if (data.citations.length > 0) {
        const citation = data.citations[0];
        expect(citation).toHaveProperty('doc_page_id');
        expect(citation).toHaveProperty('title');
        expect(citation).toHaveProperty('url');
      }
    }
  });

  test('should handle entity-specific endpoints', async ({ page }) => {
    // Use configurable test data
    const entityId = getTestEntity('primary');
    const model = getTestModel('primary');
    const section = getTestSection(model, 'primary');
    const run = getTestRun('latest');

    // Test the new endpoint structure
    const response = await page.request.get(`/api/report/entity/${entityId}/harm/model/${model}/section/${section}?run=${run}&includeDisclosures=true`);

    // Accept success, bad request, or not found responses since test data may not exist
    expect([200, 400, 404]).toContain(response.status());

    if (response.status() === 200) {
      const data = await response.json();
      expect(data).toHaveProperty('text');
      expect(data).toHaveProperty('citations');
      expect(data).toHaveProperty('metadata');

      // Check metadata structure
      expect(data.metadata).toHaveProperty('entityId');
      expect(data.metadata).toHaveProperty('modelName');
      expect(data.metadata).toHaveProperty('modelSection');
      expect(data.metadata.entityId).toBe(entityId);
      expect(data.metadata.modelName).toBe(model);
      expect(data.metadata.modelSection).toBe(section);
    }
  });

  test('should handle transparency endpoint', async ({ page }) => {
    const entityId = getTestEntity('primary');
    const run = getTestRun('latest');

    const response = await page.request.get(`/api/report/entity/${entityId}/transparency?run=${run}&includeDisclosures=true`);

    // Accept success, bad request, or not found responses since test data may not exist
    expect([200, 400, 404]).toContain(response.status());

    if (response.status() === 200) {
      const data = await response.json();
      expect(data).toHaveProperty('text');
      expect(data).toHaveProperty('citations');
      expect(data).toHaveProperty('metadata');

      // Check that transparency content is returned
      expect(data.text.length).toBeGreaterThan(0);
    }
  });

  test('should handle reliability endpoint', async ({ page }) => {
    const entityId = getTestEntity('primary');
    const run = getTestRun('latest');

    const response = await page.request.get(`/api/report/entity/${entityId}/reliability?run=${run}&includeDisclosures=true`);

    // Accept success, bad request, or not found responses since test data may not exist
    expect([200, 400, 404]).toContain(response.status());

    if (response.status() === 200) {
      const data = await response.json();
      expect(data).toHaveProperty('text');
      expect(data).toHaveProperty('citations');
      expect(data).toHaveProperty('metadata');

      // Check that reliability content is returned
      expect(data.text.length).toBeGreaterThan(0);
    }
  });

  test('should handle summarize endpoint', async ({ page }) => {
    const summaryRequest = {
      content: 'This is test content to summarize. It contains information about environmental impacts and sustainability practices.',
      prompt: 'Provide a brief summary focusing on key points',
      title: 'Test Summary'
    };
    
    const response = await page.request.post('/api/report/summarize', {
      data: summaryRequest
    });
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('text');
    expect(data).toHaveProperty('citations');
    expect(data).toHaveProperty('metadata');
    
    // Check that summary content exists (may be longer or shorter than original)
    expect(data.text.length).toBeGreaterThan(0);
    // Note: LLM may expand content rather than summarize, so we don't enforce length constraints
    
    // Check metadata
    expect(data.metadata.type).toBe('summary');
    expect(data.metadata.title).toBe(summaryRequest.title);
  });

  test('should handle missing parameters gracefully', async ({ page }) => {
    // Test endpoint without required parameters
    const response = await page.request.get('/api/report/entity/test/harm/model/sdg/section/01_no_poverty');

    // Should return error for missing run parameter (API may return 404 or 400)
    expect([200, 400, 404]).toContain(response.status());

    if (response.status() === 400) {
      const data = await response.json();
      expect(data).toHaveProperty('error');
    }
  });

  test('should handle invalid entity IDs', async ({ page }) => {
    const response = await page.request.get('/api/report/entity/invalid-entity/harm/model/sdg/section/01_no_poverty?run=latest');
    
    // Should handle gracefully, either with empty content or appropriate error
    expect([200, 404, 400]).toContain(response.status());
    
    if (response.status() === 200) {
      const data = await response.json();
      expect(data).toHaveProperty('text');
      expect(data).toHaveProperty('citations');
    }
  });

  test('should handle rate limiting', async ({ page }) => {
    // Use configurable test data
    const entityId = getTestEntity('primary');
    const model = getTestModel('primary');
    const section = getTestSection(model, 'primary');
    const run = getTestRun('latest');

    // Make multiple rapid requests to test rate limiting
    const promises = Array.from({ length: 10 }, () =>
      page.request.get(`/api/report/entity/${entityId}/harm/model/${model}/section/${section}?run=${run}`)
    );

    const responses = await Promise.all(promises);

    // All requests should either succeed, be rate limited, or return 404 for missing data
    responses.forEach(response => {
      expect([200, 400, 404, 429]).toContain(response.status());
    });
  });

  test('should return consistent citation format', async ({ page }) => {
    // Use configurable test data
    const entityId = getTestEntity('primary');
    const model = getTestModel('primary');
    const section = getTestSection(model, 'primary');
    const run = getTestRun('latest');

    const response = await page.request.get(`/api/report/entity/${entityId}/harm/model/${model}/section/${section}?run=${run}&includeDisclosures=true`);

    // Accept success, bad request, or not found responses since test data may not exist
    expect([200, 400, 404]).toContain(response.status());

    if (response.status() !== 200) return; // Skip validation if no data
    
    const data = await response.json();
    
    // Check citation format consistency
    data.citations.forEach((citation: any) => {
      expect(citation).toHaveProperty('doc_page_id');
      expect(citation).toHaveProperty('title');
      expect(citation).toHaveProperty('url');
      expect(citation).toHaveProperty('doc_id');
      expect(citation).toHaveProperty('doc_name');
      
      // Check data types
      expect(typeof citation.doc_page_id).toBe('number');
      expect(typeof citation.title).toBe('string');
      expect(typeof citation.url).toBe('string');
    });
  });

  test('should handle caching headers', async ({ page }) => {
    // Use configurable test data
    const entityId = getTestEntity('primary');
    const model = getTestModel('primary');
    const section = getTestSection(model, 'primary');
    const run = getTestRun('latest');

    const response = await page.request.get(`/api/report/entity/${entityId}/harm/model/${model}/section/${section}?run=${run}`);

    // Accept success, bad request, or not found responses since test data may not exist
    expect([200, 400, 404]).toContain(response.status());

    if (response.status() !== 200) return; // Skip validation if no data

    // Check for appropriate caching headers (some endpoints may not have them)
    const headers = response.headers();
    if (headers['cache-control']) {
      expect(headers['cache-control']).toContain('max-age');
    }

    // Make the same request again to test caching
    const response2 = await page.request.get(`/api/report/entity/${entityId}/harm/model/${model}/section/${section}?run=${run}`);
    expect([200, 400, 404]).toContain(response2.status());
  });

  test('should handle CORS headers', async ({ page }) => {
    // Use configurable test data
    const entityId = getTestEntity('primary');
    const model = getTestModel('primary');
    const section = getTestSection(model, 'primary');
    const run = getTestRun('latest');

    const response = await page.request.get(`/api/report/entity/${entityId}/harm/model/${model}/section/${section}?run=${run}`);

    // Accept success, bad request, or not found responses since test data may not exist
    expect([200, 400, 404]).toContain(response.status());

    const headers = response.headers();

    // Check for CORS headers if needed
    if (headers['access-control-allow-origin']) {
      expect(headers['access-control-allow-origin']).toBeTruthy();
    }
  });

  test('should validate request parameters', async ({ page }) => {
    // Use configurable test data
    const entityId = getTestEntity('primary');
    const model = getTestModel('primary');
    const section = getTestSection(model, 'primary');
    const run = getTestRun('latest');

    // Test with invalid model
    const response1 = await page.request.get(`/api/report/entity/${entityId}/harm/model/invalid/section/${section}?run=${run}`);
    expect([200, 400, 404]).toContain(response1.status());

    // Test with invalid section
    const response2 = await page.request.get(`/api/report/entity/${entityId}/harm/model/${model}/section/invalid?run=${run}`);
    expect([200, 400, 404]).toContain(response2.status());

    // Test with invalid run parameter
    const response3 = await page.request.get(`/api/report/entity/${entityId}/harm/model/${model}/section/${section}?run=invalid`);
    expect([200, 400, 404]).toContain(response3.status());
  });

  test('should handle large content responses', async ({ page }) => {
    // Use configurable test data
    const entityId = getTestEntity('primary');
    const model = getTestModel('primary');
    const section = getTestSection(model, 'primary');
    const run = getTestRun('latest');

    // Test with disclosures enabled (typically larger responses)
    const response = await page.request.get(`/api/report/entity/${entityId}/harm/model/${model}/section/${section}?run=${run}&includeDisclosures=true`);

    // Accept success, bad request, or not found responses since test data may not exist
    expect([200, 400, 404]).toContain(response.status());

    if (response.status() !== 200) return; // Skip validation if no data
    
    const data = await response.json();
    
    // Check that response is properly structured even for large content
    expect(data).toHaveProperty('text');
    expect(data).toHaveProperty('citations');
    expect(data).toHaveProperty('metadata');
    
    // Content should be reasonable length (not empty, not excessively long)
    expect(data.text.length).toBeGreaterThan(0);
    expect(data.text.length).toBeLessThan(100000); // Reasonable upper limit
  });
});
