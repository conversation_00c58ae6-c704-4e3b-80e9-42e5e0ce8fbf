import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';
import { getTestEntity, getTestModel } from './test-config';

test.describe('Dashboard Promises Page', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to promises page with test entity
    await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');
  });

  test('should display promises page with filtering capabilities', async ({ page }) => {
    // Wait for promises page to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for filter controls
    await expect(page.locator('[data-testid="promises-filters"]')).toBeVisible();

    // Check for promise status filter (broken vs kept)
    const statusFilter = page.locator('[data-testid="promise-status-filter"]');
    if (await statusFilter.isVisible()) {
      await statusFilter.click();
      
      // Verify filter options
      await expect(page.locator('[data-testid="filter-option-all-promises"]')).toBeVisible();
      await expect(page.locator('[data-testid="filter-option-kept-promises"]')).toBeVisible();
      await expect(page.locator('[data-testid="filter-option-broken-promises"]')).toBeVisible();
    }
  });

  test('should display broken vs kept promises visualization', async ({ page }) => {
    // Wait for promises data to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for promises summary visualization
    const promisesSummary = page.locator('[data-testid="promises-summary"]');
    if (await promisesSummary.isVisible()) {
      // Check broken promises count
      await expect(page.locator('[data-testid="broken-promises-count"]')).toBeVisible();
      
      // Check kept promises count
      await expect(page.locator('[data-testid="kept-promises-count"]')).toBeVisible();

      // Verify counts are numeric
      const brokenCount = await page.locator('[data-testid="broken-promises-count"]').textContent();
      const keptCount = await page.locator('[data-testid="kept-promises-count"]').textContent();
      
      expect(brokenCount).toMatch(/^\d+$/);
      expect(keptCount).toMatch(/^\d+$/);
    }

    // Check for visual chart/graph
    const promisesChart = page.locator('[data-testid="promises-chart"]');
    if (await promisesChart.isVisible()) {
      await expect(promisesChart).toBeVisible();
      
      // Check chart has data elements
      const chartElements = promisesChart.locator('svg, canvas, [data-testid*="chart-element"]');
      await expect(chartElements.first()).toBeVisible();
    }
  });

  test('should filter promises by status (broken/kept)', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    // Test broken promises filter
    const statusFilter = page.locator('[data-testid="promise-status-filter"]');
    if (await statusFilter.isVisible()) {
      await statusFilter.click();
      await page.click('[data-testid="filter-option-broken-promises"]');
      await page.waitForLoadState('networkidle');

      // Verify only broken promises are displayed
      const promiseItems = page.locator('[data-testid="promise-item"]');
      
      if (await promiseItems.count() > 0) {
        const promiseStatuses = await promiseItems.locator('[data-testid="promise-status"]').allTextContents();
        
        for (const status of promiseStatuses) {
          expect(status.toLowerCase()).toMatch(/broken|failed|unfulfilled/);
        }
      }

      // Test kept promises filter
      await statusFilter.click();
      await page.click('[data-testid="filter-option-kept-promises"]');
      await page.waitForLoadState('networkidle');

      // Verify only kept promises are displayed
      const newPromiseItems = page.locator('[data-testid="promise-item"]');
      
      if (await newPromiseItems.count() > 0) {
        const newPromiseStatuses = await newPromiseItems.locator('[data-testid="promise-status"]').allTextContents();
        
        for (const status of newPromiseStatuses) {
          expect(status.toLowerCase()).toMatch(/kept|fulfilled|achieved/);
        }
      }
    }
  });

  test('should display promise details correctly', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    
    if (await promiseItems.count() > 0) {
      const firstPromise = promiseItems.first();

      // Check promise content elements
      await expect(firstPromise.locator('[data-testid="promise-title"]')).toBeVisible();
      await expect(firstPromise.locator('[data-testid="promise-content"]')).toBeVisible();
      await expect(firstPromise.locator('[data-testid="promise-status"]')).toBeVisible();
      await expect(firstPromise.locator('[data-testid="promise-date"]')).toBeVisible();

      // Check promise metadata
      const promiseConfidence = firstPromise.locator('[data-testid="promise-confidence"]');
      if (await promiseConfidence.isVisible()) {
        const confidenceText = await promiseConfidence.textContent();
        expect(confidenceText).toMatch(/\d+%|\d+\.\d+/);
      }

      // Check promise target date or timeline
      const promiseTargetDate = firstPromise.locator('[data-testid="promise-target-date"]');
      if (await promiseTargetDate.isVisible()) {
        await expect(promiseTargetDate).toBeVisible();
      }

      // Check promise evidence or assessment
      const promiseEvidence = firstPromise.locator('[data-testid="promise-evidence"]');
      if (await promiseEvidence.isVisible()) {
        await expect(promiseEvidence).toBeVisible();
      }
    }
  });

  test('should show promise detail modal when clicked', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    
    if (await promiseItems.count() > 0) {
      const firstPromise = promiseItems.first();
      
      // Click on promise to view details
      await firstPromise.click();

      // Check if modal opens
      try {
        await expect(page.locator('[data-testid="promise-detail-modal"]')).toBeVisible({ timeout: 10000 });
        
        // Verify modal content
        await expect(page.locator('[data-testid="modal-promise-title"]')).toBeVisible();
        await expect(page.locator('[data-testid="modal-promise-content"]')).toBeVisible();
        await expect(page.locator('[data-testid="modal-promise-status"]')).toBeVisible();
        await expect(page.locator('[data-testid="modal-promise-assessment"]')).toBeVisible();

        // Test close functionality
        const closeButton = page.locator('[data-testid="modal-close-button"]');
        if (await closeButton.isVisible()) {
          await closeButton.click();
        } else {
          await page.keyboard.press('Escape');
        }

        // Verify modal closes
        await expect(page.locator('[data-testid="promise-detail-modal"]')).not.toBeVisible();
      } catch {
        // Check for expanded details within the list item
        await expect(firstPromise.locator('[data-testid="promise-expanded-details"]')).toBeVisible({ timeout: 5000 });
      }
    }
  });

  test('should display promise timeline and assessment history', async ({ page }) => {
    // Wait for promises page to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for promise timeline component
    const promiseTimeline = page.locator('[data-testid="promise-timeline"]');
    if (await promiseTimeline.isVisible()) {
      await expect(promiseTimeline).toBeVisible();

      // Check timeline entries
      const timelineEntries = promiseTimeline.locator('[data-testid="timeline-entry"]');
      if (await timelineEntries.count() > 0) {
        const firstEntry = timelineEntries.first();
        await expect(firstEntry.locator('[data-testid="timeline-date"]')).toBeVisible();
        await expect(firstEntry.locator('[data-testid="timeline-event"]')).toBeVisible();
      }
    }

    // Check for assessment history
    const assessmentHistory = page.locator('[data-testid="promise-assessment-history"]');
    if (await assessmentHistory.isVisible()) {
      await expect(assessmentHistory).toBeVisible();

      // Check assessment entries
      const assessmentEntries = assessmentHistory.locator('[data-testid="assessment-entry"]');
      if (await assessmentEntries.count() > 0) {
        const firstAssessment = assessmentEntries.first();
        await expect(firstAssessment.locator('[data-testid="assessment-date"]')).toBeVisible();
        await expect(firstAssessment.locator('[data-testid="assessment-status"]')).toBeVisible();
      }
    }
  });

  test('should handle loading states appropriately', async ({ page }) => {
    // Navigate to promises page
    await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);

    // Check for loading indicators
    const loadingIndicators = page.locator('[data-testid*="loading"], [class*="loading"], [class*="spinner"]');
    
    try {
      // Loading indicators should appear initially
      await expect(loadingIndicators.first()).toBeVisible({ timeout: 5000 });
      
      // Then disappear as content loads
      await expect(loadingIndicators.first()).not.toBeVisible({ timeout: 30000 });
      
      // Content should be visible
      await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible();
    } catch {
      // If loading is very fast, content should be immediately visible
      await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });
    }
  });

  test('should sort promises by relevance or date', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    // Check for sort controls
    const sortControl = page.locator('[data-testid="promises-sort"]');
    if (await sortControl.isVisible()) {
      await sortControl.click();

      // Test different sort options
      const sortByDate = page.locator('[data-testid="sort-by-date"]');
      const sortByRelevance = page.locator('[data-testid="sort-by-relevance"]');
      const sortByStatus = page.locator('[data-testid="sort-by-status"]');

      if (await sortByDate.isVisible()) {
        await sortByDate.click();
        await page.waitForLoadState('networkidle');

        // Verify promises are sorted by date
        const promiseDates = await page.locator('[data-testid="promise-date"]').allTextContents();
        // Note: Actual date comparison would require parsing dates
        expect(promiseDates.length).toBeGreaterThan(0);
      }

      if (await sortByRelevance.isVisible()) {
        await sortByRelevance.click();
        await page.waitForLoadState('networkidle');
        
        // Verify sorting changed
        await expect(page.locator('[data-testid="promises-list"]')).toBeVisible();
      }
    }
  });

  test('should display promise categories or types', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    
    if (await promiseItems.count() > 0) {
      const firstPromise = promiseItems.first();

      // Check for promise category/type
      const promiseCategory = firstPromise.locator('[data-testid="promise-category"]');
      if (await promiseCategory.isVisible()) {
        await expect(promiseCategory).toBeVisible();
        
        const categoryText = await promiseCategory.textContent();
        expect(categoryText).toBeTruthy();
      }

      // Check for promise theme (environmental, social, governance)
      const promiseTheme = firstPromise.locator('[data-testid="promise-theme"]');
      if (await promiseTheme.isVisible()) {
        await expect(promiseTheme).toBeVisible();
      }
    }
  });

  test('should handle empty states gracefully', async ({ page }) => {
    // Try with very restrictive filters or invalid entity
    await page.goto(`/customer/dashboard/gw/promises?entity=invalid-entity&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');

    // Check for empty state message
    try {
      await expect(page.locator('[data-testid="no-promises-message"]')).toBeVisible({ timeout: 15000 });
    } catch {
      // If some promises are shown, that's also valid
      const promiseItems = page.locator('[data-testid="promise-item"]');
      if (await promiseItems.count() === 0) {
        await expect(page.locator('[data-testid="empty-state"]')).toBeVisible();
      }
    }
  });

  test('should maintain filter state when navigating', async ({ page }) => {
    // Wait for promises page to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Apply broken promises filter
    const statusFilter = page.locator('[data-testid="promise-status-filter"]');
    if (await statusFilter.isVisible()) {
      await statusFilter.click();
      await page.click('[data-testid="filter-option-broken-promises"]');
      await page.waitForLoadState('networkidle');
    }

    // Navigate away and back
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    
    await page.goBack();
    await page.waitForLoadState('networkidle');

    // Verify we're back on promises page
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });
  });

  test('should display promise sources and references', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    
    if (await promiseItems.count() > 0) {
      const firstPromise = promiseItems.first();

      // Check for source information
      const promiseSource = firstPromise.locator('[data-testid="promise-source"]');
      if (await promiseSource.isVisible()) {
        await expect(promiseSource).toBeVisible();
      }

      // Check for document references
      const promiseReferences = firstPromise.locator('[data-testid="promise-references"]');
      if (await promiseReferences.isVisible()) {
        await expect(promiseReferences).toBeVisible();
      }

      // Check for external links or citations
      const promiseCitations = firstPromise.locator('[data-testid="promise-citations"]');
      if (await promiseCitations.isVisible()) {
        await expect(promiseCitations).toBeVisible();
      }
    }
  });
});