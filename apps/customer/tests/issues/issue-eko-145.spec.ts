import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('Issue EKO-145: AI Slash Commands', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should show AI commands submenu when typing /ai', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Click in editor and type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Should show "AI Commands" header
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 10000 })

    // Should show command options
    await expect(page.locator('[data-testid="ai-slash-command-improve"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-grammar"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-shorter"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-expand"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-tone"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-summarize"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-continue"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-custom"]')).toBeVisible()
  })

  test('should dismiss AI menu when clicking outside', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Verify menu is visible
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 10000 })

    // Click outside the menu
    await editor.click({ position: { x: 100, y: 100 } })

    // Menu should be dismissed
    await expect(page.locator('[data-testid="ai-commands-header"]')).not.toBeVisible()
  })

  test('should remove slash text when executing AI command', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Add some text first
    await testUtils.typeInEditor('Test text for improvement')
    await page.keyboard.press('Enter')

    // Type AI slash command
    await page.keyboard.type('/ai')

    // Wait for AI commands menu to appear
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 10000 })

    // Click on improve command
    await page.click('[data-testid="ai-slash-command-improve"]')

    // The /ai text should be removed from the editor
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).not.toContainText('/ai')
  })

  test('should handle keyboard navigation in AI menu', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Wait for AI commands menu to appear
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 10000 })

    // Test keyboard navigation
    await page.keyboard.press('ArrowDown')
    await page.keyboard.press('ArrowUp')

    // Press Enter to select current option
    await page.keyboard.press('Enter')

    // The /ai text should be removed
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).not.toContainText('/ai')
  })

  test('should dismiss AI menu with Escape key', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    const editor = await testUtils.waitForEditor()

    // Type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Wait for AI commands menu to appear
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 10000 })

    // Press Escape to dismiss
    await page.keyboard.press('Escape')

    // Menu should be dismissed
    await expect(page.locator('[data-testid="ai-commands-header"]')).not.toBeVisible()
    
    // The /ai text should still be there since we didn't execute a command
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).toContainText('/ai')
  })
})