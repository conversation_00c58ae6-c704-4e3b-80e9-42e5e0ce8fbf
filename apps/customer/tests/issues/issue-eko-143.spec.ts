import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-143: Repetitive Testing', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should demonstrate refactored ProseMirror editor utilities', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    // Test the new waitForEditor utility function
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible()

    // Test the new typeInEditorAtCursor utility function
    await testUtils.typeInEditorAtCursor('Test content added with utility function')
    await testUtils.checkEditorContent('Test content added with utility function')

    // Test the checkEditorState utility function
    await testUtils.checkEditorState(true) // Should be editable

    // Test the getEditorText utility function
    const editorText = await testUtils.getEditorText()
    expect(editorText).toContain('Test content added with utility function')
  })

  test('should verify reduced code duplication', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    // Before refactoring, each test would need:
    // const editor = page.locator('.ProseMirror')
    // await expect(editor).toBeVisible({ timeout: 10000 })
    
    // After refactoring, it's just:
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible()

    // This demonstrates the reduction in repetitive code
    await testUtils.typeInEditorAtCursor('Refactoring completed successfully!')
    
    // Verify the content is there
    await expect(editor.locator('text=Refactoring completed successfully!')).toBeVisible()
  })
})
