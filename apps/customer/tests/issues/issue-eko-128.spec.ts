import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-128: Back button navigation', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should navigate back to documents list when back button is clicked', async ({ page }) => {
    // Create a new document using test utils
    await testUtils.createDocumentFromTemplate('Blank Document')

    // Verify we're on the document editor page
    expect(page.url()).toMatch(/\/customer\/documents\/[a-f0-9-]+/)

    // Find and click the back button - using data-testid for reliability
    const backButton = page.locator('[data-testid="back-button"]')
    await expect(backButton).toBeVisible({ timeout: 10000 })
    await backButton.click()

    // Wait for navigation to complete
    await page.waitForLoadState('networkidle')

    // Verify we're back on the documents list page
    await expect(page).toHaveURL('/customer/documents')
    
    // Verify we can see the documents list interface
    await expect(page.locator('h1:has-text("Documents"), h2:has-text("Documents")')).toBeVisible()
  })

  test('should navigate back to documents list from error state', async ({ page }) => {
    // Navigate to a non-existent document to trigger error state
    await page.goto('/customer/documents/non-existent-document-id')
    await page.waitForLoadState('networkidle')

    // Wait for any error state to appear - using actual text from implementation
    const errorHeading = page.locator('h1:has-text("No Such Document"), h1:has-text("Error")')
    await expect(errorHeading).toBeVisible({ timeout: 10000 })

    // Find and click the back button in error state - using data-testid for reliability
    const backButton = page.locator('[data-testid="go-back-button"]')
    await expect(backButton).toBeVisible()
    await backButton.click()

    // Wait for navigation to complete
    await page.waitForLoadState('networkidle')

    // Verify we're back on the documents list page
    await expect(page).toHaveURL('/customer/documents')

    // Verify we can see the documents list interface
    await expect(page.locator('h1:has-text("Documents"), h2:has-text("Documents")')).toBeVisible()
  })

  test('should navigate back to documents list from loading error state', async ({ page }) => {
    // Mock a server error to trigger error state
    await page.route('**/collaborative_documents*', (route) => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      })
    })

    // Try to navigate to any document
    await page.goto('/customer/documents/test-document-id')
    await page.waitForLoadState('networkidle')

    // Wait for error state to appear - using actual text from implementation
    const errorHeading = page.locator('h1:has-text("Error"), h1:has-text("No Such Document")')
    await expect(errorHeading).toBeVisible({ timeout: 10000 })

    // Find and click the back button in error state - using data-testid for reliability
    const backButton = page.locator('[data-testid="go-back-button"]')
    await expect(backButton).toBeVisible()
    await backButton.click()

    // Wait for navigation to complete
    await page.waitForLoadState('networkidle')

    // Verify we're back on the documents list page
    await expect(page).toHaveURL('/customer/documents')
  })

  test('should maintain correct breadcrumb navigation', async ({ page }) => {
    // Go to documents page first
    await page.goto('/customer/documents')
    await page.waitForLoadState('networkidle')

    // Create a new document using the more reliable testUtils method
    await testUtils.createDocumentFromTemplate('Blank Document')

    // Verify we're on the document editor page
    expect(page.url()).toMatch(/\/customer\/documents\/[a-f0-9-]+/)

    // Check that breadcrumb shows correct navigation path
    // The navigation context should show: Dashboard > Documents > Document Title
    // This is set up in the useEffect that calls nav.changeNavPath
    
    // Click back button - using data-testid for reliability
    const backButton = page.locator('[data-testid="back-button"]')
    await expect(backButton).toBeVisible({ timeout: 10000 })
    await backButton.click()
    await page.waitForLoadState('networkidle')

    // Verify we're on the correct page
    await expect(page).toHaveURL('/customer/documents')
  })
})
