import { expect, test } from '@playwright/test'
import { TEST_CONFIG } from '../test-config'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-62: Entity Context with nuqs', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should use nuqs for URL state management with only entity-relevant parameters', async ({ page }) => {
    const testEntity = TEST_CONFIG.entities.secondary
    // Navigate to customer page with entity parameters AND unrelated parameters
    await page.goto(`/customer?entity=${testEntity}&run=123&model=doughnut&disclosures=false&unrelated=shouldbefiltered`)
    
    // Wait for the page to load and context to initialize
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(3000) // Give more time for React to re-render
    
    // The browser URL will still contain all parameters (including unrelated ones)
    const currentUrl = page.url()
    const browserUrl = new URL(currentUrl)
    expect(browserUrl.searchParams.has('unrelated')).toBe(true) // Browser URL keeps all params
    
    // Test that the entity context's queryString only contains entity-relevant parameters
    // by checking links that use entityContext.queryString
    
    // Look for links that use the entity context's queryString (like ScoreCard hrefs)
    const flagsLink = page.locator('a[href*="/customer/dashboard/flags"]').first()
    if (await flagsLink.isVisible()) {
      const href = await flagsLink.getAttribute('href')
      
      // The href should contain entity parameters but NOT unrelated ones
      expect(href).toContain(`entity=${testEntity}`)
      expect(href).toContain('run=123')
      expect(href).toContain('model=doughnut')
      expect(href).toContain('disclosures=false')
      expect(href).not.toContain('unrelated') // This should be filtered out by entity context's queryString
    } else {
      // Alternative: check claims or promises links
      const claimsLink = page.locator('a[href*="/customer/dashboard/gw/claims"]').first()
      if (await claimsLink.isVisible()) {
        const href = await claimsLink.getAttribute('href')
        
        expect(href).toContain(`entity=${testEntity}`)
        expect(href).not.toContain('unrelated') // This should be filtered out by entity context's queryString
      }
    }
  })

  test('should include non-default parameters in URL', async ({ page }) => {
    const testEntity = TEST_CONFIG.entities.secondary
    const testRun = String(TEST_CONFIG.runs.specific)
    // Navigate with non-default parameters
    await page.goto(`/customer?entity=${testEntity}&run=${testRun}&model=doughnut&disclosures=false`)
    
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
    
    const currentUrl = page.url()
    const url = new URL(currentUrl)
    
    // All non-default parameters should be present
    expect(url.searchParams.get('entity')).toBe(testEntity)
    expect(url.searchParams.get('run')).toBe(testRun)
    expect(url.searchParams.get('model')).toBe('doughnut')
    expect(url.searchParams.get('disclosures')).toBe('false')
  })

  test('should update URL when entity context parameters change', async ({ page }) => {
    const testEntity = TEST_CONFIG.entities.secondary
    // Start with basic entity
    await page.goto(`/customer?entity=${testEntity}`)
    await page.waitForLoadState('networkidle')
    
    // Find and interact with disclosure toggle if available
    const disclosureToggle = page.locator('[data-testid="disclosure-toggle"]').first()
    if (await disclosureToggle.isVisible()) {
      await disclosureToggle.click()
      await page.waitForTimeout(1000)
      
      // Check that disclosures=false was added to URL
      const currentUrl = page.url()
      const url = new URL(currentUrl)
      expect(url.searchParams.get('disclosures')).toBe('false')
    }
  })

  test('should maintain URL state on page refresh', async ({ page }) => {
    const testEntity = TEST_CONFIG.entities.secondary
    const testRun = String(TEST_CONFIG.runs.specific)
    // Set specific parameters
    await page.goto(`/customer?entity=${testEntity}&run=${testRun}&model=doughnut&disclosures=false`)
    await page.waitForLoadState('networkidle')
    
    // Refresh the page
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // Verify parameters are maintained
    const currentUrl = page.url()
    const url = new URL(currentUrl)
    expect(url.searchParams.get('entity')).toBe(testEntity)
    expect(url.searchParams.get('run')).toBe(testRun)
    expect(url.searchParams.get('model')).toBe('doughnut')
    expect(url.searchParams.get('disclosures')).toBe('false')
  })

  test('should reset run to latest when entity changes', async ({ page }) => {
    const testEntity = TEST_CONFIG.entities.secondary
    const testRun = String(TEST_CONFIG.runs.specific)
    // Start with specific run
    await page.goto(`/customer?entity=${testEntity}&run=${testRun}`)
    await page.waitForLoadState('networkidle')
    
    // If there's an entity selector, test changing entity
    const entitySelector = page.locator('[data-testid="entity-selector"]').first()
    if (await entitySelector.isVisible()) {
      // Change entity (this would trigger the reset logic)
      await entitySelector.click()
      
      // Select a different entity if available
      const otherEntity = page.locator(`[data-value="${TEST_CONFIG.entities.primary}"]`).first()
      if (await otherEntity.isVisible()) {
        await otherEntity.click()
        await page.waitForTimeout(1000)
        
        // Check that run was reset (should not be in URL if it's 'latest')
        const currentUrl = page.url()
        const url = new URL(currentUrl)
        expect(url.searchParams.has('run')).toBe(false)
      }
    }
  })
})
