import { test, expect } from '@playwright/test';

test.describe('EKO-31: Green flags summary expandable functionality', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the dashboard or flags page where green flags are displayed
    // Note: You may need to adjust this URL based on your app's routing
    await page.goto('/customer/dashboard');
    
    // Wait for the page to load and flags to be displayed
    await page.waitForLoadState('networkidle');
  });

  test('should make green flags badge list expandable in dashboard', async ({ page }) => {
    // Check if there are green flags visible
    const greenFlagsSection = page.locator('[data-testid="green-flags-section"]').or(
      page.locator('text=Positive Actions').locator('..').locator('..')
    );
    
    if (await greenFlagsSection.isVisible()) {
      // Look for the "+X more" badge
      const moreButton = page.locator('text=/\\+\\d+ more/');
      
      if (await moreButton.isVisible()) {
        // Get initial count of visible badges
        const initialBadgeCount = await page.locator('.bg-green-200, .dark\\:bg-green-950').count();
        
        // Click the "+X more" button
        await moreButton.click();
        
        // Wait for expansion
        await page.waitForTimeout(500);
        
        // Verify more badges are now visible
        const expandedBadgeCount = await page.locator('.bg-green-200, .dark\\:bg-green-950').count();
        expect(expandedBadgeCount).toBeGreaterThan(initialBadgeCount);
        
        // Verify the button text changed to "Show less"
        await expect(page.locator('text=Show less')).toBeVisible();
        
        // Click to collapse again
        await page.locator('text=Show less').click();
        await page.waitForTimeout(500);
        
        // Verify we're back to the original state
        const collapsedBadgeCount = await page.locator('.bg-green-200, .dark\\:bg-green-950').count();
        expect(collapsedBadgeCount).toBe(initialBadgeCount);
        
        // Verify "+X more" button is visible again
        await expect(moreButton).toBeVisible();
      }
    }
  });

  test('should make green flags summary expandable in flags detail page', async ({ page }) => {
    // Navigate to flags detail page
    await page.goto('/customer/dashboard/flags');
    await page.waitForLoadState('networkidle');
    
    // Click on green/positive flags tab if it exists
    const greenTab = page.locator('text=Positive Actions').or(page.locator('[data-value="green"]'));
    if (await greenTab.isVisible()) {
      await greenTab.click();
      await page.waitForTimeout(500);
    }
    
    // Look for expandable summary section
    const summarySection = page.locator('text=Overall Summary').locator('..').locator('..');
    if (await summarySection.isVisible()) {
      // Look for expand button in summary
      const expandSummaryButton = page.locator('text=/Show summary including all \\d+ actions/');
      
      if (await expandSummaryButton.isVisible()) {
        // Click to expand summary
        await expandSummaryButton.click();
        
        // Wait for the new summary to load
        await page.waitForTimeout(2000);
        
        // Verify the button text changed
        await expect(page.locator('text=Show summary of key actions only')).toBeVisible();
        
        // Click to collapse
        await page.locator('text=Show summary of key actions only').click();
        await page.waitForTimeout(2000);
        
        // Verify we're back to original state
        await expect(expandSummaryButton).toBeVisible();
      }
    }
  });

  test('should have clickable and hoverable +X more badges', async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Find any "+X more" badges
    const moreBadges = page.locator('text=/\\+\\d+ more/');
    const badgeCount = await moreBadges.count();
    
    if (badgeCount > 0) {
      for (let i = 0; i < badgeCount; i++) {
        const badge = moreBadges.nth(i);
        
        // Verify badge is visible
        await expect(badge).toBeVisible();
        
        // Verify badge has cursor pointer style (indicates it's clickable)
        await expect(badge).toHaveClass(/cursor-pointer/);
        
        // Test hover effect
        await badge.hover();
        await expect(badge).toHaveClass(/hover:bg-muted/);
        
        // Test click functionality
        await badge.click();
        await page.waitForTimeout(500);
        
        // Should either show "Show less" or expand content
        const showLess = page.locator('text=Show less');
        const expandedContent = page.locator('text=/Show summary of key actions only/');
        
        expect(await showLess.isVisible() || await expandedContent.isVisible()).toBeTruthy();
      }
    }
  });

  test('should maintain state independently for different sections', async ({ page }) => {
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Find multiple "+X more" buttons (e.g., in different charts or sections)
    const moreBadges = page.locator('text=/\\+\\d+ more/');
    const badgeCount = await moreBadges.count();
    
    if (badgeCount > 1) {
      // Expand first section
      await moreBadges.nth(0).click();
      await page.waitForTimeout(500);
      
      // Verify first section is expanded but second is not
      const firstShowLess = page.locator('text=Show less').first();
      await expect(firstShowLess).toBeVisible();
      
      // Second section should still show "+X more"
      if (badgeCount > 1) {
        const secondMoreButton = moreBadges.nth(1);
        if (await secondMoreButton.isVisible()) {
          await expect(secondMoreButton).toBeVisible();
        }
      }
    }
  });
});