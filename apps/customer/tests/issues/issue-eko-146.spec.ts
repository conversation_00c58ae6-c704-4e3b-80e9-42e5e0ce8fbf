import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-146 - AI Processing States Issue', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should reproduce the AI processing states bug', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    // First, let's test without any mocking to see the normal behavior
    const editor = await testUtils.waitForEditor()

    await testUtils.typeInEditor('Text to be improved')
    await page.keyboard.press('ControlOrMeta+a')

    // Check initial state - button should be enabled when text is selected
    await expect(page.locator('[data-testid="ai-command-improve"]')).toBeEnabled()

    console.log('Debug: Button enabled initially as expected')
  })

  test('should show the bug with mocked API response', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    // Set up console logging to debug what's happening
    page.on('console', msg => {
      console.log(`Console ${msg.type()}: ${msg.text()}`)
    })

    page.on('pageerror', error => {
      console.log(`Page error: ${error.message}`)
    })

    // Mock the AI response that causes the issue
    await page.route('**/api/ai/generate', async route => {
      console.log('Debug: AI API route intercepted')
      
      // Simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Return a simple text response (what the original test was trying to do)
      await route.fulfill({
        status: 200,
        headers: { 'Content-Type': 'text/plain' },
        body: 'Improved text content'
      })
    })

    const editor = await testUtils.waitForEditor()

    await testUtils.typeInEditor('Text to be improved')
    await page.keyboard.press('ControlOrMeta+a')

    // Check initial state
    await expect(page.locator('[data-testid="ai-command-improve"]')).toBeEnabled()
    console.log('Debug: Button enabled initially')

    // Start AI operation
    await page.click('[data-testid="ai-command-improve"]')
    console.log('Debug: Clicked AI improve button')

    // Should show processing state
    await expect(page.locator('[data-testid="ai-processing"]')).toBeVisible()
    console.log('Debug: Processing indicator visible')

    // Button should be disabled during processing
    await expect(page.locator('[data-testid="ai-command-improve"]')).toBeDisabled()
    console.log('Debug: Button disabled during processing')

    // Wait for processing to complete
    await expect(page.locator('[data-testid="ai-processing"]')).not.toBeVisible({ timeout: 10000 })
    console.log('Debug: Processing indicator hidden')

    // This is where the bug occurs - button should be enabled but remains disabled
    const buttonState = await page.locator('[data-testid="ai-command-improve"]').isEnabled()
    console.log(`Debug: Button enabled after processing: ${buttonState}`)

    // Check the text selection state after AI operation
    const hasSelection = await page.evaluate(() => {
      const selection = window.getSelection()
      return selection && selection.toString().length > 0
    })
    console.log(`Debug: Text still selected after AI operation: ${hasSelection}`)

    // Check if the issue is with text selection being lost
    if (!hasSelection) {
      console.log('Debug: Text selection was lost, re-selecting text')
      await page.keyboard.press('ControlOrMeta+a')
      await page.waitForTimeout(100)
      const hasSelectionAfterReselect = await page.evaluate(() => {
        const selection = window.getSelection()
        return selection && selection.toString().length > 0
      })
      console.log(`Debug: Text selected after re-selection: ${hasSelectionAfterReselect}`)
    }

    // Check if button is enabled after ensuring text is selected
    const buttonStateAfterSelection = await page.locator('[data-testid="ai-command-improve"]').isEnabled()
    console.log(`Debug: Button enabled after ensuring selection: ${buttonStateAfterSelection}`)

    // The actual assertion that fails
    await expect(page.locator('[data-testid="ai-command-improve"]')).toBeEnabled()
  })

  test('should test with a different response format', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    // Try a different approach - mock the response as JSON like other tests do
    await page.route('**/api/ai/generate', async route => {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await route.fulfill({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: 'Improved text content', success: true })
      })
    })

    const editor = await testUtils.waitForEditor()

    await testUtils.typeInEditor('Text to be improved')
    await page.keyboard.press('ControlOrMeta+a')

    await page.click('[data-testid="ai-command-improve"]')

    // Check if this approach works better
    await expect(page.locator('[data-testid="ai-processing"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-improve"]')).toBeDisabled()

    // Wait and check final state
    await expect(page.locator('[data-testid="ai-processing"]')).not.toBeVisible({ timeout: 10000 })
    
    // Test if this format allows the button to be re-enabled
    const isEnabled = await page.locator('[data-testid="ai-command-improve"]').isEnabled()
    console.log(`Debug: Button enabled with JSON response: ${isEnabled}`)
    
    if (isEnabled) {
      console.log('Success: JSON response format works!')
    } else {
      console.log('Issue: JSON response format also fails')
    }
  })
})
