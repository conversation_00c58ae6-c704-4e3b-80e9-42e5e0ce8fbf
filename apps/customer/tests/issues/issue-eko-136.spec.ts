import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-136: Feature Flags System', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display dashboard navigation with default feature flags', async ({ page }) => {
    await page.goto('/customer/dashboard', { timeout: 60000 })
    await page.waitForLoadState('networkidle', { timeout: 15000 })
    
    await expect(page.locator('[data-testid="nav-main-dashboard"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="nav-flags"]')).toBeVisible({ timeout: 10000 })
  })

  test('should display greenwashing navigation items when feature enabled', async ({ page }) => {
    await page.goto('/customer/dashboard', { timeout: 60000 })
    await page.waitForLoadState('networkidle', { timeout: 15000 })
    
    await expect(page.locator('[data-testid="nav-cherry-picking"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="nav-claims"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="nav-promises"]')).toBeVisible({ timeout: 10000 })
  })

  test('should display document creation navigation when feature enabled', async ({ page }) => {
    await page.goto('/customer/documents', { timeout: 60000 })
    await page.waitForLoadState('networkidle', { timeout: 15000 })
    
    await expect(page.locator('[data-testid="nav-create"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="nav-view"]')).toBeVisible({ timeout: 10000 })
  })

  test('should maintain consistent navigation across page loads', async ({ page }) => {
    await page.goto('/customer/dashboard', { timeout: 60000 })
    await page.waitForLoadState('networkidle', { timeout: 15000 })
    await expect(page.locator('[data-testid="nav-main-dashboard"]')).toBeVisible({ timeout: 10000 })
    
    await page.goto('/customer/documents', { timeout: 60000 })
    await page.waitForLoadState('networkidle', { timeout: 15000 })
    await expect(page.locator('[data-testid="nav-main-documents"]')).toBeVisible({ timeout: 10000 })
  })

  test('should load page content correctly with feature flags', async ({ page }) => {
    await page.goto('/customer/dashboard', { timeout: 60000 })
    await page.waitForLoadState('networkidle', { timeout: 15000 })
    
    expect(page.url()).toContain('/customer/dashboard')
    await expect(page.locator('[data-testid="nav-main-dashboard"]')).toBeVisible({ timeout: 10000 })
  })
})