import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-119: Share Panel Issues', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('Create SDG Report, share publicly, and verify no 404', async ({ page, context }) => {
    // Set generous timeout for this test as document loading takes time
    test.setTimeout(180000) // 3 minutes

    // Create a new document from EKO Report template (which creates an actual report structure)
    console.log('Creating document from template...')
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Add some content to make it an SDG Report
    console.log('Adding content to document...')
    await testUtils.fillEditor('# SDG Impact Report\n\nThis is a test SDG report for EKO-119.')

    // Wait for autosave to complete
    console.log('Waiting for autosave to complete...')
    await testUtils.waitForAutoSave()

    // Click the Share button in the collaboration toolbar
    console.log('Opening share panel...')
    await page.locator('[data-testid="share-button"]').click()
    
    // Wait for the side panel container to appear first
    await page.waitForSelector('[data-testid="close-side-panel"]', { timeout: 10000 })
    
    // Wait for the share tab to be active
    await page.waitForSelector('[data-testid="share-tab"]', { timeout: 5000 })
    
    // Finally wait for the share panel content
    await page.waitForSelector('[data-testid="share-panel"]', { timeout: 5000 })

    // Verify the share panel is visible
    const sharePanel = page.locator('[data-testid="share-panel"]')
    await expect(sharePanel).toBeVisible()

    // Verify copy button is visible
    const copyButton = page.locator('[data-testid="copy-share-link-button"]')
    await expect(copyButton).toBeVisible()

    // Make sure access is set to Public
    console.log('Setting document to public...')
    const publicToggle = page.locator('[data-testid="toggle-public-access-button"]')
    
    // Wait for the toggle to be available
    await expect(publicToggle).toBeVisible({ timeout: 10000 })
    
    // Check current state and toggle to public if needed
    const toggleText = await publicToggle.textContent()
    if (toggleText?.includes('Private') || toggleText?.includes('Off')) {
      await publicToggle.click()
      await page.waitForTimeout(2000) // Wait longer for the update to complete
    }

    // Verify it's now public
    await expect(page.locator('[data-testid="toggle-public-access-button"]:has-text("Public")')).toBeVisible({ timeout: 10000 })

    // Get the share link - look for readonly input in the share panel
    const shareInput = sharePanel.locator('input[readonly]').first()
    await expect(shareInput).toBeVisible({ timeout: 10000 })
    
    const shareLink = await shareInput.inputValue()
    console.log('Share link:', shareLink)

    // Verify the share link format is correct
    expect(shareLink).toMatch(/\/share\/public\/documents\/[a-f0-9-]+/)

    // Wait a bit longer for the database update to propagate
    console.log('Waiting for database update to propagate...')
    await page.waitForTimeout(3000)
    
    // Open the share link in a new page
    const newPage = await context.newPage()

    try {
      console.log('Navigating to public share link...')
      // Try to navigate with domcontentloaded instead of load to be faster
      const response = await newPage.goto(shareLink, {
        timeout: 60000,
        waitUntil: 'domcontentloaded'
      })

      console.log('Response status:', response?.status())

      // Check the HTTP status to ensure we don't get a 404
      expect(response?.status()).not.toBe(404)

      // Give the page a moment to render
      await newPage.waitForTimeout(3000)

      // Check that we're on the public document viewer page, not a 404 page
      // Look for the specific data-testid that indicates the public document viewer
      console.log('Checking for public document viewer...')
      await expect(newPage.locator('[data-testid="public-document-viewer"]')).toBeVisible({ timeout: 15000 })

      // Also check for the shared document indicator
      console.log('Checking for shared document indicator...')
      await expect(newPage.locator('text=Shared document - Read only')).toBeVisible({ timeout: 10000 })

      console.log('✅ Public document loaded successfully!')

    } finally {
      // Clean up
      await newPage.close()
    }


    console.log('✅ Test completed successfully - no 404 error when accessing public share link')
  })

  test('Share panel UI improvements', async ({ page }) => {
    // Test the UI improvements separately
    test.setTimeout(60000)
    
    // Create a document
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Open share panel
    await page.locator('[data-testid="share-button"]').click()
    
    // Wait for the side panel container to appear first
    await page.waitForSelector('[data-testid="close-side-panel"]', { timeout: 10000 })
    
    // Wait for the share tab to be active
    await page.waitForSelector('[data-testid="share-tab"]', { timeout: 5000 })
    
    // Finally wait for the share panel content
    await page.waitForSelector('[data-testid="share-panel"]', { timeout: 5000 })

    // Test share panel visibility
    const sharePanel = page.locator('[data-testid="share-panel"]')
    await expect(sharePanel).toBeVisible()
    
    // Test share link clickability - look for readonly input in the share panel
    const shareInput = sharePanel.locator('input[readonly]').first()
    await expect(shareInput).toBeVisible()
    
    // Test if input is clickable
    await shareInput.click()

    // Test copy button visibility and functionality
    const copyButton = page.locator('[data-testid="copy-share-link-button"]')
    await expect(copyButton).toBeVisible()
    
    // Click copy button and verify it works
    await copyButton.click()

    await expect(page.locator('button[title*="Copy"] svg').first()).toBeVisible({ timeout: 2000 })

    console.log('✅ Share panel UI improvements verified')
  })
})
