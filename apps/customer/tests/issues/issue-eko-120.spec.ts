import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-120: Comments Functionality', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display Comments button in collaboration toolbar', async ({ page }) => {
    // Create a document using the working test utils method
    const documentId = await testUtils.createDocumentFromTemplate('Blank Document')

    // Wait for editor to load
    await testUtils.waitForEditor()

    // Check that Comments button is visible and has correct attributes
    const commentsButton = page.locator('button:has-text("Comments")')
    await expect(commentsButton).toBeVisible()

    // Check that button has title attribute for accessibility
    await expect(commentsButton).toHaveAttribute('title', 'Comments')

    // Check that button has the MessageSquare icon
    await expect(commentsButton.locator('svg')).toBeVisible()
  })

  test('should open and close comments panel', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Initially, comments panel should not be visible
    await expect(page.locator('text=No comments yet')).not.toBeVisible()

    // Click Comments button to open panel (use specific data-testid)
    await page.click('[data-testid="comments-button"]')

    // Comments panel should now be visible
    await expect(page.locator('h3:has-text("Document Tools")')).toBeVisible()
    await expect(page.locator('text=No comments yet')).toBeVisible()

    // Check that the add comment form is visible
    await expect(page.locator('textarea[placeholder*="Add a comment"]')).toBeVisible()
    await expect(page.locator('button:has-text("Add Comment")')).toBeVisible()

    // Click Comments button again to close panel
    await page.click('[data-testid="comments-button"]')

    // Panel should be hidden
    await expect(page.locator('text=No comments yet')).not.toBeVisible()
  })

  test('should add a comment successfully', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = []
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text to comment on
    await editor.click()
    await page.keyboard.type('This is some text that will have a comment.')

    // Select the text
    await page.keyboard.press('ControlOrMeta+a')

    // Open comments panel
    await page.click('[data-testid="comments-button"]')
    await expect(page.locator('h3:has-text("Document Tools")')).toBeVisible()

    // Add a comment
    const commentText = 'This is a test comment for EKO-120'
    await page.fill('textarea[placeholder*="Add a comment"]', commentText)

    // Click add comment and wait a bit
    await page.click('[data-testid="add-comment-button"]')

    // Wait for any potential errors to be logged
    await page.waitForTimeout(3000)

    // Log any console errors for debugging
    if (consoleErrors.length > 0) {
      console.log('Console errors:', consoleErrors)
    }

    // Check if the button text changed to "Adding..." and back
    await expect(page.locator('[data-testid="add-comment-button"]')).toBeVisible({ timeout: 5000 })

    // For now, let's just check that no console errors occurred
    expect(consoleErrors.filter(error => error.includes('Error adding comment'))).toHaveLength(0)
  })

  test('should handle comment loading and real-time updates', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Open comments panel
    await page.click('[data-testid="comments-button"]')

    // Should show loading state initially (this might be very brief)
    // await expect(page.locator('text=Loading comments...')).toBeVisible({ timeout: 2000 })

    // Should eventually show "No comments yet" when loading completes
    await expect(page.locator('text=No comments yet')).toBeVisible({ timeout: 10000 })
  })

  test('should display comment author information', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add text and comment
    await editor.click()
    await page.keyboard.type('Text for author test')
    
    // Open comments panel and add comment
    await page.click('[data-testid="comments-button"]')
    const commentText = 'Comment to test author display'
    await page.fill('textarea[placeholder*="Add a comment"]', commentText)
    await page.click('[data-testid="add-comment-button"]')
    
    // Wait for comment to appear
    await expect(page.locator(`text=${commentText}`)).toBeVisible({ timeout: 5000 })
    
    // Should show author avatar or initials
    await expect(page.locator('[data-testid="comment-author-avatar"], .avatar')).toBeVisible()
    
    // Should show timestamp
    await expect(page.locator('text=/\\d+:\\d+|just now|\\d+ minutes? ago/')).toBeVisible()
  })

  test('should handle comment form validation', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Open comments panel
    await page.click('[data-testid="comments-button"]')

    // Try to submit empty comment
    const addButton = page.locator('[data-testid="add-comment-button"]')
    await expect(addButton).toBeDisabled()

    // Add some text
    await page.fill('textarea[placeholder*="Add a comment"]', 'Valid comment')
    await expect(addButton).toBeEnabled()

    // Clear text again
    await page.fill('textarea[placeholder*="Add a comment"]', '')
    await expect(addButton).toBeDisabled()
  })

  test('should show submitting state when adding comment', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Open comments panel
    await page.click('[data-testid="comments-button"]')

    // Add comment text
    await page.fill('textarea[placeholder*="Add a comment"]', 'Test comment for submitting state')

    // Click add comment and check for loading state
    await page.click('[data-testid="add-comment-button"]')

    // Should show submitting state briefly
    await expect(page.locator('button:has-text("Adding...")')).toBeVisible({ timeout: 1000 })

    // Should return to normal state
    await expect(page.locator('[data-testid="add-comment-button"]')).toBeVisible({ timeout: 5000 })
  })

  test('should handle comment errors gracefully', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Open comments panel
    await page.click('[data-testid="comments-button"]')

    // The comment functionality should work even if there are network issues
    // This test ensures the UI doesn't break under error conditions
    await expect(page.locator('textarea[placeholder*="Add a comment"]')).toBeVisible()
    await expect(page.locator('[data-testid="add-comment-button"]')).toBeVisible()
  })

  test('should maintain comments panel state when toggling', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Open comments panel
    await page.click('[data-testid="comments-button"]')
    await expect(page.locator('h3:has-text("Document Tools")')).toBeVisible()

    // Add some text to the comment form
    await page.fill('textarea[placeholder*="Add a comment"]', 'Draft comment')

    // Close and reopen panel
    await page.click('[data-testid="comments-button"]')
    await page.click('[data-testid="comments-button"]')
    
    // The form should be reset (this is expected behavior)
    await expect(page.locator('textarea[placeholder*="Add a comment"]')).toHaveValue('')
  })

  test('should show correct button states', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    const commentsButton = page.locator('[data-testid="comments-button"]')

    // Initially button should be in outline variant (not active)
    await expect(commentsButton).toHaveClass(/outline/)

    // Click to open panel
    await page.click('[data-testid="comments-button"]')

    // Button should now be in default variant (active)
    await expect(commentsButton).not.toHaveClass(/outline/)

    // Click to close panel
    await page.click('[data-testid="comments-button"]')

    // Button should return to outline variant
    await expect(commentsButton).toHaveClass(/outline/)
  })
})
