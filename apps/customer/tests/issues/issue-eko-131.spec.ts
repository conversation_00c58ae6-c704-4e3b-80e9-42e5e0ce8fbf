import { test, expect } from '@playwright/test'
import fs from 'fs';
import path from 'path';

test.describe('EKO-131: Table of Contents Extension', () => {
  test('should have TableOfContentsExtension file present', async ({ }) => {
    // This is a simple file existence test to verify our implementation
    
    const extensionPath = path.join(process.cwd(), 'components/editor/extensions/TableOfContentsExtension.tsx')
    const fileExists = fs.existsSync(extensionPath)
    
    expect(fileExists).toBe(true)
  })

  test('should have updated SlashCommands with table of contents', async ({ }) => {
    const fs = require('fs')
    const path = require('path')
    
    const slashCommandsPath = path.join(process.cwd(), 'components/editor/extensions/SlashCommands.tsx')
    const content = fs.readFileSync(slashCommandsPath, 'utf8')
    
    // Check that the table of contents command was added
    expect(content).toContain('Table of Contents')
    expect(content).toContain('Insert table of contents')
    expect(content).toContain('ListTree')
    expect(content).toContain('tableOfContents')
    expect(content).toContain('toc')
  })

  test('should have updated EditorToolbar with table of contents button', async ({ }) => {
    const fs = require('fs')
    const path = require('path')
    
    const toolbarPath = path.join(process.cwd(), 'components/editor/toolbar/EditorToolbar.tsx')
    const content = fs.readFileSync(toolbarPath, 'utf8')
    
    // Check that the table of contents toolbar button was added
    expect(content).toContain('addTableOfContents')
    expect(content).toContain('Insert Table of Contents')
    expect(content).toContain('setTableOfContents')
    expect(content).toContain('ListTree')
  })

  test('should have updated EkoDocumentEditor with TableOfContentsExtension', async ({ }) => {
    const fs = require('fs')
    const path = require('path')
    
    const editorPath = path.join(process.cwd(), 'components/editor/EkoDocumentEditor.tsx')
    const content = fs.readFileSync(editorPath, 'utf8')
    
    // Check that the extension is imported and configured
    expect(content).toContain('TableOfContentsExtension')
    expect(content).toContain('from \'./extensions/TableOfContentsExtension\'')
  })

  test('should have updated document templates to include table of contents', async ({ }) => {
    const fs = require('fs')
    const path = require('path')
    
    const templatesPath = path.join(process.cwd(), 'components/editor/templates/DocumentTemplates.tsx')
    const content = fs.readFileSync(templatesPath, 'utf8')
    
    // Check that templates include table of contents markers
    expect(content).toContain('TABLE_OF_CONTENTS')
    expect(content).toContain('tableOfContents')
  })

  test('should have updated dynamic template generator to include table of contents', async ({ }) => {
    const fs = require('fs')
    const path = require('path')
    
    const generatorPath = path.join(process.cwd(), 'components/editor/templates/dynamic-template-generator.ts')
    const content = fs.readFileSync(generatorPath, 'utf8')
    
    // Check that dynamic templates include table of contents at the beginning
    expect(content).toContain('Table of Contents at the beginning')
    expect(content).toContain('tableOfContents')
    expect(content).toContain('table-of-contents')
  })

  test('should have updated document page to process table of contents markers', async ({ }) => {
    const fs = require('fs')
    const path = require('path')
    
    const documentPagePath = path.join(process.cwd(), 'app/customer/documents/page.tsx')
    const content = fs.readFileSync(documentPagePath, 'utf8')
    
    // Check that the template processing includes table of contents handling
    expect(content).toContain('TABLE_OF_CONTENTS')
    expect(content).toContain('table-of-contents')
    expect(content).toContain('processedContent')
  })

  test('should validate TableOfContentsExtension structure', async ({ }) => {
    const fs = require('fs')
    const path = require('path')
    
    const extensionPath = path.join(process.cwd(), 'components/editor/extensions/TableOfContentsExtension.tsx')
    const content = fs.readFileSync(extensionPath, 'utf8')
    
    // Check that the extension has all required parts
    expect(content).toContain('TableOfContentsExtension')
    expect(content).toContain('TableOfContentsComponent')
    expect(content).toContain('addCommands')
    expect(content).toContain('setTableOfContents')
    expect(content).toContain('addNodeView')
    expect(content).toContain('ReactNodeViewRenderer')
    expect(content).toContain('parseHTML')
    expect(content).toContain('renderHTML')
    expect(content).toContain('table-of-contents')
    expect(content).toContain('HeadingItem')
    expect(content).toContain('glass-card')
    expect(content).toContain('No headings found')
  })
})