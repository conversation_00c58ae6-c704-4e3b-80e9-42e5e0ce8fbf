import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-135: Move charts to Shadcn/Recharts', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should render Recharts-based charts when chart data is present', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('EKO Report')
    await testUtils.waitForEditor()
    
    // Create a simple chart for testing
    const chartData = {
      type: 'bar',
      title: 'Test Chart',
      description: 'A test chart for EKO-135',
      data: [
        { month: 'Jan', revenue: 100 },
        { month: 'Feb', revenue: 150 }
      ],
      config: {
        revenue: { label: 'Revenue', color: 'hsl(210, 70%, 50%)' }
      }
    }
    
    // Insert chart using TipTap commands
    await page.evaluate((data) => {
      const editor = (window as any).testEditor
      const encodedData = btoa(JSON.stringify(data))
      editor.commands.insertContent(`<chart data-json="${encodedData}"></chart>`)
    }, chartData)
    
    // Wait for chart container to appear
    await expect(page.locator('[data-testid="chart-container"]')).toBeVisible()
    
    // Verify Recharts-based chart renders
    await expect(page.locator('[data-testid="recharts-chart"]')).toBeVisible()
    
    // Verify chart title and description
    await expect(page.locator('[data-testid="chart-title"]')).toHaveText('Test Chart')
    await expect(page.locator('[data-testid="chart-description"]')).toHaveText('A test chart for EKO-135')
    
    // Verify no chart errors
    await expect(page.locator('[data-testid="chart-error"]')).toHaveCount(0)
  })

  // test('should render legacy eCharts format with eCharts renderer', async ({ page }) => {
  //   await testUtils.createDocumentFromTemplate('EKO Report')
  //   await testUtils.waitForEditor()
  //
  //   // Create legacy eCharts format data (no 'type' field indicates legacy format)
  //
  //   const legacyChartData = {
  //     title: { text: 'Legacy Chart' },
  //     xAxis: { type: 'category', data: ['A', 'B', 'C'] },
  //     yAxis: { type: 'value' },
  //     series: [{ data: [120, 200, 150], type: 'bar' }]
  //   }
  //
  //   // Insert chart using TipTap commands
  //   await page.evaluate((data) => {
  //     const editor = (window as any).testEditor
  //     const encodedData = btoa(JSON.stringify(data))
  //     editor.commands.insertContent(`<chart data-json="${encodedData}"></chart>`)
  //   }, legacyChartData)
  //
  //   // Wait for chart container to appear
  //   await expect(page.locator('[data-testid="chart-container"]')).toBeVisible()
  //
  //   // Verify eCharts renderer is used (legacy format)
  //   await expect(page.locator('[data-testid="echarts-chart"]')).toBeVisible()
  //
  //   // Verify this is a legacy chart by checking for absence of Recharts elements
  //   await expect(page.locator('[data-testid="recharts-chart"]')).toHaveCount(0)
  //
  //   // Verify no chart errors
  //   await expect(page.locator('[data-testid="chart-error"]')).toHaveCount(0)
  // })

  test('should display error for invalid chart data', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('EKO Report')
    await testUtils.waitForEditor()
    
    // Create invalid chart data
    const invalidChartData = 'invalid json{'
    
    // Insert chart using TipTap commands
    await page.evaluate((data) => {
      const editor = (window as any).testEditor
      const encodedData = btoa(data)
      editor.commands.insertContent(`<chart data-json="${encodedData}"></chart>`)
    }, invalidChartData)
    
    // Wait for error message to appear
    await expect(page.locator('[data-testid="chart-error"]')).toBeVisible()
    
    // Verify error message content
    const errorMessage = page.locator('[data-testid="chart-error"]')
    await expect(errorMessage).toContainText('Invalid chart JSON')
    
    // Verify no chart containers or other chart elements appear
    await expect(page.locator('[data-testid="recharts-chart"]')).toHaveCount(0)
    await expect(page.locator('[data-testid="echarts-chart"]')).toHaveCount(0)
  })

  test('should render different chart types correctly', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('EKO Report')
    await testUtils.waitForEditor()
    
    const chartTypes = [
      {
        type: 'area',
        title: 'Area Chart',
        data: [
          { month: 'Jan', value: 100 },
          { month: 'Feb', value: 150 },
          { month: 'Mar', value: 120 }
        ],
        config: { value: { label: 'Value', color: 'hsl(210, 70%, 50%)' } }
      },
      {
        type: 'line',
        title: 'Line Chart', 
        data: [
          { day: 'Mon', temp: 20 },
          { day: 'Tue', temp: 22 },
          { day: 'Wed', temp: 18 }
        ],
        config: { temp: { label: 'Temperature', color: 'hsl(0, 70%, 50%)' } }
      },
      {
        type: 'pie',
        title: 'Pie Chart',
        data: [
          { category: 'A', value: 30 },
          { category: 'B', value: 45 },
          { category: 'C', value: 25 }
        ],
        config: {
          A: { label: 'Category A', color: 'hsl(0, 70%, 50%)' },
          B: { label: 'Category B', color: 'hsl(120, 70%, 50%)' },
          C: { label: 'Category C', color: 'hsl(240, 70%, 50%)' }
        }
      }
    ]
    
    // Insert charts one by one using TipTap commands
    for (const chartConfig of chartTypes) {
      await page.evaluate((data) => {
        const editor = (window as any).testEditor
        const encodedData = btoa(JSON.stringify(data))
        editor.commands.insertContent(`<chart data-json="${encodedData}"></chart>`)
        editor.commands.insertContent('<p></p>') // Add paragraph between charts
      }, chartConfig)
    }
    
    // Wait for all charts to render
    await expect(page.locator('[data-testid="chart-container"]')).toHaveCount(3)
    
    // Verify all charts are using Recharts (new format)
    await expect(page.locator('[data-testid="recharts-chart"]')).toHaveCount(3)
    
    // Verify chart titles
    await expect(page.locator('[data-testid="chart-title"]').nth(0)).toHaveText('Area Chart')
    await expect(page.locator('[data-testid="chart-title"]').nth(1)).toHaveText('Line Chart')
    await expect(page.locator('[data-testid="chart-title"]').nth(2)).toHaveText('Pie Chart')
    
    // Verify no chart errors
    await expect(page.locator('[data-testid="chart-error"]')).toHaveCount(0)

  })
})
