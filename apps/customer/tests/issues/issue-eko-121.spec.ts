import { expect, test } from '@playwright/test'
import { login } from '../helpers/auth'

test.describe('Issue EKO-121: Real AI Generate API Tests', () => {
  const API_ENDPOINT = '/api/ai/generate'

  test.beforeEach(async ({ page }) => {
    // Login user before each test to access the API
    await login(page)
  })

  test('should call real AI API for spelling corrections', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Fix spelling and grammar errors',
        selectedText: 'The qick brown fox jumps over the lasy dog. This sentance has mispellings.',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()
    
    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)
    
    // Real AI should fix spelling errors
    expect(data.text.toLowerCase()).toContain('quick')
    expect(data.text.toLowerCase()).toContain('lazy')
    expect(data.text.toLowerCase()).not.toContain('qick')
    expect(data.text.toLowerCase()).not.toContain('lasy')
  })

  test('should call real AI API for text improvement', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Improve writing clarity and flow',
        selectedText: 'The thing is that we need to do the stuff because it is important.',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()
    
    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    expect(data.text.length).toBeGreaterThan(10)
    
    // Real AI should improve vague language
    expect(data.text.toLowerCase()).not.toContain('the thing is')
    expect(data.text.toLowerCase()).not.toContain('the stuff')
  })

  test('should handle streaming responses from real AI', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Fix spelling errors',
        selectedText: 'This is a test sentance with mispellings.',
        stream: true
      }
    })

    expect(response.status()).toBe(200)
    expect(response.headers()['content-type']).toContain('text/plain')
    
    const text = await response.text()
    expect(text).toBeTruthy()
    expect(text.length).toBeGreaterThan(10)
  })

  test('should return error for missing prompt', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        selectedText: 'Some text to work with',
        stream: false
      }
    })

    expect(response.status()).toBe(400)
    const data = await response.json()
    
    expect(data.error).toBe('Prompt is required')
    expect(data.success).toBeFalsy()
  })

  test('should handle real AI with document context', async ({ request }) => {
    const response = await request.post(API_ENDPOINT, {
      data: {
        prompt: 'Improve this sentence to fit the document context',
        selectedText: 'The company did good.',
        documentContent: 'This is a formal business report analyzing quarterly performance metrics and strategic initiatives.',
        stream: false
      }
    })

    expect(response.status()).toBe(200)
    const data = await response.json()
    
    expect(data.success).toBe(true)
    expect(data.text).toBeTruthy()
    
    // Real AI should use more formal language
    expect(data.text.toLowerCase()).not.toContain('did good')
  })
})
