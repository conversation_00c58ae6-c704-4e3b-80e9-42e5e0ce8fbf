import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-132: Right Click Context Menu', () => {
  let testUtils: TestUtils
  
  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    
    // Login first
    await testUtils.login()
    
    // Create a new document with the editor
    await testUtils.createDocumentFromTemplate()
    
    // Wait for the editor to be available
    await page.waitForSelector('[data-testid="eko-document-editor"]', { timeout: 10000 })
    await page.waitForSelector('.ProseMirror', { timeout: 5000 })
  })

  test('should show context menu on right click in editor', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click in the editor
    await editor.click({ button: 'right' })
    
    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Should have basic menu items
    await expect(page.locator('[data-testid="context-menu-selectAll"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insert"]')).toBeVisible()
  })

  test('should show clipboard operations when text is selected', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Type some text
    await editor.click()
    await page.keyboard.type('Test text for clipboard operations')

    // Select all text
    await page.keyboard.press('ControlOrMeta+a')

    // Wait a moment for selection to register
    await page.waitForTimeout(100)

    // Right click on selected text
    await editor.click({ button: 'right' })

    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()

    // Should show clipboard operations (only when text is selected)
    await expect(page.locator('[data-testid="context-menu-copy"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-cut"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-paste"]')).toBeVisible()
  })

  test('should show formatting options for selected text', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Type and select text
    await editor.click()
    await page.keyboard.type('Test formatting text')
    await page.keyboard.press('ControlOrMeta+a')

    // Wait a moment for selection to register
    await page.waitForTimeout(100)

    // Right click
    await editor.click({ button: 'right' })

    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()

    // Should show format submenu (using correct ID: formatting)
    await expect(page.locator('[data-testid="context-menu-formatting"]')).toBeVisible()

    // Hover over Format to show submenu
    await page.locator('[data-testid="context-menu-formatting"]').hover()

    // Should show formatting options in submenu
    await expect(page.locator('[data-testid="context-submenu-formatting"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-heading1"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-heading2"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-bulletList"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-orderedList"]')).toBeVisible()
  })

  test('should show comprehensive Insert submenu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click in editor
    await editor.click({ button: 'right' })
    
    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Hover over Insert menu to show submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    
    // Should show insert submenu
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Should show report-specific items (using correct IDs)
    await expect(page.locator('[data-testid="context-menu-insertTOC"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertReportSummary"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertReportSection"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertReportGroup"]')).toBeVisible()
    
    // Should show multi-columns with submenu
    await expect(page.locator('[data-testid="context-menu-insertColumns"]')).toBeVisible()
    
    // Should show basic content types
    await expect(page.locator('[data-testid="context-menu-insertTable"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertChart"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertDetails"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertCodeBlock"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertBlockquote"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertMath"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertHorizontalRule"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insertImage"]')).toBeVisible()
  })

  test('should show multi-column layout submenu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Right click and navigate to Multi-Columns
    await editor.click({ button: 'right' })

    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()

    // Hover over Insert to show submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()

    // Force the submenu to be visible by adding a class override
    await page.evaluate(() => {
      const submenu = document.querySelector('[data-testid="context-submenu-insertColumns"]')
      if (submenu) {
        submenu.classList.remove('hidden')
        submenu.classList.add('block')
      }
    })

    // Hover over Multi-Columns button to trigger the submenu
    await page.locator('[data-testid="context-menu-insertColumns"]').scrollIntoViewIfNeeded()
    await page.locator('[data-testid="context-menu-insertColumns"]').hover()

    // Wait a moment for any transitions
    await page.waitForTimeout(300)

    // Should show column layout options (using correct IDs)
    await expect(page.locator('[data-testid="context-submenu-insertColumns"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns2Equal"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns3Equal"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns4Equal"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns1-2"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columns2-1"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-columnsCentered"]')).toBeVisible()
  })

  test('should insert table when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and insert table
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Navigate to Insert submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Click table option
    await page.locator('[data-testid="context-menu-insertTable"]').click()
    
    // Should have inserted a table
    await expect(editor.locator('table')).toBeVisible()
    await expect(editor.locator('tr')).toHaveCount(3) // 3 rows by default
    await expect(editor.locator('td, th')).toHaveCount(9) // 3x3 = 9 cells
  })

  test('should insert report section when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and insert report section
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Navigate to Insert submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Click report section option
    await page.locator('[data-testid="context-menu-insertReportSection"]').click()
    
    // Should have inserted a report section component
    await expect(editor.locator('[data-type="reportSection"]')).toBeVisible()
  })

  test('should insert chart when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and insert chart
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Navigate to Insert submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Click chart option
    await page.locator('[data-testid="context-menu-insertChart"]').click()
    
    // Should have inserted a chart component
    await expect(editor.locator('[data-type="chart"]')).toBeVisible()
  })

  test('should insert 2-column layout when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click and navigate to columns
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Navigate to Insert submenu
    await page.locator('[data-testid="context-menu-insert"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insert"]')).toBeVisible()
    
    // Navigate to Multi-Columns submenu
    await page.locator('[data-testid="context-menu-insertColumns"]').hover()
    await expect(page.locator('[data-testid="context-submenu-insertColumns"]')).toBeVisible()
    
    // Click 2 columns equal option
    await page.locator('[data-testid="context-menu-columns2Equal"]').click()
    
    // Should have inserted columns
    await expect(editor.locator('[data-type="columns"]')).toBeVisible()
    await expect(editor.locator('[data-type="column"]')).toHaveCount(2)
  })

  test('should show Add Comment option when text is selected and onAddComment is provided', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Type and select text
    await editor.click()
    await page.keyboard.type('Text to comment on')
    await page.keyboard.press('ControlOrMeta+a')

    // Wait a moment for selection to register
    await page.waitForTimeout(100)

    // Right click
    await editor.click({ button: 'right' })

    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()

    // Should show Add Comment option (only when text is selected and onAddComment is provided)
    await expect(page.locator('[data-testid="context-menu-addComment"]')).toBeVisible()
  })

  test('should add link when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Type and select text
    await editor.click()
    await page.keyboard.type('Link text')
    await page.keyboard.press('ControlOrMeta+a')

    // Wait a moment for selection to register
    await page.waitForTimeout(100)

    // Set up dialog handler before triggering the action
    page.on('dialog', async dialog => {
      expect(dialog.message()).toContain('Enter URL')
      await dialog.accept('https://example.com')
    })

    // Right click and add link
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()

    // Click add link option
    await page.locator('[data-testid="context-menu-addLink"]').click()

    // Should have created a link
    await expect(editor.locator('a[href="https://example.com"]')).toBeVisible()
  })

  test('should apply heading formatting when selected from context menu', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Type and select text
    await editor.click()
    await page.keyboard.type('Heading text')
    await page.keyboard.press('ControlOrMeta+a')

    // Wait a moment for selection to register
    await page.waitForTimeout(100)

    // Right click and format as heading
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()

    // Navigate to Format submenu
    await page.locator('[data-testid="context-menu-formatting"]').hover()
    await expect(page.locator('[data-testid="context-submenu-formatting"]')).toBeVisible()

    // Click Heading 1 option
    await page.locator('[data-testid="context-menu-heading1"]').click()

    // Should have applied heading formatting
    await expect(editor.locator('h1')).toHaveText('Heading text')
  })

  test('should close context menu when clicking elsewhere', async ({ page }) => {
    const editor = await testUtils.waitForEditor()
    
    // Right click to show menu
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()
    
    // Click elsewhere to close
    await page.click('body', { position: { x: 100, y: 100 } })
    
    // Menu should be hidden
    await expect(page.locator('[data-testid="right-click-context-menu"]')).not.toBeVisible()
  })

  test('should close context menu when pressing Escape', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Right click to show menu
    await editor.click({ button: 'right' })
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()

    // Press Escape to close
    await page.keyboard.press('Escape')

    // Menu should be hidden
    await expect(page.locator('[data-testid="right-click-context-menu"]')).not.toBeVisible()
  })

  test('should show AI Suggestions when AI is enabled', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Right click
    await editor.click({ button: 'right' })

    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()

    // Should show AI Suggestions option (if AI is enabled)
    // Note: This may not be visible if onAISuggestion is not provided
    const aiSuggestion = page.locator('[data-testid="context-menu-aiSuggestion"]')
    if (await aiSuggestion.isVisible()) {
      await expect(aiSuggestion).toBeVisible()
    }
  })

  test('should position context menu correctly near cursor', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Right click at a specific position
    await editor.click({ button: 'right', position: { x: 200, y: 150 } })

    const contextMenu = page.locator('[data-testid="right-click-context-menu"]')
    await expect(contextMenu).toBeVisible()

    // Menu should be positioned near the click position
    const menuBox = await contextMenu.boundingBox()
    expect(menuBox).toBeTruthy()

    if (menuBox) {
      // Menu should be near the click position (allowing for viewport adjustments)
      expect(menuBox.x).toBeGreaterThan(150)
      expect(menuBox.x).toBeLessThan(300)
      expect(menuBox.y).toBeGreaterThan(100)
      expect(menuBox.y).toBeLessThan(250)
    }
  })

  test('should not show context menu when right-clicking outside editor', async ({ page }) => {
    // Wait for editor to be ready first
    await testUtils.waitForEditor()

    // Right click outside the editor (on the body)
    await page.click('body', { button: 'right', position: { x: 50, y: 50 } })

    // Wait a moment
    await page.waitForTimeout(500)

    // Context menu should not be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).not.toBeVisible()
  })

  test('should handle conditional menu items correctly', async ({ page }) => {
    const editor = await testUtils.waitForEditor()

    // Right click without selecting text
    await editor.click({ button: 'right' })

    // Context menu should be visible
    await expect(page.locator('[data-testid="right-click-context-menu"]')).toBeVisible()

    // Copy and Cut should NOT be visible when no text is selected (they are conditionally rendered)
    await expect(page.locator('[data-testid="context-menu-copy"]')).not.toBeVisible()
    await expect(page.locator('[data-testid="context-menu-cut"]')).not.toBeVisible()

    // But Select All and Insert should always be visible
    await expect(page.locator('[data-testid="context-menu-selectAll"]')).toBeVisible()
    await expect(page.locator('[data-testid="context-menu-insert"]')).toBeVisible()

    // Paste should be visible (canPaste is always true)
    await expect(page.locator('[data-testid="context-menu-paste"]')).toBeVisible()
  })
})
