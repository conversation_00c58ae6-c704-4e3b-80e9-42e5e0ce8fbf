import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-123: Document -> Create navigation option', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`)
    })

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should navigate to /customer/documents/new when clicking Documents -> Create', async ({ page }) => {
    // Start from the dashboard or any other page
    await page.goto('/customer/dashboard')
    await page.waitForLoadState('networkidle')

    // Look for Documents menu item in navigation - use more specific selector
    const documentsMenu = page.locator('div').filter({ hasText: /^Documents$/ })
    await expect(documentsMenu).toBeVisible()

    // Look for Create submenu item - use role-based selector
    const createLink = page.getByRole('link', { name: 'Create' })
    await expect(createLink).toBeVisible()
    await createLink.click()

    // Should navigate to the new route
    await page.waitForURL('/customer/documents/new', { timeout: 10000 })

    // Verify we're on the right page
    expect(page.url()).toContain('/customer/documents/new')
  })

  test('should display template selection as a page instead of dialog', async ({ page }) => {
    // Navigate directly to the new route
    await page.goto('/customer/documents/new')
    await page.waitForLoadState('networkidle')

    // Verify the page title is set correctly
    await expect(page).toHaveTitle(/Create New Document/)

    // Should see the template dialog content but NOT in a dialog wrapper
    await expect(page.locator('[data-testid="template-dialog"]')).toBeVisible()

    // Should NOT see actual dialog wrapper elements
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()

    // Should see "Choose a Template" heading
    await expect(page.getByRole('heading', { name: 'Choose a Template' })).toBeVisible()

    // Should see template options - use more specific selectors
    await expect(page.getByRole('heading', { name: 'Blank Document' })).toBeVisible()
    await expect(page.getByRole('heading', { name: 'Meeting Notes' })).toBeVisible()
  })

  test('should navigate to created document after template selection', async ({ page }) => {
    // Navigate to the new document creation page
    await page.goto('/customer/documents/new')
    await page.waitForLoadState('networkidle')

    // Wait for template options to load
    await expect(page.locator('[data-testid="template-dialog"]')).toBeVisible()

    // Select Blank Document template using data-testid
    const blankTemplate = page.locator('[data-testid="template-blank"]')
    await expect(blankTemplate).toBeVisible()
    await blankTemplate.click()

    // Should navigate to the created document
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 15000 })

    // Verify we're on a document page (not the list or new page)
    expect(page.url()).toMatch(/\/customer\/documents\/[a-f0-9-]+$/)
    expect(page.url()).not.toContain('/customer/documents/new')
    expect(page.url()).not.toBe('/customer/documents')

    // Should see the editor
    await testUtils.waitForEditor()
  })

  test('should handle cancel/back navigation properly', async ({ page }) => {
    // Navigate to the new document creation page
    await page.goto('/customer/documents/new')
    await page.waitForLoadState('networkidle')

    // Wait for template dialog to be visible
    await expect(page.locator('[data-testid="template-dialog"]')).toBeVisible()

    // Click the Cancel button using data-testid
    const cancelButton = page.locator('[data-testid="cancel-button"]')
    await expect(cancelButton).toBeVisible()
    await cancelButton.click()

    // Should navigate back to documents list page
    await page.waitForURL('/customer/documents', { timeout: 10000 })
    expect(page.url()).toContain('/customer/documents')
    expect(page.url()).not.toContain('/customer/documents/new')
  })

  test('should maintain entity/run selection functionality', async ({ page }) => {
    // Navigate to the new document creation page
    await page.goto('/customer/documents/new')
    await page.waitForLoadState('networkidle')

    // Should see the Entity and Run selector
    await expect(page.getByRole('heading', { name: 'Select Entity and Run' })).toBeVisible()

    // Should see the DocumentEntityRunSelector component
    // Note: The actual entity/run selector functionality testing depends on
    // having test data, so we'll just verify the UI elements are present
    await expect(page.getByText('Entity:', { exact: true })).toBeVisible()
    await expect(page.getByText('Run:', { exact: true })).toBeVisible()
  })

  test('should create document with template metadata', async ({ page }) => {
    // Navigate to the new document creation page
    await page.goto('/customer/documents/new')
    await page.waitForLoadState('networkidle')

    // Select a specific template (Meeting Notes) using data-testid
    const meetingNotesTemplate = page.locator('[data-testid="template-meeting-notes"]')
    await expect(meetingNotesTemplate).toBeVisible()
    await meetingNotesTemplate.click()

    // Wait for navigation to the created document
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 15000 })

    // Verify the document was created with the correct title
    // The document should have "Meeting Notes" as the title since it's not "Blank Document"
    await testUtils.waitForEditor()

    // The document should contain the meeting notes template content
    await expect(page.locator('.ProseMirror')).toContainText('Meeting Notes')
    await expect(page.locator('.ProseMirror')).toContainText('Agenda')
  })

  test('should work with dynamic templates', async ({ page }) => {
    // Navigate to the new document creation page
    await page.goto('/customer/documents/new')
    await page.waitForLoadState('networkidle')

    // Wait for dynamic templates to load
    await page.waitForTimeout(2000)

    // Should see both static and dynamic templates
    await expect(page.getByRole('heading', { name: 'Blank Document' })).toBeVisible()

    // If there are dynamic templates, they should be visible too
    // Note: Dynamic templates depend on backend data, so we just verify the loading process
    const loadingText = page.getByText('Loading dynamic templates...')
    if (await loadingText.isVisible()) {
      await expect(loadingText).not.toBeVisible({ timeout: 10000 })
    }
  })

  test('should filter templates by category', async ({ page }) => {
    // Navigate to the new document creation page
    await page.goto('/customer/documents/new')
    await page.waitForLoadState('networkidle')

    // Should see category filter buttons using data-testid
    await expect(page.locator('[data-testid="category-filter-all"]')).toBeVisible()
    await expect(page.locator('[data-testid="category-filter-basic"]')).toBeVisible()

    // Click on Business category
    const businessFilter = page.locator('[data-testid="category-filter-business"]')
    await expect(businessFilter).toBeVisible()
    await businessFilter.click()

    // Should only see business templates
    await expect(page.getByRole('heading', { name: 'Meeting Notes' })).toBeVisible()
    await expect(page.getByRole('heading', { name: 'Project Proposal' })).toBeVisible()

    // Should not see basic templates when Business is selected
    // Note: Blank Document is in Basic category, so it shouldn't be visible
    // when Business filter is active
  })
})
