import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

// Helper function to scroll to and click EKO Report template
async function scrollAndClickESGTemplate(page: any) {
  // Find the EKO Report template card
  const esgTemplate = page.locator('[data-testid="template-dialog"] .grid > div').filter({ hasText: 'EKO Report' }).first()

  // First try scrollIntoViewIfNeeded
  await esgTemplate.scrollIntoViewIfNeeded();

  // If that doesn't work, use mouse wheel as fallback
  try {
    await expect(esgTemplate).toBeVisible({ timeout: 2000 });
  } catch {
    // Fallback to mouse wheel scrolling
    await page.mouse.wheel(0, 1000);
    await page.waitForTimeout(500);
  }

  // Verify the template is visible and enabled after scrolling
  await expect(esgTemplate).toBeVisible();
  await expect(esgTemplate).toBeEnabled();

  // Click the template
  await esgTemplate.click({ force: true });

  return esgTemplate;
}

test.describe('Issue EKO-126: Document Templates Test Review', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  });

  test('should reproduce the original issue - template selection viewport problem', async ({ page }) => {
    await page.goto('/customer/documents');
    await page.click('text=New Document');

    // Wait for template dialog to load
    await page.waitForSelector('[data-testid="template-dialog"]', { timeout: 10000 });

    // The original issue: template was outside viewport and couldn't be clicked
    // This test verifies the fix works by scrolling and using force click

    // Use helper function to scroll and click EKO Report template
    await scrollAndClickESGTemplate(page);

    // Verify document creation succeeded
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/);
    
    // Verify the document editor loaded correctly
    await expect(page.locator('.ProseMirror')).toBeVisible();
    
    // Verify report components are present
    // EKO Report creates 4 summaries: exec-summary, ecological-summary, social-summary, governance-summary
    await expect(page.locator('.report-summary')).toHaveCount(4);
    await expect(page.locator('.report-group')).toHaveCount(6);
    // Dynamic template creates many sections based on available model sections in database
    // Just verify we have some sections rather than exact count since it depends on data
    const sectionCount = await page.locator('.report-section').count();
    expect(sectionCount).toBeGreaterThan(0);
  });

  test('should verify template dialog scrolling behavior', async ({ page }) => {
    await page.goto('/customer/documents');
    await page.click('text=New Document');
    
    // Wait for template dialog to load
    await page.waitForSelector('[data-testid="template-dialog"]', { timeout: 10000 });
    
    // Check that the dialog has a scrollable area
    const templateGrid = page.locator('[data-testid="template-dialog"] .grid');
    await expect(templateGrid).toBeVisible();
    
    // Check that templates are present
    const templates = page.locator('[data-testid="template-dialog"] .grid > div');
    const templateCount = await templates.count();
    expect(templateCount).toBeGreaterThan(0);

    // Verify EKO Report template exists
    const esgTemplate = templates.filter({ hasText: 'EKO Report' })
    await expect(esgTemplate).toHaveCount(1);
    
    // Test scrolling behavior using scrollIntoViewIfNeeded
    await esgTemplate.first().scrollIntoViewIfNeeded();

    // Template should be accessible after scrolling
    await expect(esgTemplate.first()).toBeVisible();
    await expect(esgTemplate.first()).toBeEnabled();
  });

  test('should verify all template selection methods work', async ({ page }) => {
    await page.goto('/customer/documents');
    await page.click('text=New Document');
    
    // Wait for template dialog to load
    await page.waitForSelector('[data-testid="template-dialog"]', { timeout: 10000 });
    
    // Method 1: Direct click (might fail if outside viewport)
    const esgTemplate = page.locator('[data-testid="template-dialog"] .grid > div').filter({ hasText: 'EKO Report' }).first()
    
    try {
      await esgTemplate.click({ timeout: 2000 });
    } catch (error) {
      // Method 2: Use helper function for reliable scroll and click
      await scrollAndClickESGTemplate(page);
    }
    
    // Verify document creation succeeded regardless of method used
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/);
    await expect(page.locator('.ProseMirror')).toBeVisible();
  });

  test('should verify template dialog UI structure', async ({ page }) => {
    await page.goto('/customer/documents');
    await page.click('text=New Document');
    
    // Wait for template dialog to load
    await page.waitForSelector('[data-testid="template-dialog"]', { timeout: 10000 });
    
    // Verify dialog structure
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('[data-testid="template-dialog"]')).toBeVisible();
    
    // Verify grid layout
    const templateGrid = page.locator('[data-testid="template-dialog"] .grid');
    await expect(templateGrid).toBeVisible();
    
    // Verify templates have proper structure
    const templates = page.locator('[data-testid="template-dialog"] .grid > div');
    const firstTemplate = templates.first();
    
    // Each template should have title and description
    await expect(firstTemplate.locator('h3')).toBeVisible();
    await expect(firstTemplate.locator('p')).toBeVisible();
    
    // Verify hover effects work
    await firstTemplate.hover();
    
    // Verify Cancel button works
    await page.click('text=Cancel');
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });
});
