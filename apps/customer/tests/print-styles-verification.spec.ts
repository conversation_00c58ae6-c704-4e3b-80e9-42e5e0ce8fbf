import { test, expect } from '@playwright/test'

test.describe('Print Styles Verification', () => {
  test('should have correct Tailwind print utilities applied to report components', async ({ page }) => {
    // Create a simple HTML page with our report components to test the CSS
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          /* Additional styles for backdrop-blur simulation since CDN might not have it */
          .backdrop-blur-sm {
            backdrop-filter: blur(4px);
          }
        </style>
      </head>
      <body>
        <div class="report-summary relative border-2 border-purple-200 rounded-lg p-4 my-4 bg-purple-50/30 backdrop-blur-sm print:border-none print:bg-transparent print:backdrop-blur-none print:p-0 print:m-0 print:rounded-none">
          <div class="control-header absolute -top-3 left-4 flex items-center gap-2 px-3 py-1 rounded-full border bg-purple-100 border-purple-200 print:hidden">
            <span>Summary Control</span>
          </div>
          <h2>Test Summary</h2>
          <p>This is summary content that should be visible in print mode.</p>
        </div>

        <div class="report-group relative border-2 border-green-200 rounded-lg p-4 my-4 bg-green-50/30 backdrop-blur-sm print:border-none print:bg-transparent print:backdrop-blur-none print:p-0 print:m-0 print:rounded-none">
          <div class="control-header absolute -top-3 left-4 flex items-center gap-2 px-3 py-1 rounded-full border bg-green-100 border-green-200 print:hidden">
            <span>Group Control</span>
          </div>
          <h2>Test Group</h2>
          <p>This is group content that should be visible in print mode.</p>
        </div>

        <div class="report-section relative border-2 border-blue-200 rounded-lg p-4 my-4 bg-blue-50/30 backdrop-blur-sm print:border-none print:bg-transparent print:backdrop-blur-none print:p-0 print:m-0 print:rounded-none">
          <div class="control-header absolute -top-3 left-4 flex items-center gap-2 px-3 py-1 rounded-full border bg-blue-100 border-blue-200 print:hidden">
            <span>Section Control</span>
          </div>
          <h2>Test Section</h2>
          <p>This is section content that should be visible in print mode.</p>
        </div>
      </body>
      </html>
    `
    
    // Set the HTML content
    await page.setContent(htmlContent)
    
    // Wait for Tailwind to load
    await page.waitForTimeout(1000)
    
    // Test normal mode - decorations should be visible
    const reportSummary = page.locator('.report-summary')
    const reportGroup = page.locator('.report-group')
    const reportSection = page.locator('.report-section')
    
    // Verify components are visible with decorations in normal mode
    await expect(reportSummary).toBeVisible()
    await expect(reportGroup).toBeVisible()
    await expect(reportSection).toBeVisible()
    
    // Verify control headers are visible in normal mode
    await expect(page.locator('.control-header').first()).toBeVisible()
    
    // Switch to print mode
    await page.emulateMedia({ media: 'print' })
    
    // Wait for print styles to apply
    await page.waitForTimeout(500)
    
    // Verify content is still visible
    await expect(reportSummary.locator('h2')).toBeVisible()
    await expect(reportGroup.locator('h2')).toBeVisible()
    await expect(reportSection.locator('h2')).toBeVisible()
    
    // Verify control headers are hidden in print mode
    await expect(page.locator('.control-header').first()).not.toBeVisible()
    
    // Verify print styles are applied by checking computed styles
    const summaryStyles = await reportSummary.evaluate((el) => {
      const computed = window.getComputedStyle(el)
      return {
        border: computed.border,
        background: computed.background,
        padding: computed.padding,
        margin: computed.margin,
        borderRadius: computed.borderRadius
      }
    })
    
    // In print mode, these should be reset
    // Border might be "0px" or contain "none" depending on browser
    expect(summaryStyles.border === '0px' || summaryStyles.border.includes('none')).toBe(true)
    expect(summaryStyles.padding).toBe('0px')
    expect(summaryStyles.margin).toBe('0px')
  })
  
  test('should verify Tailwind print utilities are correctly configured', async ({ page }) => {
    // Test that Tailwind print utilities work as expected
    const testHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body>
        <div class="border-2 border-red-500 p-4 m-4 bg-red-100 print:border-none print:p-0 print:m-0 print:bg-transparent" id="test-element">
          <div class="print:hidden" id="hidden-element">This should be hidden in print</div>
          <div id="visible-element">This should be visible in print</div>
        </div>
      </body>
      </html>
    `
    
    await page.setContent(testHtml)
    await page.waitForTimeout(500)
    
    // Normal mode - everything visible
    await expect(page.locator('#test-element')).toBeVisible()
    await expect(page.locator('#hidden-element')).toBeVisible()
    await expect(page.locator('#visible-element')).toBeVisible()
    
    // Switch to print mode
    await page.emulateMedia({ media: 'print' })
    await page.waitForTimeout(500)
    
    // Print mode - hidden element should not be visible
    await expect(page.locator('#test-element')).toBeVisible()
    await expect(page.locator('#hidden-element')).not.toBeVisible()
    await expect(page.locator('#visible-element')).toBeVisible()
    
    // Verify print styles are applied
    const elementStyles = await page.locator('#test-element').evaluate((el) => {
      const computed = window.getComputedStyle(el)
      return {
        border: computed.border,
        padding: computed.padding,
        margin: computed.margin,
        background: computed.background
      }
    })
    
    expect(elementStyles.border).toContain('none')
    expect(elementStyles.padding).toBe('0px')
    expect(elementStyles.margin).toBe('0px')
  })
})
