import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';
import { getTestEntity, getTestModel } from './test-config';

test.describe('Dashboard Flags Page', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to flags page with test entity
    await page.goto(`/customer/dashboard/flags?entity=${getTestEntity()}&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');
  });

  test('should display flags page with filter options', async ({ page }) => {
    // Wait for flags page to load
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for filter controls
    await expect(page.locator('[data-testid="flag-type-filter"]')).toBeVisible();
    await expect(page.locator('[data-testid="disclosure-filter"]')).toBeVisible();

    // Verify flag type filter options
    const flagTypeFilter = page.locator('[data-testid="flag-type-filter"]');
    await flagTypeFilter.click();
    
    await expect(page.locator('[data-testid="filter-option-all"]')).toBeVisible();
    await expect(page.locator('[data-testid="filter-option-red"]')).toBeVisible();
    await expect(page.locator('[data-testid="filter-option-green"]')).toBeVisible();
  });

  test('should filter flags by type (red/green)', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Test red flags filter
    await page.click('[data-testid="flag-type-filter"]');
    await page.click('[data-testid="filter-option-red"]');
    await page.waitForLoadState('networkidle');

    // Verify only red flags are displayed
    const redFlags = page.locator('[data-testid="flag-item"][data-flag-type="red"]');
    const greenFlags = page.locator('[data-testid="flag-item"][data-flag-type="green"]');

    if (await redFlags.count() > 0) {
      await expect(redFlags.first()).toBeVisible();
    }
    expect(await greenFlags.count()).toBe(0);

    // Test green flags filter
    await page.click('[data-testid="flag-type-filter"]');
    await page.click('[data-testid="filter-option-green"]');
    await page.waitForLoadState('networkidle');

    // Verify only green flags are displayed
    const newGreenFlags = page.locator('[data-testid="flag-item"][data-flag-type="green"]');
    const newRedFlags = page.locator('[data-testid="flag-item"][data-flag-type="red"]');

    if (await newGreenFlags.count() > 0) {
      await expect(newGreenFlags.first()).toBeVisible();
    }
    expect(await newRedFlags.count()).toBe(0);

    // Test all flags filter
    await page.click('[data-testid="flag-type-filter"]');
    await page.click('[data-testid="filter-option-all"]');
    await page.waitForLoadState('networkidle');

    // Verify both types are displayed (if they exist)
    const allFlags = page.locator('[data-testid="flag-item"]');
    await expect(allFlags.first()).toBeVisible({ timeout: 10000 });
  });

  test('should filter flags by disclosure settings', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Test disclosure filter toggle
    const disclosureToggle = page.locator('[data-testid="disclosure-filter"]');
    
    // Check initial state
    const initialState = await disclosureToggle.isChecked();
    
    // Toggle disclosure filter
    await disclosureToggle.click();
    await page.waitForLoadState('networkidle');

    // Verify filter state changed
    const newState = await disclosureToggle.isChecked();
    expect(newState).toBe(!initialState);

    // Verify flags list updated
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible();
  });

  test('should open flag detail modal when flag is clicked', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Get first flag item
    const firstFlag = page.locator('[data-testid="flag-item"]').first();
    await expect(firstFlag).toBeVisible();

    // Click on flag to open modal
    await firstFlag.click();

    // Verify modal opens
    await expect(page.locator('[data-testid="flag-detail-modal"]')).toBeVisible({ timeout: 10000 });

    // Check modal content
    await expect(page.locator('[data-testid="modal-flag-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="modal-flag-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="modal-flag-analysis"]')).toBeVisible();

    // Test modal close functionality
    const closeButton = page.locator('[data-testid="modal-close-button"]');
    if (await closeButton.isVisible()) {
      await closeButton.click();
    } else {
      await page.keyboard.press('Escape');
    }

    // Verify modal closes
    await expect(page.locator('[data-testid="flag-detail-modal"]')).not.toBeVisible();
  });

  test('should display flag search and autocomplete functionality', async ({ page }) => {
    // Wait for page to load
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for search input
    const searchInput = page.locator('[data-testid="flag-search-input"]');
    await expect(searchInput).toBeVisible();

    // Test search functionality
    await searchInput.fill('environmental');
    await page.waitForTimeout(1000); // Wait for debounce

    // Verify search results update
    const searchResults = page.locator('[data-testid="flags-list"] [data-testid="flag-item"]');
    
    // Either results are filtered or no results message is shown
    try {
      await expect(searchResults.first()).toBeVisible({ timeout: 5000 });
      
      // Verify search term appears in results
      const firstResult = searchResults.first();
      const flagText = await firstResult.textContent();
      expect(flagText?.toLowerCase()).toContain('environmental');
    } catch {
      // No results found - check for no results message
      await expect(page.locator('[data-testid="no-flags-message"]')).toBeVisible();
    }

    // Clear search
    await searchInput.clear();
    await page.waitForTimeout(1000);

    // Verify all flags are shown again
    await expect(page.locator('[data-testid="flags-list"] [data-testid="flag-item"]').first()).toBeVisible();
  });

  test('should provide entity analysis reporting functionality', async ({ page }) => {
    // Wait for page to load
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for analysis report button/link
    const analysisButton = page.locator('[data-testid="entity-analysis-report"]');
    
    if (await analysisButton.isVisible()) {
      await analysisButton.click();

      // Verify analysis report opens or navigates
      // This could be a modal, new page, or download
      try {
        // Check for modal
        await expect(page.locator('[data-testid="analysis-report-modal"]')).toBeVisible({ timeout: 10000 });
      } catch {
        try {
          // Check for navigation to analysis page
          await page.waitForURL('**/analysis/**', { timeout: 10000 });
          await expect(page.locator('[data-testid="analysis-page-content"]')).toBeVisible();
        } catch {
          // Check for download or other action
          console.log('Analysis report action completed (download or other)');
        }
      }
    }
  });

  test('should display flag metadata correctly', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    const firstFlag = page.locator('[data-testid="flag-item"]').first();
    await expect(firstFlag).toBeVisible();

    // Check flag metadata elements
    await expect(firstFlag.locator('[data-testid="flag-title"]')).toBeVisible();
    await expect(firstFlag.locator('[data-testid="flag-confidence"]')).toBeVisible();
    await expect(firstFlag.locator('[data-testid="flag-date"]')).toBeVisible();

    // Verify flag type indicator (red/green)
    const flagTypeIndicator = firstFlag.locator('[data-testid="flag-type-indicator"]');
    await expect(flagTypeIndicator).toBeVisible();

    // Check confidence score format
    const confidenceText = await firstFlag.locator('[data-testid="flag-confidence"]').textContent();
    expect(confidenceText).toMatch(/\d+%|\d+\.\d+/); // Should be percentage or decimal
  });

  test('should handle empty states gracefully', async ({ page }) => {
    // Navigate with entity that might have no flags
    await page.goto(`/customer/dashboard/flags?entity=invalid-entity&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');

    // Check for appropriate empty state message
    try {
      await expect(page.locator('[data-testid="no-flags-message"]')).toBeVisible({ timeout: 15000 });
    } catch {
      // If flags are present, that's also valid
      await expect(page.locator('[data-testid="flags-list"]')).toBeVisible();
    }
  });

  test('should maintain filter state when navigating back', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Apply red flags filter
    await page.click('[data-testid="flag-type-filter"]');
    await page.click('[data-testid="filter-option-red"]');
    await page.waitForLoadState('networkidle');

    // Navigate away and back
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    
    await page.goBack();
    await page.waitForLoadState('networkidle');

    // Verify filter state is maintained (in URL or localStorage)
    const currentURL = page.url();
    expect(currentURL).toContain('flags');
  });

  test('should display loading states appropriately', async ({ page }) => {
    // Navigate to flags page
    await page.goto(`/customer/dashboard/flags?entity=${getTestEntity()}&model=${getTestModel()}`);

    // Check for loading indicators
    const loadingIndicators = page.locator('[data-testid*="loading"], [class*="loading"], [class*="spinner"]');
    
    try {
      await expect(loadingIndicators.first()).toBeVisible({ timeout: 5000 });
      await expect(loadingIndicators.first()).not.toBeVisible({ timeout: 30000 });
    } catch {
      // Loading was too fast, content should be visible
      await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });
    }
  });
});