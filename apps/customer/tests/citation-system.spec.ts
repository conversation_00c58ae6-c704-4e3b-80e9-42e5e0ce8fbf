import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Citation System', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display proper citation format instead of "Citation [ID]"', async ({ page }) => {
    // Mock API response with proper citation data to test formatting
    await page.route('**/api/report/**', route => {
      const mockResponse = {
        text: `
          <h2>Test Report Section</h2>
          <p>This is test content with citations [^2917579] and [^2917580].</p>
          <p>More content with additional citations [^2918121].</p>
        `,
        citations: [
          {
            doc_page_id: 2917579,
            title: "Sustainability Report 2024",
            url: "https://example.com/sustainability-2024",
            public_url: "https://example.com/sustainability-2024",
            page: 2,
            score: 0.95,
            doc_id: 12345,
            credibility: 0.9,
            doc_name: "Corporate Sustainability Report",
            year: 2024,
            authors: [{ name: "<PERSON>", cn: "<PERSON>" }]
          },
          {
            doc_page_id: 2917580,
            title: "Environmental Impact Assessment",
            url: "https://example.com/environmental-2024",
            public_url: "https://example.com/environmental-2024",
            page: 15,
            score: 0.88,
            doc_id: 12346,
            credibility: 0.85,
            doc_name: "Environmental Assessment Report",
            year: 2024,
            authors: [{ name: "Jane Doe", cn: "Jane Doe" }]
          },
          {
            doc_page_id: 2918121,
            title: "Climate Action Plan",
            url: "https://example.com/climate-plan",
            public_url: "https://example.com/climate-plan",
            page: 8,
            score: 0.92,
            doc_id: 12347,
            credibility: 0.88,
            doc_name: "Climate Action Strategy",
            year: 2023,
            authors: [{ name: "Bob Wilson", cn: "Bob Wilson" }]
          }
        ]
      };

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      });
    });

    // Create document using TestUtils which handles template selection properly
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for template to load and report sections to appear
    await page.waitForLoadState('networkidle')

    // Load a report section to get citations
    const reportSection = page.locator('report-section').first()

    // Only proceed if report sections exist (they should in EKO Report template)
    if (await reportSection.count() > 0) {
      await reportSection.click()

      // Wait for references section to appear (indicating content has loaded)
      await page.waitForSelector('#references', { timeout: 15000 })

      // Verify references section exists
      const referencesSection = page.locator('#references')
      await expect(referencesSection).toBeVisible()

      // Check citation format in references section
      const citationItems = page.locator('#references .flex.gap-2')
      const count = await citationItems.count()

      if (count > 0) {
        for (let i = 0; i < count; i++) {
          const citationItem = citationItems.nth(i)
          const citationText = await citationItem.textContent()

          console.log(`Citation ${i + 1} text:`, citationText)

          // Should NOT contain "Citation [ID]" format - this is the bug we're testing for
          expect(citationText).not.toMatch(/Citation \d+/)

          // Should contain proper citation number [1], [2], etc.
          expect(citationText).toContain(`[${i + 1}]`)

          // Should contain year information in brackets like [2024] or [2023]
          expect(citationText).toMatch(/\[20\d{2}\]/)

          // Should contain page information like "p. 2" or "p. 15"
          expect(citationText).toMatch(/p\.\s*\d+/)

          // Should have a proper title (not just "Citation [ID]")
          const titleElement = citationItem.locator('a, .text-primary').first()
          if (await titleElement.count() > 0) {
            const titleText = await titleElement.textContent()
            expect(titleText).toBeTruthy()
            expect(titleText).not.toMatch(/^Citation \d+$/)
            // Should contain meaningful titles like "Sustainability Report 2024"
            if (titleText) {
              expect(titleText.length).toBeGreaterThan(10)
            }
          }
        }
      }
    }
  })

  test('should display proper citation format in print mode', async ({ page }) => {
    // Mock API response with proper citation data
    await page.route('**/api/report/**', route => {
      const mockResponse = {
        text: `
          <h2>Test Report Section</h2>
          <p>This is test content with citations [^2917579] and [^2917580].</p>
        `,
        citations: [
          {
            doc_page_id: 2917579,
            title: "Sustainability Report 2024",
            url: "https://example.com/sustainability-2024",
            public_url: "https://example.com/sustainability-2024",
            page: 2,
            score: 0.95,
            doc_id: 12345,
            credibility: 0.9,
            doc_name: "Corporate Sustainability Report",
            year: 2024,
            authors: [{ name: "John Smith", cn: "John Smith" }]
          },
          {
            doc_page_id: 2917580,
            title: "Environmental Impact Assessment",
            url: "https://example.com/environmental-2024",
            public_url: "https://example.com/environmental-2024",
            page: 15,
            score: 0.88,
            doc_id: 12346,
            credibility: 0.85,
            doc_name: "Environmental Assessment Report",
            year: 2024,
            authors: [{ name: "Jane Doe", cn: "Jane Doe" }]
          }
        ]
      };

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      });
    });

    // Create document using TestUtils
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for template to load and report sections to appear
    await page.waitForLoadState('networkidle')

    // Load a report section to get citations
    const reportSection = page.locator('report-section').first()

    // Only proceed if report sections exist
    if (await reportSection.count() > 0) {
      await reportSection.click()

      // Wait for references section to appear
      await page.waitForSelector('#references', { timeout: 15000 })

      // Verify citations are loaded in normal mode first
      const referencesSection = page.locator('#references')
      await expect(referencesSection).toBeVisible()

      const citationItems = page.locator('#references .flex.gap-2')
      const count = await citationItems.count()

      if (count > 0) {
        // Switch to print mode
        await page.emulateMedia({ media: 'print' })

        // Wait for print styles to apply
        await page.waitForLoadState('networkidle')

        // Verify references section is still visible in print mode
        await expect(referencesSection).toBeVisible()

        // Verify citations maintain proper format in print mode
        for (let i = 0; i < count; i++) {
          const citationItem = citationItems.nth(i)
          const citationText = await citationItem.textContent()

          console.log(`Print mode citation ${i + 1} text:`, citationText)

          // Should still contain proper citation number
          expect(citationText).toContain(`[${i + 1}]`)

          // Should NOT contain "Citation [ID]" format in print mode either
          expect(citationText).not.toMatch(/Citation \d+/)

          // Should still contain year and page information
          expect(citationText).toMatch(/\[20\d{2}\]/)
          expect(citationText).toMatch(/p\.\s*\d+/)

          // Should have proper title in print mode
          const titleElement = citationItem.locator('a, .text-primary').first()
          if (await titleElement.count() > 0) {
            const titleText = await titleElement.textContent()
            expect(titleText).toBeTruthy()
            if (titleText) {
              expect(titleText).not.toMatch(/^Citation \d+$/)
            }
          }
        }

        // Verify inline citations work in print mode
        const inlineCitations = page.locator('citation')

        // Only check inline citations if they exist (they may not in this test setup)
        const inlineCount = await inlineCitations.count()
        if (inlineCount > 0) {
          for (let i = 0; i < inlineCount; i++) {
            const citation = inlineCitations.nth(i)
            const citationText = await citation.textContent()

            // Should show proper citation numbers, not error states
            expect(citationText).toMatch(/^\[\d+\]$/)
            expect(citationText).not.toContain('[Citation not found]')
            expect(citationText).not.toContain('[...]')
          }
        }
      }
    }
  })

})
