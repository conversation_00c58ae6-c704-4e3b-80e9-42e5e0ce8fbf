import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Print Mode Toggle', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should toggle print mode on and off', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)
    
    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()
    
    // Verify print mode button exists
    const printModeButton = page.locator('[data-testid="print-toggle-button"]')
    await expect(printModeButton).toBeVisible()
    
    // Verify editor is not in print mode initially
    const editor = await testUtils.waitForEditor()
    await expect(editor).not.toHaveClass(/eko-print-mode/)
    
    // Click print mode button
    await printModeButton.click()
    
    // Verify button text changes to "Normal"
    await expect(printModeButton).toHaveText('Normal')
    
    // Verify editor has print mode class
    await expect(editor).toHaveClass(/eko-print-mode/)
    
    // Click again to exit print mode
    await printModeButton.click()
    
    // Verify button text changes back to "Print"
    await expect(printModeButton).toHaveText('Print')
    
    // Verify editor no longer has print mode class
    await expect(editor).not.toHaveClass(/eko-print-mode/)
  })

  test('should maintain editability in print mode', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)
    
    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()
    
    // Enter print mode
    await page.locator('[data-testid="print-toggle-button"]').click()
    
    // Verify editor is still editable
    const editor = await testUtils.waitForEditor()
    await expect(editor).toHaveAttribute('contenteditable', 'true')
    
    // Try typing in print mode
    await editor.click()
    await page.keyboard.type('Test content in print mode')
    
    // Verify content was added
    await expect(editor.locator('text=Test content in print mode')).toBeVisible()
  })

  test('should apply print styling in print mode', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)
    
    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()
    
    // Add some content with report components
    await testUtils.typeInEditor('# Test Document\n\nThis is test content.')
    
    // Enter print mode
    await page.locator('[data-testid="print-toggle-button"]').click()
    
    // Verify print mode styles are applied
    const editor = await testUtils.waitForEditor()
    
    // Check that editor has print mode class
    await expect(editor).toHaveClass(/eko-print-mode/)
    
    // Verify background is white (print style)
    const bgColor = await editor.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor
    })
    
    // Should be white or rgb(255, 255, 255)
    expect(bgColor).toMatch(/rgb\(255,\s*255,\s*255\)|white/)
  })

  test('should hide visual decorations in print mode', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)
    
    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()
    
    // Insert a report section (which has visual decorations)
    await page.locator('button[title="Insert Report Section"]').click()
    
    // Fill in the dialog
    await page.fill('input[placeholder="Enter entity short ID"]', 'TEST')
    await page.fill('input[placeholder="Enter run ID"]', '1')
    await page.selectOption('select', 'environmental')
    await page.click('button:has-text("Insert")')
    
    // Wait for report section to load
    await testUtils.waitForComponentLoading('.report-section')
    
    // Enter print mode
    await page.locator('[data-testid="print-toggle-button"]').click()
    
    // Verify that control elements are hidden in print mode
    const reportSection = page.locator('.report-section').first()
    const controlElements = reportSection.locator('.absolute, button')
    
    // In print mode, these should be hidden
    for (let i = 0; i < await controlElements.count(); i++) {
      const element = controlElements.nth(i)
      const isVisible = await element.isVisible()
      expect(isVisible).toBe(false)
    }
  })

  test('should work with view mode combination', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to print page (view + print mode)
    await page.goto(`/customer/documents/${documentId}/print`)
    
    // Verify document loads in view mode with print styling
    await expect(page.locator('.ProseMirror')).toBeVisible()
    
    // Verify editor is not editable (view mode)
    const editor = await testUtils.waitForEditor()
    await expect(editor).toHaveAttribute('contenteditable', 'false')
    
    // Verify print styles are applied (this page should have print styling)
    const bgColor = await editor.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor
    })
    
    // Should be white or transparent for print
    expect(bgColor).toMatch(/rgb\(255,\s*255,\s*255\)|white|rgba\(0,\s*0,\s*0,\s*0\)|transparent/)
  })
})
