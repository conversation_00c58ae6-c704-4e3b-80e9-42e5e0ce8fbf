import { test, expect } from '@playwright/test';

test.describe('Authentication and Login', () => {
  
  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });
  });

  test('should display login form on unauthenticated access', async ({ page }) => {
    // Navigate to protected route without authentication
    await page.goto('/customer/dashboard');
    
    // Should redirect to login page
    await page.waitForURL('**/login**', { timeout: 15000 });
    
    // Verify login form elements
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
  });

  test('should handle login flow correctly', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // Verify login form is visible
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible({ timeout: 10000 });
    
    // Fill in credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'test1_pass');
    
    // Submit login
    await page.click('[data-testid="login-button"]');
    
    // Wait for authentication to complete
    await page.waitForURL('**/customer/**', { timeout: 30000 });
    
    // Verify successful login by checking for authenticated content
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 15000 });
    await expect(page.locator('[data-testid="user-dropdown-trigger"]')).toBeVisible();
  });

  test('should handle invalid login credentials', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // Fill in invalid credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    
    // Submit login
    await page.click('[data-testid="login-button"]');
    
    // Wait for error message
    await expect(page.locator('[data-testid="login-error"]')).toBeVisible({ timeout: 10000 });
    
    // Verify error message content
    const errorText = await page.locator('[data-testid="login-error"]').textContent();
    expect(errorText?.toLowerCase()).toContain('invalid');
    
    // Verify still on login page
    expect(page.url()).toContain('login');
  });

  test('should persist session across page reloads', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'test1_pass');
    await page.click('[data-testid="login-button"]');
    
    await page.waitForURL('**/customer/**', { timeout: 30000 });
    
    // Navigate to dashboard and verify authenticated
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 15000 });
    
    // Reload page
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Verify still authenticated
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 15000 });
    expect(page.url()).toContain('customer');
  });

  test('should load user profile after authentication', async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'test1_pass');
    await page.click('[data-testid="login-button"]');
    
    await page.waitForURL('**/customer/**', { timeout: 30000 });
    
    // Wait for profile to load
    await expect(page.locator('[data-testid="user-dropdown-trigger"]')).toBeVisible({ timeout: 15000 });
    
    // Open user dropdown to verify profile loaded
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    // Check for user info
    const userInfo = page.locator('[data-testid="user-info"]');
    if (await userInfo.isVisible()) {
      await expect(userInfo).toBeVisible();
      
      // Verify email or name is displayed
      const userText = await userInfo.textContent();
      expect(userText).toBeTruthy();
    }
    
    // Close dropdown
    await page.keyboard.press('Escape');
  });

  test('should handle feature flag access control', async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'test1_pass');
    await page.click('[data-testid="login-button"]');
    
    await page.waitForURL('**/customer/**', { timeout: 30000 });
    
    // Wait for sidebar to load
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 15000 });
    
    // Check that feature-gated items are handled correctly
    // (May or may not be visible depending on user's feature flags)
    const adminNav = page.locator('[data-testid="nav-admin"]');
    const betaFeatures = page.locator('[data-testid="nav-beta-features"]');
    
    // These should either be visible or not present, but not cause errors
    if (await adminNav.isVisible()) {
      await expect(adminNav).toBeVisible();
    }
    
    if (await betaFeatures.isVisible()) {
      await expect(betaFeatures).toBeVisible();
    }
    
    // Check user dropdown for feature-gated options
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    const advancedOptions = page.locator('[data-testid="menu-advanced-options"]');
    if (await advancedOptions.isVisible()) {
      await expect(advancedOptions).toBeVisible();
    }
    
    await page.keyboard.press('Escape');
  });

  test('should handle logout correctly', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'test1_pass');
    await page.click('[data-testid="login-button"]');
    
    await page.waitForURL('**/customer/**', { timeout: 30000 });
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 15000 });
    
    // Logout
    await page.click('[data-testid="user-dropdown-trigger"]');
    await expect(page.locator('[data-testid="user-dropdown-menu"]')).toBeVisible();
    
    await page.click('[data-testid="menu-sign-out"]');
    
    // Handle potential logout confirmation
    try {
      await expect(page.locator('[data-testid="logout-confirmation"]')).toBeVisible({ timeout: 5000 });
      await page.click('[data-testid="confirm-logout"]');
    } catch {
      // Immediate logout is also valid
    }
    
    // Verify redirect to login page
    await page.waitForURL('**/login**', { timeout: 15000 });
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
    
    // Verify session is cleared - try accessing protected route
    await page.goto('/customer/dashboard');
    await page.waitForURL('**/login**', { timeout: 15000 });
  });

  test('should handle authentication state changes', async ({ page }) => {
    // Start unauthenticated
    await page.goto('/customer/dashboard');
    await page.waitForURL('**/login**', { timeout: 15000 });
    
    // Login
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'test1_pass');
    await page.click('[data-testid="login-button"]');
    
    await page.waitForURL('**/customer/**', { timeout: 30000 });
    
    // Verify authenticated state
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 15000 });
    
    // Check auth context is properly updated
    const authIndicator = page.locator('[data-testid="auth-status"]');
    if (await authIndicator.isVisible()) {
      const authStatus = await authIndicator.textContent();
      expect(authStatus?.toLowerCase()).toContain('authenticated');
    }
  });

  test('should handle password reset flow', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // Check for forgot password link
    const forgotPasswordLink = page.locator('[data-testid="forgot-password-link"]');
    if (await forgotPasswordLink.isVisible()) {
      await forgotPasswordLink.click();
      
      // Should navigate to password reset page
      await page.waitForURL('**/reset-password**', { timeout: 10000 });
      
      // Verify reset form
      await expect(page.locator('[data-testid="reset-password-form"]')).toBeVisible();
      await expect(page.locator('[data-testid="reset-email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="send-reset-button"]')).toBeVisible();
      
      // Test reset request
      await page.fill('[data-testid="reset-email-input"]', '<EMAIL>');
      await page.click('[data-testid="send-reset-button"]');
      
      // Should show success message
      await expect(page.locator('[data-testid="reset-success-message"]')).toBeVisible({ timeout: 10000 });
    }
  });

  test('should handle registration flow if available', async ({ page }) => {
    // Navigate to login page
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // Check for sign up link
    const signUpLink = page.locator('[data-testid="sign-up-link"]');
    if (await signUpLink.isVisible()) {
      await signUpLink.click();
      
      // Should navigate to registration page
      try {
        await page.waitForURL('**/register**', { timeout: 10000 });
        
        // Verify registration form
        await expect(page.locator('[data-testid="register-form"]')).toBeVisible();
        await expect(page.locator('[data-testid="register-email-input"]')).toBeVisible();
        await expect(page.locator('[data-testid="register-password-input"]')).toBeVisible();
        await expect(page.locator('[data-testid="register-button"]')).toBeVisible();
      } catch {
        // Registration might not be available
        console.log('Registration form not available');
      }
    }
  });

  test('should handle session timeout gracefully', async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'test1_pass');
    await page.click('[data-testid="login-button"]');
    
    await page.waitForURL('**/customer/**', { timeout: 30000 });
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 15000 });
    
    // Simulate session expiry by clearing auth storage
    await page.evaluate(() => {
      localStorage.removeItem('supabase.auth.token');
      sessionStorage.clear();
    });
    
    // Try to access protected content
    await page.goto('/customer/account');
    
    // Should redirect to login
    await page.waitForURL('**/login**', { timeout: 15000 });
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
  });
});