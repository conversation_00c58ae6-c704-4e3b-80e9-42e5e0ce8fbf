import { expect, test } from '@playwright/test'
import { processMarkdownForTipTap } from '@/components/editor/utils/markdown-processor'

test.describe('HTML Element Extraction and Re-insertion', () => {
  test('should preserve table elements exactly as they are', async () => {
    const markdown = `
# Test Document

Here's some text before the table.

<table class="custom-table" style="width: 100%;">
  <thead>
    <tr>
      <th>Header 1</th>
      <th>Header 2</th>
      <th>Header 3</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Row 1, Col 1</td>
      <td>Row 1, Col 2</td>
      <td>Row 1, Col 3</td>
    </tr>
    <tr>
      <td>Row 2, Col 1</td>
      <td>Row 2, Col 2</td>
      <td>Row 2, Col 3</td>
    </tr>
  </tbody>
</table>

And some text after the table.
`

    const result = await processMarkdownForTipTap(markdown, null, { skipCitations: true })
    
    // Should contain the exact table HTML
    expect(result.html).toContain('<table class="custom-table" style="width: 100%;">')
    expect(result.html).toContain('<thead>')
    expect(result.html).toContain('<tbody>')
    expect(result.html).toContain('Row 1, Col 1')
    expect(result.html).toContain('Row 2, Col 3')
    
    // Should not contain placeholder text
    expect(result.html).not.toContain('[~markdown-table:')
    
    // Should still process the markdown around it
    expect(result.html).toContain('<h1>Test Document</h1>')
    expect(result.html).toContain('<p>Here\'s some text before the table.</p>')
    expect(result.html).toContain('<p>And some text after the table.</p>')
  })

  test('should preserve chart elements with valid JSON', async () => {
    const markdown = `
# Chart Test

<chart>
{
  "type": "bar",
  "data": {
    "labels": ["A", "B", "C"],
    "datasets": [{
      "label": "Test Data",
      "data": [1, 2, 3]
    }]
  }
}
</chart>

Text after chart.
`

    const result = await processMarkdownForTipTap(markdown, null, { skipCitations: true })
    
    // Should contain the chart element with encoded JSON
    expect(result.html).toContain('<chart data-json=')
    expect(result.html).toContain('"type": "bar"')
    expect(result.html).toContain('"labels": ["A", "B", "C"]')
    
    // Should not contain placeholder text
    expect(result.html).not.toContain('[~markdown-chart:')
    
    // Should still process the markdown around it
    expect(result.html).toContain('<h1>Chart Test</h1>')
    expect(result.html).toContain('<p>Text after chart.</p>')
  })

  test('should handle multiple tables and charts together', async () => {
    const markdown = `
# Multiple Elements Test

<table>
  <tr><td>Table 1</td></tr>
</table>

<chart>{"type": "line"}</chart>

Some text in between.

<table class="second-table">
  <tr><td>Table 2</td></tr>
</table>

<chart>{"type": "pie", "data": {"labels": ["X", "Y"]}}</chart>
`

    const result = await processMarkdownForTipTap(markdown, null, { skipCitations: true })
    
    // Should contain both tables
    expect(result.html).toContain('Table 1')
    expect(result.html).toContain('Table 2')
    expect(result.html).toContain('class="second-table"')
    
    // Should contain both charts
    expect(result.html).toContain('"type": "line"')
    expect(result.html).toContain('"type": "pie"')
    
    // Should not contain any placeholders
    expect(result.html).not.toContain('[~markdown-table:')
    expect(result.html).not.toContain('[~markdown-chart:')
    
    // Should process markdown content
    expect(result.html).toContain('<p>Some text in between.</p>')
  })

  test('should handle invalid JSON in charts gracefully', async () => {
    const markdown = `
<chart>
{invalid json content}
</chart>
`

    // Should not throw an error
    const result = await processMarkdownForTipTap(markdown, null, { skipCitations: true })
    
    // Should preserve the original content when JSON is invalid
    expect(result.html).toContain('<chart>')
    expect(result.html).toContain('{invalid json content}')
  })

  test('should handle nested HTML in tables', async () => {
    const markdown = `
<table>
  <tr>
    <td><strong>Bold text</strong></td>
    <td><a href="https://example.com">Link</a></td>
    <td><span class="highlight">Highlighted</span></td>
  </tr>
</table>
`

    const result = await processMarkdownForTipTap(markdown, null, { skipCitations: true })
    
    // Should preserve nested HTML exactly
    expect(result.html).toContain('<strong>Bold text</strong>')
    expect(result.html).toContain('<a href="https://example.com">Link</a>')
    expect(result.html).toContain('<span class="highlight">Highlighted</span>')
  })

  test('should handle empty tables and charts', async () => {
    const markdown = `
<table></table>
<chart></chart>
`

    const result = await processMarkdownForTipTap(markdown, null, { skipCitations: true })
    
    // Should preserve empty elements
    expect(result.html).toContain('<table></table>')
    expect(result.html).toContain('<chart></chart>')
  })

  test('should not interfere with markdown table processing', async () => {
    const markdown = `
| Markdown | Table |
|----------|-------|
| Cell 1   | Cell 2|

<table>
  <tr><td>HTML Table</td></tr>
</table>
`

    const result = await processMarkdownForTipTap(markdown, null, { skipCitations: true })
    
    // Should process markdown table normally
    expect(result.html).toContain('<th>Markdown</th>')
    expect(result.html).toContain('<th>Table</th>')
    expect(result.html).toContain('<td>Cell 1</td>')
    
    // Should preserve HTML table exactly
    expect(result.html).toContain('<tr><td>HTML Table</td></tr>')
  })
})
