import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report Section Loading Behavior', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should load content only once when extension has no content initially', async ({ page }) => {
    // Track API calls to report endpoints
    const apiCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      // Mock successful response
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content</p>',
          citations: []
        })
      })
    })

    // Create a new document from template
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait for entity selector to be available and auto-select
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    
    // Wait for entity auto-selection to complete - use a more reliable approach
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const value = trigger.getAttribute('data-state')
      const text = trigger.textContent || ''
      return value !== 'loading' && !text.includes('Loading entities') && !text.includes('Select entity')
    }, { timeout: 15000 })

    // Wait for report sections to be visible
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 10000 })

    // Wait a bit for any loading to complete
    await page.waitForTimeout(3000)

    // Count initial API calls - each section should load once
    const initialCallCount = apiCalls.length
    expect(initialCallCount).toBeGreaterThan(0)

    // Wait another few seconds to ensure no additional calls are made
    await page.waitForTimeout(3000)
    
    // Should not have made additional calls
    expect(apiCalls.length).toBe(initialCallCount)
    
    console.log('API calls made:', apiCalls)
  })

  test('should reload content when entity changes and auto-save first', async ({ page }) => {
    // Track API calls and auto-save calls
    const apiCalls: string[] = []
    const saveCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content for entity</p>',
          citations: []
        })
      })
    })

    // Track document save calls
    page.route('**/api/documents/**', route => {
      if (route.request().method() === 'PATCH' || route.request().method() === 'PUT') {
        saveCalls.push(route.request().url())
      }
      route.fulfill({
        status: 200,
        body: JSON.stringify({ success: true })
      })
    })

    // Create a new document
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait for initial load
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // Change entity selection
    await page.click('[data-testid="entity-select"]')
    
    // Wait for dropdown to open and select a different entity if available
    const entityOptions = page.locator('[role="option"]')
    const optionCount = await entityOptions.count()
    
    if (optionCount > 1) {
      // Select the second entity option
      await entityOptions.nth(1).click()
      
      // Wait for new content to load
      await page.waitForTimeout(3000)
      
      // Should have made new API calls for the new entity
      expect(apiCalls.length).toBeGreaterThan(initialCallCount)
      
      // Should have triggered auto-save before reloading
      expect(saveCalls.length).toBeGreaterThan(0)
    }
  })

  test('should reload content when run changes', async ({ page }) => {
    const apiCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content for run</p>',
          citations: []
        })
      })
    })

    // Create a new document
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait for initial load
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for runs to be available
    await expect(page.locator('[data-testid="run-select"]')).not.toBeDisabled({ timeout: 10000 })
    
    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // Change run selection
    await page.click('[data-testid="run-select"]')
    
    // Wait for dropdown and select a different run if available
    const runOptions = page.locator('[role="option"]')
    const runCount = await runOptions.count()
    
    if (runCount > 1) {
      // Select a different run
      await runOptions.nth(1).click()
      
      // Wait for new content to load
      await page.waitForTimeout(3000)
      
      // Should have made new API calls for the new run
      expect(apiCalls.length).toBeGreaterThan(initialCallCount)
    }
  })

  test('should not reload content on component re-renders when entity/run unchanged', async ({ page }) => {
    const apiCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content</p>',
          citations: []
        })
      })
    })

    // Create a new document
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait for initial load
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // Trigger some interactions that might cause re-renders but shouldn't reload content
    
    // Toggle disclosures switch (should not reload content)
    await page.click('[data-testid="include-disclosures"]')
    await page.waitForTimeout(1000)
    
    // Click in the editor to focus it (might cause re-renders)
    await page.click('.ProseMirror')
    await page.waitForTimeout(1000)
    
    // Type some text (should not affect report sections)
    await page.keyboard.type('Some additional text')
    await page.waitForTimeout(2000)

    // Should not have made additional API calls
    expect(apiCalls.length).toBe(initialCallCount)
  })

  test('should preserve existing content when manually refreshing', async ({ page }) => {
    const apiCalls: string[] = []
    let callCounter = 0
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      callCounter++
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: `<p>Test report content - call ${callCounter}</p>`,
          citations: []
        })
      })
    })

    // Create a new document
    await page.goto('/customer/documents')
    await page.click('text=New Document')
    await page.click('text=EKO Report')
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)

    // Wait for initial load
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // Manually refresh a report section
    const reportSection = page.locator('.report-section').first()
    await expect(reportSection).toBeVisible()
    
    // Click the menu trigger
    const menuTrigger = reportSection.locator('[data-testid="report-section-menu-trigger"]')
    if (await menuTrigger.isVisible()) {
      await menuTrigger.click()
      
      // Click refresh
      await page.click('text=Refresh')
      
      // Wait for refresh to complete
      await page.waitForTimeout(3000)
      
      // Should have made one additional API call
      expect(apiCalls.length).toBe(initialCallCount + 1)
    }
  })
})
