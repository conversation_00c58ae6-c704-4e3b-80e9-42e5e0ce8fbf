import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Citation Preservation in Report Summaries', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should preserve citations when extracting content for summarization', async ({ page }) => {
    // Navigate to documents page
    await page.goto('/customer/documents')
    await page.waitForLoadState('networkidle')

    // Create a new document
    await page.click('text=New Document')
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    // Select EKO Report template
    const templateCard = page.locator('[data-testid="template-dialog"] .grid > div').filter({ hasText: 'EKO Report' }).first()
    await templateCard.click()

    // Wait for document creation and navigation
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 60000 })
    await page.waitForTimeout(3000)

    // Mock API responses for report sections with citations
    await page.route('**/api/report/section/**', route => {
      const mockResponse = {
        text: `
          <h3>Environmental Impact Analysis</h3>
          <p>The company has made significant progress in reducing carbon emissions [^2917579]. 
          According to their latest sustainability report [^2917580], they achieved a 25% reduction 
          in greenhouse gas emissions compared to the previous year.</p>
          <p>Water usage has also been optimized [^2918121] through implementation of new 
          conservation technologies.</p>
        `,
        citations: [
          {
            doc_page_id: 2917579,
            title: "Carbon Emissions Report 2024",
            url: "https://example.com/carbon-2024",
            public_url: "https://example.com/carbon-2024",
            page: 12,
            score: 0.95,
            doc_id: 12345,
            credibility: 0.9,
            doc_name: "Carbon Footprint Analysis",
            year: 2024,
            authors: [{ name: "Environmental Team", cn: "Environmental Team" }]
          },
          {
            doc_page_id: 2917580,
            title: "Sustainability Report 2024",
            url: "https://example.com/sustainability-2024",
            public_url: "https://example.com/sustainability-2024",
            page: 45,
            score: 0.88,
            doc_id: 12346,
            credibility: 0.85,
            doc_name: "Annual Sustainability Report",
            year: 2024,
            authors: [{ name: "Sustainability Office", cn: "Sustainability Office" }]
          },
          {
            doc_page_id: 2918121,
            title: "Water Conservation Study",
            url: "https://example.com/water-conservation",
            public_url: "https://example.com/water-conservation",
            page: 8,
            score: 0.92,
            doc_id: 12347,
            credibility: 0.88,
            doc_name: "Water Usage Optimization",
            year: 2023,
            authors: [{ name: "Engineering Team", cn: "Engineering Team" }]
          }
        ]
      };

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      });
    });

    // Mock API response for summary endpoint to verify citations are preserved
    let summaryRequestContent = '';
    await page.route('**/api/report/summarize', route => {
      const request = route.request();
      const postData = request.postData();
      
      if (postData) {
        try {
          const data = JSON.parse(postData);
          summaryRequestContent = data.content || '';
          console.log('Summary request content:', summaryRequestContent);
        } catch (e) {
          console.error('Failed to parse summary request:', e);
        }
      }

      const mockSummaryResponse = {
        text: `
          ## Environmental Performance Summary
          
          The organization has demonstrated strong environmental stewardship [^2917579]. 
          Key achievements include significant carbon emission reductions [^2917580] and 
          improved water conservation practices [^2918121].
          
          These improvements reflect a comprehensive approach to environmental management.
        `,
        metadata: {
          type: 'summary',
          originalContentLength: summaryRequestContent.length,
          summaryLength: 300,
          generatedAt: new Date().toISOString()
        }
      };

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockSummaryResponse)
      });
    });

    // Load a report section first
    const reportSection = page.locator('report-section').first()
    if (await reportSection.count() > 0) {
      // Click to load the section
      await reportSection.click()
      
      // Wait for section to load
      await page.waitForTimeout(5000)
      
      // Verify the section has loaded with citations
      const sectionContent = await reportSection.textContent()
      expect(sectionContent).toContain('[1]') // Should show citation numbers
      
      // Now add a report summary that depends on this section
      // First, get the section ID
      const sectionId = await reportSection.getAttribute('id')
      
      if (sectionId) {
        // Insert a report summary via the editor
        await page.click('.ProseMirror')
        await page.keyboard.press('Enter')
        
        // Use the toolbar to insert a report summary
        const toolbarButton = page.locator('[data-testid="toolbar-report-summary"]')
        if (await toolbarButton.count() > 0) {
          await toolbarButton.click()
          
          // Configure the summary to depend on the loaded section
          const configDialog = page.locator('[role="dialog"]')
          await expect(configDialog).toBeVisible()
          
          // Set the summarize field to reference the section
          const summarizeInput = configDialog.locator('input[placeholder*="summarize"]')
          if (await summarizeInput.count() > 0) {
            await summarizeInput.fill(sectionId)
          }
          
          // Confirm the configuration
          await configDialog.locator('button:has-text("Create")').click()
          
          // Wait for summary to be created and loaded
          await page.waitForTimeout(10000)
          
          // Verify that the summary request contained citations in the correct format
          // The content should contain [^page_id] format, not just plain text
          expect(summaryRequestContent).toContain('[^2917579]')
          expect(summaryRequestContent).toContain('[^2917580]')
          expect(summaryRequestContent).toContain('[^2918121]')

          // Verify the summary was generated and contains citations
          const summaryElement = page.locator('report-summary')
          if (await summaryElement.count() > 0) {
            // Wait for summary to be fully processed
            await page.waitForTimeout(5000)

            const summaryContent = await summaryElement.textContent()

            // The summary should contain citation numbers (converted from [^page_id] back to [1], [2], etc.)
            expect(summaryContent).toContain('[1]')
            expect(summaryContent).toContain('[2]')
            expect(summaryContent).toContain('[3]')

            // Verify that the TipTap document contains actual citation elements, not just text
            const citationElements = page.locator('report-summary citation')
            const citationElementCount = await citationElements.count()
            expect(citationElementCount).toBeGreaterThan(0)

            // Verify that citation elements have the correct page_id attributes
            if (citationElementCount > 0) {
              const firstCitation = citationElements.first()
              const pageId = await firstCitation.getAttribute('page_id')
              expect(pageId).toBeTruthy()
              expect(['2917579', '2917580', '2918121']).toContain(pageId)
            }

            // Verify references section is updated with all citations
            const referencesSection = page.locator('#references')
            await expect(referencesSection).toBeVisible()

            const citationItems = page.locator('#references .flex.gap-2')
            const citationCount = await citationItems.count()

            // Should have at least 3 citations from our mock data
            expect(citationCount).toBeGreaterThanOrEqual(3)
          }
        }
      }
    }
  })

  test('should handle mixed content with and without citations in summaries', async ({ page }) => {
    // This test verifies that the citation extraction works correctly
    // when some content has citations and some doesn't
    
    await page.goto('/customer/documents')
    await page.waitForLoadState('networkidle')

    await page.click('text=New Document')
    await expect(page.locator('[role="dialog"]')).toBeVisible()

    const templateCard = page.locator('[data-testid="template-dialog"] .grid > div').filter({ hasText: 'EKO Report' }).first()
    await templateCard.click()

    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 60000 })
    await page.waitForTimeout(3000)

    // Mock mixed content - some sections with citations, some without
    await page.route('**/api/report/section/**', route => {
      const url = route.request().url()
      
      let mockResponse
      if (url.includes('environmental')) {
        // Section with citations
        mockResponse = {
          text: `<p>Environmental data shows improvement [^2917579].</p>`,
          citations: [{
            doc_page_id: 2917579,
            title: "Environmental Report",
            url: "https://example.com/env",
            public_url: "https://example.com/env",
            page: 1,
            score: 0.9,
            doc_id: 123,
            credibility: 0.8,
            doc_name: "Environmental Analysis",
            year: 2024,
            authors: []
          }]
        }
      } else {
        // Section without citations
        mockResponse = {
          text: `<p>This section has no citations but important content.</p>`,
          citations: []
        }
      }

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      });
    });

    let summaryRequestContent = '';
    await page.route('**/api/report/summarize', route => {
      const request = route.request();
      const postData = request.postData();
      
      if (postData) {
        try {
          const data = JSON.parse(postData);
          summaryRequestContent = data.content || '';
        } catch (e) {
          console.error('Failed to parse summary request:', e);
        }
      }

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify({
          text: `Summary of mixed content [^2917579].`,
          metadata: { type: 'summary' }
        })
      });
    });

    // Load multiple sections and verify citation preservation
    const reportSections = page.locator('report-section')
    const sectionCount = await reportSections.count()
    
    if (sectionCount > 0) {
      // Load first section (should have citations)
      await reportSections.first().click()
      await page.waitForTimeout(3000)
      
      // Load second section if available (should not have citations)
      if (sectionCount > 1) {
        await reportSections.nth(1).click()
        await page.waitForTimeout(3000)
      }
      
      // The summary request should preserve citations where they exist
      // and handle plain text where they don't
      if (summaryRequestContent) {
        // Should contain citation format for sections that have them
        if (summaryRequestContent.includes('Environmental data')) {
          expect(summaryRequestContent).toContain('[^2917579]')
        }
        
        // Should contain plain text for sections without citations
        expect(summaryRequestContent).toContain('important content')
      }
    }
  })
})
