import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';
import { getTestEntity, getTestModel } from './test-config';

test.describe('Entity-Model-Run (EMR) Selector', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to dashboard to ensure EMR selector is visible
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
  });

  test('should display company selector dropdown', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Check company selector
    const companySelector = page.locator('[data-testid="company-selector"]');
    await expect(companySelector).toBeVisible();

    // Test opening company dropdown
    await companySelector.click();
    await expect(page.locator('[data-testid="company-dropdown"]')).toBeVisible();

    // Verify companies from view_my_companies are loaded
    const companyOptions = page.locator('[data-testid="company-option"]');
    await expect(companyOptions.first()).toBeVisible({ timeout: 10000 });

    // Check company option has name and ID
    const firstCompany = companyOptions.first();
    await expect(firstCompany.locator('[data-testid="company-name"]')).toBeVisible();
    
    const companyId = firstCompany.locator('[data-testid="company-id"]');
    if (await companyId.isVisible()) {
      await expect(companyId).toBeVisible();
    }

    // Close dropdown
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="company-dropdown"]')).not.toBeVisible();
  });

  test('should allow selecting different companies', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Get initial company selection
    const companySelector = page.locator('[data-testid="company-selector"]');
    const initialCompany = await companySelector.textContent();

    // Open company dropdown
    await companySelector.click();
    await expect(page.locator('[data-testid="company-dropdown"]')).toBeVisible();

    // Select different company if available
    const companyOptions = page.locator('[data-testid="company-option"]');
    const optionCount = await companyOptions.count();

    if (optionCount > 1) {
      // Find a different company option
      let selectedOption;
      for (let i = 0; i < optionCount; i++) {
        const option = companyOptions.nth(i);
        const optionText = await option.textContent();
        
        if (optionText !== initialCompany) {
          selectedOption = option;
          break;
        }
      }

      if (selectedOption) {
        await selectedOption.click();
        await page.waitForLoadState('networkidle');

        // Verify company changed in selector
        const newCompany = await companySelector.textContent();
        expect(newCompany).not.toBe(initialCompany);

        // Verify URL updated with new entity parameter
        const currentURL = page.url();
        expect(currentURL).toContain('entity=');
      }
    }
  });

  test('should display run selector with date formatting', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Check run selector
    const runSelector = page.locator('[data-testid="run-selector"]');
    await expect(runSelector).toBeVisible();

    // Test opening run dropdown
    await runSelector.click();
    await expect(page.locator('[data-testid="run-dropdown"]')).toBeVisible();

    // Verify run options are loaded
    const runOptions = page.locator('[data-testid="run-option"]');
    if (await runOptions.count() > 0) {
      const firstRun = runOptions.first();
      
      // Check run has date formatting
      await expect(firstRun.locator('[data-testid="run-date"]')).toBeVisible();
      
      // Verify date format (should be readable format)
      const runDate = await firstRun.locator('[data-testid="run-date"]').textContent();
      expect(runDate).toMatch(/\d{4}|\w{3}|\d{1,2}/); // Year, month name, or day
      
      // Check run type if available
      const runType = firstRun.locator('[data-testid="run-type"]');
      if (await runType.isVisible()) {
        await expect(runType).toBeVisible();
      }
    }

    // Close dropdown
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="run-dropdown"]')).not.toBeVisible();
  });

  test('should allow selecting different runs', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Get initial run selection
    const runSelector = page.locator('[data-testid="run-selector"]');
    const initialRun = await runSelector.textContent();

    // Open run dropdown
    await runSelector.click();
    await expect(page.locator('[data-testid="run-dropdown"]')).toBeVisible();

    // Select different run if available
    const runOptions = page.locator('[data-testid="run-option"]');
    const optionCount = await runOptions.count();

    if (optionCount > 1) {
      // Select second run option
      const secondRun = runOptions.nth(1);
      await secondRun.click();
      await page.waitForLoadState('networkidle');

      // Verify run changed in selector
      const newRun = await runSelector.textContent();
      expect(newRun).not.toBe(initialRun);

      // Verify URL updated with new run parameter
      const currentURL = page.url();
      expect(currentURL).toContain('run=');
    }
  });

  test('should display model selector with available models', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Check model selector
    const modelSelector = page.locator('[data-testid="model-selector"]');
    await expect(modelSelector).toBeVisible();

    // Test opening model dropdown
    await modelSelector.click();
    await expect(page.locator('[data-testid="model-dropdown"]')).toBeVisible();

    // Verify expected models are available
    const modelOptions = page.locator('[data-testid="model-option"]');
    await expect(modelOptions.first()).toBeVisible({ timeout: 10000 });

    // Check for expected model types
    const modelTexts = await modelOptions.allTextContents();
    const expectedModels = ['ekoIntelligence', 'SDG', 'Doughnut', 'Plant Based Treaty'];
    
    // At least one expected model should be present
    const hasExpectedModel = expectedModels.some(model => 
      modelTexts.some(text => text.toLowerCase().includes(model.toLowerCase()))
    );
    expect(hasExpectedModel).toBe(true);

    // Close dropdown
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="model-dropdown"]')).not.toBeVisible();
  });

  test('should allow selecting different models', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Get initial model selection
    const modelSelector = page.locator('[data-testid="model-selector"]');
    const initialModel = await modelSelector.textContent();

    // Open model dropdown
    await modelSelector.click();
    await expect(page.locator('[data-testid="model-dropdown"]')).toBeVisible();

    // Select different model if available
    const modelOptions = page.locator('[data-testid="model-option"]');
    const optionCount = await modelOptions.count();

    if (optionCount > 1) {
      // Find a different model option
      let selectedOption;
      for (let i = 0; i < optionCount; i++) {
        const option = modelOptions.nth(i);
        const optionText = await option.textContent();
        
        if (optionText !== initialModel) {
          selectedOption = option;
          break;
        }
      }

      if (selectedOption) {
        await selectedOption.click();
        await page.waitForLoadState('networkidle');

        // Verify model changed in selector
        const newModel = await modelSelector.textContent();
        expect(newModel).not.toBe(initialModel);

        // Verify URL updated with new model parameter
        const currentURL = page.url();
        expect(currentURL).toContain('model=');
      }
    }
  });

  test('should display and toggle disclosure switch', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Check disclosure toggle
    const disclosureToggle = page.locator('[data-testid="disclosure-toggle"]');
    await expect(disclosureToggle).toBeVisible();

    // Get initial state
    const initialState = await disclosureToggle.isChecked();

    // Toggle disclosure
    await disclosureToggle.click();
    await page.waitForLoadState('networkidle');

    // Verify state changed
    const newState = await disclosureToggle.isChecked();
    expect(newState).toBe(!initialState);

    // Verify URL updated with disclosure parameter
    const currentURL = page.url();
    expect(currentURL).toContain('disclosure=');

    // Toggle back
    await disclosureToggle.click();
    await page.waitForLoadState('networkidle');

    // Verify state reverted
    const finalState = await disclosureToggle.isChecked();
    expect(finalState).toBe(initialState);
  });

  test('should handle loading states appropriately', async ({ page }) => {
    // Navigate to page that will load EMR selector
    await page.goto('/customer/dashboard');

    // Check for loading indicators in selectors
    const companySelector = page.locator('[data-testid="company-selector"]');
    const runSelector = page.locator('[data-testid="run-selector"]');
    const modelSelector = page.locator('[data-testid="model-selector"]');

    // Selectors should eventually become visible
    await expect(companySelector).toBeVisible({ timeout: 30000 });
    await expect(runSelector).toBeVisible({ timeout: 30000 });
    await expect(modelSelector).toBeVisible({ timeout: 30000 });

    // Check for loading states within dropdowns
    await companySelector.click();
    
    try {
      // Look for loading indicator in dropdown
      await expect(page.locator('[data-testid="company-dropdown-loading"]')).toBeVisible({ timeout: 5000 });
      await expect(page.locator('[data-testid="company-dropdown-loading"]')).not.toBeVisible({ timeout: 15000 });
    } catch {
      // Loading might be too fast to catch
      await expect(page.locator('[data-testid="company-dropdown"]')).toBeVisible();
    }

    await page.keyboard.press('Escape');
  });

  test('should handle disabled states correctly', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Check if any selectors are disabled (might happen during loading or based on permissions)
    const companySelector = page.locator('[data-testid="company-selector"]');
    const runSelector = page.locator('[data-testid="run-selector"]');
    const modelSelector = page.locator('[data-testid="model-selector"]');

    // If disabled, they should have appropriate visual styling
    if (await companySelector.isDisabled()) {
      await expect(companySelector).toHaveClass(/disabled|opacity/);
    }

    if (await runSelector.isDisabled()) {
      await expect(runSelector).toHaveClass(/disabled|opacity/);
    }

    if (await modelSelector.isDisabled()) {
      await expect(modelSelector).toHaveClass(/disabled|opacity/);
    }

    // Disclosure toggle should generally be enabled
    const disclosureToggle = page.locator('[data-testid="disclosure-toggle"]');
    await expect(disclosureToggle).not.toBeDisabled();
  });

  test('should manage URL parameters correctly', async ({ page }) => {
    // Navigate with specific parameters
    const testEntity = getTestEntity();
    const testModel = getTestModel();
    
    await page.goto(`/customer/dashboard?entity=${testEntity}&model=${testModel}&disclosure=true`);
    await page.waitForLoadState('networkidle');

    // Wait for EMR selector to load
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Verify selectors reflect URL parameters
    const modelSelector = page.locator('[data-testid="model-selector"]');
    const disclosureToggle = page.locator('[data-testid="disclosure-toggle"]');

    // Model should match URL parameter
    const modelText = await modelSelector.textContent();
    expect(modelText?.toLowerCase()).toContain(testModel.toLowerCase());

    // Disclosure should be checked
    await expect(disclosureToggle).toBeChecked();

    // Change a parameter and verify URL updates
    await modelSelector.click();
    const modelOptions = page.locator('[data-testid="model-option"]');
    
    if (await modelOptions.count() > 1) {
      await modelOptions.nth(1).click();
      await page.waitForLoadState('networkidle');

      // URL should be updated
      const newURL = page.url();
      expect(newURL).toContain('model=');
    }
  });

  test('should persist selections when navigating between pages', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Set specific selections
    const modelSelector = page.locator('[data-testid="model-selector"]');
    const disclosureToggle = page.locator('[data-testid="disclosure-toggle"]');

    // Change disclosure state
    const initialDisclosureState = await disclosureToggle.isChecked();
    await disclosureToggle.click();
    await page.waitForLoadState('networkidle');

    // Get current model
    const currentModel = await modelSelector.textContent();

    // Navigate to different page
    await page.goto('/customer/dashboard/flags');
    await page.waitForLoadState('networkidle');

    // Verify EMR selector maintains state
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });
    
    const newModelText = await page.locator('[data-testid="model-selector"]').textContent();
    const newDisclosureState = await page.locator('[data-testid="disclosure-toggle"]').isChecked();

    expect(newModelText).toBe(currentModel);
    expect(newDisclosureState).toBe(!initialDisclosureState);
  });

  test('should handle empty or error states gracefully', async ({ page }) => {
    // Test behavior when no companies are available (edge case)
    // This might happen with restricted permissions or empty database
    
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');

    // Wait for EMR selector
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Open company dropdown
    const companySelector = page.locator('[data-testid="company-selector"]');
    await companySelector.click();

    // Check for empty state or error message
    try {
      await expect(page.locator('[data-testid="company-dropdown"]')).toBeVisible({ timeout: 10000 });
      
      const companyOptions = page.locator('[data-testid="company-option"]');
      const optionCount = await companyOptions.count();
      
      if (optionCount === 0) {
        // Should show empty state message
        await expect(page.locator('[data-testid="no-companies-message"]')).toBeVisible();
      } else {
        // Should show companies
        await expect(companyOptions.first()).toBeVisible();
      }
    } catch {
      // Error state might prevent dropdown from opening
      console.log('Company dropdown failed to open - checking for error state');
    }

    await page.keyboard.press('Escape');
  });

  test('should provide keyboard navigation support', async ({ page }) => {
    // Wait for EMR selector to be visible
    await expect(page.locator('[data-testid="emr-selector"]')).toBeVisible({ timeout: 30000 });

    // Test keyboard navigation on company selector
    const companySelector = page.locator('[data-testid="company-selector"]');
    
    // Focus and open with keyboard
    await companySelector.focus();
    await page.keyboard.press('Enter');
    await expect(page.locator('[data-testid="company-dropdown"]')).toBeVisible();

    // Navigate with arrow keys
    await page.keyboard.press('ArrowDown');
    await page.keyboard.press('ArrowDown');
    
    // Select with Enter
    await page.keyboard.press('Enter');
    await page.waitForLoadState('networkidle');

    // Verify selection changed
    await expect(page.locator('[data-testid="company-dropdown"]')).not.toBeVisible();
  });
});