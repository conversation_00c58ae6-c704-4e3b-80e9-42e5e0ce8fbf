import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';

test.describe('Analysis Usage Page', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to usage page
    await page.goto('/customer/analysis/usage');
    await page.waitForLoadState('networkidle');
  });

  test('should display quota cards for different analysis types', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Check for quota cards
    await expect(page.locator('[data-testid="quota-cards"]')).toBeVisible();

    // Document analysis quota card
    const documentQuotaCard = page.locator('[data-testid="document-analysis-quota"]');
    if (await documentQuotaCard.isVisible()) {
      await expect(documentQuotaCard).toBeVisible();
      
      // Check quota details
      await expect(documentQuotaCard.locator('[data-testid="quota-title"]')).toBeVisible();
      await expect(documentQuotaCard.locator('[data-testid="quota-used"]')).toBeVisible();
      await expect(documentQuotaCard.locator('[data-testid="quota-limit"]')).toBeVisible();
      
      // Verify numeric values
      const usedText = await documentQuotaCard.locator('[data-testid="quota-used"]').textContent();
      const limitText = await documentQuotaCard.locator('[data-testid="quota-limit"]').textContent();
      
      expect(usedText).toMatch(/\d+/);
      expect(limitText).toMatch(/\d+/);
    }

    // Entity analysis quota card
    const entityQuotaCard = page.locator('[data-testid="entity-analysis-quota"]');
    if (await entityQuotaCard.isVisible()) {
      await expect(entityQuotaCard).toBeVisible();
      await expect(entityQuotaCard.locator('[data-testid="quota-used"]')).toBeVisible();
      await expect(entityQuotaCard.locator('[data-testid="quota-limit"]')).toBeVisible();
    }

    // Entity quota card
    const generalEntityQuotaCard = page.locator('[data-testid="entity-quota"]');
    if (await generalEntityQuotaCard.isVisible()) {
      await expect(generalEntityQuotaCard).toBeVisible();
      await expect(generalEntityQuotaCard.locator('[data-testid="quota-used"]')).toBeVisible();
      await expect(generalEntityQuotaCard.locator('[data-testid="quota-limit"]')).toBeVisible();
    }
  });

  test('should display quota progress bars correctly', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Check quota progress bars
    const quotaCards = page.locator('[data-testid*="quota"]');
    const cardCount = await quotaCards.count();

    for (let i = 0; i < cardCount; i++) {
      const card = quotaCards.nth(i);
      const progressBar = card.locator('[data-testid="quota-progress"]');
      
      if (await progressBar.isVisible()) {
        await expect(progressBar).toBeVisible();
        
        // Check progress percentage
        const progressPercent = await progressBar.getAttribute('data-progress');
        if (progressPercent) {
          const percent = parseFloat(progressPercent);
          expect(percent).toBeGreaterThanOrEqual(0);
          expect(percent).toBeLessThanOrEqual(100);
        }
        
        // Check progress bar styling based on usage level
        if (progressPercent && parseFloat(progressPercent) > 80) {
          await expect(progressBar).toHaveClass(/danger|warning|red/);
        } else if (progressPercent && parseFloat(progressPercent) > 60) {
          await expect(progressBar).toHaveClass(/warning|amber|yellow/);
        } else {
          await expect(progressBar).toHaveClass(/success|good|green/);
        }
      }
    }
  });

  test('should display analysis history tabs', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Check for analysis history section
    const analysisHistory = page.locator('[data-testid="analysis-history"]');
    if (await analysisHistory.isVisible()) {
      await expect(analysisHistory).toBeVisible();

      // Check for tabs
      await expect(page.locator('[data-testid="history-tabs"]')).toBeVisible();

      // Company Analysis tab
      const companyAnalysisTab = page.locator('[data-testid="tab-company-analysis"]');
      if (await companyAnalysisTab.isVisible()) {
        await expect(companyAnalysisTab).toBeVisible();
        
        await companyAnalysisTab.click();
        await expect(page.locator('[data-testid="company-analysis-history"]')).toBeVisible();
        
        // Check for company analysis entries
        const companyEntries = page.locator('[data-testid="company-analysis-entry"]');
        if (await companyEntries.count() > 0) {
          const firstEntry = companyEntries.first();
          await expect(firstEntry.locator('[data-testid="analysis-date"]')).toBeVisible();
          await expect(firstEntry.locator('[data-testid="company-name"]')).toBeVisible();
          await expect(firstEntry.locator('[data-testid="analysis-status"]')).toBeVisible();
        }
      }

      // Document Analysis tab
      const documentAnalysisTab = page.locator('[data-testid="tab-document-analysis"]');
      if (await documentAnalysisTab.isVisible()) {
        await expect(documentAnalysisTab).toBeVisible();
        
        await documentAnalysisTab.click();
        await expect(page.locator('[data-testid="document-analysis-history"]')).toBeVisible();
        
        // Check for document analysis entries
        const documentEntries = page.locator('[data-testid="document-analysis-entry"]');
        if (await documentEntries.count() > 0) {
          const firstEntry = documentEntries.first();
          await expect(firstEntry.locator('[data-testid="analysis-date"]')).toBeVisible();
          await expect(firstEntry.locator('[data-testid="document-title"]')).toBeVisible();
          await expect(firstEntry.locator('[data-testid="analysis-status"]')).toBeVisible();
        }
      }
    }
  });

  test('should show feature flag-based component visibility', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Check for feature-gated quota cards
    const advancedQuotaCard = page.locator('[data-testid="advanced-analysis-quota"]');
    const betaQuotaCard = page.locator('[data-testid="beta-feature-quota"]');
    
    // These may or may not be visible depending on user's feature flags
    if (await advancedQuotaCard.isVisible()) {
      await expect(advancedQuotaCard).toBeVisible();
      await expect(advancedQuotaCard.locator('[data-testid="quota-title"]')).toBeVisible();
    }
    
    if (await betaQuotaCard.isVisible()) {
      await expect(betaQuotaCard).toBeVisible();
      await expect(betaQuotaCard.locator('[data-testid="quota-title"]')).toBeVisible();
    }

    // Check for feature-gated history tabs
    const advancedHistoryTab = page.locator('[data-testid="tab-advanced-analysis"]');
    if (await advancedHistoryTab.isVisible()) {
      await expect(advancedHistoryTab).toBeVisible();
    }
  });

  test('should display real-time quota updates', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Get initial quota values
    const documentQuotaCard = page.locator('[data-testid="document-analysis-quota"]');
    if (await documentQuotaCard.isVisible()) {
      const initialUsed = await documentQuotaCard.locator('[data-testid="quota-used"]').textContent();
      
      // Simulate quota update (this would typically happen via real-time updates)
      // For testing, we can check that the display handles updates correctly
      
      // Refresh the page to simulate real-time update
      await page.reload();
      await page.waitForLoadState('networkidle');
      await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });
      
      // Verify quota values are still displayed correctly
      if (await documentQuotaCard.isVisible()) {
        const newUsed = await documentQuotaCard.locator('[data-testid="quota-used"]').textContent();
        expect(newUsed).toMatch(/\d+/);
        
        // Values should be consistent or updated
        expect(newUsed).toBeTruthy();
      }
    }
  });

  test('should handle quota limit warnings', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Check for quota warning indicators
    const warningIndicators = page.locator('[data-testid*="quota-warning"], [class*="warning"], [class*="danger"]');
    
    if (await warningIndicators.count() > 0) {
      const firstWarning = warningIndicators.first();
      await expect(firstWarning).toBeVisible();
      
      // Check warning message
      const warningMessage = page.locator('[data-testid="quota-warning-message"]');
      if (await warningMessage.isVisible()) {
        const warningText = await warningMessage.textContent();
        expect(warningText?.toLowerCase()).toContain('limit');
      }
    }

    // Check for upgrade or contact prompts when near limits
    const upgradePrompt = page.locator('[data-testid="upgrade-prompt"]');
    if (await upgradePrompt.isVisible()) {
      await expect(upgradePrompt).toBeVisible();
      await expect(upgradePrompt.locator('[data-testid="upgrade-button"]')).toBeVisible();
    }

    const contactPrompt = page.locator('[data-testid="contact-prompt"]');
    if (await contactPrompt.isVisible()) {
      await expect(contactPrompt).toBeVisible();
    }
  });

  test('should display usage statistics and trends', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Check for usage trends section
    const usageTrends = page.locator('[data-testid="usage-trends"]');
    if (await usageTrends.isVisible()) {
      await expect(usageTrends).toBeVisible();

      // Check for trend charts
      const trendChart = usageTrends.locator('[data-testid="usage-trend-chart"]');
      if (await trendChart.isVisible()) {
        await expect(trendChart).toBeVisible();
        
        // Verify chart has data elements
        const chartElements = trendChart.locator('svg, canvas, [data-testid*="chart-element"]');
        await expect(chartElements.first()).toBeVisible();
      }

      // Check for usage period selector
      const periodSelector = usageTrends.locator('[data-testid="usage-period-selector"]');
      if (await periodSelector.isVisible()) {
        await periodSelector.click();
        
        // Verify period options
        await expect(page.locator('[data-testid="period-option-week"]')).toBeVisible();
        await expect(page.locator('[data-testid="period-option-month"]')).toBeVisible();
        await expect(page.locator('[data-testid="period-option-year"]')).toBeVisible();
        
        await page.keyboard.press('Escape');
      }
    }
  });

  test('should handle loading states appropriately', async ({ page }) => {
    // Navigate to usage page
    await page.goto('/customer/analysis/usage');

    // Check for loading indicators
    const loadingIndicators = page.locator('[data-testid*="loading"], [class*="loading"], [class*="spinner"]');
    
    try {
      // Loading indicators should appear initially
      await expect(loadingIndicators.first()).toBeVisible({ timeout: 5000 });
      
      // Then disappear as content loads
      await expect(loadingIndicators.first()).not.toBeVisible({ timeout: 30000 });
      
      // Content should be visible
      await expect(page.locator('[data-testid="usage-page"]')).toBeVisible();
    } catch {
      // If loading is very fast, content should be immediately visible
      await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });
    }
  });

  test('should display quota reset information', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Check for quota reset information
    const quotaResetInfo = page.locator('[data-testid="quota-reset-info"]');
    if (await quotaResetInfo.isVisible()) {
      await expect(quotaResetInfo).toBeVisible();

      // Check reset date
      const resetDate = quotaResetInfo.locator('[data-testid="quota-reset-date"]');
      if (await resetDate.isVisible()) {
        const resetText = await resetDate.textContent();
        expect(resetText).toMatch(/\d{4}|\w{3}|\d{1,2}/); // Should contain date elements
      }

      // Check reset countdown if available
      const resetCountdown = quotaResetInfo.locator('[data-testid="quota-reset-countdown"]');
      if (await resetCountdown.isVisible()) {
        const countdownText = await resetCountdown.textContent();
        expect(countdownText).toMatch(/\d+.*day|hour|minute/);
      }
    }
  });

  test('should handle empty usage history gracefully', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Check analysis history tabs
    const analysisHistory = page.locator('[data-testid="analysis-history"]');
    if (await analysisHistory.isVisible()) {
      // Click on company analysis tab
      const companyTab = page.locator('[data-testid="tab-company-analysis"]');
      if (await companyTab.isVisible()) {
        await companyTab.click();

        // Check for empty state or entries
        const companyEntries = page.locator('[data-testid="company-analysis-entry"]');
        const entryCount = await companyEntries.count();

        if (entryCount === 0) {
          // Should show empty state message
          const emptyMessage = page.locator('[data-testid="no-company-analysis-message"]');
          await expect(emptyMessage).toBeVisible();
        } else {
          // Should show entries
          await expect(companyEntries.first()).toBeVisible();
        }
      }

      // Click on document analysis tab
      const documentTab = page.locator('[data-testid="tab-document-analysis"]');
      if (await documentTab.isVisible()) {
        await documentTab.click();

        const documentEntries = page.locator('[data-testid="document-analysis-entry"]');
        const entryCount = await documentEntries.count();

        if (entryCount === 0) {
          const emptyMessage = page.locator('[data-testid="no-document-analysis-message"]');
          await expect(emptyMessage).toBeVisible();
        } else {
          await expect(documentEntries.first()).toBeVisible();
        }
      }
    }
  });

  test('should allow navigation to upgrade or billing from quota warnings', async ({ page }) => {
    // Wait for usage page to load
    await expect(page.locator('[data-testid="usage-page"]')).toBeVisible({ timeout: 30000 });

    // Check for upgrade button
    const upgradeButton = page.locator('[data-testid="upgrade-button"]');
    if (await upgradeButton.isVisible()) {
      await upgradeButton.click();
      
      // Should navigate to billing or upgrade page
      try {
        await page.waitForURL('**/billing**', { timeout: 10000 });
        await expect(page.locator('[data-testid="billing-page"]')).toBeVisible();
      } catch {
        try {
          await page.waitForURL('**/upgrade**', { timeout: 10000 });
          await expect(page.locator('[data-testid="upgrade-page"]')).toBeVisible();
        } catch {
          // Might open external link or modal
          console.log('Upgrade action completed (external or modal)');
        }
      }
    }

    // Check for contact support button
    const contactButton = page.locator('[data-testid="contact-support-button"]');
    if (await contactButton.isVisible()) {
      await contactButton.click();
      
      // Should navigate to contact page or open external link
      try {
        await page.waitForURL('**/contact**', { timeout: 10000 });
      } catch {
        // External link or modal is also valid
        console.log('Contact action completed');
      }
    }
  });
});