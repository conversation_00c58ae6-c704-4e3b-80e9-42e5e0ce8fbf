import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';
import { getTestEntity, getTestModel } from './test-config';

test.describe('Dashboard Main Page', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to dashboard with test entity
    await page.goto(`/customer/dashboard?entity=${getTestEntity()}&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');
  });

  test('should display dashboard score cards with correct data', async ({ page }) => {
    // Wait for dashboard to load
    await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible({ timeout: 30000 });

    // Check for score cards presence
    await expect(page.locator('[data-testid="green-flags-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="red-flags-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="cherry-picking-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="false-claims-card"]')).toBeVisible();
    await expect(page.locator('[data-testid="broken-promises-card"]')).toBeVisible();

    // Verify score cards have numeric values
    const greenFlagsValue = await page.locator('[data-testid="green-flags-value"]').textContent();
    const redFlagsValue = await page.locator('[data-testid="red-flags-value"]').textContent();
    
    expect(greenFlagsValue).toMatch(/^\d+$/);
    expect(redFlagsValue).toMatch(/^\d+$/);
  });

  test('should display overall rating gauge with correct styling', async ({ page }) => {
    // Wait for rating gauge to load
    await expect(page.locator('[data-testid="overall-rating-gauge"]')).toBeVisible({ timeout: 30000 });

    // Check rating value is present
    const ratingValue = await page.locator('[data-testid="rating-value"]').textContent();
    expect(ratingValue).toMatch(/^\d+$/);

    // Verify rating gauge has appropriate styling based on value
    const rating = parseInt(ratingValue || '0');
    const gaugeElement = page.locator('[data-testid="overall-rating-gauge"]');

    if (rating >= 70) {
      await expect(gaugeElement).toHaveClass(/good|success|green/);
    } else if (rating >= 40) {
      await expect(gaugeElement).toHaveClass(/medium|warning|amber/);
    } else {
      await expect(gaugeElement).toHaveClass(/poor|danger|red/);
    }
  });

  test('should expand and collapse flag badge lists', async ({ page }) => {
    // Wait for flag badges to load
    await expect(page.locator('[data-testid="red-flags-badge"]')).toBeVisible({ timeout: 30000 });

    // Test red flags expansion
    const redFlagsBadge = page.locator('[data-testid="red-flags-badge"]');
    await redFlagsBadge.click();
    
    // Check if expansion occurred
    await expect(page.locator('[data-testid="red-flags-expanded-list"]')).toBeVisible();
    
    // Test show more/less functionality
    const showMoreButton = page.locator('[data-testid="red-flags-show-more"]');
    if (await showMoreButton.isVisible()) {
      await showMoreButton.click();
      await expect(page.locator('[data-testid="red-flags-show-less"]')).toBeVisible();
    }

    // Test green flags expansion
    const greenFlagsBadge = page.locator('[data-testid="green-flags-badge"]');
    await greenFlagsBadge.click();
    await expect(page.locator('[data-testid="green-flags-expanded-list"]')).toBeVisible();
  });

  test('should navigate between dashboard tabs', async ({ page }) => {
    // Wait for tabs to load
    await expect(page.locator('[data-testid="dashboard-tabs"]')).toBeVisible({ timeout: 30000 });

    // Test Summary tab (default)
    await expect(page.locator('[data-testid="tab-summary"]')).toHaveClass(/active|selected/);
    await expect(page.locator('[data-testid="summary-content"]')).toBeVisible();

    // Test Red Flags tab
    await page.click('[data-testid="tab-red-flags"]');
    await expect(page.locator('[data-testid="red-flags-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="tab-red-flags"]')).toHaveClass(/active|selected/);

    // Test Green Flags tab
    await page.click('[data-testid="tab-green-flags"]');
    await expect(page.locator('[data-testid="green-flags-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="tab-green-flags"]')).toHaveClass(/active|selected/);

    // Test Promises tab
    await page.click('[data-testid="tab-promises"]');
    await expect(page.locator('[data-testid="promises-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="tab-promises"]')).toHaveClass(/active|selected/);

    // Test Claims tab
    await page.click('[data-testid="tab-claims"]');
    await expect(page.locator('[data-testid="claims-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="tab-claims"]')).toHaveClass(/active|selected/);

    // Test Cherry Picking tab
    await page.click('[data-testid="tab-cherry-picking"]');
    await expect(page.locator('[data-testid="cherry-picking-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="tab-cherry-picking"]')).toHaveClass(/active|selected/);
  });

  test('should display donut charts for positive and negative actions', async ({ page }) => {
    // Wait for charts to load
    await expect(page.locator('[data-testid="positive-actions-chart"]')).toBeVisible({ timeout: 30000 });
    await expect(page.locator('[data-testid="negative-actions-chart"]')).toBeVisible({ timeout: 30000 });

    // Verify chart data is loaded
    const positiveChart = page.locator('[data-testid="positive-actions-chart"] svg');
    const negativeChart = page.locator('[data-testid="negative-actions-chart"] svg');

    await expect(positiveChart).toBeVisible();
    await expect(negativeChart).toBeVisible();

    // Check chart legends
    await expect(page.locator('[data-testid="positive-chart-legend"]')).toBeVisible();
    await expect(page.locator('[data-testid="negative-chart-legend"]')).toBeVisible();
  });

  test('should display timeline component', async ({ page }) => {
    // Wait for timeline to load
    await expect(page.locator('[data-testid="dashboard-timeline"]')).toBeVisible({ timeout: 30000 });

    // Check timeline has entries
    const timelineEntries = page.locator('[data-testid="timeline-entry"]');
    await expect(timelineEntries.first()).toBeVisible();

    // Verify timeline entries have dates
    const firstEntry = timelineEntries.first();
    await expect(firstEntry.locator('[data-testid="timeline-date"]')).toBeVisible();
    await expect(firstEntry.locator('[data-testid="timeline-content"]')).toBeVisible();
  });

  test('should apply glass-morphism styling to dashboard cards', async ({ page }) => {
    // Wait for dashboard to load
    await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible({ timeout: 30000 });

    // Check glass cards have appropriate classes
    const glassCards = page.locator('[data-testid*="card"], [class*="glass"]');
    const firstCard = glassCards.first();

    await expect(firstCard).toBeVisible();
    
    // Verify glass-morphism properties are applied
    const cardStyles = await firstCard.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        backdropFilter: styles.backdropFilter,
        borderRadius: styles.borderRadius,
        background: styles.background
      };
    });

    expect(cardStyles.backdropFilter).toContain('blur');
    expect(parseFloat(cardStyles.borderRadius)).toBeGreaterThan(20); // 1.5rem = 24px
  });

  test('should handle loading states gracefully', async ({ page }) => {
    // Navigate to dashboard
    await page.goto(`/customer/dashboard?entity=${getTestEntity()}&model=${getTestModel()}`);

    // Check for loading indicators
    const loadingIndicators = page.locator('[data-testid*="loading"], [class*="loading"], [class*="spinner"]');
    
    // Either loading indicators should be present initially and then disappear,
    // or content should be visible (if loading is very fast)
    try {
      await expect(loadingIndicators.first()).toBeVisible({ timeout: 5000 });
      await expect(loadingIndicators.first()).not.toBeVisible({ timeout: 30000 });
    } catch {
      // Loading was too fast, content should be visible
      await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible({ timeout: 30000 });
    }
  });

  test('should update dashboard when entity or model parameters change', async ({ page }) => {
    // Load initial dashboard
    await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible({ timeout: 30000 });
    
    // Get initial values
    const initialRedFlags = await page.locator('[data-testid="red-flags-value"]').textContent();
    
    // Change entity parameter (if multiple test entities available)
    const testEntity2 = 'JN6ZWej7Rw'; // Secondary test entity
    await page.goto(`/customer/dashboard?entity=${testEntity2}&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');
    
    // Verify dashboard updated
    await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible({ timeout: 30000 });
    
    // Values might be different for different entity
    const newRedFlags = await page.locator('[data-testid="red-flags-value"]').textContent();
    expect(newRedFlags).toMatch(/^\d+$/);
  });
});