import { expect, Page } from '@playwright/test'
import { getAuthCredentials, getTestTemplate } from '../../test-config'

/**
 * Common test utilities for the report system
 */

export class TestUtils {
  constructor(private page: Page) {}

  /**
   * Login with test credentials
   */
  async login(email?: string, password?: string) {
    const credentials = getAuthCredentials();
    const loginEmail = email || credentials.email;
    const loginPassword = password || credentials.password;

    // Retry navigation if server is still starting up
    let retries = 5;
    while (retries > 0) {
      try {
        await this.page.goto('/login?next=%2Fcustomer', { timeout: 30000 });
        await this.page.waitForLoadState('networkidle', { timeout: 30000 });
        break;
      } catch (error) {
        retries--;
        if (retries === 0) throw error;
        console.log(`Login navigation failed, retrying... (${retries} attempts left)`);
        await this.page.waitForTimeout(2000);
      }
    }

    await this.page.fill('#email', loginEmail);
    await this.page.fill('#password', loginPassword);
    await this.page.click('button[type="submit"]');
    
    // Wait for either /customer or /customer/dashboard - be more flexible
    try {
      await this.page.waitForURL(/\/customer/, { timeout: 30000 });
    } catch (error) {
      // If direct URL wait fails, check if we're actually on a customer page
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/customer')) {
        throw new Error(`Login failed - expected customer URL but got: ${currentUrl}`);
      }
      // If we're on a customer page, consider it successful
      console.log(`Login successful, redirected to: ${currentUrl}`);
    }
  }

  /**
   * Create a new document from template
   */
  async createDocumentFromTemplate(templateName?: string) {
    const template = templateName || getTestTemplate('primary') || 'Blank Document';
    console.log(`Creating document from template: ${template}`)

    await this.page.goto('/customer/documents', { timeout: 60000 });

    // Wait for page to load completely
    await this.page.waitForLoadState('networkidle', { timeout: 5000 })

    // Wait for authentication and data loading
    await this.page.waitForFunction(() => {
      // Use vanilla JS instead of Playwright selectors
      const buttons = Array.from(document.querySelectorAll('button, a'))
      return buttons.some(btn => btn.textContent && btn.textContent.includes('New Document'))
    }, { timeout: 30000 })

    // Click New Document button
    await this.page.click('[data-testid="new-document-button"]')
    console.log('Clicked New Document button')

    // Wait for template dialog to appear with better selectors
    await this.page.waitForSelector('[role="dialog"], .template-dialog, .modal, [data-testid="template-dialog"]', { timeout: 20000 })
    console.log('Template dialog appeared')

    // Wait for template data to load (entities and model sections)
    await this.page.waitForFunction(() => {
      const dialog = document.querySelector('[role="dialog"], .template-dialog, .modal')
      return dialog && dialog.textContent && dialog.textContent.includes('EKO Report')
    }, { timeout: 30000 })

    // Use data-testid for template selection - map template names to actual IDs
    const templateNameToId: Record<string, string> = {
      'blank document': 'blank',
      'meeting notes': 'meeting-notes',
      'project report': 'project-report',
      'eko report': 'eko-report'
    };
    
    const templateId = templateNameToId[template.toLowerCase()] || template.toLowerCase().replace(/\s+/g, '-');
    const templateSelector = `[data-testid="template-${templateId}"]`;

    // Wait for template to be available and click it
    try {
      await this.page.waitForSelector(templateSelector, { timeout: 10000 })
      await this.page.click(templateSelector)
      console.log(`Clicked template: ${template}`)
    } catch (error) {
      console.log(`Failed to find template selector: ${templateSelector}`)
      console.log('Available templates:', await this.page.locator('[data-testid^="template-"]').allTextContents())
      throw error
    }

    // Wait for navigation to document editor
    await this.page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 60000 })
    console.log('Navigated to document editor')

    // Wait for editor to load
    await this.waitForEditor(30000)
    console.log('Editor loaded successfully')

    return this.getDocumentIdFromUrl();
  }

  /**
   * Get document ID from current URL
   */
  getDocumentIdFromUrl(): string {
    const url = this.page.url();
    const match = url.match(/\/documents\/([a-f0-9-]+)/);
    if (!match) {
      throw new Error('Could not extract document ID from URL');
    }
    return match[1];
  }

  /**
   * Wait for report component to finish loading
   */
  async waitForComponentLoading(componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first();
    
    // Wait for loading to start (spinner appears)
    await expect(component.locator('.animate-spin')).toBeVisible({ timeout: 5000 });
    
    // Wait for loading to complete (spinner disappears)
    await expect(component.locator('.animate-spin')).not.toBeVisible({ timeout: 30000 });
  }

  /**
   * Open component configuration dialog
   */
  async openComponentConfig(componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first();
    
    // Use specific test ID based on component type
    let testId: string;
    if (componentSelector.includes('summary')) {
      testId = 'report-summary-menu-trigger';
    } else {
      testId = 'report-section-menu-trigger';
    }
    
    const menuTrigger = component.locator(`[data-testid="${testId}"]`);
    await menuTrigger.click();
    await this.page.click('text=Configure');

    // Wait for dialog to open
    await expect(this.page.locator('[role="dialog"]')).toBeVisible();
  }

  /**
   * Fill component configuration form
   */
  async fillComponentConfig(config: {
    id?: string;
    title?: string;
    prompt?: string;
    entity?: string;
    model?: string;
    section?: string;
  }) {
    if (config.id) {
      await this.page.fill('input[placeholder="component-id"]', config.id);
    }
    if (config.title) {
      await this.page.fill('input[placeholder="Component Title"]', config.title);
    }
    if (config.prompt) {
      await this.page.fill('textarea[placeholder*="Additional instructions"]', config.prompt);
    }
    if (config.entity) {
      await this.page.click('text=Select Entity');
      await this.page.click(`text=${config.entity}`);
    }
    if (config.model) {
      await this.page.click('text=Select Model');
      await this.page.click(`text=${config.model}`);
    }
    if (config.section) {
      await this.page.click('text=Select Section');
      await this.page.click(`text=${config.section}`);
    }
  }

  /**
   * Confirm component configuration
   */
  async confirmComponentConfig() {
    // Use data-testid for Create/Update Component button
    const confirmButton = this.page.locator('[data-testid="create-component-button"]');
    await confirmButton.click();
    await expect(this.page.locator('[role="dialog"]')).not.toBeVisible();
  }

  /**
   * Add new report component from toolbar
   */
  async addReportComponent(type: 'section' | 'group' | 'summary', config?: any) {
    const buttonMap = {
      section: 'Insert Report Section',
      group: 'Insert Report Group',
      summary: 'Insert Report Summary'
    };

    await this.page.click(`button[title*="${buttonMap[type]}"]`);
    await expect(this.page.locator('[role="dialog"]')).toBeVisible();

    if (config) {
      await this.fillComponentConfig(config);
    }

    await this.confirmComponentConfig();
  }

  /**
   * Perform component action (refresh, lock, preserve, delete)
   */
  async performComponentAction(action: 'refresh' | 'lock' | 'preserve' | 'delete', componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first();
    // Look for the menu trigger button (first button in the component)
    const menuTrigger = component.locator('button').first();
    await menuTrigger.click();

    const actionText = action.charAt(0).toUpperCase() + action.slice(1);
    await this.page.click(`text=${actionText}`);
  }

  /**
   * Check if component has specific state
   */
  async checkComponentState(state: 'locked' | 'preserved' | 'loading', componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first();
    
    const iconMap = {
      locked: '.lucide-lock',
      preserved: '.lucide-shield',
      loading: '.animate-spin'
    };

    await expect(component.locator(iconMap[state])).toBeVisible();
  }

  /**
   * Mock API response for testing
   */
  async mockApiResponse(endpoint: string, response: any) {
    await this.page.route(`**${endpoint}**`, route => 
      route.fulfill({ 
        status: 200, 
        body: JSON.stringify(response) 
      })
    );
  }

  /**
   * Mock API error for testing
   */
  async mockApiError(endpoint: string, status = 500) {
    await this.page.route(`**${endpoint}**`, route => 
      route.fulfill({ 
        status, 
        body: JSON.stringify({ error: 'Test error' }) 
      })
    );
  }

  /**
   * Wait for auto-save to complete
   */
  async waitForAutoSave() {
    // Look for save indicator
    await expect(this.page.locator('text=Saving...')).toBeVisible({ timeout: 5000 });
    await expect(this.page.locator('text=Saving...')).not.toBeVisible({ timeout: 10000 });
  }

  /**
   * Check document title
   */
  async checkDocumentTitle(expectedTitle: string) {
    await expect(this.page).toHaveTitle(new RegExp(expectedTitle));
  }

  /**
   * Navigate to documents list
   */
  async goToDocuments() {
    await this.page.goto('/customer/documents');
    await expect(this.page.locator('text=Documents')).toBeVisible();
  }

  /**
   * Check if component exists by ID
   */
  async checkComponentExists(componentId: string) {
    await expect(this.page.locator(`text=${componentId}`)).toBeVisible();
  }

  /**
   * Count components of specific type
   */
  async countComponents(type: 'report-section' | 'report-group' | 'report-summary'): Promise<number> {
    return await this.page.locator(`.${type}`).count();
  }

  /**
   * Check if citations are displayed
   */
  async checkCitationsDisplayed() {
    await expect(this.page.locator('[data-type="references"]')).toBeVisible();
    const citationCount = await this.page.locator('.citation').count();
    expect(citationCount).toBeGreaterThan(0);
  }

  /**
   * Simulate typing in editor (legacy method - use typeInEditorAtCursor for new code)
   */
  async typeInEditor(text: string) {
    return await this.typeInEditorAtCursor(text);
  }

  /**
   * Check if content exists in editor
   */
  async checkEditorContent(text: string) {
    await expect(this.page.locator(`.ProseMirror:has-text("${text}")`)).toBeVisible();
  }

  /**
   * Export document (if export functionality exists)
   */
  async exportDocument(format = 'pdf') {
    await this.page.click('button[title*="Export"]');
    await this.page.click(`text=${format.toUpperCase()}`);
    
    // Wait for download to start
    const downloadPromise = this.page.waitForEvent('download');
    const download = await downloadPromise;
    
    return download;
  }

  /**
   * Check for error messages
   */
  async checkForErrors() {
    const errorSelectors = [
      '.error',
      '.alert-error',
      '[role="alert"]',
      'text=Error',
      'text=Failed'
    ];

    for (const selector of errorSelectors) {
      const errorElement = this.page.locator(selector);
      if (await errorElement.isVisible()) {
        const errorText = await errorElement.textContent();
        throw new Error(`Error found on page: ${errorText}`);
      }
    }
  }

  /**
   * Wait for charts to render within a component
   */
  async waitForChartsToRender(componentSelector = '.report-section', expectedCount?: number) {
    const component = this.page.locator(componentSelector).first();
    const charts = component.locator('chart, [data-type="chart"]');

    if (expectedCount !== undefined) {
      await expect(charts).toHaveCount(expectedCount, { timeout: 10000 });
    } else {
      await expect(charts.first()).toBeVisible({ timeout: 10000 });
    }

    // Wait for charts to finish rendering (no loading indicators)
    await expect(charts.first().locator('.animate-spin')).not.toBeVisible({ timeout: 5000 });
  }

  /**
   * Verify citations are properly loaded and displayed
   */
  async verifyCitationsLoaded(expectedCount?: number) {
    const citations = this.page.locator('.citation-wrapper');

    if (expectedCount !== undefined) {
      await expect(citations).toHaveCount(expectedCount, { timeout: 10000 });
    } else {
      await expect(citations.first()).toBeVisible({ timeout: 10000 });
    }

    // Verify citations have proper content (not error states)
    const errorCitations = this.page.locator('.citation-error');
    await expect(errorCitations).toHaveCount(0);
  }

  /**
   * Check if nested components are properly structured
   */
  async verifyNestedStructure(groupSelector = '.report-group', expectedSections?: number) {
    const group = this.page.locator(groupSelector).first();
    await expect(group).toBeVisible();

    const sectionsInGroup = group.locator('.report-section');
    if (expectedSections !== undefined) {
      await expect(sectionsInGroup).toHaveCount(expectedSections, { timeout: 10000 });
    } else {
      await expect(sectionsInGroup.first()).toBeVisible({ timeout: 10000 });
    }
  }

  /**
   * Mock API response with charts and citations
   */
  async mockReportResponseWithCharts(chartCount = 1, citationCount = 1) {
    const charts = Array.from({ length: chartCount }, (_, i) => `
      <chart>
      {
        "title": { "text": "Test Chart ${i + 1}" },
        "xAxis": { "type": "category", "data": ["A", "B", "C"] },
        "yAxis": { "type": "value" },
        "series": [{ "data": [${(i + 1) * 10}, ${(i + 1) * 15}, ${(i + 1) * 12}], "type": "bar" }]
      }
      </chart>
    `).join('');

    const citations = Array.from({ length: citationCount }, (_, i) => ({
      doc_page_id: 900000 + i,
      title: `Test Citation ${i + 1}`,
      url: `https://example.com/test${i + 1}`,
      domain: "Test"
    }));

    const citationRefs = Array.from({ length: citationCount }, (_, i) => `[^${900000 + i}]`).join(' ');

    const mockResponse = {
      text: `
        <h2>Test Report Section</h2>
        <p>This is test content with citations ${citationRefs}.</p>
        ${charts}
        <p>Additional content after charts.</p>
      `,
      citations
    };

    await this.page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      });
    });
  }

  /**
   * Verify chart error handling
   */
  async verifyChartErrorHandling() {
    // Check for chart errors or missing charts
    const charts = this.page.locator('chart, [data-type="chart"]');
    const chartCount = await charts.count();

    if (chartCount === 0) {
      console.log('No charts rendered - this may indicate error handling for malformed JSON');
    } else {
      console.log(`${chartCount} charts rendered successfully`);
    }

    // Also check for any error indicators
    const chartErrors = this.page.locator('.chart-error, .error');
    if (await chartErrors.count() > 0) {
      await expect(chartErrors.first()).toBeVisible();
    }
  }

  /**
   * Check component loading order (sections before summaries)
   */
  async verifyLoadingOrder(apiCalls: string[]) {
    const sectionCalls = apiCalls.filter(url => url.includes('/section/'));
    const summaryCalls = apiCalls.filter(url => url.includes('/summarize'));

    if (sectionCalls.length > 0 && summaryCalls.length > 0) {
      // Find the timestamps of first section call and first summary call
      const firstSectionIndex = apiCalls.findIndex(url => url.includes('/section/'));
      const firstSummaryIndex = apiCalls.findIndex(url => url.includes('/summarize'));

      // Sections should generally load before summaries
      expect(firstSectionIndex).toBeLessThan(firstSummaryIndex);
    }
  }

  /**
   * Test component refresh functionality
   */
  async testComponentRefresh(componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first();
    await expect(component).toBeVisible();

    // Get initial content
    const initialContent = await component.textContent();

    // Trigger refresh
    const menuTrigger = component.locator('[data-testid*="menu-trigger"]').first();
    if (await menuTrigger.isVisible()) {
      await menuTrigger.click();
      await this.page.click('text=Refresh');

      // Wait for loading to complete
      await this.waitForComponentLoading(componentSelector);

      // Verify content was refreshed (this depends on the mock implementation)
      const newContent = await component.textContent();
      // Content might be the same if mock returns same data, but loading should have occurred
    }
  }

  /**
   * Verify performance metrics for large content
   */
  async verifyPerformanceMetrics(maxLoadTime = 30000) {
    const startTime = Date.now();

    // Wait for all loading to complete
    await this.page.waitForLoadState('networkidle');

    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(maxLoadTime);

    return loadTime;
  }

  /**
   * Wait for ProseMirror editor to be visible with configurable timeout
   */
  async waitForEditor(timeout = 10000) {
    const editor = this.page.locator('.ProseMirror');
    await expect(editor).toBeVisible({ timeout });
    return editor;
  }

  /**
   * Get ProseMirror editor locator and wait for it to be visible
   */
  async getEditor(timeout = 10000) {
    return await this.waitForEditor(timeout);
  }

  /**
   * Type text in the ProseMirror editor
   */
  async typeInEditorAtCursor(text: string, timeout = 10000) {
    const editor = await this.waitForEditor(timeout);
    await editor.click();
    await this.page.keyboard.type(text);
    return editor;
  }

  /**
   * Clear and fill the ProseMirror editor with new content
   */
  async fillEditor(content: string, timeout = 10000) {
    const editor = await this.waitForEditor(timeout);
    await editor.click();
    await this.page.keyboard.press('ControlOrMeta+a');
    await this.page.keyboard.type(content);
    return editor;
  }

  /**
   * Get editor content as text
   */
  async getEditorText(timeout = 10000) {
    const editor = await this.waitForEditor(timeout);
    return await editor.textContent();
  }

  /**
   * Check if editor is in a specific state (editable/readonly)
   */
  async checkEditorState(editable: boolean, timeout = 10000) {
    const editor = await this.waitForEditor(timeout);
    const expectedValue = editable ? 'true' : 'false';
    await expect(editor).toHaveAttribute('contenteditable', expectedValue);
    return editor;
  }

  /**
   * Click at a specific position in the editor
   */
  async clickEditorAt(position?: { x: number; y: number }, timeout = 10000) {
    const editor = await this.waitForEditor(timeout);
    if (position) {
      await editor.click({ position });
    } else {
      await editor.click();
    }
    return editor;
  }

  /**
   * Change heading level using the heading selector
   */
  async changeHeading(level: 'paragraph' | '1' | '2' | '3' | '4' | '5' | '6', timeout = 10000) {
    // Click the heading selector trigger
    await this.page.click('[data-testid="heading-selector"]');

    // Wait for the dropdown to open and click the desired option
    const optionText = level === 'paragraph' ? 'Paragraph' : `Heading ${level}`;
    await this.page.click(`[role="option"]:has-text("${optionText}")`);

    // Wait a moment for the change to take effect
    await this.page.waitForTimeout(500);
  }

  /**
   * Get the current heading level from the selector
   */
  async getCurrentHeading(timeout = 10000) {
    const selector = this.page.locator('[data-testid="heading-selector"]');
    await selector.waitFor({ timeout });
    return await selector.textContent();
  }
}
