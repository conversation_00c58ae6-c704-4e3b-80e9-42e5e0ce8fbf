import { Page } from '@playwright/test';
import { getAuthCredentials } from '../test-config';

/**
 * Login helper function for Playwright tests
 */
export async function login(page: Page, email?: string, password?: string) {
  const credentials = getAuthCredentials();
  const loginEmail = email || credentials.email;
  const loginPassword = password || credentials.password;

  await page.goto('/login');
  await page.fill('#email', loginEmail);
  await page.fill('#password', loginPassword);
  await page.click('button[type="submit"]');
  await page.waitForURL(/\/customer/);
}
