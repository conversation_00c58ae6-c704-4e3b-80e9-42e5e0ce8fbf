import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report Section and Summary Headings Behavior', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should show headings configuration in report section dialog', async ({ page }) => {
    // Create a new document using test utils
    await testUtils.createDocumentFromTemplate('Blank Document')
    
    // Insert a report section using slash command
    const editor = await testUtils.getEditor()
    await editor.click()
    await page.keyboard.type('/report-section')
    await page.keyboard.press('Enter')
    
    // Wait for the report section to be inserted
    await page.waitForSelector('.report-section', { timeout: 10000 })
    
    // Open component configuration
    await testUtils.openComponentConfig('.report-section')
    
    // Check that the headings dropdown is present
    await expect(page.locator('label:has-text("Headings Behaviour")')).toBeVisible()
    await expect(page.locator('select[value="remove"], [role="combobox"]:has-text("Remove Headings")')).toBeVisible()
    
    // Check that the depth input is present
    await expect(page.locator('label:has-text("Depth Override")')).toBeVisible()
    await expect(page.locator('input[type="number"][min="1"][max="6"]')).toBeVisible()
  })

  test('should show headings configuration in report summary dialog', async ({ page }) => {
    // Create a new document using test utils
    await testUtils.createDocumentFromTemplate('Blank Document')
    
    // Insert a report summary using slash command
    const editor = await testUtils.getEditor()
    await editor.click()
    await page.keyboard.type('/report-summary')
    await page.keyboard.press('Enter')
    
    // Wait for the report summary to be inserted
    await page.waitForSelector('.report-summary', { timeout: 10000 })
    
    // Open component configuration
    await testUtils.openComponentConfig('.report-summary')
    
    // Check that the headings dropdown is present
    await expect(page.locator('label:has-text("Headings Behaviour")')).toBeVisible()
    await expect(page.locator('select[value="remove"], [role="combobox"]:has-text("Remove Headings")')).toBeVisible()
    
    // Check that the depth input is present
    await expect(page.locator('label:has-text("Depth Override")')).toBeVisible()
    await expect(page.locator('input[type="number"][min="1"][max="6"]')).toBeVisible()
  })

  test('should change headings behavior options', async ({ page }) => {
    // Create a new document using test utils
    await testUtils.createDocumentFromTemplate('Blank Document')
    await testUtils.waitForEditor()
    
    // Insert a report section
    const editor = await testUtils.getEditor()
    await editor.click()
    await page.keyboard.type('/report-section')
    await page.keyboard.press('Enter')
    
    // Wait for the report section to be inserted
    await page.waitForSelector('.report-section', { timeout: 10000 })
    
    // Open component configuration
    await testUtils.openComponentConfig('.report-section')
    
    // Change headings behavior to "Keep Headings"
    await page.locator('[role="combobox"]').first().click()
    await page.locator('[role="option"]:has-text("Keep Headings")').click()
    
    // Change depth to 3
    await page.locator('input[type="number"]').fill('3')
    
    // Save the configuration
    await testUtils.confirmComponentConfig()
  })

  test('should use ESG template with proper heading depths', async ({ page }) => {
    // Create a document from EKO Report template using TestUtils
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Wait for editor to load
    await testUtils.waitForEditor()
    
    // Check that report sections are created
    // This is a basic check - in a real test we'd verify the actual heading transformation
    const reportSections = await testUtils.countComponents('report-section')
    const reportSummaries = await testUtils.countComponents('report-summary')
    
    expect(reportSections).toBeGreaterThan(0)
    expect(reportSummaries).toBeGreaterThan(0)
  })
})

test.describe('Heading Transformation Utilities', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should transform headings correctly', async ({ page }) => {
    // This test would ideally test the utility functions directly
    // For now, we'll test through the UI by checking that content is processed correctly

    // Create a new document using test utils
    await testUtils.createDocumentFromTemplate('Blank Document')
    await testUtils.waitForEditor()
    
    // Add some markdown content with headings
    await testUtils.typeInEditor('# Main Title\n\nSome content\n\n## Section 1\n\nMore content')
    
    // The actual heading transformation would be tested when report sections load content
    // This is a placeholder for more comprehensive testing
    await testUtils.checkEditorContent('Main Title')
    await testUtils.checkEditorContent('Section 1')
  })
})
