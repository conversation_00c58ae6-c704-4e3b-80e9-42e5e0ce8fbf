# Collaborative Editing with TipTap and Supabase

This document explains how to set up and use the collaborative editing features in the customer application using TipTap Cloud for real-time collaboration and Supabase for document storage and user presence.

## Architecture Overview

The collaborative editing system combines:

- **TipTap Cloud**: Handles real-time collaborative editing, conflict resolution, and live cursors
- **Supabase**: Stores documents, manages user authentication, and handles presence indicators
- **Y.js**: Provides the underlying CRDT (Conflict-free Replicated Data Type) for document synchronization

## Setup Instructions

### 1. TipTap Cloud Setup

1. Sign up for a TipTap Cloud account at [https://cloud.tiptap.dev](https://cloud.tiptap.dev)
2. Create a new environment in your dashboard
3. Copy your App ID and generate a JWT token
4. Add these to your environment variables:

```bash
NEXT_PUBLIC_TIPTAP_APP_ID=your_app_id_here
NEXT_PUBLIC_TIPTAP_TOKEN=your_jwt_token_here
```

### 2. Supabase Database Setup

Run the migration to create the necessary tables:

```sql
-- This creates the collaborative_documents table and related permissions
-- See: apps/customer/supabase/migrations/20250127_collaborative_documents.sql
```

The migration creates:
- `collaborative_documents` table for storing document content
- `document_permissions` table for fine-grained access control
- Row Level Security policies for secure access
- A view `user_document_access` for easier querying

### 3. Install Dependencies

Make sure you have the required packages installed:

```bash
npm install @tiptap/extension-collaboration @hocuspocus/provider@^2.15.2 yjs y-prosemirror y-protocols tiptap-markdown
```

## Components

### EkoEditSupabase

The main collaborative editor component that combines TipTap's collaboration features with Supabase storage.

```tsx
import { EkoEditSupabase } from '@/components/editor/EkoEditSupabase'

<EkoEditSupabase
  documentId="unique-document-id"
  markdown="Initial content"
  onUpdate={(markdown) => console.log('Content updated:', markdown)}
  editable={true}
  showToolbar={true}
  autoSave={true}
  citations={[]}
  admin={false}
/>
```

#### Props

- `documentId`: Unique identifier for the document
- `markdown`: Initial markdown content
- `onUpdate`: Callback when content changes
- `editable`: Whether the editor is editable
- `showToolbar`: Whether to show the toolbar
- `autoSave`: Whether to auto-save to Supabase
- `citations`: Array of citations for the citation extension
- `admin`: Whether the user has admin privileges

### useCollaborativeDocument Hook

A React hook for managing collaborative documents in Supabase.

```tsx
import { useCollaborativeDocument } from '@/hooks/useCollaborativeDocument'

const {
  document,
  isLoading,
  error,
  createDocument,
  updateDocument,
  deleteDocument,
  refreshDocument,
} = useCollaborativeDocument({
  documentId: 'my-document',
  autoCreate: true,
  defaultTitle: 'New Document',
  defaultContent: '<p>Hello world!</p>',
})
```

## Features

### Real-time Collaboration

- **Live Editing**: Multiple users can edit the same document simultaneously
- **Live Cursors**: See where other users are typing in real-time
- **Conflict Resolution**: Automatic conflict-free merging of changes
- **Offline Support**: Continue editing offline and sync when reconnected

### Document Management

- **Auto-save**: Automatic saving to Supabase with debouncing
- **Version Control**: Built-in version history through TipTap
- **Permissions**: Row-level security for document access
- **Metadata**: Store additional document metadata

### User Presence

- **Active Users**: See who's currently editing the document
- **User Avatars**: Display user avatars and names
- **Color Coding**: Each user gets a unique color for their cursor

## Usage Examples

### Basic Collaborative Editor

```tsx
'use client'

import { EkoEditSupabase } from '@/components/editor/EkoEditSupabase'

export default function MyEditor() {
  return (
    <EkoEditSupabase
      documentId="my-document-123"
      editable={true}
      autoSave={true}
      showToolbar={true}
    />
  )
}
```

### Document Management

```tsx
'use client'

import { useCollaborativeDocument } from '@/hooks/useCollaborativeDocument'
import { EkoEditSupabase } from '@/components/editor/EkoEditSupabase'

export default function DocumentEditor() {
  const { document, createDocument, updateDocument } = useCollaborativeDocument({
    documentId: 'my-doc',
    autoCreate: true,
  })

  const handleCreateNew = async () => {
    await createDocument('New Document', '<p>Start writing...</p>')
  }

  const handleUpdateTitle = async (newTitle: string) => {
    await updateDocument({ title: newTitle })
  }

  return (
    <div>
      <button onClick={handleCreateNew}>Create New Document</button>
      
      {document && (
        <EkoEditSupabase
          documentId={document.id}
          markdown={document.content}
          autoSave={true}
        />
      )}
    </div>
  )
}
```

## Testing

Visit `/test-eko-edit-supabase` to test the collaborative editing features:

1. Open the page in multiple browser tabs
2. Start typing in one tab and see changes appear in real-time in other tabs
3. Test document creation, saving, and deletion
4. Observe user presence indicators

## Security Considerations

### Row Level Security

The Supabase tables use Row Level Security (RLS) to ensure users can only access documents they have permission to view or edit.

### JWT Tokens

For production, generate proper JWT tokens for TipTap Cloud authentication instead of using the test token.

### Environment Variables

Never expose your TipTap tokens or Supabase service keys in client-side code. Use environment variables and proper authentication.

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check your TipTap Cloud credentials and network connection
2. **Document Not Loading**: Verify the document exists in Supabase and user has permissions
3. **Auto-save Not Working**: Check Supabase connection and user authentication
4. **Collaboration Not Working**: Ensure TipTap Cloud is properly configured and multiple users are authenticated

### Debug Mode

Enable debug logging by adding to your environment:

```bash
NEXT_PUBLIC_DEBUG_COLLABORATION=true
```

## Performance Considerations

- **Debounced Saving**: Auto-save is debounced to prevent excessive database writes
- **Efficient Updates**: Only changed content is synchronized
- **Connection Management**: Automatic reconnection on network issues
- **Memory Management**: Proper cleanup of Y.js documents and providers

## Future Enhancements

- **Comments System**: Add inline comments using TipTap's Comments extension
- **Version History**: Implement document version history and comparison
- **Advanced Permissions**: More granular permission system
- **Offline Sync**: Enhanced offline editing capabilities
- **Document Templates**: Pre-defined document templates
