import { createClient } from '@/app/supabase/client'
import { ModelSectionTypeV2 } from '@/types'

/**
 * Loads model sections from the xfer_model_sections_v2 table
 *
 * @param modelName Optional model name to filter by
 * @returns Array of model sections
 */
export async function loadModelSections(modelName?: string): Promise<ModelSectionTypeV2[]> {
  const supabase = createClient();

  let query = supabase
    .from('xfer_model_sections_v2')
    .select('*')
    .eq('status', 'active');

  if (modelName) {
    query = query.eq('model', modelName);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error loading model sections:', error);
    return [];
  }

  return data as ModelSectionTypeV2[];
}

/**
 * Gets a model section by model name and section ID
 *
 * @param modelName Model name
 * @param sectionId Section ID
 * @returns Model section or null if not found
 */
export async function getModelSection(modelName: string, sectionId: string): Promise<ModelSectionTypeV2 | null> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('xfer_model_sections_v2')
    .select('*')
    .eq('model', modelName)
    .eq('section', sectionId)
    .eq('status', 'active')
    .single();

  if (error || !data) {
    console.error('Error loading model section:', error);
    return null;
  }

  return data as ModelSectionTypeV2;
}

/**
 * Gets all sections for a specific model
 *
 * @param modelName Model name
 * @returns Array of model sections
 */
export async function getModelSections(modelName: string): Promise<ModelSectionTypeV2[]> {
  return loadModelSections(modelName);
}

/**
 * Gets all available models
 *
 * @returns Array of unique model names
 */
export async function getAvailableModels(): Promise<string[]> {
  const sections = await loadModelSections();
  const models = new Set<string>();

  sections.forEach(section => {
    if (section.model) {
      models.add(section.model);
    }
  });

  return Array.from(models);
}
