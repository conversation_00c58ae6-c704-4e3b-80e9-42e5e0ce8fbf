import { ScoreTypeV2 } from "@/types/score";

/**
 * Gets the score value from a ScoreTypeV2
 * 
 * @param scoreV2 The score from xfer_score_v2 table
 * @returns The score value (0-100)
 */
export function getScoreValue(scoreV2: ScoreTypeV2): number {
  return scoreV2.score;
}

/**
 * Gets the rating text from a ScoreTypeV2
 * 
 * @param scoreV2 The score from xfer_score_v2 table
 * @returns The rating text (e.g., 'Great', 'Good', 'Poor')
 */
export function getRatingText(scoreV2: ScoreTypeV2): string {
  return scoreV2.model.rating_text;
}

/**
 * Gets the severity text from a ScoreTypeV2
 * 
 * @param scoreV2 The score from xfer_score_v2 table
 * @returns The severity text (e.g., 'Very Serious', 'Minor')
 */
export function getSeverityText(scoreV2: ScoreTypeV2): string {
  return scoreV2.model.minor_major_text;
}

/**
 * Gets the red flags count from a ScoreTypeV2
 * 
 * @param scoreV2 The score from xfer_score_v2 table
 * @returns The number of red flags
 */
export function getRedFlagsCount(scoreV2: ScoreTypeV2): number {
  return scoreV2.model.red_flags_count;
}

/**
 * Gets the green flags count from a ScoreTypeV2
 * 
 * @param scoreV2 The score from xfer_score_v2 table
 * @returns The number of green flags
 */
export function getGreenFlagsCount(scoreV2: ScoreTypeV2): number {
  return scoreV2.model.green_flags_count;
}

/**
 * Gets the color class based on the score
 * 
 * @param scoreV2 The score from xfer_score_v2 table
 * @returns CSS color class
 */
export function getScoreColorClass(scoreV2: ScoreTypeV2): string {
  const score = scoreV2.score;
  
  if (score >= 85) return 'text-green-600';
  if (score >= 75) return 'text-green-500';
  if (score >= 60) return 'text-yellow-500';
  if (score >= 40) return 'text-orange-500';
  return 'text-red-500';
}
