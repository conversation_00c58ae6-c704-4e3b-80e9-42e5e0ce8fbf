import { CherryModel, CherryTypeV2 } from '@/types/cherry'
import { StatementAndMetadata } from '@/types/types'

/**
 * Transforms raw database data into the expected CherryTypeV2 format
 * This handles various data structures that might come from the database
 */
export function transformCherryData(rawData: any[]): CherryTypeV2[] {
  if (!rawData || !Array.isArray(rawData)) {
    console.error('Invalid cherry data:', rawData);
    return [];
  }

  return rawData.map(item => {
    try {
      // Check if the item has a model field
      if (!item.model) {
        console.warn('Item missing model field:', item);
        // Try to construct a model from the item itself
        return createCherryTypeFromRawData(item);
      }

      // Check if model is a string (JSON)
      if (typeof item.model === 'string') {
        try {
          item.model = JSON.parse(item.model);
        } catch (e) {
          console.error('Failed to parse model JSON:', e);
        }
      }

      // Ensure model is an object
      if (typeof item.model !== 'object') {
        console.warn('Model is not an object:', item.model);
        return createCherryTypeFromRawData(item);
      }

      // Ensure model has required fields
      const model = item.model as CherryModel;
      if (!model.model) {
        // Try to determine model type from other fields
        model.model = determineModelType(item) as 'cherry_picking' | 'flooding';
      }

      return {
        id: item.id || 0,
        run_id: item.run_id || 0,
        entity_xid: item.entity_xid || '',
        label: item.label || model.label || 'Unknown',
        model: ensureModelFields(model),
        created_at: item.created_at || model.created_at || null
      };
    } catch (error) {
      console.error('Error transforming cherry data item:', error, item);
      return createDefaultCherryType(item);
    }
  });
}

/**
 * Creates a default CherryTypeV2 object from raw data
 */
function createCherryTypeFromRawData(item: any): CherryTypeV2 {
  // Try to extract as much information as possible from the raw item
  const modelType = determineModelType(item);

  const model: CherryModel = {
    entity_xid: item.entity_xid || '',
    label: item.label || 'Unknown',
    negative_statements: extractStatements(item.negative_statements || item.red_flags || []),
    positive_statements: extractStatements(item.positive_statements || item.green_flags || []),
    model: modelType as 'cherry_picking' | 'flooding',
    score: item.score || 0,
    explanation: item.explanation || 'No explanation available',
    analysis: item.analysis || 'No analysis available',
    reason: item.reason || 'No reason available',
    citations: item.citations || [],
    severity: item.severity || 0,
    confidence: item.confidence || 0,
    authenticity: item.authenticity || 0
  };

  return {
    id: item.id || 0,
    run_id: item.run_id || 0,
    entity_xid: item.entity_xid || '',
    label: item.label || 'Unknown',
    model: model,
    created_at: item.created_at || null
  };
}

/**
 * Creates a minimal valid CherryTypeV2 object
 */
function createDefaultCherryType(item: any): CherryTypeV2 {
  return {
    id: item?.id || 0,
    run_id: item?.run_id || 0,
    entity_xid: item?.entity_xid || '',
    label: item?.label || 'Unknown',
    model: {
      entity_xid: item?.entity_xid || '',
      label: item?.label || 'Unknown',
      negative_statements: [],
      positive_statements: [],
      model: 'cherry_picking',
      score: 0,
      explanation: 'No data available',
      analysis: 'No data available',
      reason: 'No data available',
      citations: [],
      severity: 0,
      confidence: 0,
      authenticity: 0
    },
    created_at: item?.created_at || null
  };
}

/**
 * Ensures all required fields exist on the model
 */
function ensureModelFields(model: any): CherryModel {
  return {
    entity_xid: model.entity_xid || '',
    label: model.label || 'Unknown',
    negative_statements: extractStatements(model.negative_statements || model.red_flags || []),
    positive_statements: extractStatements(model.positive_statements || model.green_flags || []),
    model: model.model || 'cherry_picking',
    score: model.score || 0,
    explanation: model.explanation || 'No explanation available',
    analysis: model.analysis || 'No analysis available',
    reason: model.reason || 'No reason available',
    citations: model.citations || [],
    severity: model.severity || 0,
    confidence: model.confidence || 0,
    authenticity: model.authenticity || 0
  };
}

/**
 * Extracts statements from various formats
 */
function extractStatements(statements: any[]): StatementAndMetadata[] {
  if (!Array.isArray(statements)) {
    return [];
  }

  return statements.map(statement => {
    if (typeof statement === 'string') {
      return {
        statement_id: 0,
        statement_text: statement
      };
    }

    if (typeof statement === 'object') {
      return {
        statement_id: statement.statement_id || statement.id || 0,
        statement_text: statement.statement_text || statement.text || JSON.stringify(statement),
        metadata: statement.metadata || {}
      };
    }

    return {
      statement_id: 0,
      statement_text: String(statement)
    };
  });
}

/**
 * Tries to determine the model type from the data
 */
function determineModelType(item: any): string {
  // Check explicit model field
  if (item.model && typeof item.model === 'string') {
    if (item.model.includes('cherry') || item.model.includes('pick')) {
      return 'cherry_picking';
    }
    if (item.model.includes('flood')) {
      return 'flooding';
    }
  }

  // Check label
  if (item.label && typeof item.label === 'string') {
    if (item.label.toLowerCase().includes('cherry') || item.label.toLowerCase().includes('pick')) {
      return 'cherry_picking';
    }
    if (item.label.toLowerCase().includes('flood')) {
      return 'flooding';
    }
  }

  // Default to cherry_picking
  return 'cherry_picking';
}
