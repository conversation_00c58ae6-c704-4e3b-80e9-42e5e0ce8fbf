/**
 * Extracts a clean domain name from a URL
 * 
 * @param url The URL to extract the domain from
 * @returns The clean domain name (e.g., "example.com" from "https://www.example.com/page")
 */
export function extractDomain(url: string): string {
  if (!url) return '';
  
  try {
    // Use URL constructor to parse the URL
    const urlObj = new URL(url);
    let domain = urlObj.hostname;
    
    // Remove 'www.' prefix if present
    if (domain.startsWith('www.')) {
      domain = domain.substring(4);
    }
    
    return domain;
  } catch (e) {
    // If URL parsing fails, try a simple regex approach
    const match = url.match(/^(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
    return match ? match[1] : url;
  }
}

/**
 * Gets a friendly display name for a domain
 * 
 * @param domain The domain name
 * @returns A friendly display name (e.g., "example" from "example.com")
 */
export function getDomainDisplayName(domain: string): string {
  if (!domain) return '';
  
  // Remove TLD and subdomains
  const parts = domain.split('.');
  
  // If it's a common TLD with a subdomain pattern like co.uk, take the domain name before it
  if (parts.length > 2) {
    const lastTwo = `${parts[parts.length - 2]}.${parts[parts.length - 1]}`;
    if (['co.uk', 'com.au', 'co.nz', 'co.jp', 'co.za', 'org.uk', 'ac.uk'].includes(lastTwo)) {
      return parts[parts.length - 3];
    }
  }
  
  // Otherwise just take the part before the TLD
  return parts.length > 1 ? parts.slice(0, -1).join('.') : parts[0];
}
