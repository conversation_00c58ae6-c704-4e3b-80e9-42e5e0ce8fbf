/**
 * Truncates a string to a maximum number of tokens (approximately).
 * This is a simple implementation that uses a rough approximation of tokens.
 * 
 * @param text The text to truncate
 * @param maxTokens The maximum number of tokens to allow
 * @returns The truncated text, or null if the input was null
 */
export function truncate(text: string | null, maxTokens: number): string | null {
  if (!text) return null;
  
  // Rough approximation: 1 token ≈ 4 characters for English text
  const maxChars = maxTokens * 4;
  
  if (text.length <= maxChars) {
    return text;
  }
  
  // Truncate to max chars and try to end at a sentence or paragraph
  let truncated = text.substring(0, maxChars);
  
  // Try to end at a paragraph
  const lastParagraph = truncated.lastIndexOf('\n\n');
  if (lastParagraph > maxChars * 0.8) {
    return truncated.substring(0, lastParagraph + 2);
  }
  
  // Try to end at a sentence
  const lastSentence = Math.max(
    truncated.lastIndexOf('. '),
    truncated.lastIndexOf('! '),
    truncated.lastIndexOf('? ')
  );
  
  if (lastSentence > maxChars * 0.8) {
    return truncated.substring(0, lastSentence + 2);
  }
  
  // If we can't find a good breakpoint, just return the truncated text
  return truncated;
}
