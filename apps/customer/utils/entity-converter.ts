import { EntityTypeV2 } from "@/types";

/**
 * Converts an EntityTypeV2 (from xfer_entities_v2) to the old EntityType format (from xfer_entities)
 * for backward compatibility with existing code.
 *
 * @param entityV2 The entity from xfer_entities_v2 table
 * @returns An entity in the old format for compatibility
 */
export function convertEntityV2ToEntityV1(entityV2: any): any {
  // Handle the case where the input might not match the EntityTypeV2 interface exactly
  const rawEntityV2 = entityV2 as any;
  const model = rawEntityV2.model;

  if (!model) {
    console.error('Entity model is missing:', rawEntityV2);
    // Return a minimal valid EntityType to prevent errors
    return {
      entity_xid: rawEntityV2.entity_xid || '',
      name: rawEntityV2.name || 'Unknown',
      type: rawEntityV2.entity_type || rawEntityV2.type || 'unknown',
      description: null,
      lei: null,
      synonyms: [],
      ticker: null,
      url: null,
      searchable_text: null
    };
  }

  return {
    entity_xid: rawEntityV2.entity_xid,
    name: rawEntityV2.name,
    type: rawEntityV2.entity_type || rawEntityV2.type || 'unknown',
    description: model.description || null,
    lei: model.leis && model.leis.length > 0 ? model.leis[0] : null,
    synonyms: model.common_names || [],
    ticker: null, // Not present in the new model
    url: null, // Not present in the new model
    searchable_text: null // Not present in the new model
  };
}

/**
 * Gets the base entities from an EntityTypeV2
 *
 * @param entityV2 The entity from xfer_entities_v2 table
 * @returns Array of base entities
 */
export function getBaseEntities(entityV2: any) {
  const rawEntityV2 = entityV2 as any;
  const model = rawEntityV2.model;

  if (!model) {
    console.error('Entity model is missing in getBaseEntities:', rawEntityV2);
    return [];
  }

  return model.base_entities || [];
}

/**
 * Gets all common names from an EntityTypeV2
 *
 * @param entityV2 The entity from xfer_entities_v2 table
 * @returns Array of common names
 */
export function getAllCommonNames(entityV2: any) {
  const rawEntityV2 = entityV2 as any;
  const model = rawEntityV2.model;

  if (!model) {
    console.error('Entity model is missing in getAllCommonNames:', rawEntityV2);
    return [];
  }

  const commonNames = model.common_names || [];
  const aka = model.aka || [];
  const legalNames = model.legal_names || [];

  // Combine all name types and remove duplicates
  const allNames = [...commonNames, ...aka, ...legalNames];
  return allNames.filter((name, index) => allNames.indexOf(name) === index);
}
