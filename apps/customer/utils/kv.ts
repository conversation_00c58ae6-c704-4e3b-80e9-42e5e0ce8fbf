'use server'

//Use vercel kv as a cache

import { kv } from '@vercel/kv'

export async function cacheText(hashId: string, section: string) {
  await kv.hset(hashId, { section, expires: Date.now() + 60 * 60 * 1000 })
}

export async function getCacheText(hashId: string): Promise<string | null> {
  const cached: Record<string, any> | null = await kv.hgetall(hashId)
  if (cached) {
    if (!cached.expires || cached.expires < Date.now()) {
      await kv.hdel(hashId, 'section', 'expires')
      return null
    }
    return cached.section
  }
  return null
}
