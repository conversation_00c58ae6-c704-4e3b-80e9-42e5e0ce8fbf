import { ScoreTypeV2 } from "@/types/score";

/**
 * Gets the score value from a ScoreTypeV2
 * 
 * @param scoreV2 The score from xfer_score_v2 table
 * @returns The score value
 */
export function getScoreValue(scoreV2: any): number {
  // Handle the case where the input might not match the ScoreTypeV2 interface exactly
  const rawScoreV2 = scoreV2 as any;
  
  if (!rawScoreV2) {
    return 0;
  }
  
  // If the score is directly available, use it
  if (typeof rawScoreV2.score === 'number') {
    return rawScoreV2.score;
  }
  
  // Try to get the score from the model
  const model = rawScoreV2.model;
  if (!model) {
    console.error('Score model is missing:', rawScoreV2);
    return 0;
  }
  
  return model.score || 0;
}
