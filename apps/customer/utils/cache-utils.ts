import { kv } from '@vercel/kv'
import crypto from 'crypto'
import { createDataStreamResponse } from 'ai'
// Import the streamText function instead of convertToDataStream

// Default setting for caching
export const CACHING_ENABLED = true

/**
 * Creates a cache key from the provided data
 *
 * @param prefix The prefix for the cache key (usually the API endpoint name)
 * @param data The data to hash for the cache key
 * @returns A formatted cache key string
 */
export function createCacheKey(prefix: string, data: Record<string, any>): string {
  return `v2-${prefix}-${crypto.createHash('md5').update(JSON.stringify(data)).digest('hex')}`
}

/**
 * Checks if a cached response exists and returns it if found
 *
 * @param cacheKey The cache key to check
 * @param entityName The entity name for logging purposes
 * @param endpoint The API endpoint for logging purposes
 * @returns The cached response if found, null otherwise
 */
export async function checkCache(
  cacheKey: string,
  entityName: string,
  endpoint: string,
): Promise<{ response: string } | null> {
  if (!CACHING_ENABLED) {
    return null
  }

  const cachedData = await kv.hgetall(cacheKey)
  if (cachedData && cachedData.response) {
    console.log(`[API] ${endpoint} cache hit for ${entityName}`)
    return cachedData as { response: string }
  }

  return null
}

/**
 * Stores a response in the cache
 *
 * @param cacheKey The cache key to store under
 * @param response The response to store
 * @param entityName The entity name for logging purposes
 * @param endpoint The API endpoint for logging purposes
 */
export async function storeInCache(
  cacheKey: string,
  response: string | object,
  entityName: string,
  endpoint: string,
): Promise<void> {
  if (!CACHING_ENABLED) {
    return
  }

  const responseToStore = typeof response === 'string'
    ? response
    : JSON.stringify(response)

  await kv.hset(cacheKey, { response: responseToStore })
  console.log(`[API] ${endpoint} cached for ${entityName}, length: ${responseToStore.length} chars`)
}

/**
 * Creates a simple Response object from cached data
 *
 * @param cachedData The cached data to create a response from
 * @returns A Response object with the cached data
 */
export function createResponseFromCache(cachedData: { response: string }): Response {
  return new Response(cachedData.response, {
    headers: {
      'Content-Type': 'text/markdown',
      'Cache-Control': 'no-cache',
    },
  })
}

/**
 * Handles the common caching pattern for API routes
 *
 * @param options Options for the caching handler
 * @returns A Response object if cache hit, null otherwise
 */
export async function handleCaching(
  options: {
    cacheKey: string;
    entityName: string;
    endpoint: string;
    cacheEnabled?: boolean;
  },
): Promise<Response | null> {
  const { cacheKey, entityName, endpoint, cacheEnabled = CACHING_ENABLED } = options

  if (!cacheEnabled) {
    return null
  }

  const cachedData = await checkCache(cacheKey, entityName, endpoint)
  if (cachedData) {
    return createResponseFromCache(cachedData)
  }

  return null
}

/**
 * Handles the common caching pattern for streaming API routes
 *
 * @param options Options for the streaming caching handler
 * @returns A streaming Response object if cache hit, null otherwise
 */
export async function handleStreamingCaching(
  options: {
    cacheKey: string;
    entityName: string;
    endpoint: string;
    cacheEnabled?: boolean;
  },
): Promise<Response | null> {
  const { cacheKey, entityName, endpoint, cacheEnabled = CACHING_ENABLED } = options

  if (!cacheEnabled) {
    return null
  }

  const cachedData = await checkCache(cacheKey, entityName, endpoint)

  if (cachedData) {
    console.log(`[API] ${endpoint} cache hit for ${entityName}`)

// Create a data stream response for the cached content
    return createDataStreamResponse({
      async execute(dataStream) {
        // Write the text as a properly formatted data stream part
        const textPart = {
          type: 'text',
          value: cachedData.response as string,
        }
        dataStream.writeData(textPart)
      },
    })
  }

  return null
}
