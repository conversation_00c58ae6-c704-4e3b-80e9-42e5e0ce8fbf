# Utility Functions

## Cache Utils

The `cache-utils.ts` file provides common caching functionality for API routes, especially those that use AI-generated content.

## Cache Middleware

The `cache-middleware.ts` file implements the `LanguageModelV1Middleware` interface from the AI SDK to provide caching for both streaming and non-streaming responses. This is the recommended approach for caching AI responses according to the [AI SDK documentation](https://ai-sdk.dev/docs/advanced/caching#using-language-model-middleware-recommended).

### Key Functions

#### `checkCache(cacheKey, entityName, endpoint)`

Checks if a cached response exists and returns it if found:

```typescript
const cachedData = await checkCache(cacheKey, entityName, '/api/example');
if (cachedData) {
  // Use the cached data
}
```

#### `storeInCache(cacheKey, response, entityName, endpoint)`

Stores a response in the cache:

```typescript
await storeInCache(cacheKey, text, entityName, '/api/example');
```

#### `createResponseFromCache(cachedData)`

Creates a simple Response object from cached data:

```typescript
return createResponseFromCache(cachedData);
```

#### `handleCaching(options)`

Handles the common caching pattern for API routes with simple responses:

```typescript
const cachedResponse = await handleCaching({
  cacheKey,
  entityName,
  endpoint: '/api/example'
});

if (cachedResponse) {
  return cachedResponse;
}
```

#### `handleStreamingCaching(options)`

Handles the common caching pattern for streaming API routes:

```typescript
const cachedResponse = await handleStreamingCaching({
  cacheKey,
  entityName,
  endpoint: '/api/example'
});

if (cachedResponse) {
  return cachedResponse;
}
```

### Example Usage

`

#### Recommended Caching Pattern (cache-middleware.ts)

```typescript
import { streamText, wrapLanguageModel } from 'ai'
import { google } from '@ai-sdk/google'
import { cacheMiddleware } from '@/utils/cache-middleware'

// Create a cached model instance
const cachedModel = wrapLanguageModel({
  model: google('gemini-2.0-flash'),
  middleware: cacheMiddleware
});


// Use the cached model in your API route
const result = await streamText({
  model: cachedModel,
  prompt: "Your prompt here",
  temperature: 0.2,
  maxTokens: 3000,
  // No need to manually store in cache - middleware handles it
});

// Return the streaming response
return result.toDataStreamResponse();
```
