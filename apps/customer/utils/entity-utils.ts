// This file is deprecated, use entity-converter.ts instead
import { convertEntityV2ToEntityV1 as convert, getBaseEntities as getBase, getAllCommonNames as getNames } from './entity-converter';

/**
 * Converts an EntityTypeV2 (from xfer_entities_v2) to the old EntityType format (from xfer_entities)
 * for backward compatibility with existing code.
 *
 * @param entityV2 The entity from xfer_entities_v2 table
 * @returns An entity in the old format for compatibility
 */
export function convertEntityV2ToEntityV1(entityV2: any): any {
  return convert(entityV2);
}

/**
 * Gets the base entities from an EntityTypeV2
 *
 * @param entityV2 The entity from xfer_entities_v2 table
 * @returns Array of base entities
 */
export function getBaseEntities(entityV2: any) {
  return getBase(entityV2);
}

/**
 * Gets all common names from an EntityTypeV2
 *
 * @param entityV2 The entity from xfer_entities_v2 table
 * @returns Array of common names
 */
export function getAllCommonNames(entityV2: any) {
  return getNames(entityV2);
}
