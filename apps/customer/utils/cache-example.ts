import { streamText } from 'ai'
import { google } from '@ai-sdk/google'
import { truncate } from '@/utils/text-utils'
import {
  createCacheKey,
  checkCache,
  storeInCache,
  createResponseFromCache,
  handleStreamingCaching,
  CACHING_ENABLED
} from '@/utils/cache-utils'

export const AI_MODEL_NAME = 'gemini-2.0-flash';

/**
 * Example of how to use the cache utils in an API route
 */
export async function exampleApiRoute(
  entityName: string,
  modelName: string,
  prompt: string
): Promise<Response> {
  try {
    // Create a cache key
    const cacheKey = createCacheKey('example', {
      entityName,
      modelName,
      // Add any other data that should invalidate the cache when changed
    });

    // Check cache first - for non-streaming response
    // const cachedData = await checkCache(cacheKey, entityName, '/api/example');
    // if (cachedData) {
    //   return createResponseFromCache(cachedData);
    // }

    // For streaming response, use handleStreamingCaching
    const cachedResponse = await handleStreamingCaching({
      cacheKey,
      entityName,
      endpoint: '/api/example'
    });

    if (cachedResponse) {
      return cachedResponse;
    }

    // Truncate if needed
    const truncatedPrompt = truncate(prompt, 100000)!;

    // Process the text in a streaming fashion
    const result = await streamText({
      model: google(AI_MODEL_NAME),
      prompt: `You are a professional ESG report writer for a leading auditing firm specializing in environmental, social, and governance matters. You are objective and factual but diplomatic when criticizing companies. You are not conversational.\n\n${truncatedPrompt}`,
      temperature: 0.2,
      maxTokens: 3000,
      onFinish: async ({ text }) => {
        console.log(`[API] /api/example completed for ${entityName}, length: ${text.length} chars`);

        // Store in cache
        if (CACHING_ENABLED) {
          await storeInCache(cacheKey, text, entityName, '/api/example');
        }
      },
      onError: async ({ error }) => {
        console.error(`[API] /api/example error:`, error);
      }
    });

    console.log(`[API] /api/example streaming response for ${entityName}`);

    // Return the streaming response
    return result.toDataStreamResponse();
  } catch (error) {
    console.error('[API] /api/example error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to process request', message: error instanceof Error ? error.message : String(error) }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
