import { kv } from '@vercel/kv'
import crypto from 'crypto'
import {
  type LanguageModelV1,
  type LanguageModelV1Middleware,
  type LanguageModelV1StreamPart,
  simulateReadableStream,
} from 'ai'

// Default setting for caching
export const CACHING_ENABLED = true
/**
 * Caching middleware for AI SDK language models
 * This middleware intercepts calls to language models and caches the responses
 */
export const cacheMiddleware: LanguageModelV1Middleware = {
  wrapGenerate: async ({ doGenerate, params }) => {
    if (!CACHING_ENABLED) {
      return await doGenerate()
    }

    const cacheKey = `v11-ai-middleware-cache-generate-${crypto.createHash('md5').update(JSON.stringify(params)).digest('hex')}`

    // Check if the result is in the cache
    const cached = (await kv.get(cacheKey)) as Awaited<
      ReturnType<LanguageModelV1['doGenerate']>
    > | null

    // If cached, return the cached result
    if (cached !== null) {
      console.log(`[API] Cache hit for generate (key: ${cacheKey.substring(0, 20)}...)`)
      return {
        ...cached,
        response: {
          ...cached.response,
          timestamp: cached?.response?.timestamp
            ? new Date(cached?.response?.timestamp)
            : undefined,
        },
      }
    }

    // If not cached, proceed with generation
    const result = await doGenerate()

    // Store the result in the cache
    await kv.set(cacheKey, result)
    console.log(`[API] Cached generate result (key: ${cacheKey.substring(0, 20)}...)`)

    return result
  },
  
  wrapStream: async ({ doStream, params }) => {
    if (!CACHING_ENABLED) {
      return await doStream()
    }
    const cacheKey = `v11-ai-middleware-cache-stream-${crypto.createHash('md5').update(JSON.stringify(params)).digest('hex')}`


    // Check if the result is in the cache
    const cached = await kv.get(cacheKey)

    // If cached, return a simulated ReadableStream that yields the cached result
    if (cached !== null) {
      console.log(`[API] Cache hit for stream (key: ${cacheKey.substring(0, 20)}...)`)
      
      // Format the timestamps in the cached response
      const formattedChunks = (cached as LanguageModelV1StreamPart[]).map(p => {
        if (p.type === 'response-metadata' && p.timestamp) {
          return { ...p, timestamp: new Date(p.timestamp) }
        } else return p
      })
      
      return {
        stream: simulateReadableStream({
          initialDelayInMs: 0,
          chunkDelayInMs: 10,
          chunks: formattedChunks,
        }),
        rawCall: { rawPrompt: null, rawSettings: {} },
      }
    }

    // If not cached, proceed with streaming
    const { stream, ...rest } = await doStream()

    // Collect all stream parts to store in the cache
    const fullResponse: LanguageModelV1StreamPart[] = []

    // Create a transform stream to intercept and collect all stream parts
    const transformStream = new TransformStream<
      LanguageModelV1StreamPart,
      LanguageModelV1StreamPart
    >({
      transform(chunk, controller) {
        fullResponse.push(chunk)
        controller.enqueue(chunk)
      },
      flush() {
        // Store the full response in the cache after streaming is complete
        kv.set(cacheKey, fullResponse)
        console.log(`[API] Cached stream result (key: ${cacheKey.substring(0, 20)}...)`)
      },
    })

    return {
      stream: stream.pipeThrough(transformStream),
      ...rest,
    }
  },
}
