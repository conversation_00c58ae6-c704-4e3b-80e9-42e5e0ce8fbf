import { FlagTypeV2, ModelSectionType } from '@/types'

// Helper function to organize flags by model section
export function organizeFlagsBySection(
  flags: FlagTypeV2[],
  modelSections: ModelSectionType[],
  modelName: string
): Record<string, FlagTypeV2[]> {
  const flagsBySection: Record<string, FlagTypeV2[]> = {};

  flags.forEach(flag => {
    const sectionMappings = flag.model?.model_sections || {};

    Object.entries(sectionMappings).forEach(([model, sectionId]) => {
      if (model === modelName) {
        if (!flagsBySection[sectionId]) {
          flagsBySection[sectionId] = [];
        }
        flagsBySection[sectionId].push(flag);
      }
    });
  });

  return flagsBySection;
}

// Helper function to get section name from section ID
export function getSectionName(
  sectionId: string,
  modelSections: ModelSectionType[]
): string {
  const section = modelSections.find(s => s.section === sectionId);
  return section?.title || sectionId;
}

// Helper function to categorize model sections
export function categorizeSections(
  modelSections: ModelSectionType[]
): {
  environmental: ModelSectionType[];
  social: ModelSectionType[];
  governance: ModelSectionType[];
} {
  return {
    environmental: modelSections.filter(section => {
      // Check both direct level property and data.level
      const level = section.level || (section.data?.level as string | undefined);
      return level === 'environmental' ||
        level === 'ecological' ||
        level === 'Environment' ||
        level === 'environment' ||
        level === 'Ecological';
    }),
    social: modelSections.filter(section => {
      // Check both direct level property and data.level
      const level = section.level || (section.data?.level as string | undefined);
      return level === 'social' ||
        level === 'Social';
    }),
    governance: modelSections.filter(section => {
      // Check both direct level property and data.level
      const level = section.level || (section.data?.level as string | undefined);
      // If no level is specified, default to governance
      return level === 'governance' ||
        level === 'Governance' ||
        level === 'safe_space' ||
        !level ||
        level === 'other';
    })
  };
}
