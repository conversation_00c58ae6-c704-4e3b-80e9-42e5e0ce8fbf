/**
 * Converts a VagueTypeV2 (from xfer_gw_vague_v2) to the old VagueType format (from xfer_gw_vague)
 * for backward compatibility with existing code.
 *
 * @param vagueV2 The vague term analysis from xfer_gw_vague_v2 table
 * @returns A vague term analysis in the old format for compatibility
 */
export function convertVagueV2ToVagueV1(vagueV2: any): any {
  // Handle the case where the input might not match the VagueTypeV2 interface exactly
  const rawVagueV2 = vagueV2 as any;
  const model = rawVagueV2.model;

  if (!model) {
    console.error('Vague model is missing:', rawVagueV2);
    // Return a minimal valid VagueType to prevent errors
    return {
      id: rawVagueV2.id || 0,
      run_id: rawVagueV2.run_id || 0,
      entity_xid: rawVagueV2.entity_xid || '',
      phrase: rawVagueV2.phrase || '',
      score: 0,
      explanation: 'Missing data',
      analysis: 'Missing data',
      summary: 'Missing data',
      citations: [],
      rank: null,
      created_at: null
    };
  }

  return {
    id: rawVagueV2.id,
    run_id: rawVagueV2.run_id,
    entity_xid: model.entity_xid || rawVagueV2.entity_xid,
    phrase: rawVagueV2.phrase,
    score: model.score || 0,
    explanation: model.explanation || 'Missing data',
    analysis: model.analysis || 'Missing data',
    summary: model.summary || 'Missing data',
    citations: model.citations || [],
    rank: model.rank || null,
    created_at: model.created_at || null
  };
}

/**
 * Converts an array of VagueTypeV2 objects to an array of VagueType objects
 * for backward compatibility with existing code.
 *
 * @param vaguesV2 Array of vague term analyses from xfer_gw_vague_v2 table
 * @returns Array of vague term analyses in the old format for compatibility
 */
export function convertVaguesV2ToVaguesV1(vaguesV2: any[]): any[] {
  return vaguesV2.map(vagueV2 => convertVagueV2ToVagueV1(vagueV2));
}
