# TipTap Schema Integration Testing

## Overview

This document describes how to manually test the TipTap schema integration with the AI chat system implemented for EKO-94.

## What Was Implemented

1. **Schema Extraction**: The AI chat panel now extracts the TipTap schema from the editor instance using `editor.schema`
2. **API Integration**: The schema is passed to the AI chat API along with document content and messages
3. **AI Prompt Enhancement**: The AI prompt now includes schema information to help generate better JSON patch operations

## Manual Testing Steps

### Prerequisites

1. Ensure the customer app is running with proper environment variables
2. Open the document editor with AI features enabled
3. Open browser developer console to see logging output

### Test Steps

1. **Open Document Editor**
   - Navigate to a document in the customer app
   - Ensure the TipTap editor loads successfully
   - Verify AI chat button is visible in the toolbar

2. **Open AI Chat Panel**
   - Click the AI chat button to open the chat panel
   - Verify the chat interface appears

3. **Send a Test Message**
   - Type a message in the chat input (e.g., "Help me improve this document")
   - Click Send or press Enter
   - **Check Console Output**: You should see logs like:
     ```
     AIChatPanel - Schema extracted: {
       hasSchema: true,
       schemaNodes: ["doc", "paragraph", "text", "heading", ...],
       schemaMarks: ["bold", "italic", "link", ...]
     }
     ```

4. **Verify API Receives Schema**
   - After sending the message, check the console for API logs:
     ```
     AI Chat API - Schema received: {
       hasSchema: true,
       schemaNodes: ["doc", "paragraph", "text", "heading", ...],
       schemaMarks: ["bold", "italic", "link", ...]
     }
     ```

5. **Test Document Editing Request**
   - Send a message that requests document editing (e.g., "Add a heading at the top")
   - The AI should now have schema information to generate proper JSON patch operations
   - Verify the AI response includes valid TipTap node types that exist in the schema

## Expected Behavior

### Schema Extraction
- The schema should contain all node types configured in the editor (doc, paragraph, text, heading, etc.)
- The schema should contain all mark types (bold, italic, link, etc.)
- The schema should be serializable to JSON without errors

### API Integration
- The schema should be successfully transmitted to the AI API
- The API should receive the schema as a JSON object
- The schema should be included in the AI prompt

### AI Responses
- The AI should generate JSON patch operations using only valid node/mark types from the schema
- The AI should respect content model constraints defined in the schema
- Document edits should be more accurate and schema-compliant

## Troubleshooting

### No Schema Logs
If you don't see schema logs in the console:
1. Ensure the editor has fully loaded
2. Check that the AI chat panel is properly initialized
3. Verify the editor instance is available

### Schema is Empty
If the schema appears empty or incomplete:
1. Check that all TipTap extensions are properly loaded
2. Verify the editor initialization completed successfully
3. Ensure no errors occurred during editor setup

### API Errors
If the API returns errors:
1. Check that the schema is properly serialized to JSON
2. Verify the API route is handling the schema parameter correctly
3. Check for any JSON parsing errors in the API logs

## Cleanup

The console logging added for testing purposes should be removed before production deployment. The logs are marked with "temporary" comments for easy identification.

## Files Modified

- `apps/customer/app/api/ai/chat/route.ts` - Added schema parameter and logging
- `apps/customer/components/editor/panels/AIChatPanel.tsx` - Added schema extraction and logging
- `apps/customer/components/editor/providers/CustomAIProvider.ts` - Updated method signatures to include schema
- `apps/customer/tests/editor-ai-features.spec.ts` - Added test for schema inclusion
- `apps/customer/tests/schema-extraction.test.ts` - Added unit tests for schema functionality
