'use client'

import React from 'react'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { Skeleton } from '@ui/components/ui/skeleton'
import { CategorySectionProps } from './types'

export function CategorySection({
  summary,
  sectionNames,
  citations,
  categoryId
}: CategorySectionProps) {
  // Map category IDs to display names and citation prefixes
  const categoryMap = {
    'environmental': {
      title: 'Ecological',
      prefix: 'env'
    },
    'social': {
      title: 'Social',
      prefix: 'soc'
    },
    'governance': {
      title: 'Governance',
      prefix: 'gov'
    }
  }

  const { title, prefix } = categoryMap[categoryId]

  // Remove loading state since text is streamed

  if (!summary) {
    return null
  }

  return (
    <section id={categoryId} className="mb-8">
      <h3 className="text-xl font-semibold mb-4">{title}</h3>
      <div className="space-y-6">
        <div className="prose prose-slate dark:prose-invert max-w-none mb-6">
          <EkoMarkdown
            citationPrefix={`${prefix}.s.`}
            citations={citations}
            inlineCitations={true}
            admin={false}
          >
            {summary.summary}
          </EkoMarkdown>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h4 className="text-lg font-medium mb-2">Positives</h4>
            <div className="prose prose-sm prose-slate dark:prose-invert max-w-none">
              <EkoMarkdown
                citationPrefix={`${prefix}.p.`}
                citations={citations}
                inlineCitations={true}
                admin={false}
              >
                {summary.positives}
              </EkoMarkdown>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-2">Negatives</h4>
            <div className="prose prose-sm prose-slate dark:prose-invert max-w-none">
              <EkoMarkdown
                citationPrefix={`${prefix}.n.`}
                citations={citations}
                inlineCitations={true}
                admin={false}
              >
                {summary.negatives}
              </EkoMarkdown>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h4 className="text-lg font-medium mb-2">Risks</h4>
            <div className="prose prose-sm prose-slate dark:prose-invert max-w-none">
              <EkoMarkdown
                citationPrefix={`${prefix}.r.`}
                citations={citations}
                inlineCitations={true}
                admin={false}
              >
                {summary.risks}
              </EkoMarkdown>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-2">Opportunities</h4>
            <div className="prose prose-sm prose-slate dark:prose-invert max-w-none">
              <EkoMarkdown
                citationPrefix={`${prefix}.o.`}
                citations={citations}
                inlineCitations={true}
                admin={false}
              >
                {summary.opportunities}
              </EkoMarkdown>
            </div>
          </div>
        </div>

        <div className="mt-8 space-y-6">
          <h4 className="text-lg font-medium mb-4">Detailed {title} Analysis</h4>
          {Object.entries(summary.model_sections).map(([sectionId, content]) => (
            <div
              key={sectionId}
              className="mb-6 pb-4 border-b border-slate-100 dark:border-slate-800 last:border-0"
            >
              <h5 className="text-base font-medium mb-2">{sectionNames[sectionId] || sectionId}</h5>
              <div className="prose prose-sm prose-slate dark:prose-invert max-w-none">
                <EkoMarkdown
                  citationPrefix={`${prefix}.${sectionId}.`}
                  citations={citations}
                  inlineCitations={true}
                  admin={false}
                >
                  {content}
                </EkoMarkdown>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
