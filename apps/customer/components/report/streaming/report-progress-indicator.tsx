'use client'

import React, { useState } from 'react'
import { Progress } from '@ui/components/ui/progress'
import { cn } from '@utils/lib/utils'
import { Ban, CheckCircle, ChevronDown, ChevronUp, Circle, Clock, Loader2, XCircle } from 'lucide-react'

export type SectionStatus = 'pending' | 'loading' | 'completed' | 'cached' | 'error' | 'cancelled' | 'summarizing'

export interface ReportSection {
  id: string
  name: string
  status: SectionStatus
  progress?: number // 0-100
  wordCount?: number // Actual word count
  parentId?: string // Optional parent section ID for subsections
  error?: string // Optional error message
}

export interface ReportProgressIndicatorProps {
  sections: ReportSection[]
  overallProgress: number // 0-100
  className?: string
  compactMode?: boolean // Whether to show in compact mode (for small screens)
  windowHeight?: number // Current window height for responsive adjustments
}

/**
 * A glass-morphic progress indicator for report generation
 * Shows overall progress and individual section status
 */
// Helper function to get total word count
function getTotalWordCount(sections: ReportSection[]): number {
  return sections.reduce((total, section) => total + (section.wordCount || 0), 0)
}

// Helper function to get estimated total words based on current word count
function getEstimatedTotalWords(currentWords: number): number {
  // Initial estimate: 400 words (~2000 tokens)
  let estimatedTotalWords = 400

  // Adjust estimate based on current word count
  if (currentWords > 360) { // 90% of initial 400 words
    estimatedTotalWords = 1000 // ~18000 tokens
  }

  if (currentWords > 800) { // 90% of 3600 words
    estimatedTotalWords = 2000 // ~30000 tokens
  }


  if (currentWords > 1800) { // 90% of 3600 words
    estimatedTotalWords = 3000 // ~30000 tokens
  }
  return estimatedTotalWords
}

// Helper function to count completed sections (including cached)
function getCompletedSections(sections: ReportSection[]): number {
  return sections.filter(section =>
    section.status === 'completed' || section.status === 'cached'
  ).length
}

// Helper function to organize sections into a hierarchical structure
function organizeHierarchicalSections(sections: ReportSection[]): ReportSection[] {
  // Get top-level sections (those without a parentId)
  const topLevelSections = sections.filter(section => !section.parentId);

  // Create a map of parent sections to their children
  const childrenMap = new Map<string, ReportSection[]>();

  sections.forEach(section => {
    if (section.parentId) {
      if (!childrenMap.has(section.parentId)) {
        childrenMap.set(section.parentId, []);
      }
      childrenMap.get(section.parentId)?.push(section);
    }
  });

  // Return only top-level sections (children will be rendered recursively)
  return topLevelSections;
}

// Component to render a single section with its children and progress bar
function SectionItem({ section, childSections }: { section: ReportSection, childSections?: ReportSection[] }) {
  const hasChildren = childSections && childSections.length > 0;

  // Calculate subsection progress for parent sections
  const calculateSubsectionProgress = () => {
    if (!hasChildren) return null

    const totalSubsections = childSections!.length
    const completedSubsections = childSections!.filter(
      s => s.status === 'completed' || s.status === 'cached',
    ).length

    // Calculate percentage of completed subsections
    const progressPercentage = totalSubsections > 0
      ? (completedSubsections / totalSubsections) * 100
      : 0

    return {
      completed: completedSubsections,
      total: totalSubsections,
      percentage: progressPercentage,
    }
  }

  const subsectionProgress = hasChildren ? calculateSubsectionProgress() : null

  return (
    <div className="space-y-1">
      <div className="flex items-center gap-3">
        {section.status === 'completed' ? (
          <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
        ) : section.status === 'cached' ? (
          <CheckCircle className="h-5 w-5 text-slate-400 flex-shrink-0" />
        ) : section.status === 'loading' ? (
          <Loader2 className="h-5 w-5 text-blue-500 animate-spin flex-shrink-0" />
        ) : section.status === 'error' ? (
          <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
        ) : section.status === 'cancelled' ? (
          <Ban className="h-5 w-5 text-orange-500 flex-shrink-0" />
        ) : (
          <Circle className="h-5 w-5 text-muted-foreground flex-shrink-0" />
        )}

        <div className="flex-grow">
          <div className="flex justify-between text-sm">
            <span className={cn(
              section.status === 'completed' ? 'text-foreground' :
              section.status === 'cached' ? 'text-slate-500' :
              section.status === 'loading' ? 'text-foreground' :
              section.status === 'error' ? 'text-red-500' :
                section.status === 'cancelled' ? 'text-orange-500' :
              'text-muted-foreground'
            )}>
              {section.name}
            </span>
            {/* Show progress percentage for parent sections with subsections */}
            {hasChildren && subsectionProgress && section.status !== 'completed' && (subsectionProgress.completed === subsectionProgress.total) && (
              'summarizing'
            )}
            {/* Show progress for sections without children */}
            {!hasChildren && section.status === 'loading' && section.progress !== undefined && (
              <span className="text-xs">
                {Math.round(section.progress)}%
              </span>
            )}
          </div>

          {/* Show progress bar for parent sections with subsections */}
          {hasChildren && subsectionProgress && (
            <div className="mt-1">
              <Progress
                value={subsectionProgress.percentage}
                className="h-1"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-0.5">
                <span>
                  {subsectionProgress.completed}/{subsectionProgress.total} subsections complete
                </span>
                <span>
                  {Math.round(subsectionProgress.percentage)}%
                </span>
              </div>
            </div>
          )}

          {/* Show progress bar for sections without children that are loading */}
          {!hasChildren && section.status === 'loading' && (
            <div className="mt-1">
              <Progress
                value={section.progress}
                className="h-1"
              />
            </div>
          )}

          {section.status === 'error' && section.error && (
            <div className="mt-1 text-xs text-red-500">
              {section.error}
            </div>
          )}
        </div>
      </div>

      {/* Render child sections with indentation but without progress indicators */}
      {hasChildren && (
        <div className="pl-6 space-y-2 border-l-2 border-slate-200 dark:border-slate-700 ml-2">
          {childSections?.map(childSection => (
            <div key={childSection.id} className="flex items-center gap-3">
              {childSection.status === 'completed' ? (
                <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
              ) : childSection.status === 'cached' ? (
                <CheckCircle className="h-4 w-4 text-slate-400 flex-shrink-0" />
              ) : childSection.status === 'loading' ? (
                <Loader2 className="h-4 w-4 text-blue-500 animate-spin flex-shrink-0" />
              ) : childSection.status === 'error' ? (
                <XCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
              ) : childSection.status === 'cancelled' ? (
                <Ban className="h-4 w-4 text-orange-500 flex-shrink-0" />
              ) : (
                <Circle className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              )}
              <span className={cn(
                'text-sm',
                childSection.status === 'completed' ? 'text-foreground' :
                  childSection.status === 'cached' ? 'text-slate-500' :
                    childSection.status === 'loading' ? 'text-foreground' :
                      childSection.status === 'error' ? 'text-red-500' :
                        childSection.status === 'cancelled' ? 'text-orange-500' :
                          'text-muted-foreground',
              )}>
                {childSection.name}
              </span>
              {childSection.status === 'error' && childSection.error && (
                <span className="text-xs text-red-500 ml-2">
                  {childSection.error}
                </span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export function ReportProgressIndicator({
  sections,
  overallProgress,
  className,
  compactMode = false,
  windowHeight = 1080, // Default to large screen if not provided
}: ReportProgressIndicatorProps) {
  // State for showing/hiding sections list
  // Hide sections by default on screens smaller than 1080px
  const [showSections, setShowSections] = useState(!compactMode)

  // Count sections by status
  const completedCount = sections.filter(s => s.status === 'completed').length
  const cachedCount = sections.filter(s => s.status === 'cached').length
  const loadingCount = sections.filter(s => s.status === 'loading').length
  const pendingCount = sections.filter(s => s.status === 'pending').length
  const errorCount = sections.filter(s => s.status === 'error').length
  const cancelledCount = sections.filter(s => s.status === 'cancelled').length
  const summaryCount = sections.filter(s => s.status === 'summarizing').length

  // Organize sections into a hierarchical structure
  const childrenMap = new Map<string, ReportSection[]>();
  sections.forEach(section => {
    if (section.parentId) {
      if (!childrenMap.has(section.parentId)) {
        childrenMap.set(section.parentId, []);
      }
      childrenMap.get(section.parentId)?.push(section);
    }
  });

  // Get top-level sections
  const topLevelSections = sections.filter(section => !section.parentId);

  // Toggle sections visibility
  const toggleSections = () => {
    setShowSections(!showSections);
  };

  return (
    <div className={cn('glass-effect-strong-lit rounded-2xl space-y-3',
      compactMode ? 'p-4' : 'p-6',
      className
    )}>
      <span className={cn(
        'font-medium mb-1 heading-2',
        compactMode ? "text-base" : "text-lg"
      )}>Generating Report</span>

      {/* Overall progress */}
      <div className="space-y-1.5">
        <div className="flex justify-between text-sm">
          <span>Overall Progress</span>
          <span>{Math.round(overallProgress)}%</span>
        </div>
        <Progress value={overallProgress} className="h-2" />
        <p className="text-[10px] text-muted-foreground mt-1">
          Please wait while we generate your report.
        </p>
        <p className="text-xs text-muted-foreground">
          {getCompletedSections(sections) > 0 &&
            `${getCompletedSections(sections)}/${sections.length} sections completed.`}
        </p>
      </div>

      {/* Status summary - more compact for small screens */}
      <div className={cn(
        "text-sm text-muted-foreground",
        compactMode ? "grid grid-cols-2 gap-1" : "grid grid-cols-2 gap-2"
      )}>
        <div className="flex items-center gap-1.5">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span>{completedCount} completed</span>
        </div>
        <div className="flex items-center gap-1.5">
          <CheckCircle className="h-4 w-4 text-slate-400" />
          <span>{summaryCount} summarizing</span>
        </div>
        <div className="flex items-center gap-1.5">
          <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
          <span>{loadingCount} in progress</span>
        </div>
        <div className="flex items-center gap-1.5">
          <Clock className="h-4 w-4" />
          <span>{pendingCount} pending</span>
        </div>
        {errorCount > 0 && (
          <div className="flex items-center gap-1.5 col-span-2">
            <XCircle className="h-4 w-4 text-red-500" />
            <span className="text-red-500">{errorCount} error{errorCount !== 1 ? 's' : ''}</span>
          </div>
        )}
        {cancelledCount > 0 && (
          <div className="flex items-center gap-1.5 col-span-2">
            <Ban className="h-4 w-4 text-orange-500" />
            <span className="text-orange-500">{cancelledCount} cancelled</span>
          </div>
        )}
      </div>

      {/* Section list with toggle button - only show on screens >= 1080px */}
      {windowHeight >= 1080 && (
        <div className="space-y-2 mt-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium heading-3">Sections</span>
            <button
              onClick={toggleSections}
              className="text-muted-foreground hover:text-foreground transition-colors"
              aria-label={showSections ? "Hide sections" : "Show sections"}
            >
              {showSections ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </button>
          </div>

          {showSections && (
            <div
              className={cn(
                "space-y-2 overflow-y-scroll pr-1",
                compactMode ? 'max-h-[150px]' : 'max-h-[calc(500px)]',
              )}
            >
              {topLevelSections.map((section) => (
                <SectionItem
                  key={section.id}
                  section={section}
                  childSections={childrenMap.get(section.id)}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
