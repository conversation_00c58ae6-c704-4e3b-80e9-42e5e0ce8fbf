'use client'

import React, { createContext, useCallback, useContext, useMemo, useState, startTransition } from 'react'
import { ReportSection, SectionStatus } from './report-progress-indicator'

interface ReportProgressContextType {
  sections: ReportSection[]
  overallProgress: number
  registerSection: (id: string, name: string, parentId?: string) => void
  updateSectionStatus: (id: string, status: SectionStatus, progress?: number, error?: string, parentId?: string, name?: string) => void
  resetProgress: () => void
}

const ReportProgressContext = createContext<ReportProgressContextType | undefined>(undefined)

export interface ReportProgressProviderProps {
  children: React.ReactNode
}

export function ReportProgressProvider({ children }: ReportProgressProviderProps) {
  const [sections, setSections] = useState<ReportSection[]>([])

  // Calculate overall progress based on section statuses
  const overallProgress = useMemo(() => {
    if (sections.length === 0) return 0

    const completedSections = sections.filter(s => s.status === 'completed').length
    const cachedSections = sections.filter(s => s.status === 'cached').length
    const loadingSections = sections.filter(s => s.status === 'loading')
    const errorSections = sections.filter(s => s.status === 'error').length
    const cancelledSections = sections.filter(s => s.status === 'cancelled').length

    // Sum up progress of loading sections
    const loadingProgress = loadingSections.reduce((sum, section) => {
      return sum + (section.progress || 0)
    }, 0)

    // Calculate overall progress (count cached sections as 100% complete, exclude error and cancelled sections)
    const totalSections = sections.length - errorSections - cancelledSections // Don't count error or cancelled sections in the denominator
    if (totalSections === 0) return 0

    return ((completedSections + cachedSections) * 100 + loadingProgress) / totalSections
  }, [sections])

  // Register a new section - memoize to prevent unnecessary re-renders
  const registerSection = useCallback((id: string, name: string, parentId?: string) => {
    setSections(prev => {
      // Check if section already exists
      if (prev.some(s => s.id === id)) {
        return prev
      }

      // Add new section
      return [...prev, { id, name, status: 'pending', parentId }]
    })
  }, [])

  // Update a section's status - memoize to prevent unnecessary re-renders
  const updateSectionStatus = useCallback((id: string, status: SectionStatus, progress?: number, error?: string, parentId?: string, name?: string) => {
    // Use startTransition to mark this as a non-urgent update
    startTransition(() => {
      setSections(prev => {
        // Check if the section exists
        const sectionExists = prev.some(s => s.id === id);

        if (!sectionExists && parentId && name) {
          // If the section doesn't exist but we have parentId and name, create it
          return [
            ...prev,
            {
              id,
              name,
              status,
              progress: progress || 0,
              wordCount: 0,
              error,
              parentId
            }
          ];
        }

        // Otherwise update the existing section
        return prev.map(section =>
          section.id === id
            ? {
                ...section,
                status,
                progress: progress !== undefined ? progress : section.progress,
                wordCount: section.wordCount,
                error: error !== undefined ? error : section.error,
                // Only update name and parentId if they're provided
                ...(name ? { name } : {}),
                ...(parentId ? { parentId } : {})
              }
            : section
        );
      });
    });
  }, [])

  // Reset all progress - memoize to prevent unnecessary re-renders
  const resetProgress = useCallback(() => {
    setSections([])
  }, [])

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    sections,
    overallProgress,
    registerSection,
    updateSectionStatus,
    resetProgress
  }), [sections, overallProgress, registerSection, updateSectionStatus, resetProgress])

  return (
    <ReportProgressContext.Provider value={value}>
      {children}
    </ReportProgressContext.Provider>
  )
}

export function useReportProgress() {
  const context = useContext(ReportProgressContext)
  if (context === undefined) {
    throw new Error('useReportProgress must be used within a ReportProgressProvider')
  }
  return context
}
