'use client'

import React, { memo, useCallback, useEffect, useMemo, useRef, useState, startTransition } from 'react'
import { Button } from '@ui/components/ui/button'
import { Edit3 } from 'lucide-react'
import { HierarchicalReportProps } from '../types'
import { ReportHeader } from '../report-header'
import { TableOfContents } from '../table-of-contents'
import { categorizeSections } from '@/utils/report-utils'
import { ReportProgressProvider, useReportProgress } from './report-progress-context'
import { ReportProgressIndicator } from './report-progress-indicator'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { streamHierarchicalReport } from './stream'
import { useEntity } from '@/components/context/entity/entity-context'
import { cn } from '@utils/lib/utils'

// Component to conditionally render the progress indicator
// Memoize the component to prevent unnecessary re-renders
const ReportProgressIndicatorWrapper = memo(({ executiveReady }: { executiveReady: boolean }) => {
  const { sections, overallProgress } = useReportProgress()
  const [windowHeight, setWindowHeight] = useState(0)
  const [compactMode, setCompactMode] = useState(false)

  // Check if all sections are completed or cached - memoize this calculation
  const allSectionsReady = useMemo(() =>
      sections.length > 0 && sections.every(s => s.status === 'completed' || s.status === 'cached'),
    [sections],
  )

  // Effect to measure window height and set compact mode
  useEffect(() => {
    // Set initial window height
    setWindowHeight(window.innerHeight)
    // Set compact mode for screens under 900px height (more conservative)
    setCompactMode(window.innerHeight < 900)

    // Update on resize
    const handleResize = () => {
      setWindowHeight(window.innerHeight)
      setCompactMode(window.innerHeight < 900)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Only show the progress indicator if we're still loading sections
  if (executiveReady || allSectionsReady) {
    return null
  }

  // Position exactly in the middle of the viewport
  // Use 50vh in the style instead of calculating a fixed position
  // This ensures true vertical centering regardless of component height
  const topPosition = 64

  // Use console.log to track renders (can be removed in production)
  console.log('Rendering ReportProgressIndicator', { windowHeight, compactMode, topPosition })

  return (
    <>
      <div
        className="fixed inset-0 z-10 backdrop-blur-sm bg-slate-50/50 dark:bg-slate-950  print:hidden pointer-events-none" />
    <div
      className={cn(
        'grid place-items-center fixed inset-0 z-20 print:hidden',
      )}

    >
      <ReportProgressIndicator
        sections={sections}
        overallProgress={overallProgress}
        compactMode={compactMode}
        windowHeight={windowHeight}
      />
    </div>
    </>
  )
})

export function StreamingHierarchicalReport({
                                              entityName,
                                              entityDescription,
                                              modelName,
                                              flags,
                                              modelSections,
                                              claims,
                                              promises,
                                              cherryData,
                                              allCitations,
                                              runId,
                                              onPrint,
                                              onEditReport,
                                              isCreatingDocument,
                                            }: HierarchicalReportProps) {
  // Get entity context for includeDisclosures state and entity tracking
  const entityContext = useEntity()
  const includeDisclosures = entityContext.includeDisclosures

  // Track the current entity and run to detect changes
  const currentEntityId = entityContext.entity
  const currentRunId = entityContext.runObject?.id

  // Determine which categories have content
  const { environmental, social, governance } = categorizeSections(modelSections)

  const hasEnvironmental = environmental.length > 0 && flags.some(flag =>
    environmental.some(section => section.section === flag.model.model_sections[modelName]),
  )

  const hasSocial = social.length > 0 && flags.some(flag =>
    social.some(section => section.section === flag.model.model_sections[modelName]),
  )

  const hasGovernance = governance.length > 0 && flags.some(flag =>
    governance.some(section => section.section === flag.model.model_sections[modelName]),
  )

  // State for the complete report content
  const [reportContent, setReportContent] = useState<string>('')

  // State to track if the report is loading
  const [isLoading, setIsLoading] = useState<boolean>(false)

  // Use useRef for AbortController to avoid triggering re-renders
  const abortControllerRef = useRef<AbortController | null>(null)

  // Track the current report generation to prevent conflicts
  const reportGenerationIdRef = useRef<string | null>(null)

  // Define all the main sections for progress tracking
  const mainSections = [
    { id: `intro-${entityName}-${modelName}-${runId}`, name: 'Introduction' },
    { id: `exec-summary-${entityName}-${modelName}-${runId}`, name: 'Executive Summary' },
    ...(hasEnvironmental ? [{
      id: `environmental-${entityName}-${modelName}-${runId}`,
      name: 'Ecological Impact',
    }] : []),
    ...(hasSocial ? [{ id: `social-${entityName}-${modelName}-${runId}`, name: 'Social Impact' }] : []),
    ...(hasGovernance ? [{ id: `governance-${entityName}-${modelName}-${runId}`, name: 'Governance Impact' }] : []),
    { id: `reliability-${entityName}-${modelName}-${runId}`, name: 'Reliability Analysis' },
    { id: `transparency-${entityName}-${modelName}-${runId}`, name: 'Transparency Analysis' },
  ]

  // Register main sections with the progress context
  // Use memo to prevent unnecessary re-renders
  const RegisterMainSections = memo(() => {
    const { registerSection, updateSectionStatus, resetProgress } = useReportProgress()

    useEffect(() => {
      // Register all main sections
      mainSections.forEach(section => {
        registerSection(section.id, section.name)
      })
    }, [registerSection])



    // Track entity/run changes and reset report when they change
    const [lastEntityId, setLastEntityId] = useState<string | null>(currentEntityId)
    const [lastRunId, setLastRunId] = useState<number | undefined>(currentRunId)

    useEffect(() => {
      // Check if entity or run has changed
      if (currentEntityId !== lastEntityId || currentRunId !== lastRunId) {
        console.log('Entity or run changed, resetting report', {
          currentEntityId,
          lastEntityId,
          currentRunId,
          lastRunId
        })

        // Abort any ongoing report generation
        if (abortControllerRef.current) {
          console.log('Aborting ongoing report generation due to entity/run change')
          abortControllerRef.current.abort()
          abortControllerRef.current = null
        }

        // Reset progress and report content
        resetProgress()
        setReportContent('')
        setIsLoading(false)

        // Update tracking variables
        setLastEntityId(currentEntityId)
        setLastRunId(currentRunId)

        // Generate new report generation ID
        reportGenerationIdRef.current = `${currentEntityId}-${currentRunId}-${Date.now()}`
      }
    }, [currentEntityId, currentRunId, lastEntityId, lastRunId, resetProgress])

    // Memoize the content update callback to maintain a stable reference
    const updateContentCallback = useCallback((currentContent: string) => {
      // Use startTransition to mark content updates as non-urgent
      startTransition(() => {
        setReportContent(currentContent)
      })
    }, [])

    // Load the complete report
    useEffect(() => {
      // Only run this effect when we have consistent data and not already loading
      if (isLoading) {
        console.log('Already loading, skipping report load')
        return
      }

      // Ensure we have valid entity and run data before loading
      if (!currentEntityId || !currentRunId || !runId) {
        console.log('Missing entity or run data, skipping report load', {
          currentEntityId,
          currentRunId,
          runId
        })
        return
      }

      // Verify that the runId matches the current context
      if (runId !== currentRunId) {
        console.log('RunId mismatch, skipping report load', {
          reportRunId: runId,
          contextRunId: currentRunId
        })
        return
      }

      // Ensure we have sufficient data to generate a meaningful report
      if (!flags || flags.length === 0) {
        console.log('No flags data available, skipping report load')
        return
      }

      if (!modelSections || modelSections.length === 0) {
        console.log('No model sections data available, skipping report load')
        return
      }

      // Check if entity context is still loading data
      if (entityContext.isLoadingFlags || entityContext.isLoadingModelSections ||
          entityContext.isLoadingClaims || entityContext.isLoadingPromises ||
          entityContext.isLoadingCherry) {
        console.log('Entity context still loading data, skipping report load', {
          isLoadingFlags: entityContext.isLoadingFlags,
          isLoadingModelSections: entityContext.isLoadingModelSections,
          isLoadingClaims: entityContext.isLoadingClaims,
          isLoadingPromises: entityContext.isLoadingPromises,
          isLoadingCherry: entityContext.isLoadingCherry
        })
        return
      }

      // Check if we already have an active report generation for this entity/run
      const currentGenerationId = `${currentEntityId}-${currentRunId}`
      if (reportGenerationIdRef.current && reportGenerationIdRef.current.startsWith(currentGenerationId)) {
        console.log('Report generation already in progress for this entity/run, skipping')
        return
      }

      let isMounted = true

      // Only create new AbortController if we don't have one or if it's been aborted
      if (!abortControllerRef.current || abortControllerRef.current.signal.aborted) {
        console.log('Creating new AbortController for report generation')
        abortControllerRef.current = new AbortController()
        reportGenerationIdRef.current = `${currentGenerationId}-${Date.now()}`
      }

      const currentAbortController = abortControllerRef.current

      async function loadReport() {
        console.log('Starting report generation with ID:', reportGenerationIdRef.current)
        setIsLoading(true)

        try {
          await streamHierarchicalReport(
            {
              entityName,
              entityDescription,
              modelName,
              flags,
              modelSections,
              claims,
              promises,
              cherryData,
              runId,
              includeDisclosures,
              entity: currentEntityId!,
              run: currentRunId!.toString(),
            },
            updateSectionStatus,
            // Use the memoized callback
            updateContentCallback,
            // Pass the abort signal
            currentAbortController.signal,
          )
          console.log('Report generation completed successfully')
        } catch (error) {
          if (error instanceof Error && error.name === 'AbortError') {
            console.log('Report generation was aborted')
          } else {
            console.error('Error loading report:', error)
          }
        } finally {
          if (isMounted) {
            setIsLoading(false)
            // Only clear the abort controller if it's the same one we started with
            if (abortControllerRef.current === currentAbortController) {
              abortControllerRef.current = null
              reportGenerationIdRef.current = null
            }
          }
        }
      }

      loadReport()

      // Cleanup function to prevent state updates after unmount
      return () => {
        isMounted = false
        // Don't abort here - let the report complete unless explicitly cancelled
      }
    }, [
      // Remove reportOptions from dependencies to prevent unnecessary re-runs
      currentEntityId,
      currentRunId,
      runId,
      isLoading,
      entityContext.isLoadingFlags,
      entityContext.isLoadingModelSections,
      entityContext.isLoadingClaims,
      entityContext.isLoadingPromises,
      entityContext.isLoadingCherry,
      // Include the actual data in dependencies instead of the memoized object
      flags,
      modelSections,
      claims,
      promises,
      cherryData,
      includeDisclosures,
    ])

    return null
  })

  return (
    <ReportProgressProvider>
      <RegisterMainSections />
      <ReportContent
        entityName={entityName}
        entityDescription={entityDescription}
        modelName={modelName}
        hasEnvironmental={hasEnvironmental}
        hasSocial={hasSocial}
        hasGovernance={hasGovernance}
        modelSections={modelSections}
        flags={flags}
        claims={claims}
        promises={promises}
        cherryData={cherryData}
        allCitations={allCitations}
        reportContent={reportContent}
        onPrint={onPrint}
        onEditReport={onEditReport}
        isCreatingDocument={isCreatingDocument}
      />
    </ReportProgressProvider>
  )
}

// Separate component to use hooks within the ReportProgressProvider context
// Memoize the entire component to prevent unnecessary re-renders
const ReportContent = memo(({
                              entityName,
                              entityDescription,
                              modelName,
                              hasEnvironmental,
                              hasSocial,
                              hasGovernance,
                              modelSections,
                              flags,
                              claims,
                              promises,
                              cherryData,
                              allCitations,
                              reportContent,
                              onPrint,
                              onEditReport,
                              isCreatingDocument,
                            }: {
  entityName: string;
  entityDescription: string;
  modelName: string;
  hasEnvironmental: boolean;
  hasSocial: boolean;
  hasGovernance: boolean;
  modelSections: any[];
  flags: any[];
  claims: any[];
  promises: any[];
  cherryData: any[];
  allCitations: any[];
  reportContent: string;
  onPrint: (e?: any) => void;
  onEditReport?: (reportContent: string) => void;
  isCreatingDocument?: boolean;
}) => {
  // Check if all sections are loaded - now safely within the ReportProgressProvider
  const { sections } = useReportProgress()
  const allSectionsReady = useMemo(() =>
      sections.length > 0 && sections.every(s => s.status === 'completed' || s.status === 'cached'),
    [sections],
  )

  // Create a memoized wrapper
  type MemoizedMarkdownProps = {
    children: string;
    allCitations: any[];
    inlineCitations?: boolean;
  };

  // Use useMemo to create a stable reference for the citations
  // Fix: Use allCitations directly instead of JSON.stringify to avoid infinite re-renders
  const stableCitations = useMemo(() => allCitations, [allCitations])

  // Custom equality function for the memo component to prevent unnecessary re-renders
  const arePropsEqual = useCallback((prevProps: MemoizedMarkdownProps, nextProps: MemoizedMarkdownProps) => {
    // Only re-render if the content has actually changed
    return prevProps.children === nextProps.children
  }, [])

  // Memoize the component with proper dependencies
  const MemoizedMarkdown = useMemo(() => {
    return memo(({ children, inlineCitations = true }: MemoizedMarkdownProps) => {
      return (
        <EkoMarkdown
          citations={stableCitations}
          admin={false}
          citationPrefix=""
          inlineCitations={inlineCitations}
          className="no-re-render"
          skipCitations={false}
        >
          {children}
        </EkoMarkdown>
      )
    }, arePropsEqual)
  }, [stableCitations, arePropsEqual])
  console.log('Rendering ReportContent')
  return (
    <div className="prose-container prose prose-slate dark:prose-invert mx-auto px-4 py-8 max-w-4xl">
      <Button
        className="fixed top-20 right-8 z-10 print:hidden"
        onClick={onPrint}
      >
        Print
      </Button>

      {/* Edit Report Button - only show when report is ready and onEditReport is provided */}
      {onEditReport && allSectionsReady && reportContent && (
        <Button
          className="fixed top-20 right-32 z-10 print:hidden"
          onClick={() => {
            console.log('Edit Report button clicked!')
            console.log('Report content length:', reportContent.length)
            onEditReport(reportContent)
          }}
          disabled={isCreatingDocument}
        >
          <Edit3 className="w-4 h-4 mr-2" />
          {isCreatingDocument ? 'Creating...' : 'Edit Report'}
        </Button>
      )}

      {/* Report Progress Indicator */}
      <ReportProgressIndicatorWrapper executiveReady={allSectionsReady} />
      {allSectionsReady && (

      <div className="prose-document">
        <div className="print-first-page">
          <ReportHeader
            entityName={entityName}
            modelName={modelName}
          />

          {/* Front Section Overview */}
          {/*{ffShowNewReportFrontPage() &&*/}
          {/*  <ReportFrontSection*/}
          {/*    entityName={entityName}*/}
          {/*    entityDescription={entityDescription}*/}
          {/*    modelName={modelName}*/}
          {/*    flags={flags}*/}
          {/*    modelSections={modelSections}*/}
          {/*    claims={claims}*/}
          {/*    promises={promises}*/}
          {/*    cherryData={cherryData}*/}
          {/*  />*/}
          {/*}*/}
          {/* Table of Contents */}
          <TableOfContents
            hasEnvironmental={hasEnvironmental}
            hasSocial={hasSocial}
            hasGovernance={hasGovernance}
            modelSections={modelSections}
            modelName={modelName}
            flags={flags}
          />
        </div>

        {/* Single EkoMarkdown component for the entire report content */}
        <MemoizedMarkdown allCitations={allCitations} inlineCitations={false}>
          {reportContent}
        </MemoizedMarkdown>
      </div>
      )}
    </div>
  )
})
