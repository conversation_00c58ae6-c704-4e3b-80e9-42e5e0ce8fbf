'use client'
import { truncate } from '@utils/text-utils'

import { SectionStatus } from './report-progress-indicator'
import { categorizeSections } from '@/utils/report-utils'
import { hash64 } from '@utils/hash'
import { cacheText, getCacheText } from '@/utils/kv'
// Process the markdown content to remove headings if needed
export const processMarkdown = (markdown: string): string => {
  // Remove any ### headings from the content



  markdown = markdown.replace(/[|:-]{100,}/g, '')
    .replace(/^### Key Positives\s*$/gm, '#### Key Positives')
    .replace(/^### Key Negatives\s*$/gm, '#### Key Negatives')
    .replace(/^### Key Risks\s*$/gm, '#### Key Risks')
    .replace(/^### Key Opportunities\s*$/gm, '#### Key Opportunities')
    .replace(/^### Impact of Actions\s*$/gm, '#### Impact of Actions')
    .replace(/^## Summary\s*$/gm, '')
    .replace(/^## Key Positives\s*$/gm, '#### Key Positives')
    .replace(/^## Key Negatives\s*$/gm, '#### Key Negatives')
    .replace(/^## Key Risks\s*$/gm, '#### Key Risks')
    .replace(/^## Key Opportunities\s*$/gm, '#### Key Opportunities')
    .replace(/^# Reliability Analysis\s*$/gm, '')
    .replace(/^# Claims Analysis\s*$/gm, '### Claims')
    .replace(/^# Key Claim Examples\s*$/gm, '#### Examples')
    .replace(/^# Promises Analysis\s*$/gm, '### Promises')
    .replace(/^# Key Promise Examples\s*$/gm, '#### Examples')
    .replace(/^# Cherry Picking Analysis\s*$/gm, '#### Cherry Picking')
    .replace(/^# Key Examples\s*$/gm, '### Key Examples')

  // 1  Clean, deduplicated whitelist
  const whitelist = new Set([
    'Summary',
    'Key Positives',
    'Key Negatives',
    'Key Risks',
    'Key Opportunities',
    'Impact of Actions',
    'Claims',
    'Promises',
    'Cherry Picking',
    'Key Examples',
    'Reliability Analysis',
    'Claims Analysis',
    'Promises Analysis',
    'Cherry Picking Analysis',
    'Key Claim Examples',
    'Key Promise Examples',
    'Reliability',
  ])

  // 2  Process line-by-line
  const filteredLines = markdown
    .split(/\r?\n/)
    .filter(line => {
      return !line.match(/^\s*\*\*(.*)\*\*\s*$/)
    })// keep original line endings
    .filter(line => {
      const m = line.match(/^\s*(#{1,6})\s*(.*)$/) // heading 1–6?
      if (!m) return true                   // not a heading → keep
      const headingText = m[2].trim()       // actual title
      return whitelist.has(headingText)     // keep only if allowed
    })

  return filteredLines.join('\n')
}

export const removeHeadings = (markdown: string): string | null => {
  console.log('Removing headings')
  return processMarkdown(markdown)?.replace(/^\s*#+\s.*$/gm, '')
}

export interface StreamSectionOptions {
  endpoint: string
  queryParams: URLSearchParams
  hashId: string
  includeDisclosures: boolean
  salt?: string
  preprocess?: (text: string) => string
  updateSectionStatus?: (id: string, status: SectionStatus, progress?: number, error?: string, parentId?: string, name?: string) => void
  parentId?: string
  sectionName?: string
  abortSignal?: AbortSignal
}

export interface StreamSectionResult {
  text: string
  error?: string
}

/**
 * Gets content from a server endpoint (non-streaming)
 * @param options Options for getting content
 * @returns Promise with the content
 */
export async function streamSection(options: StreamSectionOptions): Promise<StreamSectionResult> {
  const {
    endpoint,
    queryParams,
    hashId,
    includeDisclosures,
    salt = '1',
    preprocess = processMarkdown,
    updateSectionStatus,
    parentId,
    sectionName,
    abortSignal,
  } = options

  // Create a cache key
  const cacheKey = `v21-report-${hashId}-${endpoint}-${includeDisclosures ? '1' : '0'}-${salt}`

  console.log(`Getting section with hashId: ${hashId}, endpoint: ${endpoint}`)

  // Immediately set status to loading before doing anything else
  if (updateSectionStatus) {
    console.log(`Setting initial loading status for ${hashId}`)
    updateSectionStatus(hashId, 'loading', 10, undefined, parentId, sectionName)
  }


  try {
    // Check if already aborted before starting
    if (abortSignal?.aborted) {
      throw new Error('Request was aborted before starting')
    }

    // Update section status to show we're about to make the request
    if (updateSectionStatus) {
      updateSectionStatus(hashId, 'loading', 30, undefined, parentId, sectionName)
    }
    let text = ''

    let cachedSection = await getCacheText(cacheKey)
    if (cachedSection) {
      text = cachedSection
    } else {
      // Build the URL with query parameters
      const url = `${endpoint}?${queryParams.toString()}`

      const response = await fetch(url, {
        method: 'GET',
        signal: abortSignal,
      })

      // Check if aborted after fetch
      if (abortSignal?.aborted) {
        throw new Error('Request was aborted after fetch')
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Get the text response directly
      text = await response.text()
      console.log(`[DEBUG] Received response for ${hashId}:`, text.substring(0, 100) + (text.length > 100 ? '...' : ''))

      // Check if aborted after getting response
      if (abortSignal?.aborted) {
        throw new Error('Request was aborted after receiving response')
      }
      await cacheText(cacheKey, text)

    }
    // Fetch from the server - send the request data directly (not wrapped in a prompt property)

    // Process the text if needed
    const processedText = preprocess(text)

    // Update progress to completed
    if (updateSectionStatus) {
      // Set progress to 100% immediately since we have the full response
      updateSectionStatus(hashId, 'completed', 100, undefined, parentId, sectionName)
    }

    return { text: processedText }
  } catch (error) {
    // Check if this is an abort error
    if (error instanceof Error && (error.name === 'AbortError' || error.message.includes('aborted'))) {
      console.log(`Request for ${hashId} was aborted:`, error.message)

      // Update progress to show it was cancelled, not errored
      if (updateSectionStatus) {
        updateSectionStatus(hashId, 'cancelled', 0, 'Cancelled', parentId, sectionName)
      }

      return {
        text: '',
        error: 'Cancelled',
      }
    }

    console.error(`Error getting content from ${endpoint} for ${hashId}:`, error)

    // Update progress to error
    if (updateSectionStatus) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      updateSectionStatus(hashId, 'error', 0, errorMessage, parentId, sectionName)
    }

    return {
      text: '',
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

/**
 * Helper function to add a section to the report content
 * @param currentContent Current report content
 * @param sectionTitle Title of the section to add
 * @param sectionContent Content of the section
 * @returns Updated report content
 */
export function addSectionToReport(
  currentContent: string,
  sectionTitle: string,
  sectionContent: string,
): string {
  // Check if section already exists
  if (currentContent.includes(`# ${sectionTitle}`)) {
    // Append to existing section
    return currentContent + sectionContent + '\n\n'
  } else {
    // Add new section
    return currentContent + `# ${sectionTitle}\n\n${sectionContent}\n\n`
  }
}

/**
 * Generate a complete hierarchical report
 * @param options Options for generating the report
 * @param updateSectionStatus Function to update section status
 * @param onSectionReady Optional callback that is called whenever a section is ready with the current report content
 * @returns The complete report content
 */
export async function streamHierarchicalReport(
  options: {
    entityName: string;
    entityDescription: string;
    modelName: string;
    flags: any[];
    modelSections: any[];
    claims: any[];
    promises: any[];
    cherryData: any[];
    runId?: number;
    includeDisclosures: boolean;
    entity: string;
    run: string;
  },
  updateSectionStatus: (id: string, status: SectionStatus, progress?: number, error?: string, parentId?: string, name?: string) => void,
  onSectionReady?: (currentContent: string) => void,
  abortSignal?: AbortSignal,
): Promise<string> {
  const {
    entityName,
    entityDescription,
    modelName,
    flags,
    modelSections,
    claims,
    promises,
    cherryData,
    runId = 0,
    includeDisclosures,
    entity,
    run,
  } = options


  // Get introduction section
  const introHashId = `intro-${entityName}-${modelName}-${runId}`
  const introQueryParams = new URLSearchParams({
    entity,
    run,
    model: modelName,
    includeDisclosures: includeDisclosures.toString(),
  })

  console.log(`Starting introduction section with hashId: ${introHashId}`)

  // Set introduction section status to loading before making the API call
  updateSectionStatus(introHashId, 'loading', 0)

  let introResultSummary = ''
  const introResultPromise = streamSection({
    endpoint: '/api/report/introduction',
    queryParams: introQueryParams,
    hashId: introHashId,
    includeDisclosures,
    updateSectionStatus,
    abortSignal,
  }).then(
    result => {
      if (result.text) {
        introResultSummary = result.text
      }
      return result
    },
  )

  // Determine which categories have content based on flags and model sections
  // Use categorizeSections to properly categorize sections by level
  const {
    environmental: environmentalSections,
    social: socialSections,
    governance: governanceSections,
  } = categorizeSections(modelSections)

  console.log(`[DEBUG] Categorized sections:`, {
    environmentalCount: environmentalSections.length,
    socialCount: socialSections.length,
    governanceCount: governanceSections.length,
  })

  const hasEnvironmental = environmentalSections.length > 0 && flags.some(flag =>
    environmentalSections.some(section => section.section === flag.model.model_sections[modelName]),
  )

  const hasSocial = socialSections.length > 0 && flags.some(flag =>
    socialSections.some(section => section.section === flag.model.model_sections[modelName]),
  )

  const hasGovernance = governanceSections.length > 0 && flags.some(flag =>
    governanceSections.some(section => section.section === flag.model.model_sections[modelName]),
  )

  console.log(`[DEBUG] Has sections with content:`, {
    hasEnvironmental,
    hasSocial,
    hasGovernance,
  })


// Process environmental sections
  let environmentalSectionText: string | null = null
  let environmentalSectionSummary: string | null = null
  let environmentalSummaryPromise: Promise<any> | null = null
  if (hasEnvironmental) {
    environmentalSummaryPromise = processSectionsWithSubsections({
      entityName,
      modelName,
      runId,
      sectionType: 'environmental',
      sections: environmentalSections,
      flags,
      modelSections,
      includeDisclosures,
      updateSectionStatus,
      onSectionReady,
      reportContent: '',
      abortSignal,
      entity,
      run,
    }).then(result => {
      if (result.summary) {
        environmentalSectionText = result.reportContent
        environmentalSectionSummary = result.summary.summary!
      }
      return result
    })

  } else {
    console.log(`[DEBUG] Skipping environmental sections (hasEnvironmental: ${hasEnvironmental})`)
  }

// Process social sections
  let socialSectionText: string | null = null
  let socialSectionSummary: string | null = null
  let socialSummaryPromise: Promise<any> | null = null
  if (hasSocial) {
    socialSummaryPromise = processSectionsWithSubsections({
      entityName,
      modelName,
      runId,
      sectionType: 'social',
      sections: socialSections,
      flags,
      modelSections,
      includeDisclosures,
      updateSectionStatus,
      onSectionReady,
      reportContent: '',
      abortSignal,
      entity,
      run,
    }).then(result => {
      if (result.summary) {
        socialSectionText = result.reportContent
        socialSectionSummary = result.summary.summary!
      }
      return result
    })

  } else {
    console.log(`[DEBUG] Skipping social sections (hasSocial: ${hasSocial})`)
  }

// Process governance sections
  let governanceSectionText: string | null = null
  let governanceSectionSummary: string | null = null
  let governanceSummaryPromise: Promise<any> | null = null
  if (hasGovernance) {
    governanceSummaryPromise = processSectionsWithSubsections({
      entityName,
      modelName,
      runId,
      sectionType: 'governance',
      sections: governanceSections,
      flags,
      modelSections,
      includeDisclosures,
      updateSectionStatus,
      onSectionReady,
      reportContent: '',
      abortSignal,
      entity,
      run,
    }).then(result => {
      if (result.summary) {
        governanceSectionText = result.reportContent
        governanceSectionSummary = result.summary.summary!
      }
      return result
    })

  } else {
    console.log(`[DEBUG] Skipping governance sections (hasGovernance: ${hasGovernance})`)
  }

  // Process reliability section
  let reliabilitySummary: string | null = null
  const reliabilityHashId = `reliability-${entityName}-${modelName}-${runId}`
  const reliabilityQueryParams = new URLSearchParams({
    entity,
    run,
    model: modelName,
    includeDisclosures: includeDisclosures.toString(),
  })

  console.log(`Starting reliability section with hashId: ${reliabilityHashId}`)

  // Set reliability section status to loading before making the API call
  updateSectionStatus(reliabilityHashId, 'loading', 0)


  const reliabilityResultPromise = streamSection({
    endpoint: '/api/report/reliability',
    queryParams: reliabilityQueryParams,
    hashId: reliabilityHashId,
    includeDisclosures,
    updateSectionStatus,
    abortSignal,
  }).then((reliabilityResult) => {
    if (reliabilityResult.text) {
      reliabilitySummary = reliabilityResult.text
    }
  })


  // Process transparency section
  let transparencySummary: string | null = null
  const transparencyHashId = `transparency-${entityName}-${modelName}-${runId}`
  const transparencyQueryParams = new URLSearchParams({
    entity,
    run,
    model: modelName,
    includeDisclosures: includeDisclosures.toString(),
  })

  console.log(`Starting transparency section with hashId: ${transparencyHashId}`)

  // Set transparency section status to loading before making the API call
  updateSectionStatus(transparencyHashId, 'loading', 0)

  const transparencyResultPromise = streamSection({
    endpoint: '/api/report/transparency',
    queryParams: transparencyQueryParams,
    hashId: transparencyHashId,
    includeDisclosures,
    updateSectionStatus,
    abortSignal,
  }).then((transparencyResult) => {
    if (transparencyResult.text) {
      transparencySummary = transparencyResult.text
      // Call the onSectionReady callback if provided

    }
  })


  let reportContent = ''
  for (const promise of [
    introResultPromise,
    environmentalSummaryPromise,
    socialSummaryPromise,
    governanceSummaryPromise,
    reliabilityResultPromise,
    transparencyResultPromise,
  ]) {
    promise?.then(() => {
      reportContent = (introResultSummary || '') + (environmentalSectionText || '') + (socialSectionText || '') + (governanceSectionText || '') + (reliabilitySummary || '') + (transparencySummary || '')
      if (onSectionReady) {
        // Use queueMicrotask to defer the callback without React-specific issues
        queueMicrotask(() => {
          onSectionReady(reportContent)
        })
      }
    })
  }


  await Promise.all([
    introResultPromise,
    environmentalSummaryPromise,
    socialSummaryPromise,
    governanceSummaryPromise,
    reliabilityResultPromise,
    transparencyResultPromise,
  ])

  reportContent = `

<report-section id="intro" title="Introduction">
${introResultSummary || ''}
</report-section>

<report-section id="environmental" title="Ecological Impact">
${environmentalSectionText || ''}
</report-section>

<report-section id="social" title="Social Impact">
${socialSectionText || ''}
</report-section>

<report-section id="governance" title="Governance Impact">
${governanceSectionText || ''}
</report-section>

<report-section id="reliability" title="Reliability Analysis">
${reliabilitySummary || ''}
</report-section>

<report-section id="transparency" title="Transparency Analysis">
${transparencySummary || ''}
</report-section>
  `

  if (onSectionReady) {
    // Use queueMicrotask to defer the callback without React-specific issues
    queueMicrotask(() => {
      onSectionReady(reportContent)
    })
  }
  // Check if we can generate an executive summary
  const executiveReady =
    (!hasSocial || socialSectionSummary) &&
    (!hasGovernance || governanceSectionSummary) &&
    (!hasEnvironmental || environmentalSectionSummary) &&
    reliabilitySummary &&
    transparencySummary

  console.log(`[DEBUG] Executive summary ready: ${executiveReady}`, {
    hasSocial,
    socialSummaryExists: !!socialSectionSummary,
    hasGovernance,
    governanceSummaryExists: !!governanceSectionSummary,
    hasEnvironmental,
    environmentalSummaryExists: !!environmentalSectionSummary,
    reliabilitySummaryExists: !!reliabilitySummary,
    transparencySummaryExists: !!transparencySummary,
  })

  if (executiveReady) {
    const execSummaryHashId = `exec-summary-${entityName}-${modelName}-${runId}`
    const execSummaryQueryParams = new URLSearchParams({
      entity,
      run,
      model: modelName,
      includeDisclosures: includeDisclosures.toString(),
    })

    if (environmentalSectionSummary) execSummaryQueryParams.set('environmental', truncate(environmentalSectionSummary, 2000)!)
    if (socialSectionSummary) execSummaryQueryParams.set('social', truncate(socialSectionSummary, 2000)!)
    if (governanceSectionSummary) execSummaryQueryParams.set('governance', truncate(governanceSectionSummary, 2000)!)
    if (reliabilitySummary) execSummaryQueryParams.set('reliability', truncate(reliabilitySummary, 2000)!)
    if (transparencySummary) execSummaryQueryParams.set('transparency', truncate(transparencySummary, 2000)!)

    console.log(`[DEBUG] Executive summary query params:`, execSummaryQueryParams.toString())
    console.log(`Starting executive summary section with hashId: ${execSummaryHashId}`)

    // Set executive summary section status to loading before making the API call
    updateSectionStatus(execSummaryHashId, 'loading', 10)

    const execSummaryResult = await streamSection({
      endpoint: '/api/report/overall',
      queryParams: execSummaryQueryParams,
      hashId: execSummaryHashId,
      includeDisclosures,
      updateSectionStatus,
      preprocess: processMarkdown,
      abortSignal,
    })
    updateSectionStatus(execSummaryHashId, 'completed', 100)

    if (execSummaryResult.text) {
      reportContent = `<report-section id="executive-summary" title="Executive Summary">${execSummaryResult.text}\n\n</report-section>${reportContent}`
      // Call the onSectionReady callback if provided
      if (onSectionReady) {
        // Use queueMicrotask to defer the callback without React-specific issues
        queueMicrotask(() => {
          onSectionReady(reportContent)
        })
      }
    }
  }

  return reportContent
}


/**
 * Process sections with subsections using promises for parallel processing
 * Uses caching to avoid redundant processing
 * @param options Options for processing sections
 * @returns Promise with the section content and summary
 */
async function processSectionsWithSubsections(
  options: {
    entityName: string;
    modelName: string;
    runId: number;
    sectionType: 'environmental' | 'social' | 'governance';
    sections: any[];
    flags: any[];
    modelSections: any[];
    includeDisclosures: boolean;
    updateSectionStatus: (id: string, status: SectionStatus, progress?: number, error?: string, parentId?: string, name?: string) => void;
    onSectionReady?: (currentContent: string) => void;
    reportContent: string;
    abortSignal?: AbortSignal;
    entity: string;
    run: string;
  },
): Promise<{
  content: string;
  summary: { summary: string; model_sections: Record<string, string> } | null;
  reportContent: string
}> {
  const {
    entityName,
    modelName,
    runId,
    sectionType,
    sections,
    flags,
    modelSections,
    includeDisclosures,
    updateSectionStatus,
    onSectionReady,
    reportContent,
    abortSignal,
    entity,
    run,
  } = options

  // Capitalize first letter for display
  const sectionTypeCapitalized = sectionType.charAt(0).toUpperCase() + sectionType.slice(1)

  console.log(`[DEBUG] Processing ${sectionType} sections (${sections.length} sections)`)
  const sectionHashId = `${sectionType}-${entityName}-${modelName}-${runId}`
  let contentArray: string[] = []
  let updatedReportContent = reportContent
  let sectionSummaries: Record<string, string> = {}

  // Register the main section
  updateSectionStatus(sectionHashId, 'loading', 10)

  const promises: Promise<any>[] = []

  function extractNamesForSection(section: any) {
    const sectionId = section.section

    // Look up the section title from modelSections, fallback to formatted section ID
    const modelSection = modelSections.find((ms: any) => ms.section === sectionId)
    const sectionName = modelSection?.title || sectionId.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())

    const sectionFlags = flags.filter(flag =>
      flag.model.model_sections[modelName] === sectionId,
    )

    console.log(`[DEBUG] ${sectionTypeCapitalized} section ${sectionId} has ${sectionFlags.length} flags`)


    const subsectionHashId = hash64([sectionHashId, sectionId])
    return { sectionId, sectionName, sectionFlags, subsectionHashId }
  }

  for (const section of sections) {
    const { sectionId, sectionName, sectionFlags, subsectionHashId } = extractNamesForSection(section)

    if (sectionFlags.length === 0) continue
    // Register this subsection with the parent section
    if (updateSectionStatus) {
      updateSectionStatus(subsectionHashId, 'pending', 0, undefined, sectionHashId, sectionName)
    }
  }

// Process each subsection in parallel
  for (const section of sections) {
    const { sectionId, sectionName, sectionFlags, subsectionHashId } = extractNamesForSection(section)

    if (sectionFlags.length === 0) continue
    // Register this subsection with the parent section

    const sectionQueryParams = new URLSearchParams({
      entity,
      run,
      model: modelName,
      sectionId,
      includeDisclosures: includeDisclosures.toString(),
    })

    // Add promise to array for parallel processing
    promises.push(
      streamSection({
        endpoint: '/api/report/model-section',
        queryParams: sectionQueryParams,
        hashId: subsectionHashId,
        includeDisclosures,
        updateSectionStatus,
        parentId: sectionHashId,
        preprocess: processMarkdown,
        sectionName,
        abortSignal,
      }).then(result => {
        if (result.text) {
          // Store the result at the index corresponding to the section's position with a subsection header
          const index = sections.findIndex(s => s.section === sectionId)
          contentArray[index] = `<report-sub-section id="${sectionType}-${sectionId}" title="${sectionName}">${result.text}</report-sub-section>`
          // Also store the section content in the sectionSummaries object for the category API
          sectionSummaries[sectionId] = result.text
          console.log(`[DEBUG] Added content for ${sectionType} section ${sectionId}`)
        }
        return result
      }))

    // Reduce concurrency levels, every 4 promises, wait for them to complete
    if (promises.length % 4 === 3) {
      await Promise.all(promises.slice(promises.length - 4, promises.length))
    }
  }

  // Wait for all promises to resolve
  await Promise.all(promises)

  // Combine all section content, filtering out any undefined entries
  const sectionContent = contentArray.filter(Boolean).join('\n\n')

  let summary = null

  if (sectionContent && Object.keys(sectionSummaries).length > 0) {
    // Now call the category API to get a summary for all sections
    const categorySummaryHashId = `${sectionType}-summary-${entityName}-${modelName}-${runId}`

    console.log(`[DEBUG] Generating ${sectionType} category summary with ${Object.keys(sectionSummaries).length} sections`)

    // Create query params for the category API
    const categorySummaryQueryParams = new URLSearchParams({
      entity,
      run,
      model: modelName,
      includeDisclosures: includeDisclosures.toString(),
      sectionSummaries: JSON.stringify(sectionSummaries),
    })

    // Update the main section status
    updateSectionStatus(sectionHashId, 'summarizing', 90)

    // Call the category API
    const categorySummaryResult = await streamSection({
      endpoint: `/api/report/category/${sectionType}`,
      queryParams: categorySummaryQueryParams,
      hashId: categorySummaryHashId,
      includeDisclosures,
      updateSectionStatus,
      abortSignal,
      preprocess: processMarkdown,
    })

    if (categorySummaryResult.text) {
      // Add the category summary to the report
      // Use "Ecological" instead of "Environmental" for display
      updatedReportContent = categorySummaryResult.text + '\n\n' + sectionContent

      // Create a summary object with both the category summary and the section summaries
      summary = {
        summary: categorySummaryResult.text,
        model_sections: sectionSummaries,
      }

      console.log(`[DEBUG] Added ${sectionTypeCapitalized} Impact section with category summary to report`)
    }
    // Update the main section status
    updateSectionStatus(sectionHashId, 'completed', 100)

    // Call the onSectionReady callback if provided
    if (onSectionReady) {
      // Use queueMicrotask to defer the callback without React-specific issues
      queueMicrotask(() => {
        onSectionReady(updatedReportContent)
      })
    }
  } else {
    console.log(`[DEBUG] No content generated for ${sectionTypeCapitalized} Impact section`)
    // Mark the section as completed but with 0 words
    updateSectionStatus(sectionHashId, 'completed', 100)
  }

  // Create the result object
  const result = {
    content: sectionContent,
    summary,
    reportContent: updatedReportContent,
  }

  return result
}
