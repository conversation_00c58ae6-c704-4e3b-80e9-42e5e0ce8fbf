'use client'

import React from 'react'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CitationType } from '@/components/citation'
import { cn } from '@utils/lib/utils'
import { TransparencySectionProps } from './types'

export function TransparencySection({
  summary,
  cherry_picking,
  citations,
  className,
  inlineCitations = true,
  citationPrefix = 'trans'
}: TransparencySectionProps) {
  console.log('TransparencySection received:', {
    summary,
    cherry_picking
  });

  // Default values if data is missing
  const cherryPickingData = cherry_picking || { summary: '', examples: [] };
  const summaryText = summary || '';

  return (
    <section id="transparency" className={cn("mb-12 print-section", className)}>
      <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-slate-200 dark:border-slate-700">
        Transparency
      </h2>

      {/* Main transparency analysis */}
      <div className="prose prose-slate dark:prose-invert mb-8">
        <EkoMarkdown
          className={className}
          citationPrefix={`${citationPrefix}.s.`}
          citations={citations}
          inlineCitations={inlineCitations}
          admin={false}
        >
          {summaryText}
        </EkoMarkdown>
      </div>

      {/* Selective Highlighting Analysis */}
      <div>
        <h3 className="text-xl font-semibold mb-4">Selective Highlighting Analysis</h3>
        <div className="prose prose-slate dark:prose-invert">
          <EkoMarkdown
            className={className}
            citationPrefix={`${citationPrefix}.c.`}
            citations={citations}
            inlineCitations={inlineCitations}
            admin={false}
          >
            {cherryPickingData.summary}
          </EkoMarkdown>
        </div>

        {cherryPickingData.examples.length > 0 && (
          <>
            <h4 className="text-lg font-medium mt-6 mb-3">Key Examples</h4>
            <ul className="list-disc pl-6 space-y-2">
              {cherryPickingData.examples.map((example, index) => (
                <li key={index} className="prose prose-slate dark:prose-invert">
                  <EkoMarkdown
                    className={className}
                    citationPrefix={`${citationPrefix}.c.e${index+1}.`}
                    citations={citations}
                    inlineCitations={inlineCitations}
                    admin={false}
                  >
                    {example}
                  </EkoMarkdown>
                </li>
              ))}
            </ul>
          </>
        )}
      </div>
    </section>
  )
}
