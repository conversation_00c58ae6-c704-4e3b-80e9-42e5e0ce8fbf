'use client'

import React from 'react'
import { CompactCitation, reduceCitations } from '@/components/citation'
import { Skeleton } from '@ui/components/ui/skeleton'
import { ReferencesProps } from './types'

export function ReferencesSection({
  citations
}: ReferencesProps) {
  // Prepare citations
  const reducedCitations = reduceCitations(citations).sort((a, b) => a.title > b.title ? 1 : 0)

  // Remove loading state since text is streamed

  return (
    <section id="references" className="mb-12 print-section">
      <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-slate-200 dark:border-slate-700">
        References
      </h2>
      <div className="space-y-3 mt-4">
        {reducedCitations.map((citation, index) => (
          <div
            key={citation.doc_id}
            className="flex gap-2 text-sm border-b border-slate-100 dark:border-slate-800 pb-2"
          >
            <span className="font-mono text-slate-500 dark:text-slate-400 min-w-[2rem]">
              [{index + 1}]
            </span>
            <CompactCitation data={citation} admin={false} />
          </div>
        ))}
      </div>
    </section>
  )
}
