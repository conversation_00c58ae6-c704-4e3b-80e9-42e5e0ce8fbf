'use client'

import { FlagTypeV2, ModelSectionType } from '@/types'
import { ClaimTypeV2 } from '@/types/claim'
import { PromiseTypeV2 } from '@/types/promise'
import { CherryTypeV2 } from '@/types/cherry'
import { CategorySummary, OverallSummary, ReliabilitySummary, TransparencySummary } from './types'

interface UseReportDataProps {
  entityName: string
  entityDescription: string
  modelName: string
  flags: FlagTypeV2[]
  modelSections: ModelSectionType[]
  claims: ClaimTypeV2[]
  promises: PromiseTypeV2[]
  cherryData: CherryTypeV2[]
}

interface UseReportDataResult {
  introduction: string
  environmentalSummary: CategorySummary | null
  socialSummary: CategorySummary | null
  governanceSummary: CategorySummary | null
  reliabilitySummary: ReliabilitySummary | null
  transparencySummary: TransparencySummary | null
  overallSummary: OverallSummary | null
  sectionContent: Record<string, string>
  sectionNames: Record<string, string>
}
