'use client'

import React from 'react'
import { TableOfContentsProps } from './types'
import { categorizeSections } from '@/utils/report-utils'

export function TableOfContents({
  hasEnvironmental,
  hasSocial,
  hasGovernance,
  modelSections = [],
  modelName = '',
  flags = []
}: TableOfContentsProps) {
  // Get categorized sections if available
  const { environmental, social, governance } = modelSections.length > 0 ?
    categorizeSections(modelSections) : { environmental: [], social: [], governance: [] };

  // Helper function to get sections with content
  const getSectionsWithContent = (categorySections: any[]) => {
    if (!modelName || !flags.length) return [];

    return categorySections.filter(section => {
      const sectionId = section.section;
      return flags.some(flag => flag.model.model_sections[modelName] === sectionId);
    });
  };

  // Get sections with content for each category
  const environmentalSections = hasEnvironmental ? getSectionsWithContent(environmental) : [];
  const socialSections = hasSocial ? getSectionsWithContent(social) : [];
  const governanceSections = hasGovernance ? getSectionsWithContent(governance) : [];

  return (
    <div className="my-8 p-6 border border-slate-200 dark:border-slate-700 rounded-lg print:mt-12 print:border-none">
      <h2 className="mt-0 mb-6 print:mb-8 print:text-2xl">Table of Contents</h2>
      <ul className="list-decimal pl-5 space-y-2 print:space-y-3">
        <li>
          <a
            href="#executive-summary"
            className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
          >
            Executive Summary
          </a>
        </li>
        <li>
          <a
            href="#introduction"
            className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
          >
            Introduction
          </a>
        </li>
        <li>
          <a
            href="#harm-benefit"
            className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
          >
            Harm and Benefit Analysis
          </a>
          <ul className="list-decimal pl-5 mt-2 mb-2 space-y-1">
            {hasEnvironmental && (
              <li>
                <a
                  href="#environmental"
                  className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
                >
                  Ecological Impact
                </a>
                {environmentalSections.length > 0 && (
                  <ul className="list-decimal pl-5 mt-1 mb-1 space-y-1">
                    {environmentalSections.map(section => (
                      <li key={section.section}>
                        <a
                          href={`#environmental-${section.section}`}
                          className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
                        >
                          {section.title || section.section}
                        </a>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            )}
            {hasSocial && (
              <li>
                <a
                  href="#social"
                  className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
                >
                  Social Impact
                </a>
                {socialSections.length > 0 && (
                  <ul className="list-decimal pl-5 mt-1 mb-1 space-y-1">
                    {socialSections.map(section => (
                      <li key={section.section}>
                        <a
                          href={`#social-${section.section}`}
                          className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
                        >
                          {section.title || section.section}
                        </a>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            )}
            {hasGovernance && (
              <li>
                <a
                  href="#governance"
                  className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
                >
                  Governance Impact
                </a>
                {governanceSections.length > 0 && (
                  <ul className="list-decimal pl-5 mt-1 mb-1 space-y-1">
                    {governanceSections.map(section => (
                      <li key={section.section}>
                        <a
                          href={`#governance-${section.section}`}
                          className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
                        >
                          {section.title || section.section}
                        </a>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            )}
          </ul>
        </li>
        <li>
          <a
            href="#reliability"
            className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
          >
            Reliability Analysis
          </a>
        </li>
        <li>
          <a
            href="#transparency"
            className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
          >
            Transparency Analysis
          </a>
        </li>
        <li>
          <a
            href="#references"
            className="no-underline hover:underline text-slate-700 dark:text-slate-300 print:text-black"
          >
            References
          </a>
        </li>
      </ul>
    </div>
  )
}
