'use client'

import React from 'react'
import { ReportHeaderProps } from './types'

export function ReportHeader({ entityName, modelName }: ReportHeaderProps) {
  return (
    <header className="text-center mb-12 pb-4 border-b border-slate-200 dark:border-slate-700 print:mb-16 print:mt-8">
      <h1 className="mb-4 mt-0 print:text-4xl">Ecological, Social and Governance Report</h1>
      <h2 className="mt-0 mb-6 text-slate-700 dark:text-slate-300 print:text-3xl print:text-black">{entityName}</h2>
      <div className="text-sm text-slate-500 dark:text-slate-400 flex flex-col gap-2 mt-6 print:text-black print:mt-8">
        <p className="print:text-base">{new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
        <p className="print:text-base">Analysis Model: {modelName}</p>
        <p className="print:text-base">Prepared by: Eko Intelligence</p>
      </div>
    </header>
  )
}
