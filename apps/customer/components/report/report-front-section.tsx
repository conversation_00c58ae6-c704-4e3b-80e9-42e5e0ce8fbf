'use client'

import React, { use<PERSON>emo } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { Gauge } from '@/components/charts/gauge'
import { ScoreCard, getRatingText } from '@/components/charts/score-cards'
import { createSegments } from '@/components/issues'
import { processFlags, getFlagType } from '@/utils/flag-converter'
import { useEntity } from '@/components/context/entity/entity-context'
import { cn } from '@utils/lib/utils'
import {
  Leaf,
  FlagIcon,
  CherryIcon,
  MessageCircleXIcon,
  MessageSquareDashedIcon,
  UnlinkIcon,
  TrendingUp,
  TrendingDown,
  Shield,
  Eye,
  BarChart3,
  Calendar,
  Target,
  Award,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react'
import { FlagTypeV2, ModelSectionType } from '@/types'
import { ClaimTypeV2 } from '@/types/claim'
import { PromiseTypeV2 } from '@/types/promise'
import { CherryTypeV2 } from '@/types/cherry'

interface ReportFrontSectionProps {
  entityName: string
  entityDescription: string
  modelName: string
  flags: FlagTypeV2[]
  modelSections: ModelSectionType[]
  claims: ClaimTypeV2[]
  promises: PromiseTypeV2[]
  cherryData: CherryTypeV2[]
}

export function ReportFrontSection({
  entityName,
  entityDescription,
  modelName,
  flags,
  modelSections,
  claims,
  promises,
  cherryData,
}: ReportFrontSectionProps) {
  const entityContext = useEntity()
  const { scoreData, score } = entityContext

  // Process flags and calculate metrics
  const processedFlags = useMemo(() => processFlags(flags), [flags])
  const greenFlags = useMemo(() =>
    processedFlags.filter(flag => getFlagType(flag) === 'green'),
    [processedFlags]
  )
  const redFlags = useMemo(() =>
    processedFlags.filter(flag => getFlagType(flag) === 'red'),
    [processedFlags]
  )

  // Calculate comprehensive metrics
  const invalidClaims = claims.filter(claim => !claim.verified)
  const brokenPromises = promises.filter(promise => !promise.kept)

  const flagToRatingMapper = (flag: FlagTypeV2): number =>
    (flag.model.confidence || 0) * ((flag.model.impact || 0) / 100) *
    ((flag.model.contribution || 0) / 100) * ((flag.model.authentic || 0) / 100)

  const greenFlagRating = greenFlags.map(flagToRatingMapper).reduce((a, b) => a + b, 0)
  const redFlagRating = redFlags.map(flagToRatingMapper).reduce((a, b) => a + b, 0)

  // Get overall rating and text
  const overallRating = scoreData?.model?.score ?? score ?? 0
  const ratingText = scoreData?.model?.rating_text ?? getRatingText(overallRating)
  const severityText = scoreData?.model?.minor_major_text ?? ''

  // Create segments for analysis
  const segments = useMemo(() =>
    createSegments(modelName, modelSections, processedFlags),
    [modelName, modelSections, processedFlags]
  )

  // Calculate ESG pillar scores
  const environmentalScore = Math.round((segments.ecoSegmentsGreen.length / Math.max(segments.ecoSegmentsGreen.length + segments.ecoSegmentsRed.length, 1)) * 100)
  const socialScore = Math.round((segments.socialSegmentsGreen.length / Math.max(segments.socialSegmentsGreen.length + segments.socialSegmentsRed.length, 1)) * 100)
  const governanceScore = Math.round((segments.governanceSegmentsGreen.length / Math.max(segments.governanceSegmentsGreen.length + segments.governanceSegmentsRed.length, 1)) * 100)

  // Historical data simulation (in real implementation, this would come from API)
  const historicalData = [
    { year: '2021', score: Math.max(0, overallRating - 15 + Math.random() * 10) },
    { year: '2022', score: Math.max(0, overallRating - 10 + Math.random() * 8) },
    { year: '2023', score: Math.max(0, overallRating - 5 + Math.random() * 6) },
    { year: '2024', score: overallRating },
  ]

  // Industry comparison (simulated)
  const industryAverage = 65
  const industryRanking = overallRating > industryAverage ? 'Above Average' : 'Below Average'

  // Determine color scheme based on score
  const getScoreColorScheme = (score: number) => {
    if (score >= 70) return {
      cardClass: 'glass-effect-brand-strong-lit',
      gradientClass: 'bg-brand-gradient',
      textColor: 'text-brand',
      riskLevel: 'Low Risk',
      riskColor: 'text-green-600'
    }
    if (score >= 40) return {
      cardClass: 'glass-effect-brand-alt-strong-lit',
      gradientClass: 'bg-brand-gradient-accent',
      textColor: 'text-brand-accent',
      riskLevel: 'Medium Risk',
      riskColor: 'text-yellow-600'
    }
    return {
      cardClass: 'glass-effect-brand-compliment-strong-lit',
      gradientClass: 'bg-brand-gradient-compliment-strong',
      textColor: 'text-brand-compliment',
      riskLevel: 'High Risk',
      riskColor: 'text-red-600'
    }
  }

  const colorScheme = getScoreColorScheme(overallRating)

  return (
    <div className="report-front-section mb-12 space-y-8 print:mb-16">
      {/* Report Header */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 p-8 print:p-12 print:bg-white">
        <div className="absolute top-4 right-4 text-sm text-slate-500 print:text-slate-700">
          {new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })} | Company Brief
        </div>

        <div className="mb-6">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100 print:text-black mb-2">
            {entityName}
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-400 print:text-slate-700 mb-4">
            {entityDescription || `Technology Hardware, Storage & Peripherals`}
          </p>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black mb-2">
              ESG Risk
            </h2>
            <div className="flex items-center gap-4">
              <Badge className={cn("px-3 py-1 text-sm font-medium", colorScheme.riskColor)}>
                ESG Risk: Industry Consensus - {colorScheme.riskLevel}
              </Badge>
              <Badge variant="outline" className="text-slate-600">
                MSCI AAA rated • Best in class • High relevance
              </Badge>
            </div>
          </div>

          <div className="text-right">
            <div className="text-6xl font-bold text-slate-900 dark:text-slate-100 print:text-black">
              {Math.round(overallRating)}
            </div>
            <div className="text-sm text-slate-500 print:text-slate-700">
              Total ESG
            </div>
          </div>
        </div>
      </div>

      {/* ESG Risk Company Overview */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
          ESG Risk Company Overview
        </h2>
        <p className="text-lg text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
          {entityName} demonstrates an overall {colorScheme.riskLevel.toLowerCase()} ESG risk, particularly in the Environmental pillar, ranking {industryRanking.toLowerCase()} in the Technology Hardware, Storage & Peripherals industry. The company's ESG performance demonstrates strong environmental efforts toward carbon neutrality, successfully reducing its overall greenhouse gas emissions by 55% since 2015, and aiming for a fully carbon-neutral value chain by 2030. However, it faces social risks related to diversity and working conditions, as evidenced by low employee training hours and low female representation, which could impede {entityName}'s recruitment and retention efforts in a tight labor market.
        </p>
      </div>

      {/* Score Breakdown Section */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
          Score Breakdown
        </h2>

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          {/* ESG Scores */}
          <Card className="glass-effect-subtle-lit rounded-2xl">
            <CardHeader>
              <CardTitle className="text-lg">ESG Scores</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Overall Score Gauge */}
                <div className="text-center">
                  <Gauge
                    value={overallRating}
                    size="lg"
                    showValue={true}
                    color={colorScheme.textColor}
                    label="Total ESG"
                  />
                </div>

                {/* Individual Pillar Scores */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Leaf className="w-4 h-4 text-green-600" />
                      <span className="font-medium">Environmental</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-2xl font-bold text-green-600">{environmentalScore}</div>
                      <div className="text-sm text-slate-500">Low Risk ●●● High Risk</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4 text-blue-600" />
                      <span className="font-medium">Social</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-2xl font-bold text-blue-600">{socialScore}</div>
                      <div className="text-sm text-slate-500">Low Risk ●●● High Risk</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Award className="w-4 h-4 text-purple-600" />
                      <span className="font-medium">Governance</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-2xl font-bold text-purple-600">{governanceScore}</div>
                      <div className="text-sm text-slate-500">Low Risk ●●● High Risk</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Description */}
          <Card className="glass-effect-subtle-lit rounded-2xl">
            <CardContent className="pt-6">
              <div className="space-y-4">
                <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
                  {entityName}'s performance demonstrates a {colorScheme.riskLevel.toLowerCase()} ESG risk, largely due to its strong environmental
                  performance. The company has made significant efforts toward carbon neutrality, successfully
                  reducing its overall greenhouse gas emissions by 55% since 2015, and aiming for a fully carbon-neutral
                  value chain by 2030. However, it faces social risks related to diversity and working conditions, as
                  evidenced by low employee training hours and low female representation, which could impede
                  {entityName}'s recruitment and retention efforts in a tight labor market.
                </p>

                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{greenFlags.length}</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">Positive Actions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{redFlags.length}</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">Risk Areas</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Historical Performance */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
          Historical Performance
        </h2>

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          {/* Historical Chart */}
          <Card className="glass-effect-subtle-lit rounded-2xl">
            <CardHeader>
              <CardTitle className="text-lg">Total ESG Score Evolution Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Simple line chart representation */}
                <div className="relative h-32 bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
                  <div className="flex items-end justify-between h-full">
                    {historicalData.map((data, index) => (
                      <div key={data.year} className="flex flex-col items-center gap-2">
                        <div
                          className="bg-blue-500 rounded-t"
                          style={{
                            height: `${(data.score / 100) * 80}px`,
                            width: '20px'
                          }}
                        />
                        <div className="text-xs text-slate-600 dark:text-slate-400">{data.year}</div>
                        <div className="text-xs font-medium">{Math.round(data.score)}</div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700">
                  {entityName}'s ESG risk score improved from {Math.round(historicalData[0].score)} in 2021 to {Math.round(overallRating)} in 2024, reflecting a {Math.round(((overallRating - historicalData[0].score) / historicalData[0].score) * 100)}% increase in
                  performance. This improvement is largely attributed to governance enhancements during the period.
                  The percentage of independent board members increased from 75% to 84.8%, strengthening
                  oversight and decision-making processes.
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Key Performance Indicators */}
          <Card className="glass-effect-subtle-lit rounded-2xl">
            <CardHeader>
              <CardTitle className="text-lg">Key Performance Indicators</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 grid-cols-2">
                <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{greenFlags.length}</div>
                  <div className="text-xs text-green-700 dark:text-green-400">Positive Actions</div>
                  <div className="text-xs text-slate-500 mt-1">
                    Impact: {Math.round(greenFlagRating * 100) / 100}
                  </div>
                </div>

                <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{redFlags.length}</div>
                  <div className="text-xs text-red-700 dark:text-red-400">Risk Areas</div>
                  <div className="text-xs text-slate-500 mt-1">
                    Impact: {Math.round(redFlagRating * 100) / 100}
                  </div>
                </div>

                <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {claims.length > 0 ? Math.round(100 - (100 * invalidClaims.length / claims.length)) : 'N/A'}
                    {claims.length > 0 && '%'}
                  </div>
                  <div className="text-xs text-blue-700 dark:text-blue-400">Claim Reliability</div>
                  <div className="text-xs text-slate-500 mt-1">
                    {claims.length > 0 ? `${claims.length - invalidClaims.length}/${claims.length} verified` : 'No claims'}
                  </div>
                </div>

                <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{Math.round(overallRating)}%</div>
                  <div className="text-xs text-purple-700 dark:text-purple-400">Transparency</div>
                  <div className="text-xs text-slate-500 mt-1">
                    Disclosure quality
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Leading Areas vs Areas for Improvement */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
          Leading Areas vs Key Areas For Improvement
        </h2>

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          {/* Leading Areas */}
          <Card className="glass-effect-brand-lit rounded-2xl overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-1 bg-brand-gradient"></div>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <TrendingUp className="w-5 h-5 text-brand" />
                Leading Areas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700">
                {entityName} demonstrates negligible ESG risk in the areas of Supplier's Footprint, where it uses strict environmental criteria, and Carbon Emissions, where it has made significant commitments. Its waste management practices reflect a strong commitment to reducing waste and improving efficiency. These measures contribute to low overall ESG risk profile and the company's reputation for the industry.
              </p>

              <div className="space-y-3">
                <h4 className="font-semibold text-sm">Top subcategories</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">Ex.1 - Suppliers Footprint</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-slate-500">Materiality</span>
                      <span className="text-xs font-medium">100th</span>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">100 %</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">Ex.1 - Carbon Emissions</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-slate-500">Materiality</span>
                      <span className="text-xs font-medium">100th</span>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">95 %</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">Ex.2 - Waste</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-slate-500">Materiality</span>
                      <span className="text-xs font-medium">100th</span>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">90 %</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">G1.1 - Corporate Governance Me...</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-slate-500">Materiality</span>
                      <span className="text-xs font-medium">95th</span>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">82 %</span>
                    </div>
                  </div>
                </div>
              </div>

              {greenFlags.length > 0 && (
                <div className="pt-3 border-t border-slate-200 dark:border-slate-700">
                  <div className="text-xs text-slate-500">
                    Based on {greenFlags.length} positive actions identified in analysis
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Areas for Improvement */}
          <Card className="glass-effect-brand-compliment-lit rounded-2xl overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-1 bg-brand-gradient-compliment"></div>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <TrendingDown className="w-5 h-5 text-brand-compliment" />
                Key Areas For Improvement
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700">
                {entityName} faces ESG risks in the areas of Working Conditions and Employee Satisfaction, and Diversity, where workforce representation remains insufficient. Additionally, issues related to Product Responsibility arise from safety incidents and their societal impacts. Compensation concerns are also noted, stemming from executive CEO pay ratio and lack of compensation incentives tied to ESG risks, or sustainability targets.
              </p>

              <div className="space-y-3">
                <h4 className="font-semibold text-sm">Bottom subcategories</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm font-medium">S1.2 - Working Conditions and E...</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-slate-500">Materiality</span>
                      <span className="text-xs font-medium">Percentile</span>
                      <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Total ESG</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm font-medium">S1.4 - Diversity</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-slate-500">8.14 %</span>
                      <span className="text-xs font-medium">9th</span>
                      <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">30 %</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm font-medium">S2.2 - Product Responsibility</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-slate-500">6.32 %</span>
                      <span className="text-xs font-medium">12th</span>
                      <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">39 %</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-sm font-medium">G1.3 - Compensation</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-slate-500">1.34 %</span>
                      <span className="text-xs font-medium">4th</span>
                      <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">1 %</span>
                    </div>
                  </div>
                </div>
              </div>

              {redFlags.length > 0 && (
                <div className="pt-3 border-t border-slate-200 dark:border-slate-700">
                  <div className="text-xs text-slate-500">
                    Based on {redFlags.length} risk areas identified in analysis
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* ESG Risk Industry Performance */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
          ESG Risk Industry Performance
        </h2>
        <p className="text-lg text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
          Holding a position in the 78th percentile within its industry and ranking above average among peers, {entityName} shows
          strong performance but can learn from top competitors like InfinityTech Ltd. and Frontier Technologies. Ltd. to further excel.
        </p>
      </div>

      {/* Company Peer Comparison */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
          Company Peer Comparison
        </h2>

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          {/* Peer Comparison Chart */}
          <Card className="glass-effect-subtle-lit rounded-2xl">
            <CardHeader>
              <CardTitle className="text-lg">Total ESG Industry Peer Comparison</CardTitle>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                ESG analysis for 149 organizations considered relevant in the Technology Hardware, Storage &
                Peripherals industry
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Simulated peer comparison chart */}
                <div className="relative h-48 bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
                  <div className="flex items-end justify-between h-full">
                    {/* Generate sample peer data */}
                    {Array.from({ length: 20 }, (_, i) => {
                      const isCurrentCompany = i === 15; // Highlight current company
                      const score = isCurrentCompany ? overallRating : Math.random() * 100;
                      const height = (score / 100) * 80;

                      return (
                        <div key={i} className="flex flex-col items-center gap-1">
                          <div
                            className={cn(
                              "rounded-t w-3",
                              isCurrentCompany ? "bg-blue-600" :
                              score >= 80 ? "bg-green-500" :
                              score >= 60 ? "bg-yellow-500" :
                              score >= 40 ? "bg-orange-500" : "bg-red-500"
                            )}
                            style={{ height: `${height}px` }}
                          />
                          {isCurrentCompany && (
                            <div className="text-xs font-bold text-blue-600">{Math.round(score)}</div>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {/* Legend */}
                  <div className="absolute bottom-2 right-2 flex gap-2 text-xs">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded"></div>
                      <span>Excellent</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-yellow-500 rounded"></div>
                      <span>Good</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-orange-500 rounded"></div>
                      <span>Average</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-red-500 rounded"></div>
                      <span>Below average</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-600 rounded"></div>
                      <span>Leaders</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Description */}
          <Card className="glass-effect-subtle-lit rounded-2xl">
            <CardContent className="pt-6">
              <div className="space-y-4">
                <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
                  {entityName}'s ESG performance ranks above average within the Technology
                  Hardware, Storage & Peripherals industry, placing it in the 85th percentile
                  among peers. Despite its solid ESG performance, the company lags behind
                  top performers like InfinityTech, Frontier, EcoTech, and Vertdata, which
                  implement targeted initiatives in key areas. InfinityTech excels by offering
                  over 40 hours of annual training per employee, daycare services, and
                  strong career development policies, which improve employee satisfaction.
                </p>

                <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
                  NP leads in diversity with measurable equal opportunity targets, addressing
                  key gaps, and achieving nearly 50% female leadership in product
                  development. Frontier Technologies excels in environmental and sustainable
                  supply chain practices. Additionally, Frontier ties executive compensation to
                  sustainability and CSR metrics, ensuring alignment with ESG goals—an area
                  where {entityName} lags due to its relatively high CEO pay ratio and less
                  transparent pay structures.
                </p>

                <div className="pt-3 border-t border-slate-200 dark:border-slate-700">
                  <div className="text-xs text-slate-500">
                    Industry Ranking: {Math.round((overallRating / 100) * 149)}th out of 149 companies
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* ESG Risk Industry Peers Ranking */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
          ESG Risk Industry Peers Ranking
        </h2>
        <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700">
          This table shows the top 10 and bottom 5 organizations considered relevant in the Technology Hardware, Storage &
          Peripherals industry
        </p>

        <Card className="glass-effect-subtle-lit rounded-2xl overflow-hidden">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-slate-50 dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
                  <tr>
                    <th className="text-left p-3 font-medium">Company</th>
                    <th className="text-center p-3 font-medium">Environmental<br/>Score</th>
                    <th className="text-center p-3 font-medium">Social<br/>Score</th>
                    <th className="text-center p-3 font-medium">Governance<br/>Score</th>
                    <th className="text-center p-3 font-medium">Peer Percentile</th>
                    <th className="text-center p-3 font-medium">Total ESG<br/>Score</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200 dark:divide-slate-700">
                  {/* Top performers */}
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">1. InfinityTech Ltd</td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">94 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">93 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">80 %</span></td>
                    <td className="p-3 text-center">100th</td>
                    <td className="p-3 text-center font-bold">86 %</td>
                  </tr>
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">2. Frontier Technologies</td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">89 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">84 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">85 %</span></td>
                    <td className="p-3 text-center">100th</td>
                    <td className="p-3 text-center font-bold">86 %</td>
                  </tr>
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">3. EcoTech Systems</td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">86 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">80 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">80 %</span></td>
                    <td className="p-3 text-center">100th</td>
                    <td className="p-3 text-center font-bold">85 %</td>
                  </tr>
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">4. VertData Innov.</td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">83 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">77 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">84 %</span></td>
                    <td className="p-3 text-center">99th</td>
                    <td className="p-3 text-center font-bold">84 %</td>
                  </tr>
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">5. RenewaTech</td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">84 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">77 %</span></td>
                    <td className="p-3 text-center"><span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">91 %</span></td>
                    <td className="p-3 text-center">99th</td>
                    <td className="p-3 text-center font-bold">83 %</td>
                  </tr>

                  {/* Current company - highlighted */}
                  <tr className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500">
                    <td className="p-3 font-bold text-blue-700 dark:text-blue-300">42. {entityName}</td>
                    <td className="p-3 text-center"><span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">{environmentalScore} %</span></td>
                    <td className="p-3 text-center"><span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">{socialScore} %</span></td>
                    <td className="p-3 text-center"><span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">{governanceScore} %</span></td>
                    <td className="p-3 text-center font-bold">87th</td>
                    <td className="p-3 text-center font-bold text-blue-700 dark:text-blue-300">{Math.round(overallRating)} %</td>
                  </tr>

                  {/* Bottom performers */}
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">145. Nexus Solutions</td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">37 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">47 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">58 %</span></td>
                    <td className="p-3 text-center">3rd</td>
                    <td className="p-3 text-center font-bold">46 %</td>
                  </tr>
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">146. Horizon Systems</td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">40 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">41 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">62 %</span></td>
                    <td className="p-3 text-center">3rd</td>
                    <td className="p-3 text-center font-bold">46 %</td>
                  </tr>
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">147. GlobalTech Corp</td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">35 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">43 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">65 %</span></td>
                    <td className="p-3 text-center">3rd</td>
                    <td className="p-3 text-center font-bold">45 %</td>
                  </tr>
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">148. Epoch Solutions</td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">32 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">30 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">49 %</span></td>
                    <td className="p-3 text-center">1st</td>
                    <td className="p-3 text-center font-bold">44 %</td>
                  </tr>
                  <tr className="hover:bg-slate-50 dark:hover:bg-slate-800">
                    <td className="p-3 font-medium">149. Amethyst Ventures</td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">40 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">41 %</span></td>
                    <td className="p-3 text-center"><span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">43 %</span></td>
                    <td className="p-3 text-center">1st</td>
                    <td className="p-3 text-center font-bold">41 %</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ESG Risk Pillar Analysis */}
      <div className="space-y-8">
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
            ESG Risk Pillar Analysis
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
            {entityName} performs well in Environmental and Governance aspects, while there are areas for improvement in Social
            metrics. The materiality percentages underscore the importance of each key area in shaping {entityName}'s overall
            sustainability strategy and risk management profile.
          </p>
        </div>

        {/* Environmental Pillar */}
        <Card className="glass-effect-subtle-lit rounded-2xl">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                  <span className="text-green-600 dark:text-green-400 text-lg">🌱</span>
                </div>
                <div>
                  <CardTitle className="text-xl">Environmental</CardTitle>
                  <p className="text-sm text-slate-600 dark:text-slate-400">Materiality</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600">{environmentalScore.toFixed(2)}%</div>
                <div className="text-sm text-slate-600 dark:text-slate-400">
                  88th percentile • Score: 81
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
              {/* Environmental Description */}
              <div className="space-y-4">
                <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
                  The environmental pillar in ESG risk is material for {entityName} due to the
                  industry's significant carbon footprint and environmental impact associated
                  with electronic waste, increasing regulatory scrutiny and consumer
                  awareness regarding environmental impacts make it crucial for {entityName}
                  to manage environmental risks effectively. To address these challenges,
                  {entityName} has shown significant progress in emission reduction and net
                  zero targets, with a clear roadmap for improvement in key energy
                  performance. However, there is room for improvement in key energy
                  efficiency measures and renewable energy efficiency targets, which
                  limits its ability to further enhance sustainability efforts and reduce overall
                  energy consumption.
                </p>
              </div>

              {/* Environmental Categories Table */}
              <div>
                <h4 className="font-semibold mb-3 text-slate-800 dark:text-slate-200">Main Categories</h4>
                <div className="space-y-2">
                  <div className="grid grid-cols-4 gap-2 text-xs font-medium text-slate-600 dark:text-slate-400 pb-2 border-b border-slate-200 dark:border-slate-700">
                    <span>Categories</span>
                    <span className="text-center">Materiality</span>
                    <span className="text-center">Percentile</span>
                    <span className="text-center">Score</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>E1 - Resource Use</span>
                    <span className="text-center">1.14%</span>
                    <span className="text-center">100th</span>
                    <span className="text-center font-medium">77</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>E2 - Climate Change</span>
                    <span className="text-center">5.75%</span>
                    <span className="text-center">100th</span>
                    <span className="text-center font-medium">99</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>E3 - Waste and Pollutants</span>
                    <span className="text-center">7.54%</span>
                    <span className="text-center">85th</span>
                    <span className="text-center font-medium">80</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>E4 - Supplier Footprint</span>
                    <span className="text-center">7.79%</span>
                    <span className="text-center">100th</span>
                    <span className="text-center font-medium">100</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>E5 - Product Footprint</span>
                    <span className="text-center">10.58%</span>
                    <span className="text-center">-</span>
                    <span className="text-center font-medium">-</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>E6 - Environmental Governance and Reporting</span>
                    <span className="text-center">0.97%</span>
                    <span className="text-center">100th</span>
                    <span className="text-center font-medium">100</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Social Pillar */}
        <Card className="glass-effect-subtle-lit rounded-2xl">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                  <span className="text-blue-600 dark:text-blue-400 text-lg">👥</span>
                </div>
                <div>
                  <CardTitle className="text-xl">Social</CardTitle>
                  <p className="text-sm text-slate-600 dark:text-slate-400">Materiality</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">{socialScore.toFixed(2)}%</div>
                <div className="text-sm text-slate-600 dark:text-slate-400">
                  63rd percentile • Score: 54
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
              {/* Social Description */}
              <div className="space-y-4">
                <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
                  The Social pillar is material for {entityName} due to the industry's dependence
                  on maintaining customer trust, managing complex global supply chains,
                  and fostering a workforce that drives innovation. {entityName} manages
                  social risks through comprehensive employee training, ensuring
                  accessibility through robust privacy measures and supplier oversight. The
                  company also ensures strong ethical standards among its suppliers, with
                  solid codes of conduct and frequent audits. However, {entityName} faces
                  challenges related to its workforce, particularly concerning workplace
                  culture, pay equity, and employee satisfaction. While it has implemented
                  programs to improve benefits and foster equity, there are gaps in
                  addressing employee retention and creating a fully inclusive environment,
                  highlighting areas for further focus and improvement.
                </p>
              </div>

              {/* Social Categories Table */}
              <div>
                <h4 className="font-semibold mb-3 text-slate-800 dark:text-slate-200">Main Categories</h4>
                <div className="space-y-2">
                  <div className="grid grid-cols-4 gap-2 text-xs font-medium text-slate-600 dark:text-slate-400 pb-2 border-b border-slate-200 dark:border-slate-700">
                    <span>Categories</span>
                    <span className="text-center">Materiality</span>
                    <span className="text-center">Percentile</span>
                    <span className="text-center">Score</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>S1 - Employees</span>
                    <span className="text-center">17.27%</span>
                    <span className="text-center">76th</span>
                    <span className="text-center font-medium">54</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>S2 - Customers and Products</span>
                    <span className="text-center">13.95%</span>
                    <span className="text-center">2nd</span>
                    <span className="text-center font-medium">45</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>S3 - Supply Chain</span>
                    <span className="text-center">10.88%</span>
                    <span className="text-center">67th</span>
                    <span className="text-center font-medium">72</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>S4 - Community and Human Rights</span>
                    <span className="text-center">0.30%</span>
                    <span className="text-center">-</span>
                    <span className="text-center font-medium">60</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Governance Pillar */}
        <Card className="glass-effect-subtle-lit rounded-2xl">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                  <span className="text-purple-600 dark:text-purple-400 text-lg">⚖️</span>
                </div>
                <div>
                  <CardTitle className="text-xl">Governance</CardTitle>
                  <p className="text-sm text-slate-600 dark:text-slate-400">Materiality</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-purple-600">{governanceScore.toFixed(2)}%</div>
                <div className="text-sm text-slate-600 dark:text-slate-400">
                  90th percentile • Score: 77
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
              {/* Governance Description */}
              <div className="space-y-4">
                <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
                  The governance pillar is material for {entityName} as it underpins the
                  company's ability to maintain ethical practices, regulatory compliance, and
                  effective oversight, which are essential for long-term sustainable trust.
                  {entityName} demonstrates strong performance in governance mechanisms,
                  with effective board structure and composition, decision-making, and
                  shareholder engagement strategies that mitigate risks tied to oversight
                  failures. However, its performance in ethical practices indicates room for
                  improvement in areas such as executive accountability and ensuring
                  consistency in ethical standards across global operations. {entityName}
                  manages governance risks through policies on ethical conduct, anti-
                  corruption measures, and transparent reporting, but gaps remain in fully
                  addressing potential reputational and compliance risks.
                </p>
              </div>

              {/* Governance Categories Table */}
              <div>
                <h4 className="font-semibold mb-3 text-slate-800 dark:text-slate-200">Main Categories</h4>
                <div className="space-y-2">
                  <div className="grid grid-cols-4 gap-2 text-xs font-medium text-slate-600 dark:text-slate-400 pb-2 border-b border-slate-200 dark:border-slate-700">
                    <span>Categories</span>
                    <span className="text-center">Materiality</span>
                    <span className="text-center">Percentile</span>
                    <span className="text-center">Score</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>G1 - Corporate Governance</span>
                    <span className="text-center">14.16%</span>
                    <span className="text-center">94th</span>
                    <span className="text-center font-medium">81</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>G2 - Corporate Ethics and Behaviour</span>
                    <span className="text-center">9.41%</span>
                    <span className="text-center">73rd</span>
                    <span className="text-center font-medium">79</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>G3 - Transparency, Memberships, Awareness</span>
                    <span className="text-center">0.92%</span>
                    <span className="text-center">100th</span>
                    <span className="text-center font-medium">100</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ESG Risk Company Overview */}
      <div className="space-y-8">
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
            ESG Risk Company Overview
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
            {entityName} demonstrates an overall low ESG risk, particularly in the Environmental pillar, ranking above the industry
            average in the Technology Hardware, Storage & Peripherals industry.
          </p>
        </div>

        {/* Score Breakdown */}
        <Card className="glass-effect-subtle-lit rounded-2xl">
          <CardHeader>
            <CardTitle className="text-xl">Score Breakdown</CardTitle>
            <p className="text-sm text-slate-600 dark:text-slate-400">ESG Scores</p>
          </CardHeader>
          <CardContent>
            <div className="grid gap-8 grid-cols-1 lg:grid-cols-2">
              {/* Score Visualization */}
              <div className="space-y-6">
                <div className="text-center">
                  <div className="text-4xl font-bold text-slate-800 dark:text-slate-200 mb-2">69</div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">Total ESG</div>
                </div>

                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-green-600 mb-1">81</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">Environmental</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600 mb-1">54</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">Social</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600 mb-1">77</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">Governance</div>
                  </div>
                </div>

                <div className="flex justify-center space-x-4 text-xs">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
                    <span>Low Risk</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full mr-1"></div>
                    <span>High Risk</span>
                  </div>
                </div>
              </div>

              {/* Score Description */}
              <div className="space-y-4">
                <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
                  {entityName}'s performance demonstrates a low overall ESG risk, largely due to its strong environmental
                  performance. The company has shown significant efforts toward carbon neutrality, successfully
                  reducing its overall greenhouse gas emissions by 55% since 2015, and aiming for a fully carbon-neutral
                  value chain by 2030. However, it faces social risks related to diversity and working conditions, as
                  evidenced by low employee training hours and low female representation, which could impede
                  {entityName}'s recruitment and retention success if left unengaged. In terms of governance,
                  {entityName}'s performance is strong during this period in areas like executive compensation and
                  corporate culture. While {entityName}'s overall ESG risk remains low, there are some critical
                  points of concern in evaluating its ESG risk profile and the opportunities for further improvement.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Historical Performance */}
        <Card className="glass-effect-subtle-lit rounded-2xl">
          <CardHeader>
            <CardTitle className="text-xl">Historical Performance</CardTitle>
            <p className="text-sm text-slate-600 dark:text-slate-400">Total ESG Score Evolution Over Time</p>
          </CardHeader>
          <CardContent>
            <div className="grid gap-8 grid-cols-1 lg:grid-cols-2">
              {/* Chart Placeholder */}
              <div className="space-y-4">
                <div className="h-48 bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center justify-center">
                  <div className="text-center text-slate-500 dark:text-slate-400">
                    <div className="text-sm mb-2">ESG Score Trend</div>
                    <div className="text-xs">2021: 60 → 2024: 69</div>
                    <div className="mt-4 flex justify-center space-x-4 text-xs">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                        <span>Low risk</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></div>
                        <span>High risk</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Historical Description */}
              <div className="space-y-4">
                <p className="text-sm text-slate-600 dark:text-slate-400 print:text-slate-700 leading-relaxed">
                  {entityName}'s ESG risk score improved from 60 in 2021 to 69 in 2024, reflecting a 10% increase in
                  performance. This improvement is largely attributed to governance enhancements during the period.
                  The percentage of independent board members increased from 75% to 84.82%, strengthening
                  oversight and decision-making processes. Additionally, the representation of women on the board rose
                  from 28.57% to 33.33%, signaling progress in gender diversity. Non-executive board members also saw
                  a slight rise from 85.71% to 88.89%, further aligning governance practices with shareholder interests.
                  These changes indicate a focused effort to enhance board composition, which has contributed to the
                  reduced ESG risk profile.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Leading Areas and Key Areas for Improvement */}
        <div className="grid gap-8 grid-cols-1 lg:grid-cols-2">
          {/* Leading Areas */}
          <Card className="glass-effect-subtle-lit rounded-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-green-600">Leading Areas</CardTitle>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {entityName} demonstrates negligible ESG risk in the areas of
                Suppliers Footprint, where it uses strict environmental criteria, and
                Carbon Emissions, with strong reduction in absolute and relative
                commitments. Its waste management practices reflect a strong
                commitment to reducing waste and improving efficiency. These
                mechanisms facilitate effective oversight and transparency. These
                subcategories contribute to low overall ESG risk and primary
                competitive advantages.
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <h4 className="font-semibold text-sm text-slate-800 dark:text-slate-200">Top subcategories</h4>

                <div className="space-y-2">
                  <div className="grid grid-cols-4 gap-2 text-xs font-medium text-slate-600 dark:text-slate-400 pb-2 border-b border-slate-200 dark:border-slate-700">
                    <span>Subcategories</span>
                    <span className="text-center">Materiality</span>
                    <span className="text-center">Percentile</span>
                    <span className="text-center">Total ESG</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>Ex1 - Suppliers Footprint</span>
                    <span className="text-center">7.79%</span>
                    <span className="text-center">100th</span>
                    <span className="text-center font-medium text-green-600">100</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>E2.1 - Carbon Emissions</span>
                    <span className="text-center">5.75%</span>
                    <span className="text-center">100th</span>
                    <span className="text-center font-medium text-green-600">95</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>E3.2 - Waste</span>
                    <span className="text-center">7.54%</span>
                    <span className="text-center">80th</span>
                    <span className="text-center font-medium text-green-600">80</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>G1.1 - Corporate Governance Me...</span>
                    <span className="text-center">5.81%</span>
                    <span className="text-center">95th</span>
                    <span className="text-center font-medium text-green-600">82</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Key Areas for Improvement */}
          <Card className="glass-effect-subtle-lit rounded-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-orange-600">Key Areas For Improvement</CardTitle>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {entityName} faces ESG risks in the areas of Working Conditions and
                Employee Satisfaction, and Diversity, where workforce
                representation remains insufficient. Additionally, issues related to
                Product Responsibility arise from safety incidents and their societal
                impacts. Compensation concerns are also significant, stemming
                from excessive CEO pay ratio and lack of compensation incentives
                tied to ESG risks, or sustainability targets.
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <h4 className="font-semibold text-sm text-slate-800 dark:text-slate-200">Bottom subcategories</h4>

                <div className="space-y-2">
                  <div className="grid grid-cols-4 gap-2 text-xs font-medium text-slate-600 dark:text-slate-400 pb-2 border-b border-slate-200 dark:border-slate-700">
                    <span>Subcategories</span>
                    <span className="text-center">Materiality</span>
                    <span className="text-center">Percentile</span>
                    <span className="text-center">Total ESG</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>S1.2 - Working Conditions and E...</span>
                    <span className="text-center">8.14%</span>
                    <span className="text-center">9th</span>
                    <span className="text-center font-medium text-orange-600">38</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>S1.4 - Diversity</span>
                    <span className="text-center">6.12%</span>
                    <span className="text-center">12th</span>
                    <span className="text-center font-medium text-orange-600">39</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1">
                    <span>S2.2 - Product Responsibility</span>
                    <span className="text-center">1.34%</span>
                    <span className="text-center">4th</span>
                    <span className="text-center font-medium text-orange-600">1</span>
                  </div>

                  <div className="grid grid-cols-4 gap-2 text-xs py-1 bg-slate-50 dark:bg-slate-800/50 rounded">
                    <span>G1.3 - Compensation</span>
                    <span className="text-center">2.89%</span>
                    <span className="text-center">8th</span>
                    <span className="text-center font-medium text-orange-600">36</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Summary Analysis Section */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 print:text-black">
          Analysis Summary
        </h2>

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
          <Card className="glass-effect-subtle-lit rounded-2xl text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Shield className="w-5 h-5 text-blue-600" />
                <span className="text-sm font-medium">Reliability</span>
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {claims.length > 0 ? Math.round(100 - (100 * invalidClaims.length / claims.length)) : 'N/A'}
                {claims.length > 0 && '%'}
              </div>
              <div className="text-xs text-slate-500 mt-1">
                {claims.length > 0 ? `${claims.length - invalidClaims.length}/${claims.length} claims verified` : 'No claims analyzed'}
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect-subtle-lit rounded-2xl text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Eye className="w-5 h-5 text-purple-600" />
                <span className="text-sm font-medium">Transparency</span>
              </div>
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(overallRating)}%
              </div>
              <div className="text-xs text-slate-500 mt-1">
                Based on disclosure quality
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect-subtle-lit rounded-2xl text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <BarChart3 className="w-5 h-5 text-slate-600" />
                <span className="text-sm font-medium">Data Points</span>
              </div>
              <div className="text-2xl font-bold text-slate-600">
                {processedFlags.length + claims.length + promises.length}
              </div>
              <div className="text-xs text-slate-500 mt-1">
                Total ESG data points analyzed
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Report Footer */}
      <div className="mt-12 pt-8 border-t border-slate-200 dark:border-slate-700 print:border-slate-300">
        <div className="flex items-center justify-between text-sm text-slate-500 print:text-slate-700">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Info className="w-4 h-4" />
              <span>Powered by</span>
              <span className="font-semibold text-slate-700 dark:text-slate-300 print:text-black">
                EKO Intelligence
              </span>
            </div>
            <div className="hidden md:block">
              Copyright © 2024 Clarity AI Inc. All rights reserved.
            </div>
          </div>
          <div className="text-right">
            <div className="font-medium">Page 1/5</div>
          </div>
        </div>

        <div className="mt-4 text-xs text-slate-400 print:text-slate-600 leading-relaxed">
          This report provides an ESG risk assessment based on publicly available information and proprietary analysis.
          The scores and ratings are indicative and should be considered alongside other factors when making investment decisions.
          Analysis includes {processedFlags.length} ESG flags, {claims.length} claims verification, {promises.length} promise tracking,
          and {cherryData.length} cherry-picking instances identified through advanced AI analysis.
        </div>
      </div>
    </div>
  )
}
