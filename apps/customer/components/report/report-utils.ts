/**
 * Utility functions for the hierarchical report component
 */

/**
 * Extract a section from markdown text based on start and end markers
 * @param text The full markdown text
 * @param startMarker The marker that indicates the start of the section
 * @param endMarker The marker that indicates the end of the section, or null for end of text
 * @returns The extracted section text
 */
export const extractMarkdownSection = (text: string, startMarker: string, endMarker: string | null): string => {
  // Handle case sensitivity by converting to lowercase for search
  const lowerText = text.toLowerCase();
  const lowerStartMarker = startMarker.toLowerCase();
  const lowerEndMarker = endMarker ? endMarker.toLowerCase() : null;

  // Find the start marker (case insensitive)
  const lowerStartIndex = lowerText.indexOf(lowerStartMarker);
  if (lowerStartIndex === -1) {
    console.log(`Start marker "${startMarker}" not found in text: ${text}`);
    return '';
  }

  // Get the actual start position in the original text
  const startIndex = lowerStartIndex;
  const start = startIndex + startMarker.length;

  // Find the end marker (case insensitive)
  let end;
  if (lowerEndMarker) {
    const lowerEndIndex = lowerText.indexOf(lowerEndMarker, start);
    end = lowerEndIndex === -1 ? text.length : lowerEndIndex;
  } else {
    end = text.length;
  }

  // Extract the section
  const extractedText = text.substring(start, end).trim();
  console.log(`Extracted section from "${startMarker}" to "${endMarker || 'end'}" (${extractedText.length} chars)`);

  return extractedText;
}

/**
 * Create a shared TextDecoder for streaming responses
 * @returns A TextDecoder instance
 */
export const createStreamDecoder = (): TextDecoder => {
  return new TextDecoder()
}

/**
 * Process a streaming response and update state with the received chunks
 * @param response The fetch response
 * @param decoder The TextDecoder instance
 * @param updateState A callback function to update state with the received text
 * @returns The complete text received from the stream
 */
export const processStreamingResponse = async (
  response: Response,
  decoder: TextDecoder,
  updateState: (text: string) => void
): Promise<string> => {
  if (!response.body) {
    throw new Error('Response body is null')
  }

  const reader = response.body.getReader()
  let fullText = ''

  try {
    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value, { stream: true })
      fullText += chunk
      updateState(fullText)
    }
  } finally {
    reader.releaseLock()
  }

  return fullText
}

/**
 * Parse a markdown response into a structured summary object
 * @param markdownText The markdown text to parse
 * @returns An object with summary, positives, negatives, risks, and opportunities
 */
export const parseMarkdownSummary = (markdownText: string) => {
  // Try with ### headings first (new format)
  let summary = extractMarkdownSection(markdownText, '### Summary', '### Key Positives')
  let positives = extractMarkdownSection(markdownText, '### Key Positives', '### Key Negatives')
  let negatives = extractMarkdownSection(markdownText, '### Key Negatives', '### Key Risks')
  let risks = extractMarkdownSection(markdownText, '### Key Risks', '### Key Opportunities')
  let opportunities = extractMarkdownSection(markdownText, '### Key Opportunities', null)

  // If any section is empty, fall back to ## headings (old format)
  if (!summary) {
    summary = extractMarkdownSection(markdownText, '## Summary', '## Key Positives')
    positives = extractMarkdownSection(markdownText, '## Key Positives', '## Key Negatives')
    negatives = extractMarkdownSection(markdownText, '## Key Negatives', '## Key Risks')
    risks = extractMarkdownSection(markdownText, '## Key Risks', '## Key Opportunities')
    opportunities = extractMarkdownSection(markdownText, '## Key Opportunities', null)
  }

  return {
    summary,
    positives,
    negatives,
    risks,
    opportunities
  }
}

/**
 * Parse a reliability response into a structured reliability summary object
 * @param markdownText The markdown text to parse
 * @returns An object with summary, claims, and promises
 */
export const parseReliabilityResponse = (markdownText: string) => {
  console.log('Parsing reliability response from text:', markdownText.substring(0, 200) + '...');

  const summary = extractMarkdownSection(markdownText, '# Reliability Analysis', '# Claims Analysis')
  console.log('Extracted summary:', summary.substring(0, 100) + '...');

  const claimsSummary = extractMarkdownSection(markdownText, '# Claims Analysis', '# Key Claim Examples')
  console.log('Extracted claims summary:', claimsSummary.substring(0, 100) + '...');

  const promisesSummary = extractMarkdownSection(markdownText, '# Promises Analysis', '# Key Promise Examples')
  console.log('Extracted promises summary:', promisesSummary.substring(0, 100) + '...');

  // Extract examples as arrays
  const claimsExamplesText = extractMarkdownSection(markdownText, '# Key Claim Examples', '# Promises Analysis')
  console.log('Extracted claims examples text:', claimsExamplesText.substring(0, 100) + '...');

  const promisesExamplesText = extractMarkdownSection(markdownText, '# Key Promise Examples', null)
  console.log('Extracted promises examples text:', promisesExamplesText.substring(0, 100) + '...');

  // Convert bullet points to array items
  const claimsExamples = claimsExamplesText
    .split('\n')
    .filter(line => line.trim().startsWith('-'))
    .map(line => line.replace(/^-\s*/, '').trim())
    .filter(line => line.length > 0)

  const promisesExamples = promisesExamplesText
    .split('\n')
    .filter(line => line.trim().startsWith('-'))
    .map(line => line.replace(/^-\s*/, '').trim())
    .filter(line => line.length > 0)

  return {
    summary,
    claims: {
      summary: claimsSummary,
      examples: claimsExamples
    },
    promises: {
      summary: promisesSummary,
      examples: promisesExamples
    }
  }
}
