import { CitationType } from '@/components/citation'
import { FlagTypeV2, ModelSectionType } from '@/types'
import { ClaimTypeV2 } from '@/types/claim'
import { PromiseTypeV2 } from '@/types/promise'
import { CherryTypeV2 } from '@/types/cherry'

// Re-export types from actions/report.ts
export type CategorySummary = {
  summary: string
  positives: string
  negatives: string
  risks: string
  opportunities: string
  model_sections: Record<string, string>
}

export type ReliabilitySummary = {
  summary: string
  claims: {
    summary: string
    examples: string[]
  }
  promises: {
    summary: string
    examples: string[]
  }
}

export type TransparencySummary = {
  summary: string
  cherry_picking: {
    summary: string
    examples: string[]
  }
}

export type OverallSummary = {
  summary: string
  positives: string
  negatives: string
  risks: string
  opportunities: string
}

export interface HierarchicalReportProps {
  entityName: string
  entityDescription: string
  modelName: string
  flags: FlagTypeV2[]
  modelSections: ModelSectionType[]
  claims: ClaimTypeV2[]
  promises: PromiseTypeV2[]
  cherryData: CherryTypeV2[]
  allCitations: CitationType[]
  runId?: number
  onPrint: (e: any) => void
  onEditReport?: (reportContent: string) => void
  isCreatingDocument?: boolean
}
export interface CategorySectionProps {
  summary: CategorySummary | null
  sectionNames: Record<string, string>
  citations: CitationType[]
  categoryId: 'environmental' | 'social' | 'governance'
}
export interface TransparencySectionProps {
  summary: string
  cherry_picking: {
    summary: string
    examples: string[]
  }
  citations: CitationType[]
  className?: string
  inlineCitations?: boolean
  citationPrefix?: string
}
export interface ReferencesProps {
  citations: CitationType[]
}

export interface ReportHeaderProps {
  entityName: string
  modelName: string
}

export interface TableOfContentsProps {
  hasEnvironmental: boolean
  hasSocial: boolean
  hasGovernance: boolean
  modelSections?: ModelSectionType[]
  modelName?: string
  flags?: FlagTypeV2[]
}
