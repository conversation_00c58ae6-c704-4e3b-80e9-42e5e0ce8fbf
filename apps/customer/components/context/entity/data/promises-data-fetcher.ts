import { PromiseTypeV2 } from '@/types'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export class PromisesDataFetcher extends BaseDataFetcher<PromiseTypeV2[]> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('promises', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: PromiseTypeV2[] | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData(null);
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            // Try to get promises from xfer_gw_promises_v2 first
            const { data: promisesV2Data, error: promisesV2Error } = await this.supabase
                .from('xfer_gw_promises_v2')
                .select('*')
                .eq('run_id', runObject!.id)
                .eq('entity_xid', entity!)
              .order('id', { ascending: false })

              .abortSignal(abortController.signal);

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (promisesV2Error) {
                this.logError(promisesV2Error, 'fetchAndFilterPromises - v2');
                if (!isStale()) {
                    setData([]);
                }
                return;
            }

            if (promisesV2Data && promisesV2Data.length > 0) {
                // Use V2 promises directly without conversion
                const filteredPromises = promisesV2Data
                    .filter(promise => {
                        // Safely access confidence with type checking
                        const model = promise.model as any;
                        return (model && typeof model === 'object' && 'confidence' in model ? model.confidence : 0) > 75;
                    })
                    .map(promise => promise as unknown as PromiseTypeV2);

                // Sort by confidence
                filteredPromises.sort((a, b) => {
                    // Safely access confidence with type checking
                    const modelA = a.model as any;
                    const modelB = b.model as any;
                    const confA = modelA && typeof modelA === 'object' && 'confidence' in modelA ? modelA.confidence : 0;
                    const confB = modelB && typeof modelB === 'object' && 'confidence' in modelB ? modelB.confidence : 0;
                    return confB - confA;
                });

                // Final check before setting data
                if (!isStale()) {
                    setData(filteredPromises);
                    this.logSuccess("Loaded promises", filteredPromises.length);
                }
            } else {
                // Fallback to old table if no promises found in v2
                await this.fetchFromV1Table(dependencies, setData, isStale, abortController);
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchAndFilterPromises');
            if (!isStale()) {
                setData([]);
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }

    private async fetchFromV1Table(
        dependencies: DataFetcherDependencies,
        setData: (data: PromiseTypeV2[] | null) => void,
        isStale: () => boolean,
        abortController: AbortController
    ): Promise<void> {
        // V1 table is not available in customer database, set empty data
        if (!isStale()) {
            console.warn("No promises found - V1 table not available in customer database");
            setData([]);
        }
    }
}
