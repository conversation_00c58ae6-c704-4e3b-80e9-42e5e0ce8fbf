import { ScoreTypeV2 } from '@/types';
import { getScoreValue } from '@/utils/score-utils';
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher';

export interface ScoreData {
    score: number;
    scoreData: ScoreTypeV2 | null;
}

export class ScoreDataFetcher extends BaseDataFetcher<ScoreData> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('score', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: ScoreData | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData({ score: 0, scoreData: null });
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            const { data: scoreV2Data, error: scoreV2Error } = await this.supabase
                .from('xfer_score_v2')
                .select('*')
                .eq('run_id', runObject!.id)
                .eq('entity_xid', entity!)
                .abortSignal(abortController.signal)
                .single();

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (scoreV2Data) {
                // Get score from V2 format
                const scoreV2DataTyped = scoreV2Data as unknown as ScoreTypeV2;
                const scoreValue = getScoreValue(scoreV2DataTyped);
                if (!isStale()) {
                    setData({ score: scoreValue, scoreData: scoreV2DataTyped });
                    this.logSuccess("Loaded score data", scoreValue);
                }
            } else {
                if (scoreV2Error) {
                    this.logError(scoreV2Error, 'fetchScoreData');
                }
                // Set default score if no data found
                if (!isStale()) {
                    setData({ score: 0, scoreData: null });
                }
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchScoreData');
            if (!isStale()) {
                setData({ score: 0, scoreData: null });
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }
}
