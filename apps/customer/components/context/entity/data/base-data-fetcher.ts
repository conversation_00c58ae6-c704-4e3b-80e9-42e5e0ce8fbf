import { createClient } from '@/app/supabase/client';
import { RunType } from '@/types';

export interface RequestCancellation {
    requestId: string;
    abortController: AbortController;
    isStale: () => boolean;
}

export interface DataFetcherDependencies {
    entity: string | null;
    runObject: RunType | null;
    model?: string;
    includeDisclosures?: boolean;
}

export abstract class BaseDataFetcher<T> {
    protected supabase = createClient();
    protected requestType: string;
    protected currentRequestRefs: Map<string, string>;
    protected abortControllersRef: Map<string, AbortController>;

    constructor(
        requestType: string,
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        this.requestType = requestType;
        this.currentRequestRefs = currentRequestRefs;
        this.abortControllersRef = abortControllersRef;
    }

    protected createRequestId(dependencies: DataFetcherDependencies): string {
        const { entity, runObject } = dependencies;
        return `${this.requestType}-${entity}-${runObject?.id || 'no-run'}-${Date.now()}-${Math.random()}`;
    }

    protected setupRequestCancellation(dependencies: DataFetcherDependencies): RequestCancellation {
        const requestId = this.createRequestId(dependencies);
        this.currentRequestRefs.set(this.requestType, requestId);

        // Cancel any existing request for this type
        const existingController = this.abortControllersRef.get(this.requestType);
        if (existingController) {
            existingController.abort();
        }

        // Create new abort controller
        const abortController = new AbortController();
        this.abortControllersRef.set(this.requestType, abortController);

        // Function to check if this request is still current for this specific request type
        const isStale = () => this.currentRequestRefs.get(this.requestType) !== requestId;

        return { requestId, abortController, isStale };
    }

    protected handleAbortError(error: unknown): boolean {
        if (error instanceof Error && error.name === 'AbortError') {
            console.log(`${this.requestType} request was cancelled`);
            return true;
        }
        return false;
    }

    protected logError(error: unknown, context: string): void {
        console.error(`Error in ${context}:`, error);
    }

    protected logSuccess(message: string, count?: number): void {
        const countText = count !== undefined ? `: ${count}` : '';
        console.log(`${message}${countText}`);
    }

    protected logCancellation(): void {
        console.log(`${this.requestType} request cancelled - newer request in progress`);
    }

    abstract fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: T | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void>;

    abstract validateDependencies(dependencies: DataFetcherDependencies): boolean;
}
