import { ClaimTypeV2 } from '@/types/claim'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export class ClaimsDataFetcher extends BaseDataFetcher<ClaimTypeV2[]> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('claims', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: ClaimTypeV2[] | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData(null);
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            const { data: claimsV2Data, error: claimsV2Error } = await this.supabase
                .from('xfer_gw_claims_v2')
                .select('*')
                .eq('run_id', runObject!.id)
                .eq('entity_xid', entity!)
              .order('id', { ascending: false })
                .abortSignal(abortController.signal);

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (claimsV2Data && claimsV2Data.length > 0) {
                // Convert V2 claims to V1 format
                const convertedClaims = (claimsV2Data as unknown as ClaimTypeV2[])
                  .filter(claim => claim.model.esg_claim && (claim.model.confidence || 0) > 60 && !claim.model.greenwashing);

                // Sort by confidence
                convertedClaims.sort((a, b) => (b.model.confidence || 0) - (a.model.confidence || 0));

                // Final check before setting data
                if (!isStale()) {
                    setData(convertedClaims);
                    this.logSuccess("Loaded claims data", convertedClaims.length);
                }
            } else {
                // Fallback to old table if no claims found in v2
                await this.fetchFromV1Table(dependencies, setData, isStale, abortController);
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchClaimsData');
            if (!isStale()) {
                setData([]);
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }

    private async fetchFromV1Table(
        dependencies: DataFetcherDependencies,
        setData: (data: ClaimTypeV2[] | null) => void,
        isStale: () => boolean,
        abortController: AbortController
    ): Promise<void> {
        // V1 table is not available in customer database, set empty data
        if (!isStale()) {
            console.warn("No claims found - V1 table not available in customer database");
            setData([]);
        }
    }
}
