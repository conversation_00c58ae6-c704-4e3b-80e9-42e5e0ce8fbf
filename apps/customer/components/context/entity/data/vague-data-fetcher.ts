import { VagueType } from '@/types';
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher';

export interface VagueData {
    vagueData: VagueType | null;
    vagueDetailData: VagueType[] | null;
}

export class VagueDataFetcher extends BaseDataFetcher<VagueData> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('vague', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: VagueData | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData({ vagueData: null, vagueDetailData: null });
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            // Get vague terms summary from xfer_gw_vague_v2
            const summaryData = await this.fetchSummaryData(dependencies, abortController, isStale);
            if (isStale()) return;

            // Get vague terms details from xfer_gw_vague_v2
            const detailData = await this.fetchDetailData(dependencies, abortController, isStale);
            if (isStale()) return;

            if (!isStale()) {
                setData({
                    vagueData: summaryData,
                    vagueDetailData: detailData
                });
                this.logSuccess("Loaded vague data");
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchVagueData');
            if (!isStale()) {
                setData({ vagueData: null, vagueDetailData: [] });
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }

    private async fetchSummaryData(
        dependencies: DataFetcherDependencies,
        abortController: AbortController,
        isStale: () => boolean
    ): Promise<VagueType | null> {
        const { entity, runObject } = dependencies;

        const { data: vagueV2Data, error: vagueV2Error } = await this.supabase
            .from("xfer_gw_vague_v2")
            .select("*")
            .eq("entity_xid", entity!)
            .eq("phrase", "__summary__")
            .eq("run_id", runObject!.id)
            .order("id", { ascending: false })
            .limit(1)
            .abortSignal(abortController.signal);

        if (isStale()) return null;

        if (vagueV2Data && vagueV2Data.length > 0) {
            return vagueV2Data[0] as unknown as VagueType;
        } else {
            // Fallback to old table if no vague term found in v2
            return await this.fetchSummaryFromV1(dependencies, abortController, isStale);
        }
    }

    private async fetchSummaryFromV1(
        dependencies: DataFetcherDependencies,
        abortController: AbortController,
        isStale: () => boolean
    ): Promise<VagueType | null> {
        // V1 table is not available in customer database
        if (isStale()) return null;
        console.warn("No vague summary found - V1 table not available in customer database");
        return null;
    }

    private async fetchDetailData(
        dependencies: DataFetcherDependencies,
        abortController: AbortController,
        isStale: () => boolean
    ): Promise<VagueType[] | null> {
        const { entity, runObject } = dependencies;

        const { data: detailV2Data, error: detailV2Error } = await this.supabase
            .from("xfer_gw_vague_v2")
            .select("*")
            .eq("entity_xid", entity!)
            .eq("run_id", runObject!.id)
            .neq("phrase", "__summary__")
            .abortSignal(abortController.signal);

        if (isStale()) return null;

        if (detailV2Data && detailV2Data.length > 0) {
            return detailV2Data as unknown as VagueType[];
        } else {
            // Fallback to old table if no vague terms found in v2
            return await this.fetchDetailFromV1(dependencies, abortController, isStale);
        }
    }

    private async fetchDetailFromV1(
        dependencies: DataFetcherDependencies,
        abortController: AbortController,
        isStale: () => boolean
    ): Promise<VagueType[] | null> {
        // V1 table is not available in customer database
        if (isStale()) return null;
        console.warn("No vague details found - V1 table not available in customer database");
        return [];
    }
}
