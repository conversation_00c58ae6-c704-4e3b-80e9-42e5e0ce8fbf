import { FlagTypeV2 } from '@/types'
import { processFlags } from '@/utils/flag-converter'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export class FlagsDataFetcher extends BaseDataFetcher<FlagTypeV2[]> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('flags', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: FlagTypeV2[] | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject, includeDisclosures = false } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData(null);
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            const { data: flagsV2Data, error: flagsV2Error } = await this.supabase
                .from('xfer_flags_v2')
                .select('*')
                .eq('run_id', runObject!.id)
                .eq('entity_xid', entity!)
              .order('id', { ascending: false })
                .abortSignal(abortController.signal);

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (flagsV2Error) {
                this.logError(flagsV2Error, 'fetchAndFilterFlags');
                setData([]);
                return;
            }

            if (flagsV2Data && flagsV2Data.length > 0) {
                // Process flags to ensure models are properly parsed
                let processedFlags = processFlags(flagsV2Data as unknown as FlagTypeV2[]);

                // Filter out disclosure-only flags if includeDisclosures is false
                // Always keep red flags regardless of disclosure setting
                if (!includeDisclosures) {
                    processedFlags = processedFlags.filter(flag => {
                        // Ensure model is parsed
                        const parsedFlag = typeof flag.model === 'string' ?
                            { ...flag, model: JSON.parse(flag.model) } : flag;

                        // Keep all red flags and non-disclosure-only green flags
                        return parsedFlag.model.flag_type === 'red' || !parsedFlag.model.is_disclosure_only;
                    });
                    console.log(`Filtered flags: ${processedFlags.length} (excluded disclosure-only green flags)`);
                }

                // Final check before setting data
                if (!isStale()) {
                    setData(processedFlags);
                    this.logSuccess("Loaded flags", processedFlags.length);
                }
            } else {
                if (!isStale()) {
                    console.warn("No flags found for this entity and run");
                    setData([]);
                }
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchAndFilterFlags');
            if (!isStale()) {
                setData([]);
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }
}
