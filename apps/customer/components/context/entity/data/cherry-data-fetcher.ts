import { CherryTypeV2 } from '@/types'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export class CherryDataFetcher extends BaseDataFetcher<CherryTypeV2[]> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('cherry', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { entity, runObject } = dependencies;
        return !!(entity && runObject && runObject.id);
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: CherryTypeV2[] | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { entity, runObject } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData(null);
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            const { data: cherryV2Data, error: cherryV2Error } = await this.supabase
                .from('xfer_gw_cherry_v2')
                .select('*')
                .eq('entity_xid', entity!)
                .eq('run_id', runObject!.id)
              .order('id', { ascending: false })
                .abortSignal(abortController.signal);

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (cherryV2Error) {
                this.logError(cherryV2Error, 'fetchCherryData');
                if (!isStale()) {
                    setData([]);
                }
                return;
            }

            if (!isStale()) {
                setData((cherryV2Data as unknown as CherryTypeV2[]) || []);
                this.logSuccess("Loaded cherry data", cherryV2Data?.length || 0);
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchCherryData');
            if (!isStale()) {
                setData([]);
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }
}
