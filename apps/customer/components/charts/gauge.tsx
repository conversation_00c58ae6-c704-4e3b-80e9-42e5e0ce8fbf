"use client";
import React, { useEffect, useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { cn } from '@utils/lib/utils'

export const Gauge = ({
                          value,
                          size = "sm",
                          showValue = true,
                          color = "text-[#69b386]",
                          bgcolor = "text-[#fff] dark:text-[#fff]",
                          label = "",
                          percentage = false,
                          className,
                          'data-testid': dataTestId
                      }: {
    value: number | null;
    size: "sm" | "md" | "lg" | "xl";
    showValue: boolean;
    color?: String;
    bgcolor?: String;
    label?: String;
    percentage?: boolean;
    className?: string;
    'data-testid'?: string;
}) => {
    const circumference = 2 * Math.PI * 40;
    const valueInCircumference = value ? (value / 100) * circumference : 0;
    const strokeDasharray = `${circumference} ${circumference}`;
    const initialOffset = circumference;
    const strokeDashoffset = initialOffset - valueInCircumference;
    const [displayVal, setDisplayVal] = React.useState(0);
    const ref = useRef(null);
    const isInView = useInView(ref, {once: true, amount: 0.5});

    const sizes = {
        sm: {
            width: "36",
            height: "36",
            textSize: "text-sm",
            labelSize: "text-[10px]",
        },
        md: {
            width: "72",
            height: "72",
            textSize: "text-lg",
            labelSize: "text-xs",
        },
        lg: {
            width: "180",
            height: "180",
            textSize: "text-xl",
            labelSize: "text-sm",
        },
        xl: {
            width: "220",
            height: "220",
            textSize: "text-4xl",
            labelSize: "text-md",
        },
    };

    useEffect(() => {
        let timer;
        if (isInView && value !== null) {
            let i = 0;
            let timer: any;

            const startCounter = () => {
                timer = setInterval(() => {
                    if (i < value) {
                        i += 1;
                        setDisplayVal(i);
                    } else {
                        clearInterval(timer);
                    }
                }, 1200 / value);
            };

            startCounter();

            return () => clearInterval(timer);
        }
    }, [isInView, value]);

    return (
        <div className={cn("flex flex-col items-center justify-center relative",className)} ref={ref} data-testid={dataTestId}>
            <motion.svg
                fill="none"
                shapeRendering="crispEdges"
                height={sizes[size].height}
                width={sizes[size].width}
                viewBox="0 0 120 120"
                strokeWidth="2"
                className="transform overflow-visible "
                initial={{opacity: 0, scale: 0.8}}
                animate={isInView ? {opacity: 1, scale: 1} : {}}
                transition={{duration: 0.5, ease: "easeOut"}}
            >
                <circle
                    className={`${bgcolor} drop-shadow-md`}
                    strokeWidth="20"
                    stroke="currentColor"
                    fill="transparent"
                    shapeRendering="geometricPrecision"
                    r="40"
                    cx="60"
                    cy="60"
                />
                <motion.circle
                    className={cn(color,'opacity-60')}
                    strokeWidth="16"
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={initialOffset}
                    shapeRendering="geometricPrecision"
                    strokeLinecap="round"
                    stroke="currentColor"
                    fill="transparent"
                    transform="rotate(-90, 60, 60)"

                    r="40"
                    cx="60"
                    cy="60"
                    initial={{strokeDashoffset: initialOffset}}
                    animate={isInView ? {strokeDashoffset} : {}}
                    transition={{duration: 1.2, ease: "easeIn"}}
                />
            </motion.svg>
            {showValue ? (
                <motion.div
                    className="absolute flex opacity-0"
                    animate={isInView ? {opacity: 1} : {}}
                    transition={{duration: 0.8, ease: "easeOut"}}
                >
                    <div className="text-center">
                        <p className={`opacity-90 ${sizes[size].textSize}`} data-testid={dataTestId ? `${dataTestId}-value` : undefined}>
                            {displayVal}
                            {percentage ? "%" : ""}
                        </p>
                        <p className={`opacity-80 ${sizes[size].labelSize}`}>{label}</p>
                    </div>
                </motion.div>
            ) : null}
        </div>
    );
};
