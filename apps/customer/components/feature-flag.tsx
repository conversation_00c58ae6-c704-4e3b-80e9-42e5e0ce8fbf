'use client';

import { ReactNode } from 'react';
import { useAuth } from '@/components/context/auth/auth-context';

interface FeatureFlagProps {
  flag: string;
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * FeatureFlag component that conditionally renders children based on feature flag status
 * Works on both server and client sides
 * 
 * @param flag - The feature flag name to check
 * @param children - Content to render if the flag is enabled
 * @param fallback - Optional content to render if the flag is disabled
 */
export function FeatureFlag({ flag, children, fallback = null }: FeatureFlagProps) {
  const { hasFeature } = useAuth();

  if (hasFeature(flag)) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
}

/**
 * Server-side FeatureFlag component for use in server components
 * This requires the feature flag configuration to be passed explicitly
 */
interface ServerFeatureFlagProps extends FeatureFlagProps {
  userFlags: string[];
  orgFlags: string[];
  defaultFlags: string[];
}

export function ServerFeatureFlag({ 
  flag, 
  children, 
  fallback = null,
  userFlags,
  orgFlags,
  defaultFlags
}: ServerFeatureFlagProps) {
  // Import the hasFeature function directly for server-side use
  const { hasFeature } = require('@/utils/feature-flags');
  
  const config = {
    userFlags,
    orgFlags,
    defaultFlags,
  };

  if (hasFeature(flag, config)) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
}