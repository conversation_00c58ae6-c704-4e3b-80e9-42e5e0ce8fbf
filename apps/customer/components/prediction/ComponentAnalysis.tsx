'use client';

import React from 'react'
import { ComponentType, PredictiveComponentAnalysis } from '@/types/prediction'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/ui/card'
import { AlertTriangle, Lightbulb } from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@ui/components/ui/tabs'

interface ComponentAnalysisModalProps {
  analysis: PredictiveComponentAnalysis;
}

export function ComponentAnalysisModal({ analysis }: ComponentAnalysisModalProps) {
  const componentColors = {
    [ComponentType.MOTIVATION]: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
    [ComponentType.STATEMENT_TYPE]: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
    [ComponentType.ENGAGEMENT]: 'bg-amber-500/20 text-amber-300 border-amber-500/30',
    [ComponentType.IMPACT]: 'bg-emerald-500/20 text-emerald-300 border-emerald-500/30',
  };

  const componentTitles = {
    [ComponentType.MOTIVATION]: 'Motivation Analysis',
    [ComponentType.STATEMENT_TYPE]: 'Statement Type Analysis',
    [ComponentType.ENGAGEMENT]: 'Engagement Analysis',
    [ComponentType.IMPACT]: 'Impact Analysis',
  };

  const componentDescriptions = {
    [ComponentType.MOTIVATION]: 'Why the entity takes actions',
    [ComponentType.STATEMENT_TYPE]: 'How the entity communicates',
    [ComponentType.ENGAGEMENT]: 'How the entity engages with issues',
    [ComponentType.IMPACT]: 'The effects of the entity\'s actions',
  };

  return (
    <Card className={cn(
      "w-full overflow-hidden glass-effect-lit rounded-2xl shadow-medium",
      "border-l-4",
      componentColors[analysis.component_type].split(' ')[0].replace('bg-', 'border-')
    )}>
      <CardHeader className="pb-3">
        <div>
          <CardTitle className="text-xl font-bold text-foreground">
            {componentTitles[analysis.component_type]} ({analysis.year})
          </CardTitle>
          <CardDescription className="text-foreground/70 mt-1">
            {componentDescriptions[analysis.component_type]} •
            Confidence: {(analysis.confidence * 100).toFixed(1)}%
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        <Tabs defaultValue="summary" className="w-full">
          <TabsList className="glass-effect-subtle rounded-xl p-1 space-x-1">
            <TabsTrigger
              value="summary"
              className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
            >
              Summary
            </TabsTrigger>
            <TabsTrigger
              value="analysis"
              className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
            >
              Detailed Analysis
            </TabsTrigger>
            <TabsTrigger
              value="insights"
              className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
            >
              Risks & Opportunities
            </TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="mt-4 text-foreground/90">
            <div className="space-y-4">
              {analysis.summary.split('\n\n').map((paragraph, i) => (
                <p key={i}>{paragraph}</p>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analysis" className="mt-4 text-foreground/90">
            <div className="space-y-4">
              {analysis.detailed_analysis.split('\n\n').map((paragraph, i) => (
                <p key={i}>{paragraph}</p>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="insights" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="glass-effect-brand-compliment-lit rounded-xl p-4 border-l-4 border-red-500">
                <h4 className="flex items-center text-red-400 font-medium mb-3">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Potential Risks
                </h4>
                <ul className="space-y-2 text-foreground/90">
                  {analysis.potential_risks.map((risk, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-red-400 mr-2">•</span>
                      <span>{risk}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="glass-effect-brand-lit rounded-xl p-4 border-l-4 border-green-500">
                <h4 className="flex items-center text-green-400 font-medium mb-3">
                  <Lightbulb className="h-4 w-4 mr-2" />
                  Potential Opportunities
                </h4>
                <ul className="space-y-2 text-foreground/90">
                  {analysis.potential_opportunities.map((opportunity, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-green-400 mr-2">•</span>
                      <span>{opportunity}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
