'use client';

import React, { useState } from 'react'
import { ClusterAnalysis, ComponentType } from '@/types/prediction'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/ui/card'
import { Button } from '@ui/components/ui/button'
import { AlertTriangle, ChevronDown, ChevronUp, Lightbulb } from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@ui/components/ui/tabs'

interface ClusterAnalysisCardProps {
  analysis: ClusterAnalysis;
  onViewComponent: (componentType: ComponentType) => void;
}

export function ClusterAnalysisCard({ analysis, onViewComponent }: ClusterAnalysisCardProps) {
  const [expanded, setExpanded] = useState(false);

  const componentColors = {
    [ComponentType.MOTIVATION]: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
    [ComponentType.STATEMENT_TYPE]: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
    [ComponentType.ENGAGEMENT]: 'bg-amber-500/20 text-amber-300 border-amber-500/30',
    [ComponentType.IMPACT]: 'bg-emerald-500/20 text-emerald-300 border-emerald-500/30',
  };

  return (
    <Card className="w-full mb-6 overflow-hidden glass-effect-lit rounded-2xl shadow-medium hover:shadow-large transition-all duration-300 hover:-translate-y-1">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-bold text-foreground">
              Cluster {analysis.cluster_id} Analysis ({analysis.year})
            </CardTitle>
            <CardDescription className="text-foreground/70 mt-1">
              Confidence: {(analysis.confidence * 100).toFixed(1)}%
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(!expanded)}
            className="text-foreground/70 hover:text-foreground transition-colors duration-200 glass-effect-subtle hover:glass-effect-lit"
          >
            {expanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        <div className="text-foreground/90 mb-4">
          {analysis.summary}
        </div>

        {expanded && (
          <div className="mt-4 space-y-6">
            <Tabs defaultValue="analysis" className="w-full">
              <TabsList className="glass-effect-subtle rounded-xl p-1 space-x-1">
                <TabsTrigger
                  value="analysis"
                  className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                >
                  Detailed Analysis
                </TabsTrigger>
                <TabsTrigger
                  value="components"
                  className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                >
                  Component Summaries
                </TabsTrigger>
                <TabsTrigger
                  value="insights"
                  className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                >
                  Risks & Opportunities
                </TabsTrigger>
              </TabsList>

              <TabsContent value="analysis" className="mt-4 text-foreground/90">
                <div className="space-y-4">
                  {analysis.detailed_analysis.split('\n\n').map((paragraph, i) => (
                    <p key={i}>{paragraph}</p>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="components" className="mt-4">
                <div className="space-y-4">
                  {Object.values(ComponentType).map((componentType) => {
                    const summaryKey = `${componentType}_summary` as keyof ClusterAnalysis;
                    const summary = analysis[summaryKey] as string;

                    return (
                      <div
                        key={componentType}
                        className={cn(
                          "p-4 rounded-xl border glass-effect-subtle",
                          componentColors[componentType]
                        )}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-foreground capitalize">{componentType.replace('_', ' ')}</h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onViewComponent(componentType)}
                            className="text-xs border-brand text-brand hover:bg-brand/10"
                          >
                            View Details
                          </Button>
                        </div>
                        <p className="text-foreground/70">{summary}</p>
                      </div>
                    );
                  })}
                </div>
              </TabsContent>

              <TabsContent value="insights" className="mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="glass-effect-brand-compliment-lit rounded-xl p-4 border-l-4 border-red-500">
                    <h4 className="flex items-center text-red-400 font-medium mb-3">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      Potential Risks
                    </h4>
                    <ul className="space-y-2 text-foreground/90">
                      {analysis.potential_risks.map((risk, i) => (
                        <li key={i} className="flex items-start">
                          <span className="text-red-400 mr-2">•</span>
                          <span>{risk}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="glass-effect-brand-lit rounded-xl p-4 border-l-4 border-green-500">
                    <h4 className="flex items-center text-green-400 font-medium mb-3">
                      <Lightbulb className="h-4 w-4 mr-2" />
                      Potential Opportunities
                    </h4>
                    <ul className="space-y-2 text-foreground/90">
                      {analysis.potential_opportunities.map((opportunity, i) => (
                        <li key={i} className="flex items-start">
                          <span className="text-green-400 mr-2">•</span>
                          <span>{opportunity}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
