1. **Report Section**: When status `idle`, should call endpoint and change status to `loading`. Only mark as `loaded'`
   after endpoint returns correct data otherwise status is `error`
2. **Report Group**: When status is `idle`, should check all descendant Report Groups, Report Summaries and Report
   Sections. If all are status `loaded`, change the group’s staus to `loaded`otherwise `loading`. If any descendents
   have status of `error` then the group should have status `error`
3. **Report Summary**: When it’s status is `idle`, should check dependencies. If any are `idle` or `loading` the
   summary’s status should be `loading`. If all are `loaded`ithe summary’s status should be `loading`. Then the
   sumarry’s summary endpoint should be called with the contents of all it’s dependencies as HTML. Upon receiving the
   result from the endpoint the sumarry’s status should become  `loaded`  or if it failes `error`.If any dependencies
   are `error` the summary’s status should be `error` also.
