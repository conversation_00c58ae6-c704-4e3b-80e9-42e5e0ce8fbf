import React, { use<PERSON>emo, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { Markdown } from 'tiptap-markdown'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import { citationLink, CitationType } from '@/components/citation'
import { cn } from '@utils/lib/utils'
import { ChartExtension } from '../editor/extensions/chart-extension'
import { CitationExtension } from '../editor/extensions/citation-extension'
import { ReportSectionExtension, ReportSubSectionExtension } from '../editor/extensions/ReportSectionExtension'

/* ------------------------------------------------------------------
 *  Shared, compiled‑once regexes
 * ------------------------------------------------------------------*/
const singleCitationRegex = /\[\^(\d+)\]/g
const multipleCitationRegex = /\[\^(\d+(?:,\s*\^?\d+)*)\]/g

/* ------------------------------------------------------------------
 *  Types & helpers
 * ------------------------------------------------------------------*/
export type EkoEditProps = {
  citations: CitationType[] | null;
  children: any;
  admin: boolean;
  className?: string;
  citationPrefix?: string;
  inlineCitations?: boolean;
  badgeStyle?: boolean;
  skipCitations?: boolean;
  editable?: boolean;
  onUpdate?: (content: string) => void;
};

/* ------------------------------------------------------------------
 *  Citation processing helpers (adapted from EkoMarkdown)
 * ------------------------------------------------------------------*/
function processCitations(
  markdown: string,
  citations: CitationType[] | null,
  admin: boolean,
  inlineCitations: boolean,
  badgeStyle: boolean,
  citationPrefix: string
) {
  if (!citations || citations.length === 0) {
    return { content: markdown, footnotes: [] };
  }

  const citationByPageId = new Map<number, CitationType>();
  citations.forEach(c => {
    citationByPageId.set(c.doc_page_id, c);
    citationByPageId.set(c.doc_id, c);
  });

  const footnotesArr: {
    id: number;
    alt_id: number;
    index: number;
    citation: CitationType;
    year: string | number;
    text: string;
    url: string;
  }[] = [];

  let md = typeof markdown === 'string' ? markdown : String(markdown);

  // Build footnotes array
  citations.forEach((citation, i) => {
    const year = citation.year || new Date().getFullYear();
    const text = citation.doc_name || citation.title || 'Unknown';
    const url = citationLink(citation, admin);

    footnotesArr.push({
      id: citation.doc_page_id,
      alt_id: citation.doc_id,
      index: i + 1,
      citation,
      year,
      text,
      url,
    });
  });

  // Helper function to replace citations
  const replaceSingle = (idStr: string) => {
    const id = +idStr;
    const fn = footnotesArr.find(f => f.id === id || f.alt_id === id);
    if (!fn) return '';

    if (inlineCitations) {
      if (badgeStyle) {
        return `<domain-badge-citation id="${fn.id}" display="${fn.index}" footnote="${id}"></domain-badge-citation>`;
      }
      return `[(${fn.text.replace(/\d{4}$/, '')}, ${fn.year})](${fn.url})`;
    }
    return ` [[${citationPrefix}${fn.index}]](${fn.url})`;
  };

  // Process multi-citations
  md = md.replace(multipleCitationRegex, (_, list: string) => {
    if (!list.includes(',')) return _; // single – leave for next pass
    const ids = list
      .split(',')
      .map(p => p.trim().replace('^', ''))
      .sort((a, b) => +a - +b);
    return ids.map(replaceSingle).join('');
  });

  // Process single citations
  md = md.replace(singleCitationRegex, (_, id: string) => replaceSingle(id));

  return { content: md, footnotes: footnotesArr };
}

/* ------------------------------------------------------------------
 *  Main component
 * ------------------------------------------------------------------*/
export const EkoEdit = React.memo((props: EkoEditProps) => {
  const {
    citations,
    children,
    admin,
    inlineCitations = true,
    badgeStyle = true,
    citationPrefix = '',
    className = '',
    skipCitations = false,
    editable = true,
    onUpdate,
  } = props;

  /* --------------------------------------------------------------
   *  Process markdown content
   * --------------------------------------------------------------*/
  const processedMarkdown = useMemo(() => {
    let markdown = typeof children === 'string' ? children : String(children);

    // Skip citations if requested
    if (skipCitations) {
      markdown = markdown.replaceAll(/\[\^.*]/g, '');
    } else if (citations) {
      // Process citations
      const { content } = processCitations(
        markdown,
        citations,
        admin,
        inlineCitations,
        badgeStyle,
        citationPrefix
      );
      markdown = content;
    }

    return markdown;
  }, [children, citations, admin, inlineCitations, badgeStyle, citationPrefix, skipCitations]);

  /* --------------------------------------------------------------
   *  TipTap Editor setup
   * --------------------------------------------------------------*/
  const editor = useEditor({
    extensions: [
      StarterKit,
      Markdown.configure({
        html: true, // Allow HTML input/output
        tightLists: true, // No <p> inside <li> in markdown output
        linkify: false, // Don't auto-create links
        breaks: false, // Don't convert \n to <br>
        transformPastedText: false, // Don't transform pasted text
        transformCopiedText: false, // Don't transform copied text
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      ChartExtension,
      CitationExtension.configure({
        citations: citations || [],
        admin,
      }),
      ReportSectionExtension,
      ReportSubSectionExtension,
    ],
    content: processedMarkdown,
    editable,
    onUpdate: ({ editor }) => {
      if (onUpdate) {
        // Get markdown content instead of HTML
        onUpdate(editor.storage.markdown.getMarkdown());
      }
    },
  });

  /* --------------------------------------------------------------
   *  Update editor content when markdown changes
   * --------------------------------------------------------------*/
  useEffect(() => {
    if (editor && processedMarkdown !== editor.storage.markdown.getMarkdown()) {
      editor.commands.setContent(processedMarkdown);
    }
  }, [editor, processedMarkdown]);

  /* --------------------------------------------------------------
   *  Render
   * --------------------------------------------------------------*/
  if (!editor) {
    return <div>Loading editor...</div>;
  }

  return (
    <div className={cn(className, 'relative max-w-full prose dark:text-foreground EkoEdit')}>
      <EditorContent editor={editor} />

      {/* Footnote list when inlineCitations is off */}
      {!inlineCitations && citations && citations.length > 0 && (
        <div className="mt-2">
          <hr className="lg:max-w-96 mt-4" />
          <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-slate-200 dark:border-slate-700">References</h2>
          <ul className="mt-1 mb-4 prose dark:text-foreground">
            {citations.map((c, i) => (
              <li key={c.doc_page_id} className="text-xs font-medium">
                {citationPrefix}
                {i + 1}.{' '}
                <a
                  href={citationLink(c, admin)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  {c.title || c.doc_name}
                </a>
                {c.year && (
                  <span className="text-muted-foreground ml-2">
                    [{c.year}]
                  </span>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
});

EkoEdit.displayName = 'EkoEdit';
