// remark-gfm-tables.js          (ES module)
import { gfmTable } from 'micromark-extension-gfm-table'
import { gfmTableFromMarkdown, gfmTableToMarkdown } from 'mdast-util-gfm-table'

/**
 * Add *only* GFM-table support to remark / react-markdown.
 *
 * @param {import('mdast-util-gfm-table').Options} [options]
 */
export default function remarkGfmTables(this: any, options: any = {}) {
  const add = (field: any, value: any) => {
    const data = (this).data();
    (data[field] ? data[field] : (data[field] = [])).push(value)
  }

  add('micromarkExtensions', gfmTable())
  add('fromMarkdownExtensions', gfmTableFromMarkdown())
  add('toMarkdownExtensions', gfmTableToMarkdown(options)) // optional
}
