import React from 'react'
import Link from 'next/link'
import { Badge } from '@ui/components/ui/badge'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'
import { extractDomain, getDomainDisplayName } from '@/utils/url-utils'
import { citationLink, CitationType } from '@/components/citation'

interface DomainBadgeCitationProps {
  citation: CitationType
  admin: boolean
  showYear?: boolean
  display?:number
}

/**
 * A compact badge-style citation that displays the domain name
 * Similar to the ChatGPT citation style
 */
export const DomainBadgeCitation = React.memo<DomainBadgeCitationProps>(({
                                                                           citation,
                                                                           admin,
                                                                           showYear = false,
                                                                           display,
                                                                         }) => {
  if (!citation || !citation.public_url) {
    return null
  }

  const domain = extractDomain(citation.public_url)
  const displayName = getDomainDisplayName(domain)
  const url = citationLink(citation, admin)
  const fullTitle = citation.title || citation.doc_name || domain
  const year = citation.year ? ` (${citation.year})` : ''

  let authors = citation.authors.map((author: any) => author.name)

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {display ? (<Link href={url} className="inline-flex align-super text-xs mr-1"><span className="">{display}</span></Link>) :
              (<Link href={url} className="inline-flex"><Badge
                variant="outline"
              className="inline-flex items-center rounded-full border px-1 py-0.5 text-xs font-medium transition-colors hover:bg-secondary/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer"
            >
                {displayName}
              </Badge></Link>)}
        </TooltipTrigger>
        <TooltipContent className="bg-background">
          <p className="text-xs">
            {fullTitle}{showYear ? year : ''} p.{citation.page}<br/>
            {citation.authors && (
              <span className="text-muted-foreground">
                {authors.filter((item, index) => authors.indexOf(item) === index).join(', ')}
              </span>
            )}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
})
