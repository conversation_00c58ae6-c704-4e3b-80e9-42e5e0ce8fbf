"use client";

import React, { useEffect, useState } from 'react'
import { Card, CardContent } from '@ui/components/ui/card'
import Link from 'next/link'
import { Badge } from '@ui/components/ui/badge'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@ui/components/ui/dialog'
import { Button } from '@ui/components/ui/button'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import TimelineSkeleton from '@/components/skeletons/timeline-skeleton'
// EntityType is now handled through entity-converter
import { useEntity } from '@/components/context/entity/entity-context'

export function Timeline({
                             preamble,
                             obj,
                             variant,
                             entity,
                             'data-testid': dataTestId
                         }: {
    preamble: string;
    obj: any;
    variant: "compact" | "full";
    entity?: any;
    'data-testid'?: string;
}) {
    const [response, setResponse] = useState<any[]>([]);
    const [openDialog, setOpenDialog] = useState<{ [key: number]: boolean }>({});
    const router = useRouter();
  const { queryString, includeDisclosures } = useEntity()

    useEffect(() => {
        let abort:boolean = false;
        setResponse([]);
        fetch('/api/timeline', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
          body: JSON.stringify({ preamble, obj, version: '1.15', includeDisclosures }),
        })
            .then(res => res.json())
            .then(data => {
                if(!abort) {
                    setResponse(data.response);
                }
            });
        return () => {
            abort = true;
        }
    }, [entity, preamble, obj, includeDisclosures]);

    const toggleDialog = (index: number) => {
        setOpenDialog(prev => ({ ...prev, [index]: !prev[index] }));
    };

    if (!response || response.length === 0) {
        return <TimelineSkeleton />;
    }

    return (
        <div className="w-full max-w-3xl mx-auto p-2 pb-6" data-testid={dataTestId}>
            <div className="relative">
                {/* Vertical line */}
                <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5">
                    <div className="absolute left-1/2 transform -translate-x-1/2 top-0 h-20 w-full bg-gradient-to-b from-transparent to-blue-200"></div>
                    <div className="absolute left-1/2 transform -translate-x-1/2 top-20 bottom-20 w-full bg-blue-200"></div>
                    <div className="absolute left-1/2 transform -translate-x-1/2 bottom-0 h-20 w-full bg-gradient-to-t to-blue-200 from-transparent"></div>
                </div>

                <div className="space-y-6">
                    {response.map((event, index) => (
                        <motion.div
                            key={index}
                            initial={{ scaleY: 0, opacity: 0 }}
                            whileInView={{ scaleY: 1, opacity: 1 }}
                            viewport={{ once: true, amount: 0.1 }}
                            transition={{
                                duration: 0.5,
                                ease: "easeOut",
                                delay: index * 0.25
                            }}
                            className={`flex flex-col lg:flex-row items-center ${index % 2 !== 0 ? 'lg:flex-row-reverse' : ''}`}
                        >
                            {/* Content */}
                            <div className="w-full lg:w-5/12">
                                <Card className="transform transition-all hover:scale-105 -mb-8 relative shadow-lg">
                                    <CardContent className="p-2">
                                        <div>
                                            <span className="text-sm font-bold text-blue-300 dark:text-blue-600">{event.year}</span>
                                            <span className="text-xs font-semibold ml-2">{event.event}</span>
                                        </div>
                                        <div className="mt-1 text-[12px] opacity-80">{event.summary}</div>
                                        {event.source === "flag" && (
                                            <Link
                                                className="absolute top-2 right-4"
                                                href={`/customer/dashboard/flags/${event.source_id}?${queryString}`}
                                            >
                                                <Badge variant="secondary" className="text-[10px] font-light h-4">Flag</Badge>
                                            </Link>
                                        )}
                                        <button
                                            className="mt-2 text-blue-500 text-[10px] font-light"
                                            onClick={() => toggleDialog(index)}
                                        >
                                            More
                                        </button>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Center circle */}
                            <div className="w-full lg:w-2/12 flex justify-center my-4 lg:my-0">
                                <motion.div
                                    initial={{ scale: 0, opacity: 0 }}
                                    animate={{ scale: 1, opacity: 1, zIndex: 10 }}
                                    transition={{ duration: 0.5, ease: "easeOut", delay: index * 0.25 }}
                                    className="w-3 h-3 bg-blue-500 rounded-full transform transition-all hover:scale-150"
                                />
                            </div>

                            {/* Empty space for alignment */}
                            <div className="w-full lg:w-5/12" />

                            {/* Dialog */}
                            {openDialog[index] && (
                                <Dialog open={openDialog[index]} onOpenChange={() => toggleDialog(index)}>
                                    <DialogContent>
                                        <DialogHeader>
                                            <DialogTitle>{event.event}</DialogTitle>
                                            <DialogDescription>
                                                <span className="text-sm font-bold text-blue-600">{event.year}</span>
                                            </DialogDescription>
                                        </DialogHeader>
                                        <span className="mt-2 text-md opacity-80">{event.description}</span>
                                        <DialogFooter>

                                            <DialogClose asChild>
                                                <Button variant="outline">Close</Button>
                                            </DialogClose>
                                        </DialogFooter>                                    </DialogContent>

                                </Dialog>
                            )}
                        </motion.div>
                    ))}
                </div>
            </div>
        </div>
    );
}
