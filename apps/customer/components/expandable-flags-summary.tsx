"use client";

import React, { useState } from 'react';
import { Summarize } from '@/components/summarize';
import { Badge } from '@ui/components/ui/badge';
import { FlagTypeV2 } from '@/types';

interface ExpandableFlagsSummaryProps {
  flags: FlagTypeV2[];
  type: 'green' | 'red';
  model: string;
  className?: string;
  preamble: string;
  maxInitialFlags?: number;
}

export function ExpandableFlagsSummary({
  flags,
  type,
  model,
  className,
  preamble,
  maxInitialFlags = 40
}: ExpandableFlagsSummaryProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const flagsForSummary = isExpanded ? flags : flags.slice(0, maxInitialFlags);
  const hasMore = flags.length > maxInitialFlags;
  
  // Create unique hash ID that changes when expansion state changes
  const hashId = `${type}-flags-summary-${model}-${isExpanded ? 'expanded' : 'collapsed'}-${flags.length}`;
  
  const expandedPreamble = isExpanded 
    ? preamble + " Include all actions mentioned in the data."
    : preamble;

  return (
    <div>
      <Summarize
        hashId={hashId}
        className={className}
        preamble={expandedPreamble}
        obj={flagsForSummary}
      />
      
      {hasMore && (
        <div className="mt-3 flex justify-start">
          <Badge 
            variant="secondary" 
            className="cursor-pointer hover:bg-muted/80 transition-colors"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded 
              ? 'Show summary of key actions only' 
              : `Show summary including all ${flags.length} actions`
            }
          </Badge>
        </div>
      )}
    </div>
  );
}