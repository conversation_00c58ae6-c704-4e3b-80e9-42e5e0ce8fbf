import React from 'react'
import { cn } from '@utils/lib/utils'

interface BlockWrapperProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'brand' | 'accent' | 'subtle';
  inset?: boolean;
  spacing?: 'none' | 'small' | 'medium' | 'large';
  container?: boolean;
  id?: string;
}

export const BlockWrapper: React.FC<BlockWrapperProps> = ({
  children,
  className,
  variant = 'default',
  inset = false,
  spacing = 'medium',
  container = true,
  id,
}) => {
  const variantClasses = {
    default: 'bg-background',
    glass: 'glass-effect',
    brand: 'bg-brand-gradient',
    accent: 'bg-brand-gradient-compliment',
    subtle: 'bg-muted/50',
  };

  const spacingClasses = {
    none: '',
    small: 'py-8',
    medium: 'py-16',
    large: 'py-24',
  };

  const insetClasses = inset
    ? 'border-t-4 border-brand dark:border-brand-gradient'
    : '';

  return (
    <section
      id={id}
      className={cn(
        variantClasses[variant],
        spacingClasses[spacing],
        insetClasses,
        'relative overflow-hidden',
        className
      )}
    >
      {container ? (
        <div className="container mx-auto px-4 relative z-10">{children}</div>
      ) : (
        <div className="relative z-10">{children}</div>
      )}
    </section>
  );
};
