import React from 'react'
import { cn } from '@utils/lib/utils'

interface GlassCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'subtle' | 'strong' | 'brand' | 'brand-strong';
  hover?: boolean;
  onClick?: () => void;
}

/**
 * A glass-morphic card component using the design system
 */
export const GlassCard: React.FC<GlassCardProps> = ({
  children,
  className,
  variant = 'default',
  hover = true,
  onClick,
}) => {
  const variantClasses = {
    default: 'glass-effect-lit relative group',
    subtle: 'glass-effect-subtle relative group',
    strong: 'glass-effect-strong relative group',
    brand: 'glass-effect-brand relative group',
    'brand-strong': 'glass-effect-brand-strong relative group',
  };

  const hoverClasses = hover
    ? 'transition-standard hover-lift-subtle shadow-soft hover:shadow-medium'
    : '';

  return (
    <div
      className={cn(
        'rounded-2xl p-6',
        variantClasses[variant],
        hoverClasses,
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {variant !== 'subtle' && variant !== 'strong' && (
        <>
          {/* Additional lighting effect on hover */}
          <div className="absolute inset-0 bg-gradient-radial from-white/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-0 rounded-2xl" style={{ background: 'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 70%)' }}></div>
        </>
      )}
      <div className="relative z-10">{children}</div>
    </div>
  );
};
