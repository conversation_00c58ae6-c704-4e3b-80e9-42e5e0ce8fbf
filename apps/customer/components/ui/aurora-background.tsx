import React from 'react'
import { cn } from '@utils/lib/utils'

interface AuroraBackgroundProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'brand' | 'accent' | 'subtle';
}

export const AuroraBackground: React.FC<AuroraBackgroundProps> = ({
  children,
  className,
  variant = 'default',
}) => {
  const variantStyles = {
    default: 'from-blue-500/20 via-purple-500/20 to-pink-500/20',
    brand: 'from-brand/20 via-brand-dark/20 to-brand-light/20',
    accent: 'from-brand-contrast/20 via-red-500/20 to-orange-500/20',
    subtle: 'from-slate-300/20 via-slate-200/20 to-slate-100/20 dark:from-slate-800/20 dark:via-slate-700/20 dark:to-slate-600/20',
  };

  return (
    <div className={cn('relative overflow-hidden', className)}>
      <div
        className={cn(
          'absolute inset-0 bg-gradient-to-r animate-aurora opacity-30 dark:opacity-20',
          variantStyles[variant]
        )}
        style={{
          backgroundSize: '400% 400%',
          filter: 'blur(50px)',
          transform: 'translateZ(0)',
        }}
      />
      <div className="relative z-10">{children}</div>
    </div>
  );
};
