import React from 'react'
import { SidebarTrigger } from '@ui/components/ui/sidebar'
import { Separator } from '@ui/components/ui/separator'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@ui/components/ui/breadcrumb'
import { useNav } from '@/components/context/nav/nav-context'
import { cn } from '@utils/lib/utils'

export interface NavigationItem {
  label: React.ReactNode | undefined;
  href?: string | undefined;
}

export function PageHeader({
  navPath,
  children,
  title,
  className
}: {
  navPath?: NavigationItem[],
  children?: React.ReactNode,
  title?: string | undefined,
  className?: string
}) {
  const nav = useNav();

  if (!title) {
    title = nav.title;
  }

  if (!navPath) {
    navPath = nav.path;
  }

  return (
    <header className={cn(
      "flex h-16 shrink-0 items-center px-4 text-foreground w-full glass-effect-subtle border-b border-border/20 backdrop-blur-md",
      className
    )}>
      <div className="flex items-center gap-2 w-full">
        <SidebarTrigger className="-ml-1 mr-2 hover:bg-background/20 transition-colors duration-200"/>
        {children && (
          <>
            <Separator orientation="vertical" className="h-6 mx-2"/>
            <div className="items-center gap-2 flex-grow">
              {children}
            </div>
          </>
        )}
        <Separator orientation="vertical" className="hidden lg:flex flex-shrink h-6 mx-2"/>
        <div className="hidden lg:flex flex-grow align-middle text-center heading-5">
          {nav.path.length > 0 && nav.path[nav.path.length - 1].label ?
            nav.path[nav.path.length - 1].label :
            (title || (typeof window !== 'undefined' && window.location.pathname))}
        </div>
      </div>
    </header>
  );
}
