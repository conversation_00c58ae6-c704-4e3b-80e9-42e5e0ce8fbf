# Document/Reports Editor

## Overview

The Document/Reports Editor is a comprehensive, TipTap-based report generation and editing framework that enables users to create, edit, and manage structured ESG reports with dynamic content generation, real-time collaboration, and AI-powered editing capabilities. The system provides an interactive editor where report components remain functional and can be refreshed, reconfigured, or edited while preserving their structured nature.

### Key Innovations

- **Template-Driven Workflow**: Create documents from pre-built or custom templates
- **Dynamic Report Components**: Functional report sections that can refresh content from APIs
- **Real-Time Collaboration**: Multi-user editing with live cursors and presence indicators
- **AI-Powered Editing**: Intelligent content generation and editing assistance
- **Visual Design System**: Glass-morphism design with intuitive component management
- **Comprehensive Testing**: Robust test suite with real database integration

## ✨ Key Features

### 🏗️ Template-Based Document Creation
- **Document Templates**: Create documents from pre-built templates stored in `document_templates` table
- **Template Scopes**: Global (pre-built), customer-specific, and organization-shared templates
- **Template Structure**: Templates contain TipTap JSON data with report components pre-configured

### 📊 Report Components

The editor supports three main types of report components:

#### 1. Report Sections (`<report-section>`)
- **Purpose**: Generate content from specific API endpoints
- **Visual**: Light blue border with component ID tab
- **Functionality**: 
  - Auto-load content from configured endpoints when first imported
  - Support for refresh, configure, lock, preserve, and delete operations
  - Configurable endpoint parameters (entity, model, section, run, disclosures)

#### 2. Report Groups (`<report-group>`)
- **Purpose**: Organize related report sections
- **Visual**: Light green border with component ID tab
- **Functionality**: Container for grouping multiple report sections or other components

#### 3. Report Summaries (`<report-summary>`)
- **Purpose**: Generate summaries from other report components
- **Visual**: Light purple border with component ID tab
- **Functionality**: 
  - Summarize content from specified report sections or groups
  - Dependency management through ReportStateManager
  - Wait for dependent components to load before generating summary

### 🤖 AI Features

#### AI-Powered Editing
- **Conversational Interface**: Chat with AI about document content
- **Context-Aware**: AI understands the current document state
- **Streaming Responses**: Real-time response generation
- **Smart Editing**: AI can directly edit document content
- **Change Tracking**: All AI edits are tracked with accept/reject controls
- **Visual Feedback**: Green highlights for additions, red for deletions

#### AI Toolbar Integration
- **Rewrite**: Improve selected text
- **Expand**: Add more detail to content
- **Summarize**: Create concise summaries
- **Translate**: Translate to different languages
- **Fix Grammar**: Correct grammar and style

### 🔄 Collaborative Features

#### Real-Time Collaboration
- **Multi-User Editing**: Multiple users can edit simultaneously
- **Live Cursors**: See where other users are typing
- **Presence Indicators**: Active user list with avatars
- **Conflict Resolution**: Automatic merging of concurrent changes

#### Comments System
- **Inline Comments**: Add comments anywhere in the document
- **Real-Time Updates**: Comments appear instantly for all users
- **Reply Threads**: Nested comment conversations
- **Resolution Tracking**: Mark comments as resolved

#### Version History
- **Auto-Versioning**: Automatic version saves
- **Version Comparison**: Side-by-side version diffs
- **Restore Capability**: Revert to previous versions
- **Change Attribution**: Track who made what changes

### 🔗 API Integration

#### Report Endpoints
The system uses structured API endpoints that return JSON with:
```json
{
  "text": "Markdown content with HTML support",
  "citations": [
    {
      "doc_page_id": 123,
      "title": "Citation title",
      "url": "https://example.com",
      "doc_id": 456,
      "doc_name": "Document name"
    }
  ],
  "metadata": {
    "entityId": "entity-id",
    "modelName": "model-name",
    "modelSection": "section-name",
    "type": "section|summary|transparency|reliability"
  }
}
```

#### Supported Endpoint Patterns
- **Harm Model Sections**: `/report/entity/[entityId]/harm/model/[model]/section/[modelSection]?run_id=X&disclosures=true`
- **Harm Model Levels**: `/report/entity/[entityId]/harm/model/[model]/level/[level]?run_id=X&disclosures=true`
- **Transparency**: `/report/entity/[entityId]/transparency?run_id=X&disclosures=true`
- **Reliability**: `/report/entity/[entityId]/reliability?run_id=X&disclosures=true`
- **Summarization**: `POST /report/summarize` with content array

### 🎛️ Component Controls

Each report component includes a control header with:
- **Component ID**: User-editable identifier
- **Dropdown Menu**: Access to component actions
- **Visual Indicators**: Icons for locked/preserved states

#### Available Actions
- **Refresh**: Regenerate content from the API endpoint
- **Configure**: Modify component properties (ID, title, prompt, endpoint parameters)
- **Lock**: Prevent any edits or refreshes (shows lock icon)
- **Preserve**: Prevent refreshes but allow manual edits (shows shield icon)
- **Delete**: Remove component from document

## 🚀 Quick Start Guide

### Prerequisites

1. **Environment Setup**:
   ```bash
   # Required environment variables
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ANTHROPIC_API_KEY=your_api_key_here  # For AI features
   ```

2. **Database Setup**:
   ```sql
   -- Run the collaborative documents migration
   -- Located in: apps/customer/supabase/migrations/20250115000000_collaborative_documents.sql
   ```

3. **Dependencies**:
   ```bash
   # Core TipTap and collaboration packages are already installed
   npm install @tiptap/react @tiptap/starter-kit
   ```

### Basic Usage

#### Creating a New Report
1. Navigate to `/customer/documents`
2. Click "New Document"
3. Select template (e.g., "ESG Report")
4. Document created with pre-configured report components
5. Select entity and run from global selectors
6. Components auto-load content based on selections

#### Customizing Components
1. Click component menu (⋮) button
2. Select "Configure" to modify properties
3. Update ID, title, prompt, or endpoint parameters
4. Save changes to regenerate content

#### Adding New Components
1. Use toolbar buttons to insert new components
2. Configure component properties in dialog
3. Component automatically inserted into document
4. Content loads based on configuration

### Code Examples

#### Basic Editor Implementation
```tsx
import { EkoDocumentEditor } from '@/components/editor/EkoDocumentEditor'

export default function DocumentPage({ params }: { params: { id: string } }) {
  return (
    <EkoDocumentEditor
      documentId={params.id}
      editable={true}
      showToolbar={true}
      showCollaboration={true}
      showAI={true}
      user={{
        id: user.id,
        name: user.name,
        email: user.email,
        color: '#3B82F6'
      }}
      featureFlags={{
        aiTools: true,
        comments: true,
        share: true,
        exportWord: true
      }}
    />
  )
}
```

#### Document Context Usage
```tsx
import { DocumentProvider } from '@/components/editor/context/DocumentContext'

function App() {
  return (
    <DocumentProvider>
      <EkoDocumentEditor documentId="doc-123" />
    </DocumentProvider>
  )
}
```

#### AI Command Usage
```typescript
// Example AI command usage
const handleAICommand = async (command: string) => {
  const response = await fetch('/api/ai/chat', {
    method: 'POST',
    body: JSON.stringify({
      messages: [{ role: 'user', content: command }],
      documentContent: editor.getJSON(),
      action: 'edit'
    })
  })
  
  const result = await response.json()
  if (result.type === 'edit') {
    editor.commands.applyAIPatchWithTracking(result.patch)
  }
}
```

## 🔧 Technical Architecture

### Core Components

#### Main Editor
- **EkoDocumentEditor.tsx**: Primary editor component with all features
- **SafeEditorContent**: Error boundary wrapper for robust rendering
- **DocumentContext**: Global state management for entity/run selection

#### Extensions System
```
/extensions/
├── ReportSectionExtension.tsx    # Dynamic API-driven content
├── ReportGroupExtension.tsx       # Container for organizing sections
├── ReportSummaryExtension.tsx     # AI-powered summarization
├── CitationExtension.tsx          # Citation management
├── AICommandExtension.tsx         # AI editing capabilities
├── ChangeTrackingExtension.tsx    # Track AI edits
├── CollaborationExtension.tsx     # Real-time collaboration
└── [20+ other extensions]
```

#### Toolbar & UI
```
/toolbar/
├── EditorToolbar.tsx              # Main formatting toolbar
├── AIToolbar.tsx                  # AI-specific commands
├── CollaborationToolbar.tsx       # User presence & sharing
└── SupabaseCollaborationToolbar.tsx

/panels/
├── AIChatPanel.tsx               # AI conversation interface
├── CommentsPanel.tsx             # Document comments
├── HistoryPanel.tsx              # Version history
├── SharePanel.tsx                # Sharing & permissions
└── TabbedSidePanel.tsx           # Container for all panels
```

#### Hooks & Utilities
```
/hooks/
├── useSupabaseAutoSave.ts        # Document persistence
├── useReportManager.ts           # Report state coordination
├── usePresence.ts                # User presence tracking
└── useDocumentAutoSave.ts        # Auto-save logic

/utils/
├── export-utils.ts               # Export functionality
├── markdown-processor.ts         # Markdown processing
└── schemaSerializer.ts           # TipTap schema management
```

### Database Schema

#### Core Tables
- **collaborative_documents**: Document storage with TipTap JSON
- **document_templates**: Template definitions and content
- **document_comments**: Inline commenting system
- **document_versions**: Version history tracking
- **document_permissions**: Access control
- **document_presence**: Real-time user presence

#### Reference Tables
- **xfer_entities_v2**: Available entities for reports
- **xfer_runs_v2**: Available analysis runs
- **xfer_model_sections_v2**: Model and section definitions

### State Management

#### Document Context
```typescript
// Global document state
interface DocumentContextType {
  selectedEntity: Entity | null
  selectedRun: string | null
  setSelectedEntity: (entity: Entity | null) => void
  setSelectedRun: (runId: string | null) => void
  replacePlaceholders: (text: string) => string
}
```

#### Report State Manager
```typescript
// Component coordination
interface ReportStateManager {
  registerComponent: (id: string, type: string) => void
  updateComponentState: (id: string, state: ComponentState) => void
  getCitations: () => Citation[]
  registerCitations: (citations: Citation[]) => void
}
```

## 🎨 Design System

### Visual Design Philosophy

#### Glass-Morphism Design System
- **Component Borders**: Color-coded borders (blue=sections, green=groups, purple=summaries)
- **Rounded Corners**: Heavily rounded elements (1.5rem standard)
- **Backdrop Effects**: Translucent surfaces with blur effects
- **Hover States**: Subtle animations and lift effects

#### Component Layout
```
┌─────────────────────────────────────┐
│ [🏷️ component-id] [🔒] [⋮ Menu]      │ ← Control Header with Status Icons
├─────────────────────────────────────┤
│                                     │
│ Component Content Area              │ ← Generated/Editable Content
│ (Markdown with HTML support)       │ ← AI-tracked changes highlighted
│ [💬 Comments] [📊 Citations]        │ ← Inline interactions
│                                     │
└─────────────────────────────────────┘
```

#### Color Coding System
```css
/* Report component colors */
.report-section {
  border: 2px solid #3B82F6; /* Blue */
  background: rgba(59, 130, 246, 0.05);
}

.report-group {
  border: 2px solid #10B981; /* Green */
  background: rgba(16, 185, 129, 0.05);
}

.report-summary {
  border: 2px solid #8B5CF6; /* Purple */
  background: rgba(139, 92, 246, 0.05);
}

/* AI change tracking */
.insertion {
  background: rgba(16, 185, 129, 0.2); /* Green highlight */
}

.deletion {
  background: rgba(239, 68, 68, 0.2); /* Red highlight */
}
```

## 🧪 Testing

### Test Suite Overview

The editor includes a comprehensive test suite covering all major functionality:

#### Test Categories
1. **Document Templates** (80% pass rate)
   - Template loading and selection
   - Document creation from templates
   - Template metadata persistence

2. **Report API** (92% pass rate)
   - Endpoint validation and response format
   - Error handling and parameter validation
   - Citation format consistency

3. **Report System** (40% pass rate)
   - Component creation and configuration
   - Component state management (lock/preserve)
   - Content loading and refresh functionality

4. **Integration Tests** (60% pass rate)
   - End-to-end workflows
   - Collaborative editing simulation
   - Performance with large documents

#### Running Tests

```bash
# Run all tests
npx playwright test --reporter=line

# Run specific test suite
npx playwright test tests/document-templates.spec.ts

# Run tests with UI
npx playwright test --ui
```

#### Test Configuration
- **Configurable Test Data**: Real database entities and runs in `tests/test-config.ts`
- **Authentication**: Demo credentials (<EMAIL>/demo)
- **Browser Support**: Chromium and Firefox (WebKit excluded due to auth issues)
- **Test Utilities**: Helper functions in `tests/helpers/test-utils.ts`

### Writing Tests

```typescript
// Example test structure
import { test, expect } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Document Editor', () => {
  test.beforeEach(async ({ page }) => {
    await TestUtils.login(page)
  })

  test('should create document from template', async ({ page }) => {
    const documentId = await TestUtils.createDocumentFromTemplate(page, 'ESG Report')
    expect(documentId).toBeTruthy()
  })
})
```

## 📋 Current Status

### ✅ Fully Implemented Features
- **Template System**: Complete template creation and selection
- **Report Components**: All three component types working
- **API Integration**: Full endpoint integration with error handling
- **Collaborative Editing**: Real-time multi-user editing
- **AI Features**: Chat, editing, and change tracking
- **Export Functions**: PDF, DOCX, Markdown, and HTML export
- **Comments & History**: Full commenting and version control
- **Auto-Save**: Robust auto-saving with conflict resolution

### ⚠️ Partially Working Features
- **Component Creation**: Dialog functionality works, insertion mechanism needs refinement
- **Lock/Preserve Indicators**: Logic implemented, visual indicators need enhancement
- **Advanced Summarization**: Basic summarization works, dependency chains need improvement

### ❌ Known Issues
- **WebKit Compatibility**: Authentication issues in Safari/WebKit browsers
- **Test Flakiness**: Some integration tests have timing issues
- **API Rate Limiting**: Concurrent requests can trigger rate limits
- **Menu Interactions**: Some dropdown menu interactions timeout in tests

### 🎯 Future Enhancements

#### Short Term
- **Enhanced Component Creation**: Improve TipTap content insertion
- **Visual Indicators**: Better lock/preserve state visualization
- **Test Stability**: Fix timing issues in integration tests
- **Performance**: Optimize for large documents

#### Long Term
- **Advanced AI**: Multi-step AI workflows and agents
- **Template Marketplace**: Organization-wide template sharing
- **Advanced Analytics**: Document usage and collaboration metrics
- **Mobile Support**: Enhanced mobile editing experience

## 🔧 Development

### Getting Started

1. **Clone and Install**:
   ```bash
   git clone <repository>
   cd apps/customer
   npm install
   ```

2. **Environment Setup**:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Database Setup**:
   ```bash
   # Run migrations
   npm run db:migrate
   
   # Seed test data
   npm run db:seed
   ```

4. **Development Server**:
   ```bash
   npm run dev
   ```

### Extension Development

#### Creating Custom Extensions

```typescript
// Example custom extension
import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import { MyCustomComponent } from './MyCustomComponent'

export const MyCustomExtension = Node.create({
  name: 'myCustomNode',
  
  addAttributes() {
    return {
      id: {
        default: null,
      },
      title: {
        default: '',
      },
    }
  },
  
  parseHTML() {
    return [{ tag: 'my-custom-node' }]
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['my-custom-node', mergeAttributes(HTMLAttributes)]
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(MyCustomComponent)
  },
})
```

### Performance Optimization

#### Memoization Strategy
```tsx
// Prevent unnecessary re-renders
const MemoizedComponent = React.memo(({ data, onUpdate }) => {
  const handleUpdate = useCallback((newData) => {
    onUpdate(newData)
  }, [onUpdate])
  
  const processedData = useMemo(() => {
    return expensiveDataProcessing(data)
  }, [data])
  
  return <ComponentUI data={processedData} onUpdate={handleUpdate} />
})
```

### Quality Assurance

#### Pre-commit Checklist
```bash
# Type checking
tsc --noEmit

# Run tests
npx playwright test --reporter=line

# Lint code
npm run lint

# Format code
npm run format
```

## 📚 Additional Documentation

### Related Files
- **DESIGN.md**: Original design goals and architecture decisions
- **PLAN.md**: Implementation status and completed features
- **AI_AGENT_DESIGN.md**: Detailed AI features architecture
- **REPORTS_PLAN.md**: Report system implementation details
- **COLLABORATIVE_EDITING.md**: Collaboration setup and usage

### API Documentation
- **Report API**: `/api/report/*` - Content generation endpoints
- **AI API**: `/api/ai/*` - AI chat and editing endpoints
- **Document API**: `/api/documents/*` - Document management

### Contributing
1. Read the existing documentation
2. Follow the established code patterns
3. Write tests for new features
4. Update documentation as needed
5. Follow the commit message conventions

## 📞 Support

For issues or questions:
1. Check the test suite for expected behavior
2. Review the existing documentation
3. Look at similar implementations in the codebase
4. Create detailed issue reports with reproduction steps

---

*This documentation is maintained alongside the codebase. Please update it when making significant changes to the editor.*
