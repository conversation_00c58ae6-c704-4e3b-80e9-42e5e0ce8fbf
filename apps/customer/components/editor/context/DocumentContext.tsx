import React, { createContext, useContext, useReducer, useRef } from 'react'
import { flushSync } from 'react-dom'
import { Editor } from '@tiptap/react'
import { CitationType } from '@/components/citation'
import { createClient } from '@/app/supabase/client'
import { useAuth } from '@/components/context/auth/auth-context'

// Types
export interface ReportComponent {
  id: string
  type: 'report-section' | 'report-group' | 'report-summary'
  status: 'idle' | 'loading' | 'loaded' | 'error' | 'preserved' | 'locked'
  title?: string
  content?: string
  endpoint?: string
  prompt?: string
  dependencies?: string[]
  parentId?: string
  children?: string[]
  error?: string
  lastRefreshed?: Date
}

export interface DocumentState {
  // Editor state
  editor: Editor | null
  content: string
  data: any
  isDirty: boolean

  // Auto-save state
  isSaving: boolean
  lastSaved: Date | null
  saveError: string | null

  // Report state
  components: Map<string, ReportComponent>
  citations: CitationType[]

  // UI state
  showSidePanel: boolean
  activePanelTab: 'comments' | 'history' | 'share' | 'ai'

  // Entity/Run tracking for report sections
  currentEntity: string | null
  currentRun: string | null

  // Document initialization state (EKO-118)
  isInitialized: boolean
  hasTriggeredInitialLoad: boolean
}

export type DocumentAction =
  | { type: 'EDITOR_CREATED'; editor: Editor }
  | { type: 'CONTENT_CHANGED'; content: string; data: any; source: 'user' | 'system' | 'restore' }
  | { type: 'SAVE_STARTED' }
  | { type: 'SAVE_COMPLETED'; timestamp: Date }
  | { type: 'SAVE_FAILED'; error: string }
  | { type: 'COMPONENT_REGISTERED'; component: ReportComponent }
  | { type: 'COMPONENT_UPDATED'; id: string; updates: Partial<ReportComponent> }
  | { type: 'COMPONENT_REMOVED'; id: string }
  | { type: 'CITATIONS_REGISTERED'; citations: CitationType[] }
  | { type: 'CITATION_ADDED'; citation: CitationType }
  | { type: 'UI_PANEL_TOGGLE'; show?: boolean; tab?: DocumentState['activePanelTab'] }
  | { type: 'ENTITY_CHANGED'; entity: string | null; run: string }
  | { type: 'RESET_DIRTY_STATE' }
  | { type: 'DOCUMENT_INITIALIZED' }
  | { type: 'INITIAL_LOAD_TRIGGERED' }

const initialState: DocumentState = {
  editor: null,
  content: '',
  data: null,
  isDirty: false,
  isSaving: false,
  lastSaved: null,
  saveError: null,
  components: new Map(),
  citations: [],
  showSidePanel: false,
  activePanelTab: 'comments',
  currentEntity: null,
  currentRun: null,
  isInitialized: false,
  hasTriggeredInitialLoad: false,
}

function documentReducer(state: DocumentState, action: DocumentAction): DocumentState {
  switch (action.type) {
    case 'EDITOR_CREATED':
      return {
        ...state,
        editor: action.editor,
      }

    case 'CONTENT_CHANGED': {
      // Mark as dirty if content actually changed, regardless of source
      const contentChanged =
        action.content !== state.content || JSON.stringify(action.data) !== JSON.stringify(state.data)
      const shouldMarkDirty = contentChanged && (action.source === 'user' || action.source === 'system')

      return {
        ...state,
        content: action.content,
        data: action.data,
        isDirty: shouldMarkDirty || state.isDirty,
        saveError: null, // Clear any previous save errors
      }
    }

    case 'SAVE_STARTED':
      return {
        ...state,
        isSaving: true,
        saveError: null,
      }

    case 'SAVE_COMPLETED':
      return {
        ...state,
        isSaving: false,
        lastSaved: action.timestamp,
        isDirty: false,
        saveError: null,
      }

    case 'SAVE_FAILED':
      return {
        ...state,
        isSaving: false,
        saveError: action.error,
      }

    case 'COMPONENT_REGISTERED': {
      const newComponents = new Map(state.components)
      const existing = newComponents.get(action.component.id)

      // Only update if the component has actually changed to prevent unnecessary re-renders
      if (!existing || JSON.stringify(existing) !== JSON.stringify(action.component)) {
        newComponents.set(action.component.id, action.component)

        return {
          ...state,
          components: newComponents,
        }
      }

      return state // No change needed
    }

    case 'COMPONENT_UPDATED': {
      const newComponents = new Map(state.components)
      const existing = newComponents.get(action.id)
      if (existing) {
        newComponents.set(action.id, { ...existing, ...action.updates })
      }

      return {
        ...state,
        components: newComponents,
      }
    }

    case 'COMPONENT_REMOVED': {
      const newComponents = new Map(state.components)
      newComponents.delete(action.id)

      return {
        ...state,
        components: newComponents,
      }
    }

    case 'CITATIONS_REGISTERED':
      return {
        ...state,
        citations: [
          ...state.citations,
          ...action.citations.filter(
            (newCit) => !state.citations.some((existing) => existing.doc_page_id === newCit.doc_page_id)
          ),
        ],
      }

    case 'CITATION_ADDED':
      if (state.citations.some((cit) => cit.doc_page_id === action.citation.doc_page_id)) {
        return state // Citation already exists
      }
      return {
        ...state,
        citations: [...state.citations, action.citation],
      }

    case 'UI_PANEL_TOGGLE':
      return {
        ...state,
        showSidePanel: action.show ?? !state.showSidePanel,
        activePanelTab: action.tab ?? state.activePanelTab,
      }

    case 'ENTITY_CHANGED':
      return {
        ...state,
        currentEntity: action.entity,
        currentRun: action.run,
      }

    case 'RESET_DIRTY_STATE':
      return {
        ...state,
        isDirty: false,
      }

    case 'DOCUMENT_INITIALIZED':
      return {
        ...state,
        isInitialized: true,
      }

    case 'INITIAL_LOAD_TRIGGERED':
      return {
        ...state,
        hasTriggeredInitialLoad: true,
      }

    default:
      return state
  }
}

interface DocumentContextValue {
  state: DocumentState
  dispatch: React.Dispatch<DocumentAction>

  // Convenience methods
  registerComponent: (component: ReportComponent) => void
  updateComponent: (id: string, updates: Partial<ReportComponent>) => void
  removeComponent: (id: string) => void
  registerCitations: (citations: CitationType[]) => void
  addCitation: (citation: CitationType) => void
  togglePanel: (show?: boolean, tab?: DocumentState['activePanelTab']) => void
  setEntityRun: (entity: string | null, run: string) => void

  // Dependency management
  areDependenciesReady: (componentId: string) => boolean
  waitForDependencies: (componentId: string) => Promise<void>

  // Entity change listeners
  addEntityChangeListener: (listener: (entity: string | null, run: string) => void) => () => void
  notifyEntityChange: (entity: string | null, run: string) => void

  // Group status management
  updateGroupStatus: (groupId: string, immediate?: boolean) => void
  updateAllGroupStatuses: () => void

  // Document versioning and saving
  createDocumentVersion: (changeSummary: string) => Promise<void>
  triggerImmediateSave: () => Promise<void>
  triggerAutoSaveVersion: () => Promise<void>

  // Refresh functionality
  refreshAllDescendants: (groupId: string) => void

  // Document initialization (EKO-118)
  triggerInitialLoad: () => void

  // Check if all components are loaded
  areAllComponentsLoaded: () => boolean
}

const DocumentContext = createContext<DocumentContextValue | null>(null)

export const useDocumentContext = () => {
  const context = useContext(DocumentContext)
  if (!context) {
    throw new Error('useDocumentContext must be used within a DocumentProvider')
  }
  return context
}

interface DocumentProviderProps {
  children: React.ReactNode
  documentId: string
  onSave?: (content: string, data?: any) => Promise<void> | void
  initialEntity?: string | null
  initialRun?: string
}

export const DocumentProvider: React.FC<DocumentProviderProps> = ({
  children,
  documentId,
  onSave,
  initialEntity,
  initialRun = 'latest',
}) => {
  const [state, dispatch] = useReducer(documentReducer, initialState)

  // Dependency management state
  const dependencyWaitersRef = useRef<Map<string, Array<() => void>>>(new Map())
  const entityChangeListenersRef = useRef<Set<(entity: string | null, run: string) => void>>(new Set())

  // Debounced group status update mechanism
  const groupStatusUpdateTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const pendingGroupUpdates = useRef<Set<string>>(new Set())

  // Track orphaned components waiting for their parents
  const orphanedComponentsRef = useRef<Map<string, ReportComponent[]>>(new Map())

  // Keep a ref to the current state to avoid stale closures in debounced functions
  const stateRef = useRef(state)
  stateRef.current = state

  // Debounced group status update function
  const scheduleGroupStatusUpdate = React.useCallback((groupId: string, immediate = false) => {
    console.log(`DocumentContext.scheduleGroupStatusUpdate: Scheduling update for ${groupId}, immediate: ${immediate}`)

    // Clear existing timeout for this group
    const existingTimeout = groupStatusUpdateTimeouts.current.get(groupId)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
      groupStatusUpdateTimeouts.current.delete(groupId)
    }

    // Add to pending updates
    pendingGroupUpdates.current.add(groupId)

    if (immediate) {
      // Execute immediately with flushSync for synchronous state updates
      flushSync(() => {
        updateGroupStatusInternal(groupId)
      })
      pendingGroupUpdates.current.delete(groupId)
    } else {
      // Debounce the update
      const timeout = setTimeout(() => {
        if (pendingGroupUpdates.current.has(groupId)) {
          updateGroupStatusInternal(groupId)
          pendingGroupUpdates.current.delete(groupId)
        }
        groupStatusUpdateTimeouts.current.delete(groupId)
      }, 50) // 50ms debounce delay

      groupStatusUpdateTimeouts.current.set(groupId, timeout)
    }
  }, [])

  // Initialize entity/run from props
  React.useEffect(() => {
    if (initialEntity && initialEntity !== state.currentEntity) {
      console.log(`DocumentContext: Setting initial entity/run: ${initialEntity}/${initialRun}`)
      dispatch({ type: 'ENTITY_CHANGED', entity: initialEntity, run: initialRun })
    }
  }, [initialEntity, initialRun, state.currentEntity])

  // Cleanup timeouts on unmount
  React.useEffect(() => {
    return () => {
      // Clear all pending group status update timeouts
      groupStatusUpdateTimeouts.current.forEach((timeout) => clearTimeout(timeout))
      groupStatusUpdateTimeouts.current.clear()
      pendingGroupUpdates.current.clear()
      // Clear orphaned components
      orphanedComponentsRef.current.clear()
      // Clear pending registrations
      pendingRegistrationsRef.current.clear()
      // Clear initialization timeout (EKO-118)
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current)
        initializationTimeoutRef.current = null
      }
    }
  }, [])

  // Helper function to get all descendants (recursive)
  const getAllDescendants = React.useCallback(
    (parentId: string): ReportComponent[] => {
      const directChildren = Array.from(state.components.values()).filter((component) => component.parentId === parentId)

      const allDescendants: ReportComponent[] = [...directChildren]

      // Recursively get descendants of each child group
      directChildren.forEach((child) => {
        if (child.type === 'report-group') {
          allDescendants.push(...getAllDescendants(child.id))
        }
      })

      return allDescendants
    },
    [state.components]
  )

  // Helper function to get all descendants using a specific components map (for avoiding stale closures)
  const getAllDescendantsFromState = (groupId: string, components: Map<string, ReportComponent>): ReportComponent[] => {
    const directChildren = Array.from(components.values()).filter((component) => component.parentId === groupId)

    const allDescendants: ReportComponent[] = [...directChildren]

    // Recursively get descendants of each child group
    directChildren.forEach((child) => {
      if (child.type === 'report-group') {
        allDescendants.push(...getAllDescendantsFromState(child.id, components))
      }
    })

    return allDescendants
  }

  // Internal group status management - performs the actual status update
  const updateGroupStatusInternal = React.useCallback(
    (groupId: string) => {
      console.log(`DocumentContext.updateGroupStatus: Called for ${groupId}`)

      // Get the current state from the reducer to avoid stale closures
      const currentState = stateRef.current
      console.log(
        `DocumentContext.updateGroupStatus: Current components:`,
        Array.from(currentState.components.keys())
      )
      const group = currentState.components.get(groupId)
      if (!group) {
        console.log(`DocumentContext.updateGroupStatus: ${groupId} not found in components map`)
        return
      }
      if (group.type !== 'report-group') {
        console.log(
          `DocumentContext.updateGroupStatus: ${groupId} is not a report-group (type: ${group.type})`
        )
        return
      }

      // Skip if group is in a final state (locked/preserved)
      if (group.status === 'locked' || group.status === 'preserved') {
        console.log(`DocumentContext.updateGroupStatus: ${groupId} is ${group.status}, skipping`)
        return
      }

      // Get ALL descendants (not just direct children) using current state
      const descendants = getAllDescendantsFromState(groupId, currentState.components)

      console.log(
        `DocumentContext.updateGroupStatus: ${groupId} has ${descendants.length} descendants:`,
        descendants.map((c) => `${c.id}(${c.type}):${c.status}`)
      )

      if (descendants.length === 0) {
        // No descendants, group is loaded
        if (group.status !== 'loaded') {
          console.log(
            `DocumentContext.updateGroupStatus: ${groupId} has no descendants, setting status to 'loaded'`
          )
          dispatch({ type: 'COMPONENT_UPDATED', id: groupId, updates: { status: 'loaded' } })
        }
        return
      }

      // Check ALL descendant status (Report Groups, Report Summaries, Report Sections)
      const hasError = descendants.some((descendant) => descendant.status === 'error')
      const hasIdleOrLoading = descendants.some(
        (descendant) => descendant.status === 'idle' || descendant.status === 'loading'
      )
      const allLoaded = descendants.every(
        (descendant) =>
          descendant.status === 'loaded' ||
          descendant.status === 'preserved' ||
          descendant.status === 'locked'
      )

      console.log(`DocumentContext.updateGroupStatus: ${groupId} detailed analysis:`)
      console.log(`  - Current group status: ${group.status}`)
      console.log(`  - Total descendants: ${descendants.length}`)
      console.log(`  - Has error: ${hasError}`)
      console.log(`  - Has idle/loading: ${hasIdleOrLoading}`)
      console.log(`  - All loaded: ${allLoaded}`)
      console.log(
        `  - Descendant details:`,
        descendants.map((d) => ({
          id: d.id,
          type: d.type,
          status: d.status,
          parentId: d.parentId,
        }))
      )

      // Enhanced logging for the 'report' group that exec-summary depends on
      if (groupId === 'report') {
        console.log(`REPORT GROUP DEBUG: Enhanced analysis for group '${groupId}':`)
        console.log(
          `  - Direct children:`,
          Array.from(currentState.components.values())
            .filter((c) => c.parentId === groupId)
            .map((c) => `${c.id}(${c.type}):${c.status}`)
        )
        console.log(
          `  - All descendants:`,
          descendants.map((d) => `${d.id}(${d.type}):${d.status}`)
        )
        console.log(`  - Status breakdown:`)
        console.log(
          `    - Loaded: ${descendants.filter((d) => d.status === 'loaded').length}`
        )
        console.log(
          `    - Loading: ${descendants.filter((d) => d.status === 'loading').length}`
        )
        console.log(
          `    - Idle: ${descendants.filter((d) => d.status === 'idle').length}`
        )
        console.log(
          `    - Error: ${descendants.filter((d) => d.status === 'error').length}`
        )
        console.log(
          `    - Preserved: ${descendants.filter((d) => d.status === 'preserved').length}`
        )
        console.log(
          `    - Locked: ${descendants.filter((d) => d.status === 'locked').length}`
        )
      }

      let newStatus: ReportComponent['status']
      if (hasError) {
        newStatus = 'error'
      } else if (allLoaded) {
        newStatus = 'loaded'
      } else {
        newStatus = 'loading'
      }

      console.log(
        `DocumentContext.updateGroupStatus: ${groupId} determined new status: ${newStatus}`
      )

      // Only update if status actually changed
      if (group.status !== newStatus) {
        console.log(
          `DocumentContext.updateGroupStatus: ${groupId} updating status from '${group.status}' to '${newStatus}' based on ${descendants.length} descendants`
        )
        dispatch({ type: 'COMPONENT_UPDATED', id: groupId, updates: { status: newStatus } })

        // Verify the update was applied
        setTimeout(() => {
          const updatedGroup = state.components.get(groupId)
          console.log(
            `DocumentContext.updateGroupStatus: ${groupId} verification - status after update: ${updatedGroup?.status}`
          )
        }, 10)
      } else {
        console.log(
          `DocumentContext.updateGroupStatus: ${groupId} status already '${newStatus}', no update needed`
        )
      }
    },
    [dispatch]
  )

  // Public group status update function - uses debounced mechanism
  const updateGroupStatus = React.useCallback(
    (groupId: string, immediate = false) => {
      scheduleGroupStatusUpdate(groupId, immediate)
    },
    [scheduleGroupStatusUpdate]
  )

  // Track components that need post-registration processing
  const pendingRegistrationsRef = useRef<Set<string>>(new Set())

  // EKO-118: Document initialization tracking
  const initializationTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const INITIALIZATION_DELAY = 1000 // 1 second delay to detect when component registration is complete

  // Convenience methods - memoized to prevent unnecessary re-renders
  const registerComponent = React.useCallback(
    (component: ReportComponent) => {
      console.log(
        `DocumentContext.registerComponent: Registering ${component.type} ${component.id} with parent ${
          component.parentId || 'none'
        }`
      )

      // Always register the component first
      dispatch({ type: 'COMPONENT_REGISTERED', component })

      // Mark this component for post-registration processing
      pendingRegistrationsRef.current.add(component.id)
    },
    [dispatch]
  )

  // Effect to handle post-registration processing after state updates
  React.useEffect(() => {
    if (pendingRegistrationsRef.current.size === 0) return

    // Use setTimeout to ensure the state has been fully updated
    const timeoutId = setTimeout(() => {
      // Process all pending registrations
      const pendingIds = Array.from(pendingRegistrationsRef.current)
      pendingRegistrationsRef.current.clear()

      console.log(
        `DocumentContext: Processing ${pendingIds.length} pending registrations`
      )
      console.log(
        `DocumentContext: Current components in state:`,
        Array.from(state.components.keys())
      )

      pendingIds.forEach((componentId) => {
        const component = state.components.get(componentId)
        if (!component) {
          console.warn(
            `DocumentContext: Component ${componentId} not found after registration`
          )
          return
        }

        console.log(
          `DocumentContext: Post-registration processing for ${component.type} ${component.id}`
        )

        // Handle parent-child relationships
        if (component.parentId) {
          const parentExists = state.components.has(component.parentId)
          if (parentExists) {
            console.log(
              `DocumentContext: Parent ${component.parentId} exists, triggering status update`
            )
            updateGroupStatus(component.parentId, false) // Use debounced update to avoid flushSync issues
          } else {
            // Parent doesn't exist yet, add to orphaned list
            console.log(
              `DocumentContext: Parent ${component.parentId} not found, adding ${component.id} to orphaned list`
            )
            if (!orphanedComponentsRef.current.has(component.parentId)) {
              orphanedComponentsRef.current.set(component.parentId, [])
            }
            orphanedComponentsRef.current.get(component.parentId)!.push(component)
          }
        }

        // If this is a group component, check for orphaned children and adopt them
        if (component.type === 'report-group') {
          const orphanedChildren = orphanedComponentsRef.current.get(component.id) || []
          if (orphanedChildren.length > 0) {
            console.log(
              `DocumentContext: Group ${component.id} adopting ${orphanedChildren.length} orphaned children:`,
              orphanedChildren.map((c) => c.id)
            )

            // Clear the orphaned list for this parent
            orphanedComponentsRef.current.delete(component.id)
          }

          // Trigger status update for the group to evaluate its current state
          console.log(
            `DocumentContext: Triggering status update for newly registered group ${component.id}`
          )
          updateGroupStatus(component.id, false)
        }
      })
    }, 0) // Use 0ms timeout to run after current execution stack

    return () => clearTimeout(timeoutId)
  }, [state.components, updateGroupStatus])

  // EKO-118: Document initialization detection and auto-trigger
  React.useEffect(() => {
    // Only proceed if we have an editor and haven't triggered initial load yet
    if (!state.editor || state.hasTriggeredInitialLoad) {
      return
    }

    // Clear any existing timeout
    if (initializationTimeoutRef.current) {
      clearTimeout(initializationTimeoutRef.current)
    }

    // Set a timeout to detect when component registration is complete
    initializationTimeoutRef.current = setTimeout(() => {
      console.log('DocumentContext: Initialization timeout reached, checking for idle components')

      // Mark document as initialized
      if (!state.isInitialized) {
        console.log('DocumentContext: Marking document as initialized')
        dispatch({ type: 'DOCUMENT_INITIALIZED' })
      }

      // Find all idle components that should be auto-triggered
      const idleComponents = Array.from(state.components.values()).filter(
        (component) =>
          component.status === 'idle' &&
          component.type === 'report-section' && // Only auto-trigger sections, not groups or summaries
          !component.endpoint?.includes('preserved') && // Don't trigger preserved components
          !component.endpoint?.includes('locked') // Don't trigger locked components
      )

      if (idleComponents.length > 0) {
        console.log(
          `DocumentContext: Found ${idleComponents.length} idle report sections to auto-trigger:`,
          idleComponents.map(c => c.id)
        )

        // Mark that we've triggered initial load to prevent future auto-triggers
        dispatch({ type: 'INITIAL_LOAD_TRIGGERED' })

        // Trigger loading for idle sections with a small delay between each to avoid overwhelming the system
        idleComponents.forEach((component, index) => {
          setTimeout(() => {
            console.log(`DocumentContext: Auto-triggering load for idle component ${component.id}`)
            // The component will detect this status change and start loading
            // We don't change the status here - the component's own logic will handle it
            // Instead, we just trigger a dummy update to wake up the component
            dispatch({ type: 'COMPONENT_UPDATED', id: component.id, updates: {} })
          }, index * 100) // 100ms delay between each component
        })
      } else {
        console.log('DocumentContext: No idle report sections found to auto-trigger')
        // Still mark as triggered to prevent future checks
        dispatch({ type: 'INITIAL_LOAD_TRIGGERED' })
      }
    }, INITIALIZATION_DELAY)

    return () => {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current)
        initializationTimeoutRef.current = null
      }
    }
  }, [state.editor, state.components, state.hasTriggeredInitialLoad, state.isInitialized, dispatch])

  // Trigger an auto-save version (not manual) - defined early for use in updateComponent
  const triggerAutoSaveVersion = React.useCallback(async () => {
    if (!state.editor || !documentId) {
      console.warn('DocumentContext.triggerAutoSaveVersion: Editor or documentId not available')
      return
    }

    try {
      // Dispatch save started
      dispatch({ type: 'SAVE_STARTED' })

      const supabase = createClient()
      const content = state.editor.getHTML()
      const data = state.editor.getJSON()
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        console.warn('DocumentContext.triggerAutoSaveVersion: No user available')
        dispatch({ type: 'SAVE_FAILED', error: 'No user available' })
        return
      }

      // Update the main document
      const { error: updateError } = await supabase
        .from('collaborative_documents')
        .update({
          content,
          data,
          updated_at: new Date().toISOString(),
          updated_by: user.id,
        })
        .eq('id', documentId)

      if (updateError) throw updateError

      // Get the current version number
      const { data: versions, error: versionError } = await supabase
        .from('document_versions')
        .select('version_number')
        .eq('document_id', documentId)
        .order('version_number', { ascending: false })
        .limit(1)

      if (versionError) throw versionError

      const nextVersionNumber = (versions?.[0]?.version_number || 0) + 1

      // Create an auto-save version
      const { error: createVersionError } = await supabase
        .from('document_versions')
        .insert({
          document_id: documentId,
          version_number: nextVersionNumber,
          title: `Version ${nextVersionNumber}`,
          content,
          data,
          created_by: user.id,
          change_summary: 'All report components loaded - Automatic save',
          is_auto_save: true, // This is an auto-save, not manual
        })

      if (createVersionError) throw createVersionError

      console.log(`DocumentContext.triggerAutoSaveVersion: Created auto-save version ${nextVersionNumber}`)
      
      dispatch({ type: 'SAVE_COMPLETED', timestamp: new Date() })

      // Clean up old auto-save versions (keep only 5 most recent)
      const { data: autoSaveVersions, error: fetchError } = await supabase
        .from('document_versions')
        .select('id, version_number')
        .eq('document_id', documentId)
        .eq('is_auto_save', true)
        .order('version_number', { ascending: false })

      if (!fetchError && autoSaveVersions && autoSaveVersions.length > 5) {
        const versionsToDelete = autoSaveVersions.slice(5)
        const idsToDelete = versionsToDelete.map(v => v.id)

        const { error: deleteError } = await supabase
          .from('document_versions')
          .delete()
          .in('id', idsToDelete)

        if (deleteError) {
          console.warn('Failed to clean up old auto-save versions:', deleteError)
        }
      }
    } catch (error) {
      console.error('DocumentContext.triggerAutoSaveVersion: Error:', error)
      dispatch({ type: 'SAVE_FAILED', error: error instanceof Error ? error.message : 'Failed to save' })
    }
  }, [state.editor, documentId, dispatch])

  const updateComponent = React.useCallback(
    (id: string, updates: Partial<ReportComponent>) => {
      const existing = state.components.get(id)
      dispatch({ type: 'COMPONENT_UPDATED', id, updates })

      // If component just finished loading or reached any final state, notify any waiters
      if (existing) {
        const finalStates = ['loaded', 'error', 'preserved', 'locked']
        if (!finalStates.includes(existing.status) && updates.status && finalStates.includes(updates.status)) {
          console.log(`DocumentContext: Component ${id} reached final state ${updates.status}, notifying waiters`)
          const waiters = dependencyWaitersRef.current.get(id)
          if (waiters) {
            waiters.forEach((waiter) => waiter())
            dependencyWaitersRef.current.delete(id)
          }
        }

        // If status changed, handle special requirements
        if (updates.status && existing.status !== updates.status) {
          console.log(
            `DocumentContext: Component ${id} status changed from '${existing.status}' to '${updates.status}'`
          )

          // EKO-117: If component reaches 'loaded' state, trigger immediate save
          if (updates.status === 'loaded') {
            console.log(
              `DocumentContext: Component ${id} reached 'loaded' state, triggering immediate save`
            )
            setTimeout(() => {
              if (state.editor) {
                dispatch({
                  type: 'CONTENT_CHANGED',
                  content: state.editor.getHTML(),
                  data: state.editor.getJSON(),
                  source: 'system',
                })

                // Call onSave callback if provided
                if (onSave) {
                  try {
                    onSave(state.editor.getHTML(), state.editor.getJSON())
                  } catch (error) {
                    console.error('DocumentContext: Error in onSave callback:', error)
                  }
                }
              }
            }, 50)

            // EKO-130: Check if all components are now loaded after this status change
            setTimeout(() => {
              // Get the updated component state (after the status update is applied)
              const updatedComponents = new Map(state.components)
              if (existing) {
                updatedComponents.set(id, { ...existing, ...updates })
              }

              // Check if all components are now in a loaded state
              const allComponentsLoaded = Array.from(updatedComponents.values()).every(component => {
                const finalStates = ['loaded', 'preserved', 'locked', 'error']
                return finalStates.includes(component.status)
              })

              if (allComponentsLoaded && updatedComponents.size > 0) {
                console.log('DocumentContext: All components are now loaded, triggering auto-save version')
                triggerAutoSaveVersion().catch(error => {
                  console.error('DocumentContext: Error triggering auto-save version:', error)
                })
              }
            }, 100) // Delay to ensure state updates are applied
          }

          // EKO-117: If component moves from 'loaded' to 'idle', create document version
          if (existing.status === 'loaded' && updates.status === 'idle') {
            console.log(
              `DocumentContext: Component ${id} moved from 'loaded' to 'idle', creating document version`
            )
            const component = state.components.get(id)
            const componentType = component?.type || 'component'
            const componentTitle = component?.title || id
            const changeSummary = `${componentType} "${componentTitle}" was reset from loaded to idle state`

            setTimeout(async () => {
              if (state.editor && documentId) {
                try {
                  const supabase = createClient()
                  const content = state.editor.getHTML()
                  const data = state.editor.getJSON()

                  // Get the current version number
                  const { data: versions, error: versionError } = await supabase
                    .from('document_versions')
                    .select('version_number')
                    .eq('document_id', documentId)
                    .order('version_number', { ascending: false })
                    .limit(1)

                  if (versionError) {
                    console.error('DocumentContext: Error fetching versions:', versionError)
                    return
                  }

                  const nextVersionNumber = (versions?.[0]?.version_number || 0) + 1

                  const { error: createVersionError } = await supabase
                    .from('document_versions')
                    .insert({
                      document_id: documentId,
                      version_number: nextVersionNumber,
                      title: `Version ${nextVersionNumber}`,
                      content,
                      data,
                      created_by: state.editor.storage?.user?.id,
                      change_summary: changeSummary,
                      is_auto_save: false,
                    })

                  if (createVersionError) {
                    console.error(
                      'DocumentContext: Error creating version:',
                      createVersionError
                    )
                    return
                  }

                  console.log(
                    `DocumentContext: Created version ${nextVersionNumber} with summary: ${changeSummary}`
                  )
                } catch (error) {
                  console.error(
                    'DocumentContext: Unexpected error creating version:',
                    error
                  )
                }
              }
            }, 100)
          }

          // EKO-117: If component changes to 'idle', trigger reload from database
          if (updates.status === 'idle') {
            console.log(
              `DocumentContext: Component ${id} changed to 'idle' state, triggering reload`
            )
            // The reload will be handled by the individual component extensions
            // We just need to ensure the status change is persisted
          }

          // Trigger document save to persist status changes
          setTimeout(() => {
            if (state.editor) {
              dispatch({
                type: 'CONTENT_CHANGED',
                content: state.editor.getHTML(),
                data: state.editor.getJSON(),
                source: 'system',
              })
            }
          }, 100)

          // Find all summaries that depend on this component and trigger re-evaluation
          Array.from(state.components.values())
            .filter(
              (comp) =>
                comp.type === 'report-summary' && comp.dependencies?.includes(id)
            )
            .forEach((summary) => {
              // Trigger re-evaluation for summaries in 'idle' or 'loading' states
              // 'loading' summaries might be waiting for multiple dependencies
              if (summary.status === 'idle' || summary.status === 'loading') {
                console.log(
                  `DocumentContext: Triggering re-evaluation for summary ${summary.id} (status: ${summary.status}) due to dependency ${id} status change`
                )
                // Trigger re-evaluation by dispatching a dummy update
                dispatch({ type: 'COMPONENT_UPDATED', id: summary.id, updates: {} })
              } else {
                console.log(
                  `DocumentContext: Skipping re-evaluation for summary ${summary.id} (status: ${summary.status})`
                )
              }
            })

          // Find parent groups and trigger their status evaluation using debounced mechanism
          const component = state.components.get(id)
          if (component?.parentId) {
            console.log(
              `DocumentContext: Component ${id} has parent ${component.parentId}, triggering parent group evaluation`
            )
            updateGroupStatus(component.parentId, false) // Use debounced update
          }
        }
      }
    },
    [dispatch, state.components, updateGroupStatus, state.editor, documentId, onSave, triggerAutoSaveVersion]
  )

  const removeComponent = React.useCallback(
    (id: string) => {
      dispatch({ type: 'COMPONENT_REMOVED', id })
    },
    [dispatch]
  )

  const registerCitations = React.useCallback(
    (citations: CitationType[]) => {
      dispatch({ type: 'CITATIONS_REGISTERED', citations })
    },
    [dispatch]
  )

  const addCitation = React.useCallback(
    (citation: CitationType) => {
      dispatch({ type: 'CITATION_ADDED', citation })
    },
    [dispatch]
  )

  const togglePanel = React.useCallback(
    (show?: boolean, tab?: DocumentState['activePanelTab']) => {
      dispatch({ type: 'UI_PANEL_TOGGLE', show, tab })
    },
    [dispatch]
  )

  const setEntityRun = React.useCallback(
    (entity: string | null, run: string) => {
      dispatch({ type: 'ENTITY_CHANGED', entity, run })
      // Notify entity change listeners
      entityChangeListenersRef.current.forEach((listener) => {
        try {
          listener(entity, run)
        } catch (error) {
          console.error('Error in entity change listener:', error)
        }
      })
    },
    [dispatch]
  )

  // Dependency management methods
  const areDependenciesReady = React.useCallback(
    (componentId: string): boolean => {
      const component = state.components.get(componentId)
      if (!component?.dependencies || component.dependencies.length === 0) {
        console.log(`DocumentContext: areDependenciesReady(${componentId}): No dependencies, returning true`)
        return true
      }

      console.log(
        `DocumentContext: areDependenciesReady(${componentId}): Checking ${component.dependencies.length} dependencies`
      )

      const readyDependencies = component.dependencies.filter((depId) => {
        const dep = state.components.get(depId)
        if (!dep) {
          console.log(
            `DocumentContext: areDependenciesReady(${componentId}): Dependency ${depId} not found`
          )
          return false
        }

        const finalStates = ['loaded', 'preserved', 'locked']
        const isReady = finalStates.includes(dep.status)
        console.log(
          `DocumentContext: areDependenciesReady(${componentId}): Dependency ${depId} (${dep.type}) status: ${dep.status}, ready: ${isReady}`
        )

        return isReady
      })

      const result = readyDependencies.length === component.dependencies.length
      console.log(
        `DocumentContext: areDependenciesReady(${componentId}): Ready deps: ${readyDependencies.length}/${component.dependencies.length}, Result: ${result}`
      )

      return result
    },
    [state.components]
  )

  const waitForDependencies = React.useCallback(
    async (componentId: string): Promise<void> => {
      console.log(`DocumentContext: waitForDependencies called for ${componentId}`)

      if (areDependenciesReady(componentId)) {
        console.log(`DocumentContext: Dependencies already ready for ${componentId}`)
        return Promise.resolve()
      }

      return new Promise((resolve) => {
        const component = state.components.get(componentId)
        if (!component?.dependencies || component.dependencies.length === 0) {
          console.log(`DocumentContext: No dependencies for ${componentId}, resolving immediately`)
          resolve()
          return
        }

        console.log(
          `DocumentContext: Setting up waiters for ${componentId}, dependencies: ${component.dependencies.join(
            ', '
          )}`
        )

        const waiterCallback = () => {
          if (areDependenciesReady(componentId)) {
            console.log(
              `DocumentContext: Dependencies now ready for ${componentId}, resolving`
            )
            resolve()
          }
        }

        // Set up waiters for each dependency that's not ready
        let waitersAdded = 0
        component.dependencies.forEach((depId) => {
          const dep = state.components.get(depId)
          const finalStates = ['loaded', 'error', 'preserved', 'locked']
          if (dep && !finalStates.includes(dep.status)) {
            if (!dependencyWaitersRef.current.has(depId)) {
              dependencyWaitersRef.current.set(depId, [])
            }
            dependencyWaitersRef.current.get(depId)!.push(waiterCallback)
            waitersAdded++
            console.log(
              `DocumentContext: Added waiter for ${depId} (status: ${dep.status})`
            )
          }
        })

        // Check again in case dependencies resolved while setting up waiters
        if (areDependenciesReady(componentId)) {
          console.log(
            `DocumentContext: Dependencies became ready while setting up waiters for ${componentId}`
          )
          resolve()
        } else if (waitersAdded === 0) {
          // No waiters were added, which means all dependencies are in final states
          console.warn(
            `DocumentContext: No waiters added for ${componentId} but dependencies not ready`
          )
          resolve()
        }
      })
    },
    [state.components, areDependenciesReady]
  )

  // Entity change listeners
  const addEntityChangeListener = React.useCallback(
    (listener: (entity: string | null, run: string) => void) => {
      entityChangeListenersRef.current.add(listener)
      return () => entityChangeListenersRef.current.delete(listener)
    },
    []
  )

  const notifyEntityChange = React.useCallback(
    (entity: string | null, run: string) => {
      setEntityRun(entity, run)
    },
    [setEntityRun]
  )

  // Force update all group statuses - useful for ensuring consistency
  const updateAllGroupStatuses = React.useCallback(() => {
    console.log('DocumentContext.updateAllGroupStatuses: Updating all group statuses')
    const groups = Array.from(state.components.values()).filter((comp) => comp.type === 'report-group')
    groups.forEach((group) => {
      updateGroupStatus(group.id, false) // Use debounced updates
    })
  }, [state.components, updateGroupStatus])

  // Document versioning and saving functions
  const createDocumentVersion = React.useCallback(
    async (changeSummary: string) => {
      if (!state.editor || !documentId) {
        console.warn('DocumentContext.createDocumentVersion: Editor or documentId not available')
        return
      }

      try {
        const supabase = createClient()
        const content = state.editor.getHTML()
        const data = state.editor.getJSON()

        // Get the current version number
        const { data: versions, error: versionError } = await supabase
          .from('document_versions')
          .select('version_number')
          .eq('document_id', documentId)
          .order('version_number', { ascending: false })
          .limit(1)

        if (versionError) {
          console.error('DocumentContext.createDocumentVersion: Error fetching versions:', versionError)
          return
        }

        const nextVersionNumber = (versions?.[0]?.version_number || 0) + 1

        const { error: createVersionError } = await supabase
          .from('document_versions')
          .insert({
            document_id: documentId,
            version_number: nextVersionNumber,
            title: `Version ${nextVersionNumber}`,
            content,
            data,
            created_by: state.editor.storage?.user?.id, // Get user from editor storage if available
            change_summary: changeSummary,
            is_auto_save: false,
          })

        if (createVersionError) {
          console.error('DocumentContext.createDocumentVersion: Error creating version:', createVersionError)
          return
        }

        console.log(`DocumentContext.createDocumentVersion: Created version ${nextVersionNumber} with summary: ${changeSummary}`)
      } catch (error) {
        console.error('DocumentContext.createDocumentVersion: Unexpected error:', error)
      }
    },
    [state.editor, documentId]
  )

  const triggerImmediateSave = React.useCallback(
    async () => {
      if (!state.editor) {
        console.warn('DocumentContext.triggerImmediateSave: Editor not available')
        return
      }

      console.log('DocumentContext.triggerImmediateSave: Triggering immediate save')

      // Trigger immediate content change to force auto-save
      dispatch({
        type: 'CONTENT_CHANGED',
        content: state.editor.getHTML(),
        data: state.editor.getJSON(),
        source: 'system',
      })

      // Call onSave callback if provided
      if (onSave) {
        try {
          await onSave(state.editor.getHTML(), state.editor.getJSON())
        } catch (error) {
          console.error('DocumentContext.triggerImmediateSave: Error in onSave callback:', error)
        }
      }
    },
    [state.editor, onSave, dispatch]
  )

  // Refresh all descendants of a group - sets status to idle and triggers reload
  const refreshAllDescendants = React.useCallback(
    (groupId: string) => {
      console.log(`DocumentContext.refreshAllDescendants: Refreshing all descendants of group ${groupId}`)

      const group = state.components.get(groupId)
      if (!group || group.type !== 'report-group') {
        console.warn(`DocumentContext.refreshAllDescendants: ${groupId} is not a valid report group`)
        return
      }

      // Get all descendants using the existing helper function
      const descendants = getAllDescendantsFromState(groupId, state.components)
      console.log(
        `DocumentContext.refreshAllDescendants: Found ${descendants.length} descendants to refresh:`,
        descendants.map((d) => `${d.id}(${d.type})`)
      )

      // Set all descendants to 'idle' status to trigger refresh
      descendants.forEach((descendant) => {
        if (descendant.status !== 'locked' && descendant.status !== 'preserved') {
          console.log(`DocumentContext.refreshAllDescendants: Setting ${descendant.id} (${descendant.type}) status to 'idle'`)
          updateComponent(descendant.id, { status: 'idle' })
        } else {
          console.log(
            `DocumentContext.refreshAllDescendants: Skipping ${descendant.id} (${descendant.type}) - status is ${descendant.status}`
          )
        }
      })

      // Also set the group itself to 'loading' to indicate refresh is in progress
      if (group.status !== 'locked' && group.status !== 'preserved') {
        console.log(`DocumentContext.refreshAllDescendants: Setting group ${groupId} status to 'loading'`)
        updateComponent(groupId, { status: 'loading' })
      }
    },
    [state.components, updateComponent]
  )

  // EKO-118: Manual trigger for initial load
  const triggerInitialLoad = React.useCallback(() => {
    console.log('DocumentContext.triggerInitialLoad: Manually triggering initial load')

    // Find all idle components that should be triggered
    const idleComponents = Array.from(state.components.values()).filter(
      (component) =>
        component.status === 'idle' &&
        component.type === 'report-section'
    )

    if (idleComponents.length > 0) {
      console.log(
        `DocumentContext.triggerInitialLoad: Found ${idleComponents.length} idle report sections to trigger:`,
        idleComponents.map(c => c.id)
      )

      // Mark that we've triggered initial load
      dispatch({ type: 'INITIAL_LOAD_TRIGGERED' })

      // Trigger loading for idle sections
      idleComponents.forEach((component, index) => {
        setTimeout(() => {
          console.log(`DocumentContext.triggerInitialLoad: Triggering load for component ${component.id}`)
          dispatch({ type: 'COMPONENT_UPDATED', id: component.id, updates: {} })
        }, index * 100)
      })
    } else {
      console.log('DocumentContext.triggerInitialLoad: No idle report sections found')
    }
  }, [state.components, dispatch])

  // Check if all components are loaded
  const areAllComponentsLoaded = React.useCallback((): boolean => {
    const components = Array.from(state.components.values())
    
    // If no components, consider it "loaded"
    if (components.length === 0) return true
    
    // Check if all components are in a final "loaded" state
    const allLoaded = components.every(component => {
      const finalStates = ['loaded', 'preserved', 'locked', 'error']
      return finalStates.includes(component.status)
    })
    
    return allLoaded
  }, [state.components])

  const contextValue: DocumentContextValue = React.useMemo(
    () => ({
      state,
      dispatch,
      registerComponent,
      updateComponent,
      removeComponent,
      registerCitations,
      addCitation,
      togglePanel,
      setEntityRun,
      areDependenciesReady,
      waitForDependencies,
      addEntityChangeListener,
      notifyEntityChange,
      updateGroupStatus,
      updateAllGroupStatuses,
      createDocumentVersion,
      triggerImmediateSave,
      triggerAutoSaveVersion,
      refreshAllDescendants,
      triggerInitialLoad,
      areAllComponentsLoaded,
    }),
    [
      state,
      registerComponent,
      updateComponent,
      removeComponent,
      registerCitations,
      addCitation,
      togglePanel,
      setEntityRun,
      areDependenciesReady,
      waitForDependencies,
      addEntityChangeListener,
      notifyEntityChange,
      updateGroupStatus,
      updateAllGroupStatuses,
      createDocumentVersion,
      triggerImmediateSave,
      triggerAutoSaveVersion,
      refreshAllDescendants,
      triggerInitialLoad,
      areAllComponentsLoaded,
    ]
  )

  return <DocumentContext.Provider value={contextValue}>{children}</DocumentContext.Provider>
}