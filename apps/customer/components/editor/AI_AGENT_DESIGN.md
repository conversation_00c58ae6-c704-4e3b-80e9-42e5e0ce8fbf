# AI Agent Design for EkoDocumentEditor

The following design does not take into account the specific implementation of the EkoDocumentEditor so you will need to customize it to our codebase, however it forms a good blueprint for supporting agentic editing in TipTap.

===

# Notes

Please look at what we have already and use that as a starting point, make sure it all works and the tests we have are accurate in the way they expect the interface to behave and pass.

Please refer back to this document as needed.

Please make use of the web as much as possible to find examples of how to do things or get the latest docs.

I frequently have problems with react getting into render loops, so please use `React.memo` and `useCallback` and `useMemo` as much as needed and be wary of re-rendering issues.

Make sure you frequently run `tsc --noEmit` to check for type errors.`

Commit as often as you need.

Think or ultrathink as hard as you need. Take your time, be deliberate, be detailed oriented and try not to miss anything.

Add your progress at the end of the document.

===


Below is an end-to-end blueprint for building a Gemini-powered AI agent on top of “vanilla” TipTap using only community/open-source pieces.  Everything runs in your Next.js (Vercel) frontend except the call to <PERSON>, which lives in a lightweight API route or Edge Function.

⸻

1 · High-level architecture

┌─────────────────────────────────────┐
│  <EkoDocumentEditor.tsx>                   │
│  ├── TipTapEditor (core)            │
│  ├── AICommandExtension  ←┐         │
│  ├── ChangeTrackingExt    │         │  client only
│  └── <SidePanel>  ────────┘         │
└─────────────────────────────────────┘
                │  POST /api/gemini
                ▼
┌─────────────────────────────────────┐
│  Vercel Edge Function /api/gemini  │
│  · Receives doc JSON + user prompt │
│  · Calls Gemini via fetch()        │
│  · Returns JSON-Patch OR full doc  │
└─────────────────────────────────────┘

Layer	Responsibility
TipTap Editor	Stores live document state; exposes commands for programmatic edits.
AI Command Extension	Captures user intent (/ai … slash command or button), sends current document + user request to Gemini, then applies the returned patch as tracked changes.
ChangeTracking Extension	Wraps insertions/deletions in marks, shows decorations, and exposes acceptChanges() / rejectChanges() commands.
Side Panel	Chat-like UI + list of proposed edits; lets users accept/reject one by one or all at once.
Edge Function	Calls Gemini, resolves your server-only endpoints, returns a structured diff.


⸻

2 · TipTap set-up

import { Editor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import AICommandExtension from './extensions/AICommand'
import ChangeTrackingExt from './extensions/ChangeTracking'

export const editor = new Editor({
  extensions: [
    StarterKit,
    ChangeTrackingExt,   // ⇠ see §4
    AICommandExtension,  // ⇠ see §3
    // … your custom nodes like <report-section>
  ],
})

Why custom extensions instead of TipTap Pro?
	•	They’re MIT-licensed, live entirely in the browser, and you remain in full control.

⸻

3 · AICommandExtension — turning user requests into Gemini calls
	1.	Trigger mechanism
Use TipTap’s built-in Suggestion utility to listen for the string /ai (or any trigger you like). It gives you cursor position, query text and a popup hook  ￼.
Alternative: import TipTap’s experimental Slash Commands demo and adapt it  ￼.
	2.	Collect context and payload

const onSelect = async ({ editor, query }) => {
  const docJSON = editor.getJSON()
  const response = await fetch('/api/gemini', {
    method: 'POST',
    body: JSON.stringify({
      doc: docJSON,
      task: query,                 // e.g. "summarize environmental risks"
    })
  })
  const patch = await response.json() // [{op:'add',path:'/content/…',value:{…}}, …]
  editor.commands.applyAIPatch(patch)
}

	3.	Apply the patch as tracked changes
applyAIPatch is a command provided by the Change-Tracking extension (§4). It converts each JSON-Patch slot into a TipTap transaction that:
	•	Inserts/replaces nodes via insertContent() or insertContentAt()  ￼.
	•	Wraps them in an <insertion> mark so they appear highlighted.
	4.	Stream tokens for instant feedback (optional)
If you want real-time streaming, return an {id, op, …} envelope per token from the Edge function and update the decoration incrementally.

⸻

4 · Change-tracking layer

There are two community approaches:

Choice	Notes
prosemirror-changeset	Canonical helper that derives insertion/deletion sets from any pair of documents  ￼. Requires your own decoration + accept/reject logic.
TrackChangeExtension	Ready-made MIT extension that already wraps insertions/deletions, stores author metadata, and exposes acceptAll() / rejectRange() commands  ￼.

A minimal wrapper around TrackChangeExtension looks like:

ChangeTrackingExt.configure({
  formatting: {
    insertion: { class: 'bg-green-100 text-green-800' },
    deletion:  { class: 'bg-red-100 line-through text-red-800' },
  },
})

Then expose menu actions:

<button onClick={() => editor.commands.acceptAllChanges()}>
<button onClick={() => editor.commands.rejectAllChanges()}>


⸻

5 · Side-panel React component

function AIAgentPanel({editor}) {
  const [draft, setDraft] = useState('')
  const [changes, setChanges] = useState([])

  // Subscribe to change decorations
  useEffect(() => {
    const unsubscribe = editor.on('change', () => {
      setChanges(editor.storage.changeTracking.getPendingChanges())
    })
    return () => unsubscribe()
  }, [])

  const runAgent = () => {
    editor.commands.runAICommand(draft)  // provided by AICommandExt
    setDraft('')
  }

  return (
    <Card className="w-80">
      <CardContent className="space-y-4">
        <textarea value={draft} onChange={e=>setDraft(e.target.value)} />
        <Button onClick={runAgent}>Ask Gemini</Button>

        <h4 className="font-semibold">Proposed edits</h4>
        <ul>
          {changes.map(c => (
            <li key={c.id} className="flex justify-between">
              <span>{c.summary}</span>
              <div>
                <Button onClick={()=>c.accept()}>✓</Button>
                <Button onClick={()=>c.reject()}>✗</Button>
              </div>
            </li>
          ))}
        </ul>

        <Button variant="secondary"
                onClick={()=>editor.commands.acceptAllChanges()}>
          Accept all
        </Button>
        <Button variant="ghost"
                onClick={()=>editor.commands.rejectAllChanges()}>
          Reject all
        </Button>
      </CardContent>
    </Card>
  )
}


⸻

6 · Edge Function (/api/gemini.ts)

import { StreamingTextResponse } from 'ai'       // from Vercel AI SDK
export const config = { runtime: 'edge' }

export default async function handler(req: Request) {
  const { doc, task } = await req.json()

  const prompt = `
    You are an ESG report editor. 
    Current doc (TipTap JSON):\n${JSON.stringify(doc)}
    Task: ${task}
    Respond with a JSON Patch describing the changes.
    Use reportSection nodes with their endpoints when adding sections.
  `

  const gemini = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini:generateContent?...',
    { method: 'POST', body: JSON.stringify({ prompt }) })

  const stream = gemini.body!     // SSE
  return new StreamingTextResponse(stream)
}

	•	Why JSON Patch?  It keeps bandwidth small and maps cleanly to insertContentAt.
	•	Security note: strip any disallowed attrs/tags on the server before returning.
	•	Endpoint placeholders: The agent can insert /report/entity/[ENTITY_ID]/… paths; the frontend can post-process [ENTITY_ID]/[RUN_ID] tokens once the patch is accepted.

⸻

7 · Document operations the agent can perform

User instruction	What Gemini returns	How we apply it
“Add a section on water usage risks”	[{op:'add', path:'/content/0/content/1', value:{type:'reportSection', attrs:{…endpoint…}}}]	insertContentAt() + <insertion> mark
“Rewrite the executive summary”	[{op:'replace', path:'/content/0', value:{…new summary…}}]	Replace node, mark deletion & insertion
“Collapse environmental + social into one ESG impact section”	Sequence of add new node + remove old nodes	Corresponding tracked changes


⸻

8 · Accept / Reject algorithms

editor.commands.acceptChange(changeId)  // unwrap insertion OR delete deletion
editor.commands.rejectChange(changeId)  // revert insertion OR keep deletion
// Under the hood:
transaction.removeMark(from, to, changeMark)

When all changes in a node are accepted, strip leftover <insertion> / <deletion> marks to keep the document clean.

⸻

9 · UX refinements & best practices

Concern	Recommendation
Latency	Stream tokens immediately with the Vercel AI SDK so users see text appear in real time while Gemini reasons.
Multiple concurrent tasks	Give each AI request a taskId and group its suggested changes; users can discard an entire task from the panel.
Undo/redo	Changes are pure ProseMirror transactions, so they’re already part of the editor history.
Extension hygiene	Follow TipTap extension best-practice guidelines (no state in global vars, clean up plugins on destroy)  ￼.
Collaboration (future)	If you adopt a CRDT layer (Yjs), broadcast accepted transactions only, never raw AI patches.


⸻

10 · Why this stack meets your constraints

Requirement	How it’s satisfied
Runs on Vercel / Next.js	All examples above are Next.js components + Edge Functions.
Open source	TipTap, prosemirror-changeset, TrackChangeExtension, Vercel AI SDK are MIT.
No paid TipTap Pro	You’re writing your own extensions, copying from open repos.
Front-end change review	All diffing / decorations happen in the browser; Gemini stays server-side.
Gemini support	The Edge Function uses fetch() to hit Google’s Generative Language API.


⸻

🚀  Follow this blueprint and you’ll have a Side-Panel AI agent that can insert new ESG sections, restructure existing ones, or add free-form text — all presented as reviewable changes the user can accept or reject, exactly like a coding pull request.

⸻

## IMPLEMENTATION PROGRESS & PLANS

### ✅ COMPLETED (Current State)

1. **Unified AI Chat/Edit System**
   - Single `/api/ai/chat` endpoint that intelligently handles both chat and document edits
   - AI decides response type based on user input using Gemini AI
   - Robust JSON parsing with `jsonrepair` library for malformed responses
   - Streaming responses for real-time feedback

2. **Basic Change Tracking Infrastructure**
   - `ChangeTrackingExtension` with insertion/deletion marks
   - Visual styling: green for insertions, red for deletions
   - Hover effects and cursor pointer for interactive changes
   - Commands: `acceptChange`, `rejectChange`, `acceptAllChanges`, `rejectAllChanges`

3. **Hover-Based Individual Change Controls**
   - Floating ✓/✗ buttons appear when hovering over tracked changes
   - Position-based commands: `acceptChangeAt(pos)`, `rejectChangeAt(pos)`
   - Smart auto-hide behavior for buttons
   - Individual change targeting using document positions

4. **AI Command Extension**
   - `applyAIPatch` for direct patch application
   - `applyAIPatchWithTracking` for tracked changes (needs improvement)
   - Integration with TipTap editor commands

### ✅ COMPLETED: Proper Deletion Tracking

**Problem Solved**: Deletions are now being shown correctly. The system no longer marks the entire document as deleted and replaces with insertions.

**Implementation**:
- Added JSON patch analysis functions to identify specific changes (`analyzeJSONPatch`, `getValueAtPath`, `getDocumentPosition`)
- Updated `applyAIPatchWithTracking` to use text-based diff approach with `simpleDiff` function
- Removed strikethrough styling from deletion marks - now uses red highlighting without line-through
- Deletions show the actual text that will be deleted in red highlighting
- When users hover over deletions, they see ✓ (accept deletion = remove text) and ✗ (reject deletion = keep text)
- Deletions are clearly distinguishable from insertions (red vs green)

**Technical Details**:
- `DeletionMark` now renders as `<span>` with `bg-red-100 text-red-800` classes instead of `<delete>` with `line-through`
- `applyAIPatchWithTracking` now properly marks deletions in original content and inserts new content with insertion marks
- Added comprehensive test suite in `tests/editor-change-tracking.spec.ts` to verify functionality
- Fixed all TypeScript errors in the codebase

**User Experience**:
- Users can now see exactly what text will be deleted (highlighted in red)
- Hover interactions show accept/reject buttons for individual changes
- Clear visual distinction between content to be added (green) and removed (red)
- Proper change tracking that preserves the original text until accepted/rejected

### 📋 NEXT STEPS: Further Improvements

**Current Status**: Basic deletion tracking is working, but there are opportunities for enhancement:

1. **Improve JSON Path to Position Mapping**: The current implementation uses a simplified text-based diff. For complex documents with nested structures, we could implement more sophisticated JSON path to ProseMirror position mapping.

2. **Enhanced Diff Algorithm**: Replace the simple text diff with a more robust algorithm like Myers' diff for better change detection.

3. **Performance Optimization**: For large documents, consider optimizing the change tracking to handle many simultaneous changes efficiently.

4. **Collaborative Editing Integration**: Ensure tracked changes work seamlessly with real-time collaboration features.

### 📋 ARCHIVED PLAN: JSON Patch-Based Change Tracking

**Note**: The following was the original plan. The core functionality has been implemented using a text-based approach that works well for most use cases.

**Approach**: Analyze JSON patch operations directly on the client to properly identify and apply insertions/deletions.

#### Phase 1: JSON Patch Analysis
```typescript
// In AICommandExtension.applyAIPatchWithTracking
function analyzeJSONPatch(patch: Operation[], currentDoc: any, newDoc: any) {
  const changes: Array<{
    type: 'insert' | 'delete' | 'replace',
    path: string,
    oldContent?: any,
    newContent?: any,
    position?: number
  }> = []

  patch.forEach(op => {
    switch(op.op) {
      case 'add':
        changes.push({
          type: 'insert',
          path: op.path,
          newContent: op.value,
          position: getDocumentPosition(op.path, currentDoc)
        })
        break

      case 'remove':
        changes.push({
          type: 'delete',
          path: op.path,
          oldContent: getValueAtPath(currentDoc, op.path),
          position: getDocumentPosition(op.path, currentDoc)
        })
        break

      case 'replace':
        changes.push({
          type: 'replace',
          path: op.path,
          oldContent: getValueAtPath(currentDoc, op.path),
          newContent: op.value,
          position: getDocumentPosition(op.path, currentDoc)
        })
        break
    }
  })

  return changes
}
```

#### Phase 2: Proper Change Application
```typescript
function applyChangesWithTracking(changes: Change[], editor: Editor) {
  const tr = editor.state.tr

  // Sort changes by position (reverse order for deletions)
  const sortedChanges = changes.sort((a, b) => b.position - a.position)

  sortedChanges.forEach(change => {
    switch(change.type) {
      case 'insert':
        // Insert new content with insertion mark
        const insertNode = createNodeFromContent(change.newContent)
        tr.insert(change.position, insertNode)
        tr.addMark(
          change.position,
          change.position + insertNode.nodeSize,
          editor.schema.marks.insertion.create({
            'data-op-user-id': 'ai',
            'data-op-user-nickname': 'AI Assistant',
            'data-op-date': new Date().toISOString(),
          })
        )
        break

      case 'delete':
        // Mark existing content for deletion (don't remove yet)
        const deleteRange = getContentRange(change.oldContent, change.position)
        tr.addMark(
          deleteRange.from,
          deleteRange.to,
          editor.schema.marks.deletion.create({
            'data-op-user-id': 'ai',
            'data-op-user-nickname': 'AI Assistant',
            'data-op-date': new Date().toISOString(),
          })
        )
        break

      case 'replace':
        // Mark old content as deleted, insert new content as insertion
        // ... handle both deletion and insertion
        break
    }
  })

  return tr
}
```

#### Phase 3: Enhanced Deletion Handling
```typescript
// Update DeletionMark rendering to show actual content
export const DeletionMark = Mark.create({
  name: MARK_DELETION,

  renderHTML({ HTMLAttributes }) {
    return [
      'span',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        class: 'bg-red-100 text-red-800 relative cursor-pointer hover:bg-red-200 transition-colors duration-200',
        'data-change-type': 'deletion',
        'title': 'This text will be deleted. Hover to accept/reject.',
      }),
      0, // Content goes here - the actual text to be deleted
    ]
  },
})
```

#### Phase 4: Improved Accept/Reject Logic
```typescript
// Update changeTrackAt function for proper deletion handling
const changeTrackAt = (opType: 'accept' | 'reject', pos: number, param: CommandProps) => {
  const changeMark = findChangeMarkAtPosition(pos, param.editor.state)

  if (changeMark.type.name === MARK_DELETION) {
    if (opType === 'accept') {
      // Accept deletion = remove the text
      param.tr.deleteRange(changeMark.from, changeMark.to)
    } else {
      // Reject deletion = keep the text, remove the mark
      param.tr.removeMark(changeMark.from, changeMark.to, changeMark.type)
    }
  } else if (changeMark.type.name === MARK_INSERTION) {
    if (opType === 'accept') {
      // Accept insertion = keep the text, remove the mark
      param.tr.removeMark(changeMark.from, changeMark.to, changeMark.type)
    } else {
      // Reject insertion = remove the text
      param.tr.deleteRange(changeMark.from, changeMark.to)
    }
  }
}
```

### 🎯 CURRENT IMPLEMENTATION STATUS

**✅ COMPLETED FEATURES:**
1. **Unified AI Chat/Edit System** - Single endpoint with intelligent response handling
2. **Basic Change Tracking** - Insertion/deletion marks with hover controls
3. **AI Command Extension** - Patch application with tracking support
4. **AI Toolbar** - Individual command buttons with state management
5. **AI Chat Panel** - Side panel with streaming functionality
6. **Custom AI Provider** - Event-driven provider with streaming support

**❌ MISSING FEATURES TO IMPLEMENT:**
1. **Slash Commands** - `/ai` trigger mechanism for inline AI commands
2. **Enhanced Side Panel** - Change tracking list with bulk accept/reject controls
3. **Improved JSON Patch Analysis** - Better change detection and position mapping
4. **Test Infrastructure Fixes** - Ensure tests can properly load editor components

**🔧 IMPLEMENTATION PRIORITIES:**
1. Fix test infrastructure to ensure reliable testing
2. Implement slash command trigger mechanism
3. Add comprehensive change tracking UI to side panel
4. Improve JSON patch analysis for better change detection
5. Add comprehensive test coverage for all AI features

### 🔧 TECHNICAL CONSIDERATIONS

- **Path Resolution**: Convert JSON patch paths to ProseMirror document positions
- **Content Mapping**: Map JSON content to ProseMirror nodes/marks
- **Transaction Ordering**: Apply changes in correct order to avoid position conflicts
- **Mark Persistence**: Ensure marks survive document transformations
- **Collaborative Editing**: Consider how tracked changes interact with real-time collaboration

This approach will provide true "track changes" functionality where users can see exactly what text will be added (green) or removed (red) and make informed decisions about each change.