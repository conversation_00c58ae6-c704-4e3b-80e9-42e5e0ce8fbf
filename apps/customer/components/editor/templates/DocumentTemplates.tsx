'use client'

import React, { useEffect, useState } from 'react'
import { But<PERSON> } from '@ui/components/ui/button'
import { ScrollArea } from '@ui/components/ui/scroll-area'
import { Badge } from '@ui/components/ui/badge'
import {
  BookOpen,
  Briefcase,
  Calendar,
  CheckSquare,
  Columns,
  FileText,
  Leaf,
  Lightbulb,
  MessageSquare,
  Shield,
  Target,
  TrendingDown,
  TrendingUp,
  Users,
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { DynamicTemplate, generateDynamicTemplates } from './dynamic-template-generator'
import { DocumentEntityRunSelector } from '../DocumentEntityRunSelector'

// Icon mapping for dynamic templates
const iconMap = {
  FileText,
  Briefcase,
  Users,
  TrendingUp,
  Calendar,
  CheckSquare,
  MessageSquare,
  BookOpen,
  Target,
  Lightbulb,
  TrendingDown,
  Shield,
  Leaf,
  Columns,
}

// Helper function to get icon component from string name
function getIconComponent(iconName: string): React.ComponentType<{ className?: string }> {
  return iconMap[iconName as keyof typeof iconMap] || FileText
}

interface DocumentTemplate {
  id: string
  name: string
  description: string
  category: string
  icon: React.ComponentType<{ className?: string }> | string
  content?: string
  data?: any
  tags: string[]
}

interface DocumentTemplatesProps {
  onSelectTemplate: (template: DocumentTemplate, entityId?: string, runId?: string) => void
  onClose: () => void
  className?: string
}

const templates: DocumentTemplate[] = [
  {
    id: 'blank',
    name: 'Blank Document',
    description: 'Start with a clean slate',
    category: 'Basic',
    icon: FileText,
    content: '',
    tags: ['basic', 'empty']
  },
  {
    id: 'meeting-notes',
    name: 'Meeting Notes',
    description: 'Template for capturing meeting discussions and action items',
    category: 'Business',
    icon: Users,
    content: `# Meeting Notes

**Date:** ${new Date().toLocaleDateString()}
**Attendees:** 
**Meeting Type:** 

## Agenda
- [ ] Item 1
- [ ] Item 2
- [ ] Item 3

## Discussion Points

### Topic 1


### Topic 2


## Action Items
- [ ] **[Owner]** Action item 1 - Due: 
- [ ] **[Owner]** Action item 2 - Due: 

## Next Steps


## Notes

`,
    tags: ['meeting', 'business', 'notes', 'action-items']
  },
  {
    id: 'project-proposal',
    name: 'Project Proposal',
    description: 'Comprehensive template for project proposals',
    category: 'Business',
    icon: Briefcase,
    content: `# Project Proposal: [Project Name]

<!-- TABLE_OF_CONTENTS -->

## Executive Summary


## Project Overview

### Objectives
- 
- 
- 

### Scope
**In Scope:**
- 
- 

**Out of Scope:**
- 
- 

## Timeline

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| Phase 1 | | |
| Phase 2 | | |
| Phase 3 | | |

## Resources Required

### Team
- **Project Manager:** 
- **Developer(s):** 
- **Designer(s):** 

### Budget
| Item | Cost |
|------|------|
| | |
| **Total** | |

## Risk Assessment

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| | | | |

## Success Metrics


## Conclusion

`,
    tags: ['project', 'proposal', 'business', 'planning']
  },
  {
    id: 'weekly-report',
    name: 'Weekly Report',
    description: 'Template for weekly status reports',
    category: 'Reports',
    icon: TrendingUp,
    content: `# Weekly Report - Week of ${new Date().toLocaleDateString()}

## Summary
Brief overview of the week's activities and achievements.

## Key Accomplishments
- ✅ 
- ✅ 
- ✅ 

## Challenges & Blockers
- ⚠️ 
- ⚠️ 

## Metrics & KPIs

| Metric | Target | Actual | Status |
|--------|--------|--------|--------|
| | | | |

## Next Week's Priorities
1. 
2. 
3. 

## Additional Notes

`,
    tags: ['report', 'weekly', 'status', 'metrics']
  },
  {
    id: 'task-list',
    name: 'Task List',
    description: 'Organized task management template',
    category: 'Productivity',
    icon: CheckSquare,
    content: `# Task List - ${new Date().toLocaleDateString()}

## High Priority 🔴
- [ ] 
- [ ] 
- [ ] 

## Medium Priority 🟡
- [ ] 
- [ ] 
- [ ] 

## Low Priority 🟢
- [ ] 
- [ ] 
- [ ] 

## Completed ✅
- [x] 

## Notes

`,
    tags: ['tasks', 'productivity', 'todo', 'checklist']
  },
  {
    id: 'research-notes',
    name: 'Research Notes',
    description: 'Template for organizing research findings',
    category: 'Academic',
    icon: BookOpen,
    content: `# Research Notes: [Topic]

<!-- TABLE_OF_CONTENTS -->

**Date:** ${new Date().toLocaleDateString()}
**Researcher:** 
**Source:** 

## Research Question


## Methodology


## Key Findings

### Finding 1


### Finding 2


### Finding 3


## Data & Evidence

| Source | Type | Key Points | Reliability |
|--------|------|------------|-------------|
| | | | |

## Analysis


## Conclusions


## Further Research Needed
- 
- 
- 

## References
1. 
2. 
3. 

`,
    tags: ['research', 'academic', 'notes', 'analysis']
  },
  {
    id: 'brainstorm',
    name: 'Brainstorming Session',
    description: 'Template for capturing creative ideas',
    category: 'Creative',
    icon: Lightbulb,
    content: `# Brainstorming Session: [Topic]

**Date:** ${new Date().toLocaleDateString()}
**Participants:**
**Duration:**
**Facilitator:**

## Challenge/Problem Statement


## Ground Rules
- No judgment during idea generation
- Build on others' ideas
- Quantity over quality initially
- Stay focused on the topic

## Ideas 💡

### Category 1
-
-
-

### Category 2
-
-
-

### Wild Ideas 🚀
-
-
-

## Evaluation & Next Steps

### Top Ideas
1. **Idea:**
   **Pros:**
   **Cons:**
   **Next Steps:**

2. **Idea:**
   **Pros:**
   **Cons:**
   **Next Steps:**

## Action Items
- [ ]
- [ ]
- [ ]

`,
    tags: ['brainstorm', 'creative', 'ideas', 'collaboration']
  },
  {
    id: 'multi-column-layout',
    name: 'Multi-Column Layout',
    description: 'Showcase document with various column layouts and content types',
    category: 'Layout',
    icon: Columns,
    data: {
      type: 'doc',
      content: [
        {
          type: 'heading',
          attrs: { level: 1 },
          content: [{ type: 'text', text: 'Multi-Column Layout Showcase' }],
        },
        {
          type: 'tableOfContents',
          attrs: {
            id: 'table-of-contents',
            headings: null,
          },
        },
        {
          type: 'paragraph',
          content: [
            { type: 'text', text: 'This template demonstrates the various column layouts available in the editor. You can use columns to create professional documents with side-by-side content, comparisons, and structured layouts.' }
          ],
        },
        {
          type: 'heading',
          attrs: { level: 2 },
          content: [{ type: 'text', text: 'Two Equal Columns' }],
        },
        {
          type: 'columnBlock',
          attrs: { layout: 'equal-2' },
          content: [
            {
              type: 'column',
              content: [
                {
                  type: 'heading',
                  attrs: { level: 3 },
                  content: [{ type: 'text', text: 'Left Column' }],
                },
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'This is the left column. You can add any content here including text, lists, images, and more. Each column is independent and can be edited separately.' }
                  ],
                },
                {
                  type: 'bulletList',
                  content: [
                    {
                      type: 'listItem',
                      content: [
                        {
                          type: 'paragraph',
                          content: [{ type: 'text', text: 'Feature 1' }],
                        },
                      ],
                    },
                    {
                      type: 'listItem',
                      content: [
                        {
                          type: 'paragraph',
                          content: [{ type: 'text', text: 'Feature 2' }],
                        },
                      ],
                    },
                    {
                      type: 'listItem',
                      content: [
                        {
                          type: 'paragraph',
                          content: [{ type: 'text', text: 'Feature 3' }],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
            {
              type: 'column',
              content: [
                {
                  type: 'heading',
                  attrs: { level: 3 },
                  content: [{ type: 'text', text: 'Right Column' }],
                },
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'This is the right column. Perfect for comparisons, additional information, or complementary content that should appear side-by-side.' }
                  ],
                },
                {
                  type: 'bulletList',
                  content: [
                    {
                      type: 'listItem',
                      content: [
                        {
                          type: 'paragraph',
                          content: [{ type: 'text', text: 'Benefit A' }],
                        },
                      ],
                    },
                    {
                      type: 'listItem',
                      content: [
                        {
                          type: 'paragraph',
                          content: [{ type: 'text', text: 'Benefit B' }],
                        },
                      ],
                    },
                    {
                      type: 'listItem',
                      content: [
                        {
                          type: 'paragraph',
                          content: [{ type: 'text', text: 'Benefit C' }],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          type: 'heading',
          attrs: { level: 2 },
          content: [{ type: 'text', text: 'Three Equal Columns' }],
        },
        {
          type: 'columnBlock',
          attrs: { layout: 'equal-3' },
          content: [
            {
              type: 'column',
              content: [
                {
                  type: 'heading',
                  attrs: { level: 4 },
                  content: [{ type: 'text', text: 'Column 1' }],
                },
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'First of three columns. Great for organizing information into categories or showing step-by-step processes.' }
                  ],
                },
              ],
            },
            {
              type: 'column',
              content: [
                {
                  type: 'heading',
                  attrs: { level: 4 },
                  content: [{ type: 'text', text: 'Column 2' }],
                },
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'Second column with different content. Each column maintains its own formatting and structure.' }
                  ],
                },
              ],
            },
            {
              type: 'column',
              content: [
                {
                  type: 'heading',
                  attrs: { level: 4 },
                  content: [{ type: 'text', text: 'Column 3' }],
                },
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'Third column completing the set. Perfect for comparisons or categorized information.' }
                  ],
                },
              ],
            },
          ],
        },
        {
          type: 'heading',
          attrs: { level: 2 },
          content: [{ type: 'text', text: 'Ratio Columns (1/3 - 2/3)' }],
        },
        {
          type: 'columnBlock',
          attrs: { layout: 'ratio-1-2' },
          content: [
            {
              type: 'column',
              content: [
                {
                  type: 'heading',
                  attrs: { level: 4 },
                  content: [{ type: 'text', text: 'Sidebar' }],
                },
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'This narrow column is perfect for sidebars, navigation, or supplementary information.' }
                  ],
                },
              ],
            },
            {
              type: 'column',
              content: [
                {
                  type: 'heading',
                  attrs: { level: 4 },
                  content: [{ type: 'text', text: 'Main Content' }],
                },
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'This wider column takes up 2/3 of the space and is ideal for main content, detailed explanations, or primary information. The ratio layout is perfect for creating sidebar-style documents or highlighting key information alongside detailed content.' }
                  ],
                },
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'You can add multiple paragraphs, lists, tables, and other content elements in this main content area while keeping related information in the sidebar.' }
                  ],
                },
              ],
            },
          ],
        },
        {
          type: 'heading',
          attrs: { level: 2 },
          content: [{ type: 'text', text: 'Centered Column' }],
        },
        {
          type: 'columnBlock',
          attrs: { layout: 'centered' },
          content: [
            {
              type: 'column',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'This is a centered column that takes up 2/3 of the page width. It\'s perfect for focused content, quotes, or important announcements that you want to draw attention to.' }
                  ],
                },
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'The centered layout creates visual emphasis and is great for highlighting key information or creating a more focused reading experience.' }
                  ],
                },
              ],
            },
          ],
        },
        {
          type: 'heading',
          attrs: { level: 2 },
          content: [{ type: 'text', text: 'How to Use Columns' }],
        },
        {
          type: 'paragraph',
          content: [
            { type: 'text', text: 'To create columns in your document:' }
          ],
        },
        {
          type: 'orderedList',
          content: [
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'Type ' },
                    { type: 'text', marks: [{ type: 'code' }], text: '/2 columns' },
                    { type: 'text', text: ', ' },
                    { type: 'text', marks: [{ type: 'code' }], text: '/3 columns' },
                    { type: 'text', text: ', or ' },
                    { type: 'text', marks: [{ type: 'code' }], text: '/4 columns' },
                    { type: 'text', text: ' for equal columns' }
                  ],
                },
              ],
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'Type ' },
                    { type: 'text', marks: [{ type: 'code' }], text: '/1/3' },
                    { type: 'text', text: ' or ' },
                    { type: 'text', marks: [{ type: 'code' }], text: '/2/3' },
                    { type: 'text', text: ' for ratio columns' }
                  ],
                },
              ],
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'Type ' },
                    { type: 'text', marks: [{ type: 'code' }], text: '/centered' },
                    { type: 'text', text: ' for a centered column' }
                  ],
                },
              ],
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'Hover over columns to see layout options and switch between different column types' }
                  ],
                },
              ],
            },
            {
              type: 'listItem',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    { type: 'text', text: 'Click in any column to start typing and add content independently' }
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    tags: ['columns', 'layout', 'design', 'showcase', 'multi-column']
  },
  {
    id: 'esg-report',
    name: 'ESG Report',
    description: 'Comprehensive ESG analysis report with automated sections',
    category: 'Reports',
    icon: TrendingUp,
    data: {
      type: 'doc',
      content: [
        {
          type: 'reportSummary',
          attrs: {
            id: 'exec-summary',
            prompt: 'Executive summary of the ESG performance',
            title: 'Executive Summary',
            summarize: 'environmental,social,governance',
            headings: 'remove',
            depth: 1,
          },
        },
        {
          type: 'reportGroup',
          attrs: { id: 'esg-report' },
          content: [
            {
              type: 'heading',
              attrs: { level: 1 },
              content: [{ type: 'text', text: 'ESG Report' }],
            },
            {
              type: 'reportGroup',
              attrs: { id: 'environmental' },
              content: [
                {
                  type: 'heading',
                  attrs: { level: 2 },
                  content: [{ type: 'text', text: 'Environmental Impact' }],
                },
                {
                  type: 'reportSummary',
                  attrs: {
                    id: 'environmental-summary',
                    prompt: 'Summary of environmental impacts',
                    title: 'Environmental Summary',
                    summarize: 'environmental-risks',
                    headings: 'remove',
                    depth: 3,
                  },
                },
                {
                  type: 'reportSection',
                  attrs: {
                    id: 'environmental-risks',
                    title: 'Environmental Risks',
                    endpoint: '/report/entity/[ENTITY_ID]/[RUN_ID]/harm/model/sdg/section/13_climate_action',
                    headings: 'remove',
                    depth: 4,
                  },
                },
                {
                  type: 'reportSection',
                  attrs: {
                    id: 'climate-impact',
                    title: 'Climate Impact',
                    endpoint: '/report/entity/[ENTITY_ID]/[RUN_ID]/harm/model/sdg/section/15_life_on_land',
                    headings: 'remove',
                    depth: 4,
                  },
                },
              ],
            },
            {
              type: 'reportGroup',
              attrs: { id: 'social' },
              content: [
                {
                  type: 'heading',
                  attrs: { level: 2 },
                  content: [{ type: 'text', text: 'Social Impact' }],
                },
                {
                  type: 'reportSummary',
                  attrs: {
                    id: 'social-summary',
                    prompt: 'Summary of social impacts',
                    title: 'Social Summary',
                    summarize: 'social-risks',
                    headings: 'remove',
                    depth: 3,
                  },
                },
                {
                  type: 'reportSection',
                  attrs: {
                    id: 'social-risks',
                    title: 'Social Risks',
                    endpoint: '/report/entity/[ENTITY_ID]/[RUN_ID]/harm/model/sdg/section/16_peace_justice_strong_institutions',
                    headings: 'remove',
                    depth: 4,
                  },
                },
                {
                  type: 'reportSection',
                  attrs: {
                    id: 'human-rights',
                    title: 'Human Rights',
                    endpoint: '/report/entity/[ENTITY_ID]/[RUN_ID]/harm/model/sdg/section/10_reduced_inequalities',
                    headings: 'remove',
                    depth: 4,
                  },
                },
              ],
            },
            {
              type: 'reportGroup',
              attrs: { id: 'governance' },
              content: [
                {
                  type: 'heading',
                  attrs: { level: 2 },
                  content: [{ type: 'text', text: 'Governance' }],
                },
                {
                  type: 'reportSection',
                  attrs: {
                    id: 'governance-risks',
                    title: 'Governance Risks',
                    endpoint: '/report/entity/[ENTITY_ID]/[RUN_ID]/harm/model/sdg/section/16_peace_justice_strong_institutions',
                    headings: 'remove',
                    depth: 3,
                  },
                },
                {
                  type: 'reportSection',
                  attrs: {
                    id: 'board-oversight',
                    title: 'Board Oversight',
                    endpoint: '/report/entity/[ENTITY_ID]/[RUN_ID]/harm/model/sdg/section/17_partnerships_for_goals',
                    headings: 'remove',
                    depth: 3,
                  },
                },
              ],
            },
            {
              type: 'reportGroup',
              attrs: { id: 'reliability' },
              content: [
                {
                  type: 'heading',
                  attrs: { level: 2 },
                  content: [{ type: 'text', text: 'Data Reliability' }],
                },
                {
                  type: 'reportSection',
                  attrs: {
                    id: 'reliability-analysis',
                    title: 'Reliability Assessment',
                    endpoint: '/report/entity/[ENTITY_ID]/[RUN_ID]/reliability',
                    headings: 'remove',
                    depth: 3,
                  },
                },
              ],
            },
            {
              type: 'reportGroup',
              attrs: { id: 'transparency' },
              content: [
                {
                  type: 'heading',
                  attrs: { level: 2 },
                  content: [{ type: 'text', text: 'Transparency & Disclosure' }],
                },
                {
                  type: 'reportSection',
                  attrs: {
                    id: 'transparency-analysis',
                    title: 'Transparency Analysis',
                    endpoint: '/report/entity/[ENTITY_ID]/[RUN_ID]/transparency',
                    headings: 'remove',
                    depth: 3,
                  },
                },
              ],
            },
          ],
        },
      ],
    },
    tags: ['esg', 'sustainability', 'report', 'environmental', 'social', 'governance'],
  }
]

// Categories will be calculated dynamically in the component

export function DocumentTemplates({ onSelectTemplate, onClose, className }: DocumentTemplatesProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('All')
  const [dynamicTemplates, setDynamicTemplates] = useState<DynamicTemplate[]>([])
  const [isLoadingDynamic, setIsLoadingDynamic] = useState(true)
  const [selectedEntity, setSelectedEntity] = useState<string | null>(null)
  const [selectedRun, setSelectedRun] = useState<string>('latest')
  const [includeDisclosures, setIncludeDisclosures] = useState<boolean>(true)

  // Load dynamic templates on component mount
  useEffect(() => {
    async function loadDynamicTemplates() {
      try {
        setIsLoadingDynamic(true)
        const dynamic = await generateDynamicTemplates()
        setDynamicTemplates(dynamic)
      } catch (error) {
        console.error('Failed to load dynamic templates:', error)
      } finally {
        setIsLoadingDynamic(false)
      }
    }

    loadDynamicTemplates()
  }, [])

  // Combine static and dynamic templates
  const allTemplates = [...dynamicTemplates, ...templates]

  // Calculate categories from all templates
  const categories = Array.from(new Set(allTemplates.map(t => t.category)))

  const filteredTemplates = selectedCategory === 'All'
    ? allTemplates
    : allTemplates.filter(t => t.category === selectedCategory)

  const handleSelectTemplate = (template: DocumentTemplate) => {
    onSelectTemplate(template, selectedEntity || undefined, selectedRun)
  }

  return (
    <div className={cn('w-full max-w-4xl mx-auto p-6', className)} data-testid="template-dialog">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold">Choose a Template</h2>
          <p className="text-muted-foreground">Start with a pre-designed template or create a blank document</p>
        </div>
        <Button variant="outline" onClick={onClose} data-testid="cancel-button">
          Cancel
        </Button>
      </div>

      {/* Entity/Run Selector */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Select Entity and Run</h3>
        <DocumentEntityRunSelector
          selectedEntity={selectedEntity}
          selectedRun={selectedRun}
          includeDisclosures={includeDisclosures}
          onEntityChange={setSelectedEntity}
          onRunChange={setSelectedRun}
          onDisclosuresChange={setIncludeDisclosures}
          className="mb-4"
        />
      </div>

      {/* Category Filter */}
      <div className="flex gap-2 mb-6 overflow-x-auto">
        <Button
          size="sm"
          variant={selectedCategory === 'All' ? 'default' : 'outline'}
          onClick={() => setSelectedCategory('All')}
          data-testid="category-filter-all"
        >
          All
        </Button>
        {categories.map(category => (
          <Button
            key={category}
            size="sm"
            variant={selectedCategory === category ? 'default' : 'outline'}
            onClick={() => setSelectedCategory(category)}
            data-testid={`category-filter-${category.toLowerCase()}`}
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Loading State */}
      {isLoadingDynamic && (
        <div className="text-center py-4 text-muted-foreground">
          Loading dynamic templates...
        </div>
      )}

      {/* Templates Grid */}
      <ScrollArea className="h-[600px]">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTemplates.map((template) => {
            // Handle both static templates (with icon component) and dynamic templates (with icon string)
            const IconComponent = typeof template.icon === 'string'
              ? getIconComponent(template.icon)
              : template.icon
            return (
              <div
                key={template.id}
                className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer group"
                onClick={() => handleSelectTemplate(template)}
                data-testid={`template-${template.id}`}
              >
                <div className="flex items-start gap-3 mb-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <IconComponent className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold group-hover:text-primary transition-colors">
                      {template.name}
                    </h3>
                    <Badge variant="secondary" className="text-xs mt-1">
                      {template.category}
                    </Badge>
                  </div>
                </div>
                
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {template.description}
                </p>
                
                <div className="flex flex-wrap gap-1">
                  {template.tags.slice(0, 3).map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {template.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{template.tags.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </ScrollArea>
    </div>
  )
}
