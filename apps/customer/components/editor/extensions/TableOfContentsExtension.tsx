import React from 'react'
import { Node } from '@tiptap/core'
import { <PERSON>de<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@tiptap/react'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    tableOfContents: {
      setTableOfContents: () => ReturnType
    }
  }
}

/* ------------------------------------------------------------------
 *  Types
 * ------------------------------------------------------------------*/
interface HeadingItem {
  id: string
  level: number
  text: string
  anchor: string
}

interface TableOfContentsComponentProps {
  node: any
  editor?: any
  updateAttributes?: (attrs: any) => void
}

/* ------------------------------------------------------------------
 *  Table of Contents Component
 * ------------------------------------------------------------------*/
const TableOfContentsComponent = React.memo<TableOfContentsComponentProps>(({
  node,
  editor,
  updateAttributes
}) => {
  const [headings, setHeadings] = React.useState<HeadingItem[]>([])

  // Extract headings from the document
  const extractHeadings = React.useCallback(() => {
    if (!editor) return []

    const headingNodes: HeadingItem[] = []
    const doc = editor.state.doc

    doc.descendants((node: any, pos: number) => {
      if (node.type.name === 'heading') {
        const level = node.attrs.level
        const text = node.textContent
        
        if (text.trim()) {
          // Generate anchor from text
          const anchor = text
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim()
            .replace(/^-+|-+$/g, '')
          
          headingNodes.push({
            id: `heading-${pos}`,
            level,
            text: text.trim(),
            anchor: anchor || `heading-${pos}`,
          })
        }
      }
    })

    return headingNodes
  }, [editor])

  // Track last saved headings to prevent unnecessary updates
  const lastSavedHeadings = React.useRef<string | null>(node.attrs.headings)

  // Update headings when document changes
  React.useEffect(() => {
    if (!editor) return

    const updateHeadings = () => {
      const newHeadings = extractHeadings()
      setHeadings(newHeadings)
      
      // Update node attributes if headings changed
      const headingsJson = JSON.stringify(newHeadings)
      if (updateAttributes && headingsJson !== lastSavedHeadings.current) {
        lastSavedHeadings.current = headingsJson
        updateAttributes({ headings: headingsJson })
      }
    }

    // Initial update
    updateHeadings()

    // Listen for document updates
    editor.on('update', updateHeadings)
    
    return () => {
      editor.off('update', updateHeadings)
    }
  }, [editor, extractHeadings, updateAttributes])

  // Load headings from node attributes on mount
  React.useEffect(() => {
    const storedHeadings = node.attrs.headings
    if (storedHeadings) {
      try {
        const parsedHeadings = JSON.parse(storedHeadings)
        if (Array.isArray(parsedHeadings)) {
          setHeadings(parsedHeadings)
        }
      } catch (error) {
        console.error('TableOfContents: Failed to parse stored headings:', error)
      }
    }
  }, [node.attrs.headings])

  // Scroll to heading when clicked
  const scrollToHeading = (anchor: string) => {
    // First try to find an element with the exact anchor id
    let element = document.getElementById(anchor)
    
    // If not found, try to find a heading with this text
    if (!element) {
      const headingSelectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
      for (const selector of headingSelectors) {
        const headingElements = document.querySelectorAll(selector)
        for (const heading of headingElements) {
          if (heading.textContent?.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-') === anchor) {
            element = heading as HTMLElement
            break
          }
        }
        if (element) break
      }
    }
    
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  // Render nested table of contents
  const renderTOC = (items: HeadingItem[]) => {
    if (items.length === 0) {
      return (
        <div className="text-neutral-500 italic py-4">
          No headings found. Add some headings to see them in the table of contents.
        </div>
      )
    }

    return (
      <nav className="space-y-1">
        {items.map((heading) => {
          const indent = Math.max(0, (heading.level - 1)) * 16 // 16px per level, starting from 0
          
          return (
            <div
              key={heading.id}
              className="group"
              style={{ paddingLeft: `${indent}px` }}
            >
              <button
                onClick={() => scrollToHeading(heading.anchor)}
                className="text-left w-full py-1 px-2 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors duration-200 text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-neutral-100"
                title={`Go to "${heading.text}"`}
              >
                <span className={`
                  block truncate
                  ${heading.level === 1 ? 'font-semibold text-base' : ''}
                  ${heading.level === 2 ? 'font-medium text-sm' : ''}
                  ${heading.level >= 3 ? 'font-normal text-sm' : ''}
                `}>
                  {heading.text}
                </span>
              </button>
            </div>
          )
        })}
      </nav>
    )
  }

  return (
    <NodeViewWrapper className="table-of-contents-wrapper">
      <div className="glass-card p-6 my-4">
        <div className="flex items-center gap-2 mb-4 pb-2 border-b border-neutral-200 dark:border-neutral-700">
          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
          <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
            Table of Contents
          </h3>
        </div>
        
        {renderTOC(headings)}
      </div>
    </NodeViewWrapper>
  )
})

TableOfContentsComponent.displayName = 'TableOfContentsComponent'

/* ------------------------------------------------------------------
 *  TipTap Table of Contents Extension
 * ------------------------------------------------------------------*/
export const TableOfContentsExtension = Node.create({
  name: 'tableOfContents',

  group: 'block',

  atom: true,

  addAttributes() {
    return {
      id: {
        default: 'table-of-contents',
      },
      headings: {
        default: null, // JSON string of headings array
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="table-of-contents"]',
        getAttrs: (element) => {
          if (typeof element === 'string') return false
          return {
            id: element.getAttribute('id') || 'table-of-contents',
            headings: element.getAttribute('headings'),
          }
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', { 'data-type': 'table-of-contents', ...HTMLAttributes }]
  },

  addNodeView() {
    return ReactNodeViewRenderer((props) => (
      <TableOfContentsComponent
        {...props}
        editor={props.editor}
        updateAttributes={props.updateAttributes}
      />
    ))
  },

  addCommands() {
    return {
      setTableOfContents:
        () =>
        ({ commands }: { commands: any }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              headings: null,
            },
          })
        },
    }
  },
})

/* ------------------------------------------------------------------
 *  Export types for use in other components
 * ------------------------------------------------------------------*/
export type { TableOfContentsComponentProps, HeadingItem }