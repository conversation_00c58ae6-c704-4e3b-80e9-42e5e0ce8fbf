'use client'

import { Node, Extension, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import React from 'react'
import { NodeViewWrapper, NodeViewContent } from '@tiptap/react'

// Column layout types
export type ColumnLayout =
  | 'equal-2'      // 2 equal columns
  | 'equal-3'      // 3 equal columns
  | 'equal-4'      // 4 equal columns
  | 'ratio-1-2'    // 1/3 - 2/3 ratio
  | 'ratio-2-1'    // 2/3 - 1/3 ratio
  | 'centered'     // centered 2/3 width single column

// Individual Column Component
const ColumnComponent = () => {
  console.log('ColumnComponent rendered')
  return (
    <NodeViewWrapper className="column">
      <NodeViewContent />
    </NodeViewWrapper>
  )
}

// Column Block Component
const ColumnBlockComponent = ({ node, updateAttributes, deleteNode }: any) => {
  const layout = node.attrs.layout as ColumnLayout
  const [isHovered, setIsHovered] = React.useState(false)

  console.log('ColumnBlockComponent rendered with layout:', layout, 'node:', node)

  const getColumnCount = (layout: ColumnLayout) => {
    switch (layout) {
      case 'equal-2':
      case 'ratio-1-2':
      case 'ratio-2-1':
        return 2
      case 'equal-3':
        return 3
      case 'equal-4':
        return 4
      case 'centered':
        return 1
      default:
        return 2
    }
  }

  const columnCount = getColumnCount(layout)

  return (
    <NodeViewWrapper
      className={`column-block ${isHovered ? 'column-block-hovered' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      data-layout={layout}
    >
      {/* Layout selector */}
      {isHovered && (
        <div className="layout-selector">
          <button
            onClick={() => updateAttributes({ layout: 'equal-2' })}
            className={layout === 'equal-2' ? 'active' : ''}
            title="2 Equal Columns"
          >
            ⚏
          </button>
          <button
            onClick={() => updateAttributes({ layout: 'equal-3' })}
            className={layout === 'equal-3' ? 'active' : ''}
            title="3 Equal Columns"
          >
            ⚏⚏
          </button>
          <button
            onClick={() => updateAttributes({ layout: 'equal-4' })}
            className={layout === 'equal-4' ? 'active' : ''}
            title="4 Equal Columns"
          >
            ⚏⚏⚏
          </button>
          <button
            onClick={() => updateAttributes({ layout: 'ratio-1-2' })}
            className={layout === 'ratio-1-2' ? 'active' : ''}
            title="1/3 - 2/3 Ratio"
          >
            ⚏⚏⚏
          </button>
          <button
            onClick={() => updateAttributes({ layout: 'ratio-2-1' })}
            className={layout === 'ratio-2-1' ? 'active' : ''}
            title="2/3 - 1/3 Ratio"
          >
            ⚏⚏⚏
          </button>
          <button
            onClick={() => updateAttributes({ layout: 'centered' })}
            className={layout === 'centered' ? 'active' : ''}
            title="Centered Column"
          >
            ⚏
          </button>
          <button
            onClick={deleteNode}
            className="delete"
            title="Delete Columns"
          >
            ×
          </button>
        </div>
      )}

      {/* Render columns - content is handled by individual Column nodes */}
      <NodeViewContent />
    </NodeViewWrapper>
  )
}

// Individual Column Node
export const Column = Node.create({
  name: 'column',

  group: 'block',

  content: 'block+',

  isolating: true,

  parseHTML() {
    return [
      {
        tag: 'div[data-type="column"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'column', class: 'column' }), 0]
  },

  addNodeView() {
    return ReactNodeViewRenderer(ColumnComponent)
  },
})

// Column Block Node
export const ColumnBlock = Node.create({
  name: 'columnBlock',

  group: 'block',

  content: 'column+',

  isolating: true,

  addAttributes() {
    return {
      layout: {
        default: 'equal-2',
        parseHTML: element => element.getAttribute('data-layout') || 'equal-2',
        renderHTML: attributes => ({
          'data-layout': attributes.layout,
        }),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="column-block"]',
        getAttrs: element => {
          if (typeof element === 'string') return false
          return {
            layout: element.getAttribute('data-layout') || 'equal-2',
          }
        },
      },
      {
        tag: 'div.column-block',
        getAttrs: element => {
          if (typeof element === 'string') return false
          return {
            layout: element.getAttribute('data-layout') || 'equal-2',
          }
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'column-block', class: 'column-block' }), 0]
  },

  addNodeView() {
    return ReactNodeViewRenderer(ColumnBlockComponent)
  },

  addCommands() {
    return {
      setColumns:
        (layout: ColumnLayout) =>
        ({ commands }) => {
          const getColumnCount = (layout: ColumnLayout) => {
            switch (layout) {
              case 'equal-2':
              case 'ratio-1-2':
              case 'ratio-2-1':
                return 2
              case 'equal-3':
                return 3
              case 'equal-4':
                return 4
              case 'centered':
                return 1
              default:
                return 2
            }
          }

          return commands.insertContent({
            type: this.name,
            attrs: { layout },
            content: Array.from({ length: getColumnCount(layout) }, () => ({
              type: 'column',
              content: [{ type: 'paragraph' }],
            })),
          })
        },
    }
  },
})

// Main Columns Extension
export const ColumnsExtension = Extension.create({
  name: 'columns',

  addExtensions() {
    return [Column, ColumnBlock]
  },

  addCommands() {
    return {
      insertColumns:
        (layout: ColumnLayout = 'equal-2') =>
        ({ commands }) => {
          return commands.setColumns(layout)
        },
    }
  },
})

// Declare module for TypeScript
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    columns: {
      setColumns: (layout: ColumnLayout) => ReturnType
      insertColumns: (layout?: ColumnLayout) => ReturnType
    }
  }
}
