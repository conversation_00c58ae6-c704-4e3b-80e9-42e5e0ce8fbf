// Utility function to find parent report-group
import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from 'react'
import { useReportManager } from '@/components/editor/hooks/useReportManager'
import { ReportComponentConfig, ReportComponentDialog } from '@/components/editor/dialogs/ReportComponentDialog'
import { AlertTriangle, GripVertical, Lock, MoreVertical, RefreshCw, Settings, Shield, Trash2 } from 'lucide-react'
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'
import { NodeViewContent, NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { cn } from '@utils/lib/utils'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import { Button } from '@ui/components/ui/button'
import { Node } from '@tiptap/core'

/**
 * Check if a TipTap node has meaningful content
 * @param node The TipTap node to check
 * @returns true if the node has content, false if empty
 */
function hasNodeContent(node: any): boolean {
  if (!node || !node.content) return false

  // Check if node has any content at all
  if (node.content.size === 0) return false

  // Extract text content and check if it's meaningful
  const textContent = node.textContent || ''
  const trimmedContent = textContent.trim()

  // Consider empty if only whitespace or very short content
  return trimmedContent.length > 0
}

export function findParentReportGroup(editor: any, pos: number): string | null {
  if (!editor || pos === undefined) return null

  try {
    const resolvedPos = editor.state.doc.resolve(pos)

    // Walk up the document tree to find a parent report-group
    for (let depth = resolvedPos.depth; depth >= 0; depth--) {
      const node = resolvedPos.node(depth)
      if (node.type.name === 'reportGroup' && node.attrs.id) {
        return node.attrs.id
      }
    }
  } catch (error) {
    console.warn('Error finding parent report group:', error)
  }

  return null
}

/* ------------------------------------------------------------------
 *  Report Group React Component (formerly Report Section)
 * ------------------------------------------------------------------*/
export const ReportGroupComponent = React.memo<{
  node: any;
  updateAttributes: any;
  deleteNode: any;
  editor: any;
  getPos: () => number | undefined
}>(({ node, updateAttributes, deleteNode, editor, getPos }) => {
  const id = React.useMemo(() => node.attrs.id, [node.attrs.id])
  const title = React.useMemo(() => node.attrs.title, [node.attrs.title])
  const locked = React.useMemo(() => node.attrs.locked, [node.attrs.locked])
  const preserved = React.useMemo(() => node.attrs.preserved, [node.attrs.preserved])
  const reportManager = useReportManager()
  const [configDialogOpen, setConfigDialogOpen] = useState(false)

  // Find parent report group for nested groups
  const parentGroupId = React.useMemo(() => {
    const pos = getPos()
    return pos !== undefined ? findParentReportGroup(editor, pos) : null
  }, [editor, getPos])

  // Get current component state
  const currentComponentState = reportManager.components.get(id)
  const status = currentComponentState?.status || 'idle'

  useEffect(() => {
    // Set initial status - respect status from HTML attributes
    let initialStatus: 'idle' | 'loaded' | 'preserved' | 'locked' | 'loading' | 'error' = node.attrs.status || 'idle'

    // Override with locked/preserved if those attributes are set
    if (preserved) {
      initialStatus = 'preserved'
    } else if (locked) {
      initialStatus = 'locked'
    }

    console.log(`Report group ${id}: Registering with status '${initialStatus}' (from HTML: '${node.attrs.status}', locked: ${locked}, preserved: ${preserved})`)

    // Register this component with the report manager
    reportManager.registerComponent({
      id,
      type: 'report-group',
      status: initialStatus,
      title,
      parentId: parentGroupId || undefined,
    })

    return () => {
      reportManager.removeComponent(id)
    }
  }, [id, title, parentGroupId, node.attrs.status, preserved, locked]) // Remove reportManager from deps to prevent re-registration

  // Debounced status synchronization to prevent infinite loops
  const statusSyncTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastSyncedStatusRef = useRef(node.attrs.status)
  const isSyncingRef = useRef(false)

  // Sync component status to node attributes with debouncing
  const syncComponentState = reportManager.components.get(id)
  const currentComponentStatus = syncComponentState?.status

  useEffect(() => {
    // Skip if we're currently in a sync operation to prevent loops
    if (isSyncingRef.current) return

    if (currentComponentStatus && currentComponentStatus !== lastSyncedStatusRef.current) {
      // Clear any existing timeout
      if (statusSyncTimeoutRef.current) {
        clearTimeout(statusSyncTimeoutRef.current)
      }

      // Debounce the status update
      statusSyncTimeoutRef.current = setTimeout(() => {
        if (currentComponentStatus !== node.attrs.status) {
          console.log(`Report group ${id}: Syncing status from component '${currentComponentStatus}' to node (was '${node.attrs.status}')`)
          isSyncingRef.current = true
          lastSyncedStatusRef.current = currentComponentStatus
          updateAttributes({ status: currentComponentStatus })

          // Reset sync flag after a brief delay
          setTimeout(() => {
            isSyncingRef.current = false
          }, 50)
        }
      }, 100) // 100ms debounce
    }

    return () => {
      if (statusSyncTimeoutRef.current) {
        clearTimeout(statusSyncTimeoutRef.current)
      }
    }
  }, [currentComponentStatus, id, updateAttributes, node.attrs.status])

  // Update tracking when node attributes change from external sources
  useEffect(() => {
    if (!isSyncingRef.current) {
      lastSyncedStatusRef.current = node.attrs.status
    }
  }, [node.attrs.status])

  // EKO-117: Watch for status changes to 'idle' and trigger reload
  const previousStatusRef = useRef<string | undefined>(undefined)
  useEffect(() => {
    const currentStatus = syncComponentState?.status
    const previousStatus = previousStatusRef.current

    // Update the previous status ref
    previousStatusRef.current = currentStatus

    // If status changed to 'idle', trigger reload from database
    if (currentStatus === 'idle' && previousStatus && previousStatus !== 'idle') {
      console.log(`Report group ${id}: Status changed to 'idle' from '${previousStatus}', triggering reload`)

      // For groups, we trigger re-evaluation of the group status
      // The group status is automatically calculated based on its children
      if (!locked && !preserved) {
        // Small delay to ensure status change is fully processed
        setTimeout(() => {
          reportManager.updateGroupStatus(id, true) // Force immediate update
        }, 100)
      }
    }
  }, [syncComponentState?.status, id, locked, preserved, reportManager])

  const handleMenuAction = useCallback((action: string) => {
    switch (action) {
      case 'refresh':
        if (!locked && !preserved) {
          console.log(`Report group ${id}: Refreshing all descendants`)
          reportManager.refreshAllDescendants(id)
        }
        break
      case 'delete':
        deleteNode()
        break
      case 'configure':
        setConfigDialogOpen(true)
        break
      case 'lock':
        updateAttributes({ locked: true })
        break
      case 'preserve':
        updateAttributes({ preserved: true })
        break
    }
  }, [id, locked, preserved, reportManager, updateAttributes, deleteNode])

  const handleConfigConfirm = useCallback((config: ReportComponentConfig) => {
    updateAttributes({
      id: config.id,
      title: config.title,
    })
  }, [updateAttributes])

  // Get status icon and styling based on group state
  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <RefreshCw className="w-3 h-3 text-green-600 animate-spin" />
      case 'error':
        const errorMessage = currentComponentState?.error || 'One or more child components have errors'
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <AlertTriangle className="w-3 h-3 text-red-600 cursor-help" />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="text-sm">{errorMessage}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      case 'loaded':
        return <span className="w-3 h-3 text-green-600">✓</span>
      default:
        return null
    }
  }

  const getStatusStyling = () => {
    switch (status) {
      case 'loading':
        return {
          border: 'border-yellow-200',
          bg: 'bg-yellow-50/30',
          header: 'bg-yellow-100 border-yellow-200',
          text: 'text-yellow-700',
        }
      case 'error':
        return {
          border: 'border-red-200',
          bg: 'bg-red-50/30',
          header: 'bg-red-100 border-red-200',
          text: 'text-red-700',
        }
      case 'loaded':
        return {
          border: 'border-green-200',
          bg: 'bg-green-50/30',
          header: 'bg-green-100 border-green-200',
          text: 'text-green-700',
        }
      default:
        return {
          border: 'border-gray-200',
          bg: 'bg-gray-50/30',
          header: 'bg-gray-100 border-gray-200',
          text: 'text-gray-700',
        }
    }
  }

  const styling = getStatusStyling()

  return (
    <NodeViewWrapper className={cn(
      'report-group relative border-2 rounded-lg p-4 my-4 backdrop-blur-sm',
      styling.border,
      styling.bg,
      // Print mode: hide all visual decorations
      'print:border-none print:bg-transparent print:backdrop-blur-none print:p-0 print:m-0 print:rounded-none',
      status === 'loading' && 'opacity-80',
    )}>
      {/* Group Header with Controls */}
      <div className={cn(
        'absolute -top-3 left-4 flex items-center gap-2 px-3 py-1 rounded-full border print:hidden',
        styling.header,
      )}>
        <GripVertical className="w-4 h-4 text-green-600 cursor-grab" />
        <span className={cn('text-sm font-medium', styling.text)}>{id}</span>
        {getStatusIcon()}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-green-600 hover:text-green-800"
              data-testid="report-group-menu-trigger"
            >
              <MoreVertical className="w-3 h-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => handleMenuAction('refresh')}
              disabled={locked || preserved || status === 'loading'}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('configure')}>
              <Settings className="w-4 h-4 mr-2" />
              Configure
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('lock')}>
              <Lock className="w-4 h-4 mr-2" />
              Lock
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('preserve')}>
              <Shield className="w-4 h-4 mr-2" />
              Preserve
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('delete')} className="text-red-600">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {title && hasNodeContent(node) && (
        <a id={id}>
          <span className="report-section-title block heading-2 mt-4">{title}</span>
        </a>
      )}
      <NodeViewContent className="content mt-2" />

      <ReportComponentDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        onConfirm={handleConfigConfirm}
        type="report-group"
        initialConfig={{
          id,
          title,
          type: 'report-group',
        }}
      />
    </NodeViewWrapper>
  )
})
/* ------------------------------------------------------------------
 *  TipTap Report Group Extension (formerly Report Section)
 * ------------------------------------------------------------------*/
export const ReportGroupExtension = Node.create({
  name: 'reportGroup',

  group: 'block',

  content: 'block*',

  addAttributes() {
    return {
      id: {
        default: null,
      },
      title: {
        default: null,
      },
      locked: {
        default: false,
      },
      preserved: {
        default: false,
      },
      status: {
        default: 'idle',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'report-group',
        getAttrs: (element) => {
          if (typeof element === 'string') return false
          return {
            id: element.getAttribute('id'),
            title: element.getAttribute('title'),
            locked: element.getAttribute('locked') === 'true',
            preserved: element.getAttribute('preserved') === 'true',
            status: element.getAttribute('status') || 'idle',
          }
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['report-group', HTMLAttributes, 0]
  },

  addNodeView() {
    return ReactNodeViewRenderer(ReportGroupComponent)
  },
})
ReportGroupComponent.displayName = 'ReportGroupComponent'
