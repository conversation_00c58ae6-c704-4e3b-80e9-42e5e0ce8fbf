import { <PERSON>, <PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON>, getMarksBetween, isMarkActive, mergeAttributes } from '@tiptap/core'
import { <PERSON>lug<PERSON>, Plugin<PERSON>ey } from 'prosemirror-state'
import { Decoration, DecorationSet } from 'prosemirror-view'
import type { <PERSON><PERSON><PERSON>, Editor, MarkRange } from '@tiptap/core'

export const MARK_INSERTION = 'insertion'
export const MARK_DELETION = 'deletion'

// Define insertion mark
export const InsertionMark = Mark.create({
  name: MARK_INSERTION,

  addAttributes() {
    return {
      'data-op-user-id': {
        type: 'string',
        default: () => '',
      },
      'data-op-user-nickname': {
        type: 'string',
        default: () => '',
      },
      'data-op-date': {
        type: 'string',
        default: () => '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'insert',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'insert',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        class: 'bg-green-100 text-green-800 decoration-none relative cursor-pointer hover:bg-green-200 transition-colors duration-200 border-b-2 border-green-300',
        'data-change-type': 'insertion',
        style: 'background-color: rgb(220 252 231) !important; color: rgb(22 101 52) !important; border-bottom: 2px solid rgb(134 239 172) !important;',
      }),
      0,
    ]
  },
})

// Define deletion mark
export const DeletionMark = Mark.create({
  name: MARK_DELETION,

  addAttributes() {
    return {
      'data-op-user-id': {
        type: 'string',
        default: () => '',
      },
      'data-op-user-nickname': {
        type: 'string',
        default: () => '',
      },
      'data-op-date': {
        type: 'string',
        default: () => '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'delete',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'span',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        class: 'bg-red-100 text-red-800 relative cursor-pointer hover:bg-red-200 transition-colors duration-200 line-through border-b-2 border-red-300',
        'data-change-type': 'deletion',
        'title': 'This text will be deleted. Hover to accept/reject.',
        style: 'background-color: rgb(254 226 226) !important; color: rgb(153 27 27) !important; text-decoration: line-through !important; border-bottom: 2px solid rgb(252 165 165) !important;',
      }),
      0, // Content goes here - the actual text to be deleted
    ]
  },
})

// Helper function to get current minute timestamp
const getMinuteTime = () => Math.round(new Date().getTime() / 1000 / 60) * 1000 * 60

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    changeTracking: {
      setTrackChangeStatus: (enabled: boolean) => ReturnType
      getTrackChangeStatus: () => ReturnType
      toggleTrackChangeStatus: () => ReturnType
      acceptChange: () => ReturnType
      rejectChange: () => ReturnType
      acceptChangeAt: (pos: number) => ReturnType
      rejectChangeAt: (pos: number) => ReturnType
      acceptAllChanges: () => ReturnType
      rejectAllChanges: () => ReturnType
      updateOpUserOption: (opUserId: string, opUserNickname: string) => ReturnType
      hasPendingChanges: () => ReturnType
    }
  }
}

// Helper function to handle change operations
const changeTrack = (opType: 'accept' | 'reject' | 'accept-all' | 'reject-all', param: CommandProps) => {
  const from = param.editor.state.selection.from
  const to = param.editor.state.selection.to

  let markRanges: Array<MarkRange> = []

  // Handle different operation types
  if ((opType === 'accept' || opType === 'reject') && from === to) {
    // No selection - find mark near cursor
    const isInsertBeforeCursor = isMarkActive(param.editor.state, MARK_INSERTION)
    const isDeleteBeforeCursor = isMarkActive(param.editor.state, MARK_DELETION)

    let leftRange
    if (isInsertBeforeCursor) {
      leftRange = getMarkRange(param.editor.state.selection.$from, param.editor.state.doc.type.schema.marks.insertion)
    } else if (isDeleteBeforeCursor) {
      leftRange = getMarkRange(param.editor.state.selection.$from, param.editor.state.doc.type.schema.marks.deletion)
    }

    if (leftRange) {
      markRanges = getMarksBetween(leftRange.from, leftRange.to, param.editor.state.doc)
    }
  } else if (opType === 'accept-all' || opType === 'reject-all') {
    // All content
    markRanges = getMarksBetween(0, param.editor.state.doc.content.size, param.editor.state.doc)
  } else {
    // Selection range
    markRanges = getMarksBetween(from, to, param.editor.state.doc)
  }

  // Filter to only track change marks
  markRanges = markRanges.filter(markRange =>
    markRange.mark.type.name === MARK_DELETION || markRange.mark.type.name === MARK_INSERTION
  )

  if (!markRanges.length) {
    return false
  }

  const currentTr = param.tr
  let offset = 0

  markRanges.forEach((markRange) => {
    const isAcceptInsert = (opType === 'accept' || opType === 'accept-all') && markRange.mark.type.name === MARK_INSERTION
    const isRejectDelete = (opType === 'reject' || opType === 'reject-all') && markRange.mark.type.name === MARK_DELETION

    if (isAcceptInsert || isRejectDelete) {
      // Remove mark (accept insertion or reject deletion)
      currentTr.removeMark(markRange.from - offset, markRange.to - offset, markRange.mark.type)
    } else {
      // Remove content (reject insertion or accept deletion)
      currentTr.deleteRange(markRange.from - offset, markRange.to - offset)
      offset += (markRange.to - markRange.from)
    }
  })

  if (currentTr.steps.length) {
    currentTr.setMeta('trackManualChanged', true)
    const newState = param.editor.state.apply(currentTr)
    param.editor.view.updateState(newState)
  }

  return false
}

// Helper function to handle position-based change operations
const changeTrackAt = (opType: 'accept' | 'reject', pos: number, param: CommandProps) => {
  console.log('changeTrackAt called', { opType, pos })

  // Find the mark at the given position
  const doc = param.editor.state.doc
  const $pos = doc.resolve(pos)

  // Check for insertion or deletion marks at this position
  let marks = $pos.marks()
  let changeMarks = marks.filter(mark =>
    mark.type.name === MARK_INSERTION || mark.type.name === MARK_DELETION
  )

  console.log('Found marks at position', { marks: marks.map(m => m.type.name), changeMarks: changeMarks.map(m => m.type.name) })

  // If no marks found at exact position, search in a small range around it
  if (!changeMarks.length) {
    console.log('No change marks found at exact position, searching nearby...')

    // Search in a range of ±3 positions around the target
    for (let offset = -3; offset <= 3; offset++) {
      const searchPos = pos + offset
      if (searchPos >= 0 && searchPos <= doc.content.size) {
        const $searchPos = doc.resolve(searchPos)
        const searchMarks = $searchPos.marks()
        const searchChangeMarks = searchMarks.filter(mark =>
          mark.type.name === MARK_INSERTION || mark.type.name === MARK_DELETION
        )

        if (searchChangeMarks.length > 0) {
          console.log(`Found marks at offset ${offset} (position ${searchPos})`, searchChangeMarks.map(m => m.type.name))
          marks = searchMarks
          changeMarks = searchChangeMarks
          // Update pos to the found position
          pos = searchPos
          break
        }
      }
    }
  }

  if (!changeMarks.length) {
    console.log('No change marks found in range')
    return false
  }

  // Get the range of the first change mark found
  const changeMark = changeMarks[0]
  const $updatedPos = doc.resolve(pos) // Use the potentially updated position
  const markRange = getMarkRange($updatedPos, changeMark.type)

  console.log('Mark range found', { markRange, markType: changeMark.type.name })

  if (!markRange) {
    console.log('No mark range found')
    return false
  }

  // Create a fresh transaction from the current editor state
  const { state, view } = param.editor
  const currentTr = state.tr

  const isAcceptInsert = opType === 'accept' && changeMark.type.name === MARK_INSERTION
  const isRejectDelete = opType === 'reject' && changeMark.type.name === MARK_DELETION
  const isAcceptDelete = opType === 'accept' && changeMark.type.name === MARK_DELETION
  const isRejectInsert = opType === 'reject' && changeMark.type.name === MARK_INSERTION

  console.log('Operation flags', { isAcceptInsert, isRejectDelete, isAcceptDelete, isRejectInsert })

  if (isAcceptInsert || isRejectDelete) {
    // Remove mark (accept insertion or reject deletion) - keep the text
    console.log('Removing mark, keeping text')
    currentTr.removeMark(markRange.from, markRange.to, changeMark.type)
  } else if (isAcceptDelete || isRejectInsert) {
    // Remove content (accept deletion or reject insertion) - delete the text
    console.log('Removing content, deleting text')
    currentTr.deleteRange(markRange.from, markRange.to)
  }

  console.log('Transaction steps:', currentTr.steps.length)

  if (currentTr.steps.length) {
    currentTr.setMeta('trackManualChanged', true)
    // Use the editor's dispatch method instead of directly updating state
    view.dispatch(currentTr)
    console.log('Transaction dispatched successfully')
  } else {
    console.log('No transaction steps to apply')
  }

  return true
}

export const ChangeTrackingExtension = Extension.create<{
  enabled: boolean
  onStatusChange?: Function
  dataOpUserId?: string
  dataOpUserNickname?: string
}>({
  name: 'changeTracking',

  addOptions() {
    return {
      enabled: true,
      onStatusChange: undefined,
      dataOpUserId: '',
      dataOpUserNickname: '',
    }
  },

  addExtensions() {
    return [InsertionMark, DeletionMark]
  },

  addCommands() {
    return {
      setTrackChangeStatus: (enabled: boolean) => (param: CommandProps) => {
        this.options.enabled = enabled
        if (this.options.onStatusChange) {
          this.options.onStatusChange(this.options.enabled)
        }
        return false
      },

      getTrackChangeStatus: () => () => {
        return this.options.enabled
      },

      toggleTrackChangeStatus: () => (param: CommandProps) => {
        this.options.enabled = !this.options.enabled
        if (this.options.onStatusChange) {
          this.options.onStatusChange(this.options.enabled)
        }
        return false
      },

      acceptChange: () => (param: CommandProps) => {
        return changeTrack('accept', param)
      },

      acceptAllChanges: () => (param: CommandProps) => {
        return changeTrack('accept-all', param)
      },

      rejectChange: () => (param: CommandProps) => {
        return changeTrack('reject', param)
      },

      acceptChangeAt: (pos: number) => (param: CommandProps) => {
        return changeTrackAt('accept', pos, param)
      },

      rejectChangeAt: (pos: number) => (param: CommandProps) => {
        return changeTrackAt('reject', pos, param)
      },

      rejectAllChanges: () => (param: CommandProps) => {
        return changeTrack('reject-all', param)
      },

      updateOpUserOption: (opUserId: string, opUserNickname: string) => () => {
        this.options.dataOpUserId = opUserId
        this.options.dataOpUserNickname = opUserNickname
        return false
      },

      hasPendingChanges: () => ({ editor }) => {
        // Check if there are any insertion or deletion marks in the document
        const markRanges = getMarksBetween(0, editor.state.doc.content.size, editor.state.doc)
        const changeMarks = markRanges.filter(markRange =>
          markRange.mark.type.name === MARK_DELETION || markRange.mark.type.name === MARK_INSERTION
        )
        return changeMarks.length > 0
      },
    }
  },

  addProseMirrorPlugins() {
    const options = this.options
    const editor = this.editor

    return [
      new Plugin({
        key: new PluginKey('changeTracking'),
        state: {
          init(): { enabled: boolean } {
            return {
              enabled: options.enabled,
            }
          },
          apply(tr: any, value: { enabled: boolean }) {
            return value
          },
        },
      }),

      // Hover plugin for change tracking
      new Plugin({
        key: new PluginKey('changeTrackingHover'),
        props: {
          handleDOMEvents: {
            mouseover: (view, event) => {
              const target = event.target as HTMLElement

              // Check if we're hovering over a change mark
              if (target.hasAttribute('data-change-type')) {
                const changeType = target.getAttribute('data-change-type')

                // Create hover buttons if they don't exist
                let hoverButtons = document.getElementById('change-tracking-hover-buttons')
                if (!hoverButtons) {
                  hoverButtons = document.createElement('div')
                  hoverButtons.id = 'change-tracking-hover-buttons'
                  hoverButtons.className = 'fixed z-50 flex gap-2 p-2 bg-white border border-gray-300 rounded-lg shadow-lg pointer-events-auto'
                  document.body.appendChild(hoverButtons)
                }

                // Position the buttons to follow cursor but avoid overlapping
                const updatePosition = (e: MouseEvent) => {
                  const buttonsRect = hoverButtons!.getBoundingClientRect()
                  const viewportWidth = window.innerWidth
                  const viewportHeight = window.innerHeight

                  let left = e.clientX + 10
                  let top = e.clientY - 10

                  // Adjust if buttons would go off screen
                  if (left + buttonsRect.width > viewportWidth) {
                    left = e.clientX - buttonsRect.width - 10
                  }
                  if (top + buttonsRect.height > viewportHeight) {
                    top = e.clientY - buttonsRect.height - 10
                  }
                  if (top < 0) {
                    top = e.clientY + 20
                  }

                  hoverButtons!.style.left = `${left}px`
                  hoverButtons!.style.top = `${top}px`
                }

                // Initial positioning
                updatePosition(event as MouseEvent)
                hoverButtons.style.display = 'flex'

                // Create accept button with text
                const acceptBtn = document.createElement('button')
                acceptBtn.innerHTML = `
                  <span class="flex items-center gap-1">
                    <span class="text-green-600">✓</span>
                    <span class="text-xs font-medium">Accept</span>
                  </span>
                `
                acceptBtn.className = 'px-3 py-1 text-xs bg-green-50 text-green-700 border border-green-200 rounded hover:bg-green-100 transition-colors duration-150 flex items-center gap-1'
                acceptBtn.title = `Accept ${changeType}`

                // Create reject button with text
                const rejectBtn = document.createElement('button')
                rejectBtn.innerHTML = `
                  <span class="flex items-center gap-1">
                    <span class="text-red-600">✗</span>
                    <span class="text-xs font-medium">Reject</span>
                  </span>
                `
                rejectBtn.className = 'px-3 py-1 text-xs bg-red-50 text-red-700 border border-red-200 rounded hover:bg-red-100 transition-colors duration-150 flex items-center gap-1'
                rejectBtn.title = `Reject ${changeType}`

                // Get position in document - find position within the marked content
                let pos: number
                try {
                  // Try to get position at the middle of the text content
                  const textContent = target.textContent || ''
                  if (textContent.length > 0) {
                    // Get position at the middle of the text to ensure we're inside the mark
                    const offset = Math.floor(textContent.length / 2)
                    pos = view.posAtDOM(target, offset)
                  } else {
                    pos = view.posAtDOM(target, 0)
                  }
                } catch (error) {
                  console.warn('posAtDOM failed, trying alternative approach', error)
                  // Fallback: try to find the first text node within the target
                  const walker = document.createTreeWalker(
                    target,
                    NodeFilter.SHOW_TEXT,
                    null
                  )
                  const firstTextNode = walker.nextNode()
                  if (firstTextNode) {
                    pos = view.posAtDOM(firstTextNode, 0)
                  } else {
                    pos = view.posAtDOM(target, 0)
                  }
                }

                console.log('Position calculated:', pos, 'for target:', target, 'with text:', target.textContent)

                // Add click handlers with debugging
                acceptBtn.onclick = (e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  console.log('Accept button clicked', { pos, changeType, target })

                  try {
                    // Use the editor's chain command for better transaction handling
                    const result = editor.chain().focus().acceptChangeAt(pos).run()
                    console.log('Accept command result:', result)
                    hoverButtons!.style.display = 'none'
                  } catch (error) {
                    console.error('Error accepting change:', error)
                    // Try alternative approach if chain fails
                    try {
                      const directResult = editor.commands.acceptChangeAt(pos)
                      console.log('Direct command result:', directResult)
                      hoverButtons!.style.display = 'none'
                    } catch (directError) {
                      console.error('Direct command also failed:', directError)
                    }
                  }
                }

                rejectBtn.onclick = (e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  console.log('Reject button clicked', { pos, changeType, target })

                  try {
                    // Use the editor's chain command for better transaction handling
                    const result = editor.chain().focus().rejectChangeAt(pos).run()
                    console.log('Reject command result:', result)
                    hoverButtons!.style.display = 'none'
                  } catch (error) {
                    console.error('Error rejecting change:', error)
                    // Try alternative approach if chain fails
                    try {
                      const directResult = editor.commands.rejectChangeAt(pos)
                      console.log('Direct command result:', directResult)
                      hoverButtons!.style.display = 'none'
                    } catch (directError) {
                      console.error('Direct command also failed:', directError)
                    }
                  }
                }

                // Clear and add buttons
                hoverButtons.innerHTML = ''
                hoverButtons.appendChild(acceptBtn)
                hoverButtons.appendChild(rejectBtn)

                // Add mousemove listener to follow cursor with lag (but not when over buttons)
                let moveTimeout: NodeJS.Timeout | null = null
                const mouseMoveHandler = (e: MouseEvent) => {
                  const buttonsRect = hoverButtons!.getBoundingClientRect()
                  const isOverButtons = e.clientX >= buttonsRect.left &&
                                       e.clientX <= buttonsRect.right &&
                                       e.clientY >= buttonsRect.top &&
                                       e.clientY <= buttonsRect.bottom

                  if (!isOverButtons) {
                    // Clear any existing timeout
                    if (moveTimeout) {
                      clearTimeout(moveTimeout)
                    }

                    // Add a delay before moving the buttons to allow cursor to reach them
                    moveTimeout = setTimeout(() => {
                      // Check again if cursor is still not over buttons
                      const currentButtonsRect = hoverButtons!.getBoundingClientRect()
                      const stillNotOverButtons = e.clientX < currentButtonsRect.left ||
                                                 e.clientX > currentButtonsRect.right ||
                                                 e.clientY < currentButtonsRect.top ||
                                                 e.clientY > currentButtonsRect.bottom

                      if (stillNotOverButtons) {
                        updatePosition(e)
                      }
                    }, 300) // 300ms delay to allow cursor to reach buttons
                  }
                }

                // Store the handler so we can remove it later
                target.addEventListener('mousemove', mouseMoveHandler)

                // Store handler reference for cleanup
                hoverButtons.setAttribute('data-mousemove-attached', 'true')
                ;(hoverButtons as any)._mouseMoveHandler = mouseMoveHandler
                ;(hoverButtons as any)._targetElement = target
                ;(hoverButtons as any)._moveTimeout = moveTimeout

                return false
              }

              return false
            },

            mouseout: (view, event) => {
              const target = event.target as HTMLElement
              const relatedTarget = event.relatedTarget as HTMLElement

              // Hide buttons if we're not moving to the buttons themselves
              if (target.hasAttribute('data-change-type') &&
                  (!relatedTarget || !relatedTarget.closest('#change-tracking-hover-buttons'))) {

                // Clean up mousemove listener and timeout
                const hoverButtons = document.getElementById('change-tracking-hover-buttons')
                if (hoverButtons && (hoverButtons as any)._mouseMoveHandler && (hoverButtons as any)._targetElement) {
                  ;(hoverButtons as any)._targetElement.removeEventListener('mousemove', (hoverButtons as any)._mouseMoveHandler)

                  // Clear any pending timeout
                  if ((hoverButtons as any)._moveTimeout) {
                    clearTimeout((hoverButtons as any)._moveTimeout)
                    delete (hoverButtons as any)._moveTimeout
                  }

                  hoverButtons.removeAttribute('data-mousemove-attached')
                  delete (hoverButtons as any)._mouseMoveHandler
                  delete (hoverButtons as any)._targetElement
                }

                setTimeout(() => {
                  const hoverButtons = document.getElementById('change-tracking-hover-buttons')
                  if (hoverButtons && !hoverButtons.matches(':hover')) {
                    hoverButtons.style.display = 'none'
                  }
                }, 100)
              }

              return false
            }
          }
        }
      })
    ]
  },
})
