import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON><PERSON> } from 'prosemirror-state'
import { v4 as uuidv4 } from 'uuid'

export interface UniqueIdOptions {
  attributeName: string
  types: string[]
  generateID?: () => string
}

export const UniqueIdExtension = Extension.create<UniqueIdOptions>({
  name: 'uniqueId',

  addOptions() {
    return {
      attributeName: 'id',
      types: ['heading', 'paragraph'],
      generateID: () => uuidv4(),
    }
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          [this.options.attributeName]: {
            default: null,
            parseHTML: element => element.getAttribute(this.options.attributeName),
            renderHTML: attributes => {
              if (!attributes[this.options.attributeName]) {
                return {}
              }
              return {
                [this.options.attributeName]: attributes[this.options.attributeName],
              }
            },
          },
        },
      },
    ]
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('uniqueId'),
        appendTransaction: (transactions, oldState, newState) => {
          // Only run on changes that actually modify the document
          const docChanged = transactions.some(transaction => transaction.docChanged)
          if (!docChanged) {
            return null
          }

          const tr = newState.tr
          let modified = false

          newState.doc.descendants((node, pos) => {
            // Check if this node type should have a unique ID
            if (this.options.types.includes(node.type.name)) {
              const id = node.attrs[this.options.attributeName]
              
              // If the node doesn't have an ID, add one
              if (!id) {
                tr.setNodeMarkup(pos, undefined, {
                  ...node.attrs,
                  [this.options.attributeName]: this.options.generateID?.() || uuidv4(),
                })
                modified = true
              }
            }
          })

          return modified ? tr : null
        },
      }),
    ]
  },
})

export default UniqueIdExtension
