import { Extension } from '@tiptap/core'
import { <PERSON><PERSON><PERSON>, <PERSON>lug<PERSON><PERSON><PERSON> } from 'prosemirror-state'
import { Decoration, DecorationSet } from 'prosemirror-view'
import { applyPatch, Operation, compare } from 'fast-json-patch'

// Types for change analysis
interface ChangeOperation {
  type: 'insert' | 'delete' | 'replace'
  path: string
  oldContent?: any
  newContent?: any
  position?: number
}

// Helper function to get value at JSON path
function getValueAtPath(obj: any, path: string): any {
  const parts = path.split('/').filter(p => p !== '')
  let current = obj
  for (const part of parts) {
    if (current === null || current === undefined) return undefined
    current = current[part]
  }
  return current
}

// Helper function to convert JSON path to document position
function getDocumentPosition(path: string, doc: any, editor: any): number {
  // This is a simplified implementation
  // In a real implementation, you'd need to traverse the document structure
  // and calculate the actual position based on the JSON path

  const parts = path.split('/').filter(p => p !== '')
  if (parts.length === 0) return 0

  // For now, return a basic position calculation
  // This would need to be more sophisticated for complex documents
  try {
    const pathIndex = parseInt(parts[parts.length - 1])
    if (!isNaN(pathIndex)) {
      return Math.min(pathIndex, editor.state.doc.content.size)
    }
  } catch (e) {
    // Fallback to end of document
  }

  return editor.state.doc.content.size
}

// Helper function to get content range for a given content at position
function getContentRange(content: any, position: number, editor: any): { from: number, to: number } {
  // Simplified implementation - in reality you'd need to calculate
  // the actual size of the content being deleted
  const contentSize = typeof content === 'string' ? content.length : 1
  return {
    from: position,
    to: Math.min(position + contentSize, editor.state.doc.content.size)
  }
}

// Analyze JSON patch operations to identify specific changes
function analyzeJSONPatch(patch: Operation[], currentDoc: any, newDoc: any, editor: any): ChangeOperation[] {
  const changes: ChangeOperation[] = []

  patch.forEach(op => {
    switch(op.op) {
      case 'add':
        changes.push({
          type: 'insert',
          path: op.path,
          newContent: op.value,
          position: getDocumentPosition(op.path, currentDoc, editor)
        })
        break

      case 'remove':
        changes.push({
          type: 'delete',
          path: op.path,
          oldContent: getValueAtPath(currentDoc, op.path),
          position: getDocumentPosition(op.path, currentDoc, editor)
        })
        break

      case 'replace':
        changes.push({
          type: 'replace',
          path: op.path,
          oldContent: getValueAtPath(currentDoc, op.path),
          newContent: op.value,
          position: getDocumentPosition(op.path, currentDoc, editor)
        })
        break
    }
  })

  return changes
}

// Simple diff function to find insertions and deletions
function simpleDiff(oldText: string, newText: string) {
  const changes: Array<{type: 'insert' | 'delete' | 'keep', text: string, oldPos?: number, newPos?: number}> = []

  // Very basic diff - in production you'd use a proper diff algorithm like Myers
  // For now, we'll handle simple cases

  if (oldText === newText) {
    return changes
  }

  // Simple case: text was added at the beginning
  if (newText.startsWith(oldText)) {
    const added = newText.slice(oldText.length)
    if (oldText) changes.push({type: 'keep', text: oldText})
    changes.push({type: 'insert', text: added})
    return changes
  }

  // Simple case: text was added at the end
  if (newText.endsWith(oldText)) {
    const added = newText.slice(0, newText.length - oldText.length)
    changes.push({type: 'insert', text: added})
    if (oldText) changes.push({type: 'keep', text: oldText})
    return changes
  }

  // Simple case: text was removed from the beginning
  if (oldText.startsWith(newText)) {
    const removed = oldText.slice(newText.length)
    changes.push({type: 'delete', text: removed})
    if (newText) changes.push({type: 'keep', text: newText})
    return changes
  }

  // Simple case: text was removed from the end
  if (oldText.endsWith(newText)) {
    const removed = oldText.slice(0, oldText.length - newText.length)
    if (newText) changes.push({type: 'keep', text: newText})
    changes.push({type: 'delete', text: removed})
    return changes
  }

  // Complex case: treat as complete replacement
  if (oldText) changes.push({type: 'delete', text: oldText})
  if (newText) changes.push({type: 'insert', text: newText})

  return changes
}

export interface AICommandOptions {
  onAICommand?: (command: string, selectedText: string, editor: any) => Promise<void>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    aiCommand: {
      runAICommand: (prompt: string) => ReturnType
      applyAIPatch: (patch: any[]) => ReturnType
      applyAIPatchWithTracking: (patch: any[]) => ReturnType
    }
  }
}

export const AICommandExtension = Extension.create<AICommandOptions>({
  name: 'aiCommand',

  addOptions() {
    return {
      onAICommand: undefined,
    }
  },

  addCommands() {
    return {
      runAICommand:
        (prompt: string) =>
        ({ editor, state, dispatch }) => {
          const { from, to } = state.selection
          const selectedText = state.doc.textBetween(from, to)

          if (this.options.onAICommand) {
            this.options.onAICommand(prompt, selectedText, editor)
              .catch(error => {
                console.error('AI command failed:', error)
              })
          }

          return true
        },

      applyAIPatch:
        (patch: Operation[]) =>
        ({ editor, state, dispatch, tr }) => {
          if (!patch || !Array.isArray(patch)) {
            return false
          }

          try {
            // Get current document as JSON
            const currentDoc = editor.getJSON()

            // Apply JSON Patch operations
            const patchedDoc = applyPatch(currentDoc, patch, false, false).newDocument

            // Replace the entire document content
            const transaction = tr.replaceWith(
              0,
              state.doc.content.size,
              state.schema.nodeFromJSON(patchedDoc)
            )

            if (dispatch) {
              dispatch(transaction)
            }

            return true
          } catch (error) {
            console.error('Failed to apply AI patch:', error)
            return false
          }
        },

      applyAIPatchWithTracking:
        (patch: Operation[]) =>
        ({ editor, state, dispatch, tr }) => {
          if (!patch || !Array.isArray(patch)) {
            return false
          }

          try {
            // Get current document as JSON
            const currentDoc = editor.getJSON()

            // Apply JSON Patch operations to get the new document
            const patchedDoc = applyPatch(currentDoc, patch, false, false).newDocument

            // Analyze the patch to identify specific changes
            const changes = analyzeJSONPatch(patch, currentDoc, patchedDoc, editor)

            if (changes.length === 0) {
              return false
            }

            // For now, use a simplified approach: compare text content
            // This is a fallback until we implement proper JSON path to position mapping
            const currentText = editor.getText()
            const newText = state.schema.nodeFromJSON(patchedDoc).textContent

            if (currentText === newText) {
              return false
            }

            // Use text-based diff for now
            const textChanges = simpleDiff(currentText, newText)

            if (textChanges.length === 0) {
              return false
            }

            // Apply changes with proper tracking
            let currentPos = 0

            // First, mark deletions in the original content
            textChanges.forEach(change => {
              if (change.type === 'delete') {
                const from = currentPos
                const to = currentPos + change.text.length

                // Mark the text for deletion (don't remove it yet)
                tr.addMark(from, to, state.schema.marks.deletion.create({
                  'data-op-user-id': 'ai',
                  'data-op-user-nickname': 'AI Assistant',
                  'data-op-date': new Date().toISOString(),
                }))

                currentPos = to
              } else if (change.type === 'keep') {
                currentPos += change.text.length
              }
            })

            // Then, insert new content with insertion marks
            currentPos = 0
            let insertOffset = 0

            textChanges.forEach(change => {
              if (change.type === 'insert') {
                const insertPos = currentPos + insertOffset

                // Insert the new text
                tr.insertText(change.text, insertPos)

                // Mark it as an insertion
                tr.addMark(
                  insertPos,
                  insertPos + change.text.length,
                  state.schema.marks.insertion.create({
                    'data-op-user-id': 'ai',
                    'data-op-user-nickname': 'AI Assistant',
                    'data-op-date': new Date().toISOString(),
                  })
                )

                insertOffset += change.text.length
              } else if (change.type === 'keep') {
                currentPos += change.text.length
              } else if (change.type === 'delete') {
                currentPos += change.text.length
              }
            })

            if (dispatch) {
              dispatch(tr)
            }

            return true
          } catch (error) {
            console.error('Failed to apply AI patch with tracking:', error)
            return false
          }
        },
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('aiCommand'),
        state: {
          init(): { isProcessing: boolean; activeCommand: string | null } {
            return {
              isProcessing: false,
              activeCommand: null,
            }
          },
          apply(tr: any, value: { isProcessing: boolean; activeCommand: string | null }) {
            // Handle AI command state updates
            const meta = tr.getMeta('aiCommand')
            if (meta) {
              return { ...value, ...meta }
            }
            return value
          },
        },
      }),
    ]
  },
})
