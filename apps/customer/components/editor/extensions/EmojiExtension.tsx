import { Extension } from '@tiptap/core'
import { ReactRenderer } from '@tiptap/react'
import Suggestion from '@tiptap/suggestion'
import { PluginKey } from 'prosemirror-state'
import tippy from 'tippy.js'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'

// Common emoji data
const EMOJI_DATA = [
  { emoji: '😀', name: 'grinning face', keywords: ['happy', 'smile', 'grin'] },
  { emoji: '😃', name: 'grinning face with big eyes', keywords: ['happy', 'smile', 'joy'] },
  { emoji: '😄', name: 'grinning face with smiling eyes', keywords: ['happy', 'smile', 'joy'] },
  { emoji: '😁', name: 'beaming face with smiling eyes', keywords: ['happy', 'smile', 'grin'] },
  { emoji: '😆', name: 'grinning squinting face', keywords: ['happy', 'laugh', 'smile'] },
  { emoji: '😅', name: 'grinning face with sweat', keywords: ['happy', 'sweat', 'relief'] },
  { emoji: '🤣', name: 'rolling on the floor laughing', keywords: ['laugh', 'funny', 'lol'] },
  { emoji: '😂', name: 'face with tears of joy', keywords: ['laugh', 'cry', 'funny'] },
  { emoji: '🙂', name: 'slightly smiling face', keywords: ['smile', 'happy'] },
  { emoji: '🙃', name: 'upside-down face', keywords: ['silly', 'sarcasm'] },
  { emoji: '😉', name: 'winking face', keywords: ['wink', 'flirt'] },
  { emoji: '😊', name: 'smiling face with smiling eyes', keywords: ['happy', 'smile', 'blush'] },
  { emoji: '😇', name: 'smiling face with halo', keywords: ['angel', 'innocent'] },
  { emoji: '🥰', name: 'smiling face with hearts', keywords: ['love', 'happy', 'hearts'] },
  { emoji: '😍', name: 'smiling face with heart-eyes', keywords: ['love', 'heart', 'eyes'] },
  { emoji: '🤩', name: 'star-struck', keywords: ['star', 'eyes', 'excited'] },
  { emoji: '😘', name: 'face blowing a kiss', keywords: ['kiss', 'love'] },
  { emoji: '😗', name: 'kissing face', keywords: ['kiss'] },
  { emoji: '☺️', name: 'smiling face', keywords: ['smile', 'happy'] },
  { emoji: '😚', name: 'kissing face with closed eyes', keywords: ['kiss', 'love'] },
  { emoji: '😙', name: 'kissing face with smiling eyes', keywords: ['kiss', 'smile'] },
  { emoji: '🥲', name: 'smiling face with tear', keywords: ['happy', 'cry', 'tear'] },
  { emoji: '😋', name: 'face savoring food', keywords: ['food', 'yum', 'tongue'] },
  { emoji: '😛', name: 'face with tongue', keywords: ['tongue', 'silly'] },
  { emoji: '😜', name: 'winking face with tongue', keywords: ['wink', 'tongue', 'silly'] },
  { emoji: '🤪', name: 'zany face', keywords: ['crazy', 'silly', 'goofy'] },
  { emoji: '😝', name: 'squinting face with tongue', keywords: ['tongue', 'silly'] },
  { emoji: '🤑', name: 'money-mouth face', keywords: ['money', 'rich'] },
  { emoji: '🤗', name: 'hugging face', keywords: ['hug', 'love'] },
  { emoji: '🤭', name: 'face with hand over mouth', keywords: ['quiet', 'secret'] },
  { emoji: '🤫', name: 'shushing face', keywords: ['quiet', 'shh'] },
  { emoji: '🤔', name: 'thinking face', keywords: ['think', 'hmm'] },
  { emoji: '🤐', name: 'zipper-mouth face', keywords: ['quiet', 'zip'] },
  { emoji: '🤨', name: 'face with raised eyebrow', keywords: ['suspicious', 'skeptical'] },
  { emoji: '😐', name: 'neutral face', keywords: ['neutral', 'meh'] },
  { emoji: '😑', name: 'expressionless face', keywords: ['blank', 'meh'] },
  { emoji: '😶', name: 'face without mouth', keywords: ['quiet', 'silent'] },
  { emoji: '😏', name: 'smirking face', keywords: ['smirk', 'smug'] },
  { emoji: '😒', name: 'unamused face', keywords: ['unamused', 'meh'] },
  { emoji: '🙄', name: 'face with rolling eyes', keywords: ['eye roll', 'annoyed'] },
  { emoji: '😬', name: 'grimacing face', keywords: ['grimace', 'awkward'] },
  { emoji: '🤥', name: 'lying face', keywords: ['lie', 'pinocchio'] },
  { emoji: '😔', name: 'pensive face', keywords: ['sad', 'pensive'] },
  { emoji: '😪', name: 'sleepy face', keywords: ['tired', 'sleepy'] },
  { emoji: '🤤', name: 'drooling face', keywords: ['drool', 'sleep'] },
  { emoji: '😴', name: 'sleeping face', keywords: ['sleep', 'zzz'] },
  { emoji: '😷', name: 'face with medical mask', keywords: ['sick', 'mask'] },
  { emoji: '🤒', name: 'face with thermometer', keywords: ['sick', 'fever'] },
  { emoji: '🤕', name: 'face with head-bandage', keywords: ['hurt', 'injured'] },
  { emoji: '🤢', name: 'nauseated face', keywords: ['sick', 'nausea'] },
  { emoji: '🤮', name: 'face vomiting', keywords: ['sick', 'vomit'] },
  { emoji: '🤧', name: 'sneezing face', keywords: ['sick', 'sneeze'] },
  { emoji: '🥵', name: 'hot face', keywords: ['hot', 'heat'] },
  { emoji: '🥶', name: 'cold face', keywords: ['cold', 'freeze'] },
  { emoji: '🥴', name: 'woozy face', keywords: ['dizzy', 'drunk'] },
  { emoji: '😵', name: 'dizzy face', keywords: ['dizzy', 'confused'] },
  { emoji: '🤯', name: 'exploding head', keywords: ['mind blown', 'shocked'] },
  { emoji: '🤠', name: 'cowboy hat face', keywords: ['cowboy', 'hat'] },
  { emoji: '🥳', name: 'partying face', keywords: ['party', 'celebration'] },
  { emoji: '🥸', name: 'disguised face', keywords: ['disguise', 'glasses'] },
  { emoji: '😎', name: 'smiling face with sunglasses', keywords: ['cool', 'sunglasses'] },
  { emoji: '🤓', name: 'nerd face', keywords: ['nerd', 'glasses'] },
  { emoji: '🧐', name: 'face with monocle', keywords: ['monocle', 'fancy'] },
  // Add more emojis as needed...
]

interface EmojiListProps {
  items: typeof EMOJI_DATA
  command: (item: any) => void
}

interface EmojiListRef {
  onKeyDown: (props: { event: KeyboardEvent }) => boolean
}

const EmojiList = forwardRef<EmojiListRef, EmojiListProps>((props, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const selectItem = (index: number) => {
    const item = props.items[index]
    if (item) {
      props.command(item)
    }
  }

  const upHandler = () => {
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length)
  }

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % props.items.length)
  }

  const enterHandler = () => {
    selectItem(selectedIndex)
  }

  useEffect(() => setSelectedIndex(0), [props.items])

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      if (event.key === 'ArrowUp') {
        upHandler()
        return true
      }

      if (event.key === 'ArrowDown') {
        downHandler()
        return true
      }

      if (event.key === 'Enter') {
        enterHandler()
        return true
      }

      return false
    },
  }))

  return (
    <div className="emoji-list bg-white border border-gray-200 rounded-lg shadow-lg p-2 max-h-64 overflow-y-auto">
      {props.items.length ? (
        props.items.map((item, index) => (
          <button
            className={`emoji-item flex items-center gap-2 w-full text-left px-3 py-2 rounded hover:bg-gray-100 ${
              index === selectedIndex ? 'bg-blue-100' : ''
            }`}
            key={index}
            onClick={() => selectItem(index)}
          >
            <span className="text-lg">{item.emoji}</span>
            <span className="text-sm text-gray-600">{item.name}</span>
          </button>
        ))
      ) : (
        <div className="p-3 text-sm text-gray-500">No emojis found</div>
      )}
    </div>
  )
})

EmojiList.displayName = 'EmojiList'

export const emojiSuggestion = {
  items: ({ query }: { query: string }) => {
    return EMOJI_DATA
      .filter(item => {
        const searchTerm = query.toLowerCase()
        return (
          item.name.toLowerCase().includes(searchTerm) ||
          item.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm))
        )
      })
      .slice(0, 10)
  },

  render: () => {
    let component: ReactRenderer
    let popup: any

    return {
      onStart: (props: any) => {
        component = new ReactRenderer(EmojiList, {
          props,
          editor: props.editor,
        })

        if (!props.clientRect) {
          return
        }

        popup = tippy('body', {
          getReferenceClientRect: props.clientRect,
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: 'manual',
          placement: 'bottom-start',
        })
      },

      onUpdate(props: any) {
        component.updateProps(props)

        if (!props.clientRect) {
          return
        }

        popup[0].setProps({
          getReferenceClientRect: props.clientRect,
        })
      },

      onKeyDown(props: any) {
        if (props.event.key === 'Escape') {
          popup[0].hide()
          return true
        }

        return (component.ref as EmojiListRef)?.onKeyDown(props)
      },

      onExit() {
        popup[0].destroy()
        component.destroy()
      },
    }
  },
}

export const EmojiExtension = Extension.create({
  name: 'emoji',

  addOptions() {
    return {
      suggestion: {
        char: ':',
        command: ({ editor, range, props }: any) => {
          editor
            .chain()
            .focus()
            .deleteRange(range)
            .insertContent(props.emoji)
            .run()
        },
      },
    }
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        ...this.options.suggestion,
        pluginKey: new PluginKey('emoji'),
      }),
    ]
  },
})

export default EmojiExtension
