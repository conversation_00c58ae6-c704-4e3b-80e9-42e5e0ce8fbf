import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON><PERSON> } from 'prosemirror-state'

export interface FileHandlerOptions {
  allowedMimeTypes: string[]
  maxFileSize: number // in bytes
  onDrop?: (files: File[], pos: number) => void
  onPaste?: (files: File[]) => void
  onUpload?: (file: File) => Promise<string> // Should return URL
}

export const FileHandlerExtension = Extension.create<FileHandlerOptions>({
  name: 'fileHandler',

  addOptions() {
    return {
      allowedMimeTypes: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
        'application/pdf',
        'text/plain',
        'text/markdown',
      ],
      maxFileSize: 10 * 1024 * 1024, // 10MB
      onDrop: undefined,
      onPaste: undefined,
      onUpload: undefined,
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('fileHandler'),
        props: {
          handleDOMEvents: {
            drop: (view, event) => {
              const hasFiles = event.dataTransfer?.files?.length

              if (!hasFiles) {
                return false
              }

              const files = Array.from(event.dataTransfer.files)
              const validFiles = validateFiles(files, this.options)

              if (validFiles.length === 0) {
                return false
              }

              event.preventDefault()

              const coordinates = view.posAtCoords({
                left: event.clientX,
                top: event.clientY,
              })

              if (coordinates && this.options.onDrop) {
                this.options.onDrop(validFiles, coordinates.pos)
              }

              return true
            },

            paste: (view, event) => {
              const hasFiles = event.clipboardData?.files?.length

              if (!hasFiles) {
                return false
              }

              const files = Array.from(event.clipboardData.files)
              const validFiles = validateFiles(files, this.options)

              if (validFiles.length === 0) {
                return false
              }

              event.preventDefault()

              if (this.options.onPaste) {
                this.options.onPaste(validFiles)
              }

              return true
            },
          },
        },
      }),
    ]
  },



})

// Helper function to validate files
function validateFiles(files: File[], options: FileHandlerOptions): File[] {
  return files.filter(file => {
    // Check file type
    if (!options.allowedMimeTypes.includes(file.type)) {
      console.warn(`File type ${file.type} not allowed`)
      return false
    }

    // Check file size
    if (file.size > options.maxFileSize) {
      console.warn(`File ${file.name} is too large (${file.size} bytes)`)
      return false
    }

    return true
  })
}

// Helper function to handle file uploads to Supabase
export async function uploadFileToSupabase(file: File): Promise<string> {
  const formData = new FormData()
  formData.append('file', file)

  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.error || 'Upload failed')
  }

  const { url } = await response.json()
  return url
}

// Default file handler configuration
export const createFileHandler = (uploadHandler?: (file: File) => Promise<string>) => {
  return FileHandlerExtension.configure({
    onDrop: async (files, pos) => {
      const editor = (window as any).currentEditor // You'll need to set this
      if (!editor || !uploadHandler) return

      for (const file of files) {
        try {
          const url = await uploadHandler(file)
          
          if (file.type.startsWith('image/')) {
            editor.chain().focus().setImage({ src: url }).run()
          } else {
            editor.chain().focus().insertContent(`<a href="${url}" target="_blank">${file.name}</a>`).run()
          }
        } catch (error) {
          console.error('Failed to upload file:', error)
        }
      }
    },

    onPaste: async (files) => {
      const editor = (window as any).currentEditor
      if (!editor || !uploadHandler) return

      for (const file of files) {
        try {
          const url = await uploadHandler(file)
          
          if (file.type.startsWith('image/')) {
            editor.chain().focus().setImage({ src: url }).run()
          } else {
            editor.chain().focus().insertContent(`<a href="${url}" target="_blank">${file.name}</a>`).run()
          }
        } catch (error) {
          console.error('Failed to upload file:', error)
        }
      }
    },

    onUpload: uploadHandler,
  })
}

export default FileHandlerExtension
