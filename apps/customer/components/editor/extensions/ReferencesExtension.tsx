import React from 'react'
import { Node } from '@tiptap/core'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON> } from '@tiptap/react'
import { CitationType, CompactCitation } from '@/components/citation'
import { useDocumentContext } from '../context/DocumentContext'

/* ------------------------------------------------------------------
 *  Types
 * ------------------------------------------------------------------*/
interface ReferencesComponentProps {
  node: any
  admin: boolean
  editor?: any
  updateAttributes?: (attrs: any) => void
}

/* ------------------------------------------------------------------
 *  References Component
 * ------------------------------------------------------------------*/
const ReferencesComponent = React.memo<ReferencesComponentProps>(({
  node,
  admin,
  editor,
  updateAttributes
}) => {
  const { state, registerCitations } = useDocumentContext()

  // Track if we've already loaded citations to prevent re-registration
  const hasLoadedCitations = React.useRef(false)

  // Load citations from node attributes on mount ONLY (no dependencies to prevent loops)
  React.useEffect(() => {
    if (hasLoadedCitations.current) return

    const storedCitations = node.attrs.citations
    if (storedCitations && state.citations.length === 0) { // Only load if no citations exist
      try {
        const parsedCitations = JSON.parse(storedCitations)
        if (Array.isArray(parsedCitations) && parsedCitations.length > 0) {
          console.log('References: Loading stored citations from node attributes:', parsedCitations.length)
          registerCitations(parsedCitations)
          hasLoadedCitations.current = true
        }
      } catch (error) {
        console.error('References: Failed to parse stored citations:', error)
      }
    }
  }, []) // Empty dependency array - only run on mount

  // Track last saved citations to prevent unnecessary updates
  const lastSavedCitations = React.useRef<string | null>(node.attrs.citations)

  // Update the ref when node attributes change externally (e.g., from document load)
  React.useEffect(() => {
    lastSavedCitations.current = node.attrs.citations
  }, [node.attrs.citations])

  // Update node attributes when citations change (with debouncing and loop prevention)
  React.useEffect(() => {
    if (!updateAttributes || state.citations.length === 0) return

    // Create a stable JSON string (sorted by doc_page_id to ensure consistent ordering)
    const sortedCitations = [...state.citations].sort((a, b) => a.doc_page_id - b.doc_page_id)
    const citationsJson = JSON.stringify(sortedCitations)

    // Only update if actually different from what we last saved
    if (citationsJson !== lastSavedCitations.current) {
      console.log('References: Updating node attributes with citations:', state.citations.length)
      lastSavedCitations.current = citationsJson
      updateAttributes({ citations: citationsJson })
    }
  }, [state.citations, updateAttributes]) // Removed node.attrs.citations to prevent loops

  // Get all citations from DocumentContext
  const citations = state.citations

  // Get unique citations, sorted by doc_page_id for now
  const sortedCitations = React.useMemo(() => {
    // Create a map to deduplicate by doc_page_id
    const uniqueCitations = new Map<number, CitationType>()

    citations.forEach((citation, index) => {
      if (citation.doc_page_id) {
        // Only keep the first occurrence of each citation
        if (!uniqueCitations.has(citation.doc_page_id)) {
          uniqueCitations.set(citation.doc_page_id, citation)
        }
      }
    })

    // Convert to array and sort by doc_page_id for consistent ordering
    return Array.from(uniqueCitations.values())
      .sort((a, b) => (a.doc_page_id || 0) - (b.doc_page_id || 0))
  }, [citations])

  // If no citations are available, show a placeholder
  if (sortedCitations.length === 0) {
    return (
      <NodeViewWrapper className="references-wrapper">
        <div className="text-muted-foreground text-sm italic p-4 border border-dashed border-slate-300 dark:border-slate-600 rounded">
          No citations found in this document
        </div>
      </NodeViewWrapper>
    )
  }

  return (
    <NodeViewWrapper className="references-wrapper">
      <section id="references" className="mb-12 print-section">
        <h2 className="text-2xl font-bold mb-6 pb-2 border-b border-slate-200 dark:border-slate-700">
          References
        </h2>
        <div className="space-y-3 mt-4">
          {sortedCitations.map((citation, index) => {
            const citationNumber = index + 1 // Simple sequential numbering for now
            return (
              <div
                key={citation.doc_page_id}
                id={`reference-${citationNumber}`}
                className="flex gap-2 text-sm border-b border-slate-100 dark:border-slate-800 pb-2"
              >
                <span className="font-mono text-slate-500 dark:text-slate-400 min-w-[2rem]">
                  [{citationNumber}]
                </span>
                <div className="flex-1">
                  <CompactCitation
                    data={{
                      doc_id: citation.doc_id,
                      doc_name: citation.doc_name,
                      title: citation.title,
                      public_url: citation.public_url,
                      url: citation.url,
                      pages: [citation.page],
                      year: citation.year,
                      score: citation.score,
                      credibility: citation.credibility,
                      authors: citation.authors || []
                    }}
                    admin={admin}
                  />
                </div>
              </div>
            )
          })}
        </div>
      </section>
    </NodeViewWrapper>
  )
})

ReferencesComponent.displayName = 'ReferencesComponent'

/* ------------------------------------------------------------------
 *  TipTap References Extension
 * ------------------------------------------------------------------*/
export const ReferencesExtension = Node.create({
  name: 'references',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      admin: false,
    }
  },

  addAttributes() {
    return {
      id: {
        default: 'references',
      },
      citations: {
        default: null, // JSON string of citations array
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="references"]',
        getAttrs: (element) => {
          if (typeof element === 'string') return false
          return {
            id: element.getAttribute('id') || 'references',
            citations: element.getAttribute('citations'),
          }
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', { 'data-type': 'references', ...HTMLAttributes }]
  },

  addNodeView() {
    return ReactNodeViewRenderer((props) => (
      <ReferencesComponent
        {...props}
        admin={this.options.admin}
        editor={props.editor}
        updateAttributes={props.updateAttributes}
      />
    ))
  },
})

/* ------------------------------------------------------------------
 *  Export types for use in other components
 * ------------------------------------------------------------------*/
export type { ReferencesComponentProps }
