import { Extension } from '@tiptap/core'
import { Plugin, Plug<PERSON><PERSON><PERSON> } from 'prosemirror-state'
import { Decoration, DecorationSet } from 'prosemirror-view'

export interface AccessibilityOptions {
  /**
   * Whether to add ARIA roles to content elements
   */
  addAriaRoles: boolean
  /**
   * Whether to add table accessibility attributes
   */
  enhanceTableAccessibility: boolean
  /**
   * Whether to add list accessibility attributes
   */
  enhanceListAccessibility: boolean
  /**
   * Whether to add heading accessibility attributes
   */
  enhanceHeadingAccessibility: boolean
}

/**
 * AccessibilityExtension adds proper ARIA attributes to content elements
 * for improved screen reader compatibility
 */
export const AccessibilityExtension = Extension.create<AccessibilityOptions>({
  name: 'accessibility',

  addOptions() {
    return {
      addAriaRoles: true,
      enhanceTableAccessibility: true,
      enhanceListAccessibility: true,
      enhanceHeadingAccessibility: true,
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('accessibility'),
        props: {
          decorations: (state: any) => {
            const decorations: Decoration[] = []
            const { doc } = state

            doc.descendants((node: any, pos: number) => {
              // Add ARIA roles to headings
              if (this.options.enhanceHeadingAccessibility && node.type.name === 'heading') {
                const level = node.attrs.level || 1
                decorations.push(
                  Decoration.node(pos, pos + node.nodeSize, {
                    role: 'heading',
                    'aria-level': level.toString(),
                  })
                )
              }

              // Add ARIA roles to tables
              if (this.options.enhanceTableAccessibility && node.type.name === 'table') {
                decorations.push(
                  Decoration.node(pos, pos + node.nodeSize, {
                    role: 'table',
                  })
                )
              }

              // Add ARIA roles to table rows
              if (this.options.enhanceTableAccessibility && node.type.name === 'tableRow') {
                decorations.push(
                  Decoration.node(pos, pos + node.nodeSize, {
                    role: 'row',
                  })
                )
              }

              // Add ARIA roles to table cells
              if (this.options.enhanceTableAccessibility && node.type.name === 'tableCell') {
                decorations.push(
                  Decoration.node(pos, pos + node.nodeSize, {
                    role: 'cell',
                  })
                )
              }

              // Add ARIA roles to table headers
              if (this.options.enhanceTableAccessibility && node.type.name === 'tableHeader') {
                decorations.push(
                  Decoration.node(pos, pos + node.nodeSize, {
                    role: 'columnheader',
                    scope: 'col',
                  })
                )
              }

              // Add ARIA roles to lists
              if (this.options.enhanceListAccessibility && (node.type.name === 'bulletList' || node.type.name === 'orderedList')) {
                decorations.push(
                  Decoration.node(pos, pos + node.nodeSize, {
                    role: 'list',
                  })
                )
              }

              // Add ARIA roles to list items
              if (this.options.enhanceListAccessibility && node.type.name === 'listItem') {
                decorations.push(
                  Decoration.node(pos, pos + node.nodeSize, {
                    role: 'listitem',
                  })
                )
              }

              return true
            })

            return DecorationSet.create(doc, decorations)
          },
        },
      }),
    ]
  },

  addGlobalAttributes() {
    return [
      {
        types: ['heading'],
        attributes: {
          role: {
            default: 'heading',
            renderHTML: (attributes) => {
              return {
                role: 'heading',
                'aria-level': attributes.level || 1,
              }
            },
          },
        },
      },
      {
        types: ['table'],
        attributes: {
          role: {
            default: 'table',
            renderHTML: () => {
              return {
                role: 'table',
              }
            },
          },
        },
      },
      {
        types: ['tableRow'],
        attributes: {
          role: {
            default: 'row',
            renderHTML: () => {
              return {
                role: 'row',
              }
            },
          },
        },
      },
      {
        types: ['tableCell'],
        attributes: {
          role: {
            default: 'cell',
            renderHTML: () => {
              return {
                role: 'cell',
              }
            },
          },
        },
      },
      {
        types: ['tableHeader'],
        attributes: {
          role: {
            default: 'columnheader',
            renderHTML: () => {
              return {
                role: 'columnheader',
                scope: 'col',
              }
            },
          },
        },
      },
      {
        types: ['bulletList', 'orderedList'],
        attributes: {
          role: {
            default: 'list',
            renderHTML: () => {
              return {
                role: 'list',
              }
            },
          },
        },
      },
      {
        types: ['listItem'],
        attributes: {
          role: {
            default: 'listitem',
            renderHTML: () => {
              return {
                role: 'listitem',
              }
            },
          },
        },
      },
    ]
  },
})
