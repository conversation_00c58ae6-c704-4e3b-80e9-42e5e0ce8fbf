'use client'

import React, { useEffect, useRef, useState } from 'react'
import { But<PERSON> } from '@ui/components/ui/button'
import { Input } from '@ui/components/ui/input'
import { ScrollArea } from '@ui/components/ui/scroll-area'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { AlertCircle, Bot, Check, Loader2, Send, Trash2, User, X } from 'lucide-react'
import { CustomAIProvider } from '../providers/CustomAIProvider'
import { Editor } from '@tiptap/react'
import { cn } from '@utils/lib/utils'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { serializeSchema } from '../utils/schemaSerializer'

interface AIChatPanelProps {
  editor: Editor | null
  aiProvider: CustomAIProvider
  className?: string
  onClose?: () => void
}



export function AIChatPanel({ editor, aiProvider, className, onClose }: AIChatPanelProps) {
  const [input, setInput] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [streamingContent, setStreamingContent] = useState('')
  const [hasPendingChanges, setHasPendingChanges] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const [state, setState] = useState(aiProvider.getState())

  // Function to check for pending changes
  const checkPendingChanges = () => {
    if (!editor) return false
    try {
      return editor.commands.hasPendingChanges()
    } catch (error) {
      console.error('Error checking pending changes:', error)
      return false
    }
  }

  // Update pending changes state when editor changes
  useEffect(() => {
    if (!editor) return

    const updatePendingChanges = () => {
      setHasPendingChanges(checkPendingChanges())
    }

    // Check immediately
    updatePendingChanges()

    // Listen for editor updates
    editor.on('update', updatePendingChanges)

    return () => {
      editor.off('update', updatePendingChanges)
    }
  }, [editor])

  useEffect(() => {
    const handleStateChange = (newState: any) => {
      setState(newState)
    }

    const handleChatChunk = (chunk: string) => {
      // The chunk is just the delta, but we don't need to accumulate here
      // since the streamChatMessage generator yields the full accumulated content
      // This event is mainly for other components that might want to react to chunks
    }

    const handleMessageReceived = () => {
      setIsStreaming(false)
      setStreamingContent('')
    }

    const handleDocumentEdited = (editData: { patch: any[], description: string }) => {
      if (!editor) return

      try {
        // Enable change tracking for AI edits
        editor.commands.setTrackChangeStatus(true)

        // Apply the edit with change tracking to show inline changes
        editor.commands.applyAIPatchWithTracking(editData.patch)

        // Update pending changes state after applying edits
        setHasPendingChanges(checkPendingChanges())

        // The changes are now tracked inline in the document with visual indicators
      } catch (error) {
        console.error('Failed to apply AI edit:', error)
      }
    }

    const handleError = (error: Error) => {
      setIsStreaming(false)
      setStreamingContent('')
      console.error('AI Chat Error:', error)
    }

    aiProvider.on('stateChange', handleStateChange)
    aiProvider.on('chatChunk', handleChatChunk)
    aiProvider.on('messageReceived', handleMessageReceived)
    aiProvider.on('documentEdited', handleDocumentEdited)
    aiProvider.on('error', handleError)

    return () => {
      aiProvider.off('stateChange', handleStateChange)
      aiProvider.off('chatChunk', handleChatChunk)
      aiProvider.off('messageReceived', handleMessageReceived)
      aiProvider.off('documentEdited', handleDocumentEdited)
      aiProvider.off('error', handleError)
    }
  }, [aiProvider])

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [state.messages, streamingContent])



  const handleSendMessage = async () => {
    if (!input.trim() || state.isGenerating || !editor) return

    const message = input.trim()
    setInput('')
    setIsStreaming(true)
    setStreamingContent('')

    try {
      const documentContent = JSON.stringify(editor.getJSON())
      const documentId = 'current-document' // You might want to pass this as a prop

      // Extract and serialize the TipTap schema from the editor
      const serializedSchema = serializeSchema(editor.schema)

      // Log schema information for verification (temporary)
      console.log('AIChatPanel - Schema serialized:', {
        hasSchema: !!serializedSchema,
        schemaNodes: serializedSchema?.nodes ? Object.keys(serializedSchema.nodes) : 'No nodes',
        schemaMarks: serializedSchema?.marks ? Object.keys(serializedSchema.marks) : 'No marks'
      })

      // Always use the unified streaming chat endpoint
      // The AI will decide whether to return a chat response or document edit
      const stream = aiProvider.streamChatMessage(message, documentContent, documentId, serializedSchema)

      for await (const accumulatedContent of stream) {
        // Update the streaming content with the full accumulated content
        setStreamingContent(accumulatedContent)
        // Document edits will be handled by the 'documentEdited' event
      }
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  const handleAcceptAllChanges = () => {
    if (!editor) return
    editor.commands.acceptAllChanges()
    // Update pending changes state after accepting all changes
    setHasPendingChanges(checkPendingChanges())
  }

  const handleRejectAllChanges = () => {
    if (!editor) return
    editor.commands.rejectAllChanges()
    // Update pending changes state after rejecting all changes
    setHasPendingChanges(checkPendingChanges())
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleClearChat = () => {
    aiProvider.clearMessages()
    setStreamingContent('')
    setIsStreaming(false)
  }

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <Card className={cn('flex flex-col h-full overflow-hidden', className)} data-testid="ai-chat-panel">
      <CardHeader className="flex-shrink-0 pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI Assistant
          </CardTitle>
          <div className="flex items-center gap-2">
            {state.messages.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearChat}
                disabled={state.isGenerating}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose} aria-label="Close" data-testid="ai-chat-close-button">
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        {state.error && (
          <div className="flex items-center gap-2 text-sm text-red-600 bg-red-50 p-2 rounded">
            <AlertCircle className="h-4 w-4" />
            {state.error}
          </div>
        )}
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0 overflow-hidden">
        <ScrollArea className="flex-1 px-4 overflow-y-auto" ref={scrollAreaRef}>
          <div className="space-y-4 pb-4">
            {state.messages.length === 0 && !isStreaming && (
              <div className="text-center text-gray-500 py-8">
                <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-sm">
                  Start a conversation with the AI assistant to get help with your document.
                </p>
                <p className="text-xs mt-2 opacity-75">
                  I can help you write, edit, format, and improve your content.
                </p>
              </div>
            )}

            {state.messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  'flex gap-3 p-3 rounded-lg',
                  message.role === 'user'
                    ? 'bg-blue-50 ml-8'
                    : 'bg-gray-50 mr-8'
                )}
              >
                <div className="flex-shrink-0">
                  {message.role === 'user' ? (
                    <User className="h-5 w-5 text-blue-600" />
                  ) : (
                    <Bot className="h-5 w-5 text-gray-600" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant={message.role === 'user' ? 'default' : 'secondary'}>
                      {message.role === 'user' ? 'You' : 'AI Assistant'}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {formatTimestamp(message.timestamp)}
                    </span>
                  </div>
                  <div className="text-sm break-words">
                    {message.role === 'assistant' ? (
                      <EkoMarkdown
                        citations={null}
                        admin={false}
                        className="prose-sm max-w-none"
                        skipCitations={true}
                      >
                        {message.content}
                      </EkoMarkdown>
                    ) : (
                      <div className="whitespace-pre-wrap">{message.content}</div>
                    )}
                  </div>
                  {message.toolCalls && message.toolCalls.length > 0 && (
                    <div className="mt-2 text-xs text-gray-600">
                      <Badge variant="outline" className="text-xs">
                        Used tools: {message.toolCalls.map(tc => tc.function?.name).join(', ')}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {isStreaming && (
              <div className="flex gap-3 p-3 rounded-lg bg-gray-50 mr-8">
                <div className="flex-shrink-0">
                  <Bot className="h-5 w-5 text-gray-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant="secondary">AI Assistant</Badge>
                    <Loader2 className="h-3 w-3 animate-spin" data-testid="ai-thinking" />
                  </div>
                  <div className="text-sm break-words">
                    <EkoMarkdown
                      citations={null}
                      admin={false}
                      className="prose-sm max-w-none"
                      skipCitations={true}
                    >
                      {streamingContent}
                    </EkoMarkdown>
                    <span className="inline-block w-2 h-4 bg-gray-400 animate-pulse ml-1" />
                  </div>
                </div>
              </div>
            )}

            {/* Change Tracking Controls - Only show when there are pending changes */}
            {hasPendingChanges && (
              <div className="mt-6 border-t pt-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-gray-700">
                    Document Changes
                  </h4>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleAcceptAllChanges}
                      className="h-7 px-3 text-green-600 border-green-200 hover:bg-green-50"
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Accept All
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleRejectAllChanges}
                      className="h-7 px-3 text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <X className="h-3 w-3 mr-1" />
                      Reject All
                    </Button>
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  AI changes are shown inline in the document. Click individual changes to accept/reject them, or use the buttons above for all changes.
                </p>
              </div>
            )}
          </div>
        </ScrollArea>

        <div className="flex-shrink-0 p-4 border-t">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask the AI assistant to help with your document..."
              disabled={state.isGenerating}
              className="flex-1"
              data-testid="ai-chat-input"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!input.trim() || state.isGenerating}
              size="sm"
              data-testid="ai-chat-send-button"
            >
              {state.isGenerating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          <div className="text-xs text-gray-500 mt-2">
            Press Enter to send, Shift+Enter for new line
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
