'use client'

import React, { use<PERSON><PERSON>back, useEffect, useState, useRef } from 'react'
import { Button } from '@ui/components/ui/button'
import { Input } from '@ui/components/ui/input'
import { ScrollArea } from '@ui/components/ui/scroll-area'
import { Badge } from '@ui/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/ui/avatar'
import { Separator } from '@ui/components/ui/separator'
import { Check, Copy, Crown, Edit3, ExternalLink, Eye, Globe, Loader2, Share2, Trash2, UserPlus, Users, X } from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import { createClient } from '@/app/supabase/client'
import { useToast } from '@ui/hooks/use-toast'

interface SharedUser {
  id: string
  email: string
  name: string
  avatar?: string
  permission: string | null
  grantedAt: Date
  grantedBy: string | null
}

interface SharePanelProps {
  documentId: string
  currentUser?: {
    id: string
    name: string
    email?: string
    avatar?: string
  }
  isOwner?: boolean
  onClose?: () => void
  className?: string
}

export function SharePanel({
  documentId,
  currentUser,
  isOwner = false,
  onClose,
  className
}: SharePanelProps) {
  const [sharedUsers, setSharedUsers] = useState<SharedUser[]>([])
  const [newUserEmail, setNewUserEmail] = useState('')
  const [newUserPermission, setNewUserPermission] = useState<'read' | 'write'>('read')
  const [loading, setLoading] = useState(false)
  const [sharing, setSharing] = useState(false)
  const [shareLink, setShareLink] = useState('')
  const [linkCopied, setLinkCopied] = useState(false)
  const [isPublic, setIsPublic] = useState(false)

  const { toast } = useToast()
  const supabase = createClient()
  const shareLinkInputRef = useRef<HTMLInputElement>(null)

  // Load shared users
  const loadSharedUsers = useCallback(async () => {
    if (!documentId) return

    setLoading(true)
    try {
      // Get document metadata for public status
      const { data: document, error: docError } = await supabase
        .from('collaborative_documents')
        .select('metadata')
        .eq('id', documentId)
        .single()

      if (docError) {
        throw docError
      }

      setIsPublic((document?.metadata as any)?.isPublic || false)

      // Get document permissions with user information
      const { data: permissions, error } = await supabase
        .from('document_permissions')
        .select(`
          id,
          user_id,
          permission_type,
          granted_at,
          granted_by,
          user:user_id (
            id,
            email,
            name,
            full_name,
            avatar_url
          )
        `)
        .eq('document_id', documentId)

      if (error) {
        throw error
      }

      // Transform permissions to match expected format
      const transformedPermissions = permissions?.map(permission => ({
        id: permission.user_id || 'unknown',
        email: (permission.user as any)?.email || '',
        name: (permission.user as any)?.name ||
               (permission.user as any)?.full_name ||
               (permission.user as any)?.email?.split('@')[0] || 'Unknown User',
        avatar: (permission.user as any)?.avatar_url,
        permission: permission.permission_type,
        grantedAt: new Date(permission.granted_at || new Date()),
        grantedBy: permission.granted_by
      })) || []

      setSharedUsers(transformedPermissions)
    } catch (error) {
      console.error('Error loading shared users:', error)
      toast({
        title: 'Error',
        description: 'Failed to load sharing information',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [documentId, toast, supabase])

  // Load shared users and check public status on mount
  useEffect(() => {
    loadSharedUsers()

    // Check if document is public and load public status
    const checkPublicStatus = async () => {
      try {
        const { data: document, error } = await supabase
          .from('collaborative_documents')
          .select('metadata, is_public')
          .eq('id', documentId)
          .single()

        if (!error && document) {
          const metadata = (document as any).metadata as any
          // Check both is_public column and metadata.isPublic for backwards compatibility
          const isDocumentPublic = (document as any).is_public === true || metadata?.isPublic === true
          setIsPublic(isDocumentPublic)
        }
      } catch (error) {
        console.error('Error checking public status:', error)
      }
    }

    checkPublicStatus()
  }, [loadSharedUsers, documentId, supabase])

  // Update share link based on public status
  useEffect(() => {
    const baseUrl = window.location.origin
    if (isPublic) {
      setShareLink(`${baseUrl}/share/public/documents/${documentId}`)
    } else {
      setShareLink(`${baseUrl}/customer/documents/${documentId}`)
    }
  }, [documentId, isPublic])

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getPermissionIcon = (permission: string | null) => {
    switch (permission) {
      case 'admin':
        return <Crown className="w-3 h-3 text-yellow-500" />
      case 'write':
        return <Edit3 className="w-3 h-3 text-blue-500" />
      case 'read':
        return <Eye className="w-3 h-3 text-gray-500" />
      default:
        return <Eye className="w-3 h-3 text-gray-500" />
    }
  }

  const getPermissionLabel = (permission: string | null) => {
    switch (permission) {
      case 'admin':
        return 'Admin'
      case 'write':
        return 'Can edit'
      case 'read':
        return 'Can view'
      default:
        return 'Can view'
    }
  }

  const handleShareWithUser = async () => {
    if (!newUserEmail.trim() || sharing) return

    setSharing(true)
    try {
      // Find the user by email using auth.users view (if accessible) or a user lookup
      // Note: In production, you might need a server function to look up users
      // For now, we'll assume the user exists and handle the error if not

      const { data: { user: currentUser } } = await supabase.auth.getUser()

      if (!currentUser) {
        throw new Error('Not authenticated')
      }

      // Check if permission already exists and update or insert
      const { data: existingPermission } = await supabase
        .from('document_permissions')
        .select('id')
        .eq('document_id', documentId)
        .eq('user_id', newUserEmail.trim()) // This would need to be user ID in production
        .single()

      if (existingPermission) {
        // Update existing permission
        const { error: updateError } = await supabase
          .from('document_permissions')
          .update({
            permission_type: newUserPermission,
            granted_by: currentUser.id,
            granted_at: new Date().toISOString()
          })
          .eq('document_id', documentId)
          .eq('user_id', newUserEmail.trim())

        if (updateError) {
          throw updateError
        }
      } else {
        // Create new permission
        const { error: insertError } = await supabase
          .from('document_permissions')
          .insert({
            document_id: documentId,
            user_id: newUserEmail.trim(), // This would need to be user ID in production
            permission_type: newUserPermission,
            granted_by: currentUser.id
          })

        if (insertError) {
          throw insertError
        }
      }

      setNewUserEmail('')
      setNewUserPermission('read')
      loadSharedUsers()

      toast({
        title: 'Success',
        description: `Document shared with ${newUserEmail}`
      })
    } catch (error) {
      console.error('Error sharing document:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to share document',
        variant: 'destructive'
      })
    } finally {
      setSharing(false)
    }
  }

  const handleUpdatePermission = async (userId: string, permission: string) => {
    try {
      const { data: { user: currentUser } } = await supabase.auth.getUser()

      const { error } = await supabase
        .from('document_permissions')
        .update({
          permission_type: permission,
          granted_by: currentUser?.id,
          granted_at: new Date().toISOString()
        })
        .eq('document_id', documentId)
        .eq('user_id', userId)

      if (error) {
        throw error
      }

      loadSharedUsers()
      toast({
        title: 'Success',
        description: 'Permission updated successfully'
      })
    } catch (error) {
      console.error('Error updating permission:', error)
      toast({
        title: 'Error',
        description: 'Failed to update permission',
        variant: 'destructive'
      })
    }
  }

  const handleRemoveUser = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('document_permissions')
        .delete()
        .eq('document_id', documentId)
        .eq('user_id', userId)

      if (error) {
        throw error
      }

      loadSharedUsers()
      toast({
        title: 'Success',
        description: 'User removed from document'
      })
    } catch (error) {
      console.error('Error removing user:', error)
      toast({
        title: 'Error',
        description: 'Failed to remove user',
        variant: 'destructive'
      })
    }
  }

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareLink)
      setLinkCopied(true)
      setTimeout(() => setLinkCopied(false), 2000)

      toast({
        title: 'Success',
        description: 'Share link copied to clipboard'
      })
    } catch (error) {
      console.error('Error copying link:', error)
      toast({
        title: 'Error',
        description: 'Failed to copy link',
        variant: 'destructive'
      })
    }
  }

  const handleTogglePublic = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()

      // Get current metadata
      const { data: document, error: fetchError } = await supabase
        .from('collaborative_documents')
        .select('metadata')
        .eq('id', documentId)
        .single()

      if (fetchError) {
        throw fetchError
      }

      // Update both the is_public column and metadata for backwards compatibility
      const updatedMetadata = {
        ...(typeof document.metadata === 'object' && document.metadata !== null ? document.metadata : {}),
        isPublic: !isPublic
      }

      const { error } = await supabase
        .from('collaborative_documents')
        .update({
          is_public: !isPublic,
          metadata: updatedMetadata,
          updated_at: new Date().toISOString(),
          updated_by: user?.id
        })
        .eq('id', documentId)

      if (error) {
        throw error
      }

      setIsPublic(!isPublic)
      toast({
        title: 'Success',
        description: isPublic ? 'Document is now private' : 'Document is now public'
      })
    } catch (error) {
      console.error('Error updating public access:', error)
      toast({
        title: 'Error',
        description: 'Failed to update public access',
        variant: 'destructive'
      })
    }
  }

  return (
    <div
      className={cn('w-full bg-background border-l flex flex-col h-full', className)}
      data-testid="share-panel"
    >
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Share2 className="w-4 h-4" />
            <h3 className="font-semibold">Share Document</h3>
          </div>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      <ScrollArea className="flex-1 p-4">
        <div className="space-y-6">
          {/* Share Link */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Share Link</h4>
            <div className="flex gap-2">
              <Input
                ref={shareLinkInputRef}
                value={shareLink}
                readOnly
                className="flex-1 text-xs cursor-pointer"
                onClick={() => {
                  // Use React ref to select all text in this specific input
                  if (shareLinkInputRef.current) {
                    shareLinkInputRef.current.select()
                  }
                }}
              />
              <Button
                size="sm"
                variant="outline"
                onClick={() => window.open(shareLink, '_blank', 'noopener,noreferrer')}
                className="shrink-0"
                title="Open share link in new tab"
                data-testid="open-share-link-button"
              >
                <ExternalLink className="w-3 h-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCopyLink}
                className="shrink-0"
                title="Copy link to clipboard"
                data-testid="copy-share-link-button"
              >
                {linkCopied ? (
                  <Check className="w-3 h-3" />
                ) : (
                  <Copy className="w-3 h-3" />
                )}
              </Button>
            </div>

            {/* Public Access Toggle */}
            {isOwner && (
              <div className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  <span className="text-sm">Public access</span>
                </div>
                <Button
                  size="sm"
                  variant={isPublic ? 'default' : 'outline'}
                  onClick={handleTogglePublic}
                  data-testid="toggle-public-access-button"
                >
                  {isPublic ? 'Public' : 'Private'}
                </Button>
              </div>
            )}
          </div>

          <Separator />

          {/* Add User */}
          {isOwner && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Invite People</h4>
              <div className="space-y-2">
                <Input
                  placeholder="Enter email address"
                  value={newUserEmail}
                  onChange={(e) => setNewUserEmail(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleShareWithUser()
                    }
                  }}
                  data-testid="share-email-input"
                />
                <div className="flex gap-2">
                  <Select
                    value={newUserPermission}
                    onValueChange={(value: 'read' | 'write') => setNewUserPermission(value)}
                  >
                    <SelectTrigger className="flex-1" data-testid="permission-select">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="read">Can view</SelectItem>
                      <SelectItem value="write">Can edit</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={handleShareWithUser}
                    disabled={!newUserEmail.trim() || sharing}
                    size="sm"
                    data-testid="share-with-user-button"
                  >
                    {sharing ? (
                      <Loader2 className="w-3 h-3 animate-spin" />
                    ) : (
                      <UserPlus className="w-3 h-3" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}

          <Separator />

          {/* Shared Users */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <h4 className="text-sm font-medium">People with access</h4>
              <Badge variant="secondary" className="text-xs">
                {sharedUsers.length + 1}
              </Badge>
            </div>

            <div className="space-y-2">
              {loading ? (
                <div className="text-center py-4">
                  <Loader2 className="w-6 h-6 mx-auto animate-spin" />
                </div>
              ) : (
                <>
                  {/* Current User (Owner) */}
                  <div className="flex items-center gap-3 p-2 rounded border">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={currentUser?.avatar} alt={currentUser?.name} />
                      <AvatarFallback className="text-xs">
                        {getInitials(currentUser?.name || 'You')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {currentUser?.name || 'You'} (Owner)
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        {currentUser?.email}
                      </p>
                    </div>
                    <div className="flex items-center gap-1">
                      <Crown className="w-3 h-3 text-yellow-500" />
                      <span className="text-xs">Owner</span>
                    </div>
                  </div>

                  {/* Shared Users */}
                  {sharedUsers.map((user) => (
                    <div key={user.id} className="flex items-center gap-3 p-2 rounded border">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={user.avatar} alt={user.name} />
                        <AvatarFallback className="text-xs">
                          {getInitials(user.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{user.name}</p>
                        <p className="text-xs text-muted-foreground truncate">{user.email}</p>
                      </div>
                      <div className="flex items-center gap-1">
                        {getPermissionIcon(user.permission)}
                        {isOwner ? (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-xs h-auto p-1">
                                {getPermissionLabel(user.permission)}
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleUpdatePermission(user.id, 'read')}
                              >
                                <Eye className="w-3 h-3 mr-2" />
                                Can view
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleUpdatePermission(user.id, 'write')}
                              >
                                <Edit3 className="w-3 h-3 mr-2" />
                                Can edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleRemoveUser(user.id)}
                                className="text-destructive"
                              >
                                <Trash2 className="w-3 h-3 mr-2" />
                                Remove
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        ) : (
                          <span className="text-xs">{getPermissionLabel(user.permission)}</span>
                        )}
                      </div>
                    </div>
                  ))}
                </>
              )}
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
