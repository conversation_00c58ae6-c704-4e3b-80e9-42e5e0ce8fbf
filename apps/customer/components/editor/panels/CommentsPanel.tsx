'use client'

import React, { use<PERSON>allback, useEffect, useState } from 'react'
import { Editor } from '@tiptap/react'
import { Button } from '@ui/components/ui/button'
import { Textarea } from '@ui/components/ui/textarea'
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/ui/avatar'
import { Badge } from '@ui/components/ui/badge'
import { ScrollArea } from '@ui/components/ui/scroll-area'
import { Check, Edit3, Loader2, MessageSquare, MoreHorizontal, Plus, Reply, Trash2, X } from 'lucide-react'
import { cn } from '@utils/lib/utils'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import { createClient } from '@/app/supabase/client'
import { useToast } from '@ui/hooks/use-toast'

interface Comment {
  id: string
  content: string
  author: {
    id: string
    name: string
    avatar?: string
  }
  createdAt: Date
  updatedAt?: Date
  replies?: Comment[]
  resolved?: boolean | null
  resolvedBy?: string | null
  resolvedAt?: Date
  position?: {
    from: number
    to: number
  }
  parentCommentId?: string | null
}

interface CommentsPanelProps {
  editor: Editor
  documentId: string
  currentUser?: {
    id: string
    name: string
    avatar?: string
    email?: string
  }
  className?: string
}

export function CommentsPanel({
  editor,
  documentId,
  currentUser,
  className
}: CommentsPanelProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')
  const [editingComment, setEditingComment] = useState<string | null>(null)
  const [editContent, setEditContent] = useState('')
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  const { toast } = useToast()
  const supabase = createClient()

  // Load comments from Supabase
  const loadComments = useCallback(async () => {
    if (!documentId) return

    setLoading(true)
    try {
      const { data: comments, error } = await supabase
        .from('document_comments')
        .select(`
          id,
          content,
          position_from,
          position_to,
          parent_comment_id,
          resolved,
          resolved_by,
          resolved_at,
          created_at,
          updated_at,
          user_id,
          profiles (
            id,
            full_name,
            username,
            avatar_url
          )
        `)
        .eq('document_id', documentId)
        .order('created_at', { ascending: true })

      if (error) {
        throw error
      }

      // Transform comments to match the expected format
      const transformedComments = comments?.map(comment => {
        // Handle both array and object cases for profiles
        const profile = Array.isArray(comment.profiles) ? comment.profiles[0] : comment.profiles
        return {
          id: comment.id,
          content: comment.content,
          author: {
            id: comment.user_id || 'unknown',
            name: profile?.full_name || profile?.username || 'Unknown User',
            email: profile?.username, // Using username as email fallback
            avatar: profile?.avatar_url
          },
          createdAt: new Date(comment.created_at || new Date()),
          updatedAt: comment.updated_at ? new Date(comment.updated_at) : undefined,
          resolved: comment.resolved,
          resolvedBy: comment.resolved_by,
          resolvedAt: comment.resolved_at ? new Date(comment.resolved_at) : undefined,
          position: comment.position_from !== null && comment.position_to !== null ? {
            from: comment.position_from,
            to: comment.position_to
          } : undefined,
          parentCommentId: comment.parent_comment_id
        }
      }) || []

      // Organize comments into threads (parent comments with their replies)
      const commentThreads = transformedComments
        .filter(comment => !comment.parentCommentId)
        .map(parentComment => ({
          ...parentComment,
          replies: transformedComments.filter(comment => comment.parentCommentId === parentComment.id)
        }))

      setComments(commentThreads)
    } catch (error) {
      console.error('Error loading comments:', error)
      toast({
        title: 'Error',
        description: 'Failed to load comments',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [documentId, toast, supabase])

  // Load comments on mount and set up real-time subscription
  useEffect(() => {
    loadComments()

    // Set up real-time subscription for comments
    const channel = supabase
      .channel(`document_comments:${documentId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'document_comments',
          filter: `document_id=eq.${documentId}`
        },
        () => {
          // Reload comments when changes occur
          loadComments()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [documentId, loadComments, supabase])

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleAddComment = async () => {
    if (!newComment.trim() || submitting) return

    setSubmitting(true)
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        throw new Error('User not authenticated')
      }

      // Get current selection for positioning
      const { from, to } = editor.state.selection
      const position = from !== to ? { from, to } : undefined

      const { error } = await supabase
        .from('document_comments')
        .insert({
          document_id: documentId,
          user_id: user.id,
          content: newComment.trim(),
          position_from: position?.from || null,
          position_to: position?.to || null,
          parent_comment_id: null
        })

      if (error) {
        throw error
      }

      setNewComment('')
      toast({
        title: 'Success',
        description: 'Comment added successfully'
      })
    } catch (error) {
      console.error('Error adding comment:', error)
      toast({
        title: 'Error',
        description: 'Failed to add comment',
        variant: 'destructive'
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleReply = async (commentId: string) => {
    if (!replyContent.trim() || submitting) return

    setSubmitting(true)
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        throw new Error('User not authenticated')
      }

      const { error } = await supabase
        .from('document_comments')
        .insert({
          document_id: documentId,
          user_id: user.id,
          content: replyContent.trim(),
          parent_comment_id: commentId
        })

      if (error) {
        throw error
      }

      setReplyingTo(null)
      setReplyContent('')
      toast({
        title: 'Success',
        description: 'Reply added successfully'
      })
    } catch (error) {
      console.error('Error adding reply:', error)
      toast({
        title: 'Error',
        description: 'Failed to add reply',
        variant: 'destructive'
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleEdit = async (commentId: string) => {
    if (!editContent.trim() || submitting) return

    setSubmitting(true)
    try {
      const { error } = await supabase
        .from('document_comments')
        .update({
          content: editContent.trim(),
          updated_at: new Date().toISOString()
        })
        .eq('id', commentId)

      if (error) {
        throw error
      }

      setEditingComment(null)
      setEditContent('')
      toast({
        title: 'Success',
        description: 'Comment updated successfully'
      })
    } catch (error) {
      console.error('Error editing comment:', error)
      toast({
        title: 'Error',
        description: 'Failed to edit comment',
        variant: 'destructive'
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleDelete = async (commentId: string) => {
    if (submitting) return

    setSubmitting(true)
    try {
      const { error } = await supabase
        .from('document_comments')
        .delete()
        .eq('id', commentId)

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Comment deleted successfully'
      })
    } catch (error) {
      console.error('Error deleting comment:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete comment',
        variant: 'destructive'
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleResolve = async (commentId: string) => {
    if (submitting) return

    setSubmitting(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()

      const { error } = await supabase
        .from('document_comments')
        .update({
          resolved: true,
          resolved_by: user?.id,
          resolved_at: new Date().toISOString()
        })
        .eq('id', commentId)

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Comment resolved successfully'
      })
    } catch (error) {
      console.error('Error resolving comment:', error)
      toast({
        title: 'Error',
        description: 'Failed to resolve comment',
        variant: 'destructive'
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleUnresolve = async (commentId: string) => {
    if (submitting) return

    setSubmitting(true)
    try {
      const { error } = await supabase
        .from('document_comments')
        .update({
          resolved: false,
          resolved_by: null,
          resolved_at: null
        })
        .eq('id', commentId)

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Comment unresolve successfully'
      })
    } catch (error) {
      console.error('Error unresolving comment:', error)
      toast({
        title: 'Error',
        description: 'Failed to unresolve comment',
        variant: 'destructive'
      })
    } finally {
      setSubmitting(false)
    }
  }

  const startEdit = (comment: Comment) => {
    setEditingComment(comment.id)
    setEditContent(comment.content)
  }

  const cancelEdit = () => {
    setEditingComment(null)
    setEditContent('')
  }

  const startReply = (commentId: string) => {
    setReplyingTo(commentId)
    setReplyContent('')
  }

  const cancelReply = () => {
    setReplyingTo(null)
    setReplyContent('')
  }

  const renderComment = (comment: Comment, isReply = false) => (
    <div
      key={comment.id}
      className={cn('space-y-2', isReply && 'ml-6 border-l-2 border-muted pl-3')}
      data-testid={comment.resolved ? "resolved-comment" : "comment-thread"}
    >
      <div className="flex items-start gap-2">
        <Avatar className="w-6 h-6" data-testid="comment-author-avatar">
          <AvatarImage src={comment.author.avatar} alt={comment.author.name} />
          <AvatarFallback className="text-xs">
            {getInitials(comment.author.name)}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium">{comment.author.name}</span>
            <span className="text-xs text-muted-foreground">
              {comment.createdAt.toLocaleString()}
            </span>
            {comment.resolved && (
              <Badge variant="secondary" className="text-xs">
                Resolved
              </Badge>
            )}
          </div>

          {editingComment === comment.id ? (
            <div className="space-y-2">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                placeholder="Edit your comment..."
                className="min-h-[60px]"
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={() => handleEdit(comment.id)}>
                  <Check className="w-3 h-3 mr-1" />
                  Save
                </Button>
                <Button size="sm" variant="outline" onClick={cancelEdit}>
                  <X className="w-3 h-3 mr-1" />
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <>
              <p className="text-sm text-foreground whitespace-pre-wrap">
                {comment.content}
              </p>
              {comment.updatedAt && comment.updatedAt > comment.createdAt && (
                <span className="text-xs text-muted-foreground italic">
                  (edited)
                </span>
              )}
            </>
          )}
        </div>

        {editingComment !== comment.id && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <MoreHorizontal className="w-3 h-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {!isReply && (
                <DropdownMenuItem onClick={() => startReply(comment.id)}>
                  <Reply className="w-3 h-3 mr-2" />
                  Reply
                </DropdownMenuItem>
              )}
              {comment.author.id === currentUser?.id && (
                <DropdownMenuItem onClick={() => startEdit(comment)}>
                  <Edit3 className="w-3 h-3 mr-2" />
                  Edit
                </DropdownMenuItem>
              )}
              {!comment.resolved ? (
                <DropdownMenuItem onClick={() => handleResolve(comment.id)} data-testid="resolve-button">
                  <Check className="w-3 h-3 mr-2" />
                  Resolve
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => handleUnresolve(comment.id)} data-testid="unresolve-button">
                  <X className="w-3 h-3 mr-2" />
                  Unresolve
                </DropdownMenuItem>
              )}
              {(comment.author.id === currentUser?.id || !isReply) && (
                <DropdownMenuItem
                  onClick={() => handleDelete(comment.id)}
                  className="text-destructive"
                >
                  <Trash2 className="w-3 h-3 mr-2" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Reply form */}
      {replyingTo === comment.id && (
        <div className="ml-8 space-y-2">
          <Textarea
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
            placeholder="Write a reply..."
            className="min-h-[60px]"
          />
          <div className="flex gap-2">
            <Button size="sm" onClick={() => handleReply(comment.id)} data-testid="reply-button">
              <Reply className="w-3 h-3 mr-1" />
              Reply
            </Button>
            <Button size="sm" variant="outline" onClick={cancelReply}>
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="space-y-2">
          {comment.replies.map(reply => renderComment(reply, true))}
        </div>
      )}
    </div>
  )

  return (
    <div
      className={cn('w-80 bg-background border-l flex flex-col h-full', className)}
      data-testid="comments-panel"
    >
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center gap-2">
          <MessageSquare className="w-4 h-4" />
          <h3 className="font-semibold">Comments</h3>
          <Badge variant="secondary" className="ml-auto">
            {comments.length}
          </Badge>
        </div>
      </div>

      {/* Comments List */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {loading ? (
            <div className="text-center text-muted-foreground py-8">
              <Loader2 className="w-8 h-8 mx-auto mb-2 animate-spin" />
              <p className="text-sm">Loading comments...</p>
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No comments yet</p>
              <p className="text-xs">Select text and add a comment to get started</p>
            </div>
          ) : (
            comments.map(comment => renderComment(comment))
          )}
        </div>
      </ScrollArea>

      {/* Add Comment Form */}
      <div className="p-4 border-t">
        <div className="space-y-2">
          <Textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Add a comment..."
            className="min-h-[80px]"
            disabled={submitting}
          />
          <Button
            onClick={handleAddComment}
            disabled={!newComment.trim() || submitting}
            className="w-full"
            data-testid="add-comment-button"
          >
            {submitting ? (
              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
            ) : (
              <Plus className="w-3 h-3 mr-1" />
            )}
            {submitting ? 'Adding...' : 'Add Comment'}
          </Button>
        </div>
      </div>
    </div>
  )
}
