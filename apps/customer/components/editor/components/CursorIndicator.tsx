'use client'

import React from 'react'
import { cn } from '@utils/lib/utils'

interface CursorIndicatorProps {
  user: {
    id: string
    name: string
    color?: string
  }
  position: {
    x: number
    y: number
  }
  className?: string
}

export function CursorIndicator({ user, position, className }: CursorIndicatorProps) {
  const cursorColor = user.color || '#3B82F6'
  
  return (
    <div
      className={cn(
        'absolute pointer-events-none z-50 transition-all duration-100 ease-out',
        className
      )}
      style={{
        left: position.x,
        top: position.y,
        color: cursorColor,
      }}
      data-testid="cursor-indicator"
      data-user-id={user.id}
      data-user-name={user.name}
    >
      {/* Cursor line */}
      <div 
        className="w-0.5 h-5 animate-pulse"
        style={{ backgroundColor: cursorColor }}
      />
      
      {/* User name label */}
      <div
        className="absolute -top-6 left-0 transform -translate-x-1/2 px-2 py-1 rounded text-xs font-medium text-white whitespace-nowrap"
        style={{ backgroundColor: cursorColor }}
      >
        {user.name}
      </div>
    </div>
  )
}

interface CommentHighlightProps {
  children: React.ReactNode
  isResolved?: boolean
  onClick?: () => void
  className?: string
}

export function CommentHighlight({ 
  children, 
  isResolved = false, 
  onClick, 
  className 
}: CommentHighlightProps) {
  return (
    <span
      className={cn(
        'relative cursor-pointer transition-colors duration-200',
        isResolved 
          ? 'bg-gray-100 dark:bg-gray-800/30 border-l-2 border-gray-300 dark:border-gray-600 pl-1 opacity-60' 
          : 'bg-yellow-100 dark:bg-yellow-900/30 border-l-2 border-yellow-400 dark:border-yellow-600 pl-1 hover:bg-yellow-200 dark:hover:bg-yellow-900/50',
        'comment-highlight',
        className
      )}
      onClick={onClick}
      data-resolved={isResolved}
    >
      {children}
    </span>
  )
}

interface TypingIndicatorProps {
  users: Array<{
    id: string
    name: string
    color?: string
  }>
  className?: string
}

export function TypingIndicator({ users, className }: TypingIndicatorProps) {
  if (users.length === 0) return null

  const displayText = users.length === 1 
    ? `${users[0].name} is typing...`
    : users.length === 2
    ? `${users[0].name} and ${users[1].name} are typing...`
    : `${users[0].name} and ${users.length - 1} others are typing...`

  return (
    <div className={cn(
      'inline-flex items-center gap-2 px-3 py-1 bg-muted rounded-full text-xs text-muted-foreground',
      className
    )}>
      <span>{displayText}</span>
      <div className="flex gap-1">
        <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
    </div>
  )
}

interface CollaboratorPresenceProps {
  users: Array<{
    id: string
    name: string
    avatar?: string
    color?: string
    isActive?: boolean
  }>
  className?: string
}

export function CollaboratorPresence({ users, className }: CollaboratorPresenceProps) {
  return (
    <div className={cn('flex items-center gap-1', className)}>
      {users.map((user) => (
        <div
          key={user.id}
          className="relative"
          data-testid="collaborator-avatar"
        >
          <div
            className="w-6 h-6 rounded-full border-2 border-background flex items-center justify-center text-xs font-medium text-white"
            style={{ backgroundColor: user.color || '#3B82F6' }}
            title={user.name}
          >
            {user.avatar ? (
              <img 
                src={user.avatar} 
                alt={user.name}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              user.name.charAt(0).toUpperCase()
            )}
          </div>
          
          {/* Active indicator */}
          {user.isActive && (
            <div className="absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-500 border border-background rounded-full" />
          )}
        </div>
      ))}
    </div>
  )
}
