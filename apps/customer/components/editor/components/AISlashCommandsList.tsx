'use client'

import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { cn } from '@utils/lib/utils'

interface AISlashCommand {
  title: string
  description: string
  command: string
  icon: string
}

interface AISlashCommandsListProps {
  items: AISlashCommand[]
  command: (props: { command: string }) => void
}

interface AISlashCommandsListRef {
  onKeyDown: (props: { event: KeyboardEvent }) => boolean
}

export const AISlashCommandsList = forwardRef<
  AISlashCommandsListRef,
  AISlashCommandsListProps
>((props, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const selectItem = (index: number) => {
    const item = props.items[index]
    if (item) {
      props.command({ command: item.command })
    }
  }

  const upHandler = () => {
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length)
  }

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % props.items.length)
  }

  const enterHandler = () => {
    selectItem(selectedIndex)
  }

  useEffect(() => setSelectedIndex(0), [props.items])

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }: { event: KeyboardEvent }) => {
      if (event.key === 'ArrowUp') {
        upHandler()
        return true
      }

      if (event.key === 'ArrowDown') {
        downHandler()
        return true
      }

      if (event.key === 'Enter') {
        enterHandler()
        return true
      }

      return false
    },
  }))

  return (
    <div className="z-50 min-w-[280px] max-w-[320px] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md">
      <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground" data-testid="ai-commands-header">
        AI Commands
      </div>
      {props.items.length ? (
        props.items.map((item, index) => (
          <button
            key={index}
            className={cn(
              'relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors',
              index === selectedIndex
                ? 'bg-accent text-accent-foreground'
                : 'hover:bg-accent hover:text-accent-foreground'
            )}
            onClick={() => selectItem(index)}
            data-testid={`ai-slash-command-${item.command}`}
          >
            <div className="flex items-center gap-3">
              <span className="text-lg">{item.icon}</span>
              <div className="flex flex-col items-start">
                <span className="font-medium">{item.title}</span>
                <span className="text-xs text-muted-foreground">
                  {item.description}
                </span>
              </div>
            </div>
          </button>
        ))
      ) : (
        <div className="px-2 py-1.5 text-sm text-muted-foreground">
          No AI commands found
        </div>
      )}
    </div>
  )
})
