import React from 'react'
import { Editor } from '@tiptap/react'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuShortcut,
  ContextMenuLabel,
  ContextMenuTrigger,
} from '@ui/components/ui/context-menu'
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Code,
  Highlighter,
  Copy,
  Scissors,
  Clipboard,
  Link,
  MessageSquare,
  Heading1,
  Heading2,
  Heading3,
  Type,
  List,
  ListOrdered,
  Quote,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Palette,
  Superscript,
  Subscript,
  Smile,
  FileText,
  Calculator,
  ChevronDown,
} from 'lucide-react'

interface EditorContextMenuProps {
  editor: Editor
  children: React.ReactNode
  onAddComment?: (position: number) => void
}

export function EditorContextMenu({ editor, children, onAddComment }: EditorContextMenuProps) {
  const handleCopy = () => {
    document.execCommand('copy')
  }

  const handleCut = () => {
    document.execCommand('cut')
  }

  const handlePaste = () => {
    document.execCommand('paste')
  }

  const handleAddLink = () => {
    const url = window.prompt('Enter URL:')
    if (url) {
      editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
    }
  }

  const handleAddComment = () => {
    const { from } = editor.state.selection
    onAddComment?.(from)
  }

  const handleHeadingChange = (level: 1 | 2 | 3) => {
    editor.chain().focus().toggleHeading({ level }).run()
  }

  const handleTextAlign = (alignment: 'left' | 'center' | 'right' | 'justify') => {
    editor.chain().focus().setTextAlign(alignment).run()
  }

  const handleHighlight = (color?: string) => {
    if (color) {
      editor.chain().focus().toggleHighlight({ color }).run()
    } else {
      editor.chain().focus().toggleHighlight().run()
    }
  }

  const isTextSelected = !editor.state.selection.empty

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent className="w-64">
        {/* Basic Clipboard Operations */}
        <ContextMenuItem onClick={handleCopy} disabled={!isTextSelected}>
          <Copy className="w-4 h-4" />
          Copy
          <ContextMenuShortcut>⌘C</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem onClick={handleCut} disabled={!isTextSelected}>
          <Scissors className="w-4 h-4" />
          Cut
          <ContextMenuShortcut>⌘X</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem onClick={handlePaste}>
          <Clipboard className="w-4 h-4" />
          Paste
          <ContextMenuShortcut>⌘V</ContextMenuShortcut>
        </ContextMenuItem>

        <ContextMenuSeparator />

        {/* Text Formatting */}
        <ContextMenuLabel>Text Formatting</ContextMenuLabel>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleBold().run()}
          disabled={!isTextSelected}
        >
          <Bold className="w-4 h-4" />
          Bold
          <ContextMenuShortcut>⌘B</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleItalic().run()}
          disabled={!isTextSelected}
        >
          <Italic className="w-4 h-4" />
          Italic
          <ContextMenuShortcut>⌘I</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          disabled={!isTextSelected}
        >
          <Underline className="w-4 h-4" />
          Underline
          <ContextMenuShortcut>⌘U</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleStrike().run()}
          disabled={!isTextSelected}
        >
          <Strikethrough className="w-4 h-4" />
          Strikethrough
          <ContextMenuShortcut>⌘⇧X</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleCode().run()}
          disabled={!isTextSelected}
        >
          <Code className="w-4 h-4" />
          Inline Code
          <ContextMenuShortcut>⌘E</ContextMenuShortcut>
        </ContextMenuItem>

        {/* Highlight Submenu */}
        <ContextMenuSub>
          <ContextMenuSubTrigger disabled={!isTextSelected}>
            <Highlighter className="w-4 h-4" />
            Highlight
          </ContextMenuSubTrigger>
          <ContextMenuSubContent>
            <ContextMenuItem onClick={() => handleHighlight()}>
              <Highlighter className="w-4 h-4" />
              Default Highlight
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleHighlight('#fef3c7')}>
              <div className="w-4 h-4 rounded bg-yellow-200 border" />
              Yellow
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleHighlight('#fecaca')}>
              <div className="w-4 h-4 rounded bg-red-200 border" />
              Red
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleHighlight('#bbf7d0')}>
              <div className="w-4 h-4 rounded bg-green-200 border" />
              Green
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleHighlight('#bfdbfe')}>
              <div className="w-4 h-4 rounded bg-blue-200 border" />
              Blue
            </ContextMenuItem>
            <ContextMenuItem onClick={() => editor.chain().focus().unsetHighlight().run()}>
              <Palette className="w-4 h-4" />
              Remove Highlight
            </ContextMenuItem>
          </ContextMenuSubContent>
        </ContextMenuSub>

        {/* Superscript/Subscript */}
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleSuperscript().run()}
          disabled={!isTextSelected}
        >
          <Superscript className="w-4 h-4" />
          Superscript
        </ContextMenuItem>
        <ContextMenuItem
          onClick={() => editor.chain().focus().toggleSubscript().run()}
          disabled={!isTextSelected}
        >
          <Subscript className="w-4 h-4" />
          Subscript
        </ContextMenuItem>

        <ContextMenuSeparator />

        {/* Block Types */}
        <ContextMenuLabel>Convert to</ContextMenuLabel>
        <ContextMenuItem onClick={() => editor.chain().focus().setParagraph().run()}>
          <Type className="w-4 h-4" />
          Paragraph
        </ContextMenuItem>
        <ContextMenuItem onClick={() => handleHeadingChange(1)}>
          <Heading1 className="w-4 h-4" />
          Heading 1
        </ContextMenuItem>
        <ContextMenuItem onClick={() => handleHeadingChange(2)}>
          <Heading2 className="w-4 h-4" />
          Heading 2
        </ContextMenuItem>
        <ContextMenuItem onClick={() => handleHeadingChange(3)}>
          <Heading3 className="w-4 h-4" />
          Heading 3
        </ContextMenuItem>
        <ContextMenuItem onClick={() => editor.chain().focus().toggleBulletList().run()}>
          <List className="w-4 h-4" />
          Bullet List
        </ContextMenuItem>
        <ContextMenuItem onClick={() => editor.chain().focus().toggleOrderedList().run()}>
          <ListOrdered className="w-4 h-4" />
          Numbered List
        </ContextMenuItem>
        <ContextMenuItem onClick={() => editor.chain().focus().toggleBlockquote().run()}>
          <Quote className="w-4 h-4" />
          Quote
        </ContextMenuItem>

        <ContextMenuSeparator />

        {/* Text Alignment */}
        <ContextMenuSub>
          <ContextMenuSubTrigger>
            <AlignLeft className="w-4 h-4" />
            Text Align
          </ContextMenuSubTrigger>
          <ContextMenuSubContent>
            <ContextMenuItem onClick={() => handleTextAlign('left')}>
              <AlignLeft className="w-4 h-4" />
              Left
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleTextAlign('center')}>
              <AlignCenter className="w-4 h-4" />
              Center
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleTextAlign('right')}>
              <AlignRight className="w-4 h-4" />
              Right
            </ContextMenuItem>
            <ContextMenuItem onClick={() => handleTextAlign('justify')}>
              <AlignJustify className="w-4 h-4" />
              Justify
            </ContextMenuItem>
          </ContextMenuSubContent>
        </ContextMenuSub>

        <ContextMenuSeparator />

        {/* Links and Comments */}
        <ContextMenuItem onClick={handleAddLink} disabled={!isTextSelected}>
          <Link className="w-4 h-4" />
          Add Link
          <ContextMenuShortcut>⌘K</ContextMenuShortcut>
        </ContextMenuItem>
        {onAddComment && (
          <ContextMenuItem onClick={handleAddComment} disabled={!isTextSelected}>
            <MessageSquare className="w-4 h-4" />
            Add Comment
            <ContextMenuShortcut>⌘⇧M</ContextMenuShortcut>
          </ContextMenuItem>
        )}

        <ContextMenuSeparator />

        {/* Advanced Features */}
        <ContextMenuLabel>Insert</ContextMenuLabel>
        <ContextMenuItem onClick={() => editor.chain().focus().setInlineMath('').run()}>
          <Calculator className="w-4 h-4" />
          Math Expression
          <ContextMenuShortcut>⌘⇧M</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem onClick={() => editor.chain().focus().toggleDetails().run()}>
          <ChevronDown className="w-4 h-4" />
          Collapsible Section
          <ContextMenuShortcut>⌘⇧D</ContextMenuShortcut>
        </ContextMenuItem>
        <ContextMenuItem onClick={() => {
          // Trigger emoji picker by inserting colon
          editor.chain().focus().insertContent(':').run()
        }}>
          <Smile className="w-4 h-4" />
          Emoji
          <ContextMenuShortcut>:</ContextMenuShortcut>
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  )
}
