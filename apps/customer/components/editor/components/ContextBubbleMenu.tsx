import React, { useMemo } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>u, Editor } from '@tiptap/react'
import { BubbleMenuErrorBoundary } from './BubbleMenuErrorBoundary'
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Code,
  Highlighter,
  Copy,
  Scissors,
  Clipboard,
  Link,
  MessageSquare,
  Heading1,
  Heading2,
  Heading3,
  Type,
  List,
  ListOrdered,
  Quote,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Palette,
  Superscript,
  Subscript,
  Smile,
  Calculator,
  ChevronDown,
  MoreHorizontal,
} from 'lucide-react'

interface EditorContextBubbleMenuProps {
  editor: Editor
  onAddComment?: (position: number) => void
}

export function EditorContextBubbleMenu({ editor, onAddComment }: EditorContextBubbleMenuProps) {
  // Generate a stable key for this editor instance
  const editorKey = useMemo(() => `context-bubble-menu-${Math.random().toString(36).substr(2, 9)}`, [editor])

  const handleCopy = () => {
    document.execCommand('copy')
  }

  const handleCut = () => {
    document.execCommand('cut')
  }

  const handlePaste = () => {
    document.execCommand('paste')
  }

  const handleAddLink = () => {
    const url = window.prompt('Enter URL:')
    if (url) {
      editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
    }
  }

  const handleAddComment = () => {
    try {
      const { from } = editor.state.selection
      // Defer the callback to avoid DOM manipulation conflicts
      setTimeout(() => {
        onAddComment?.(from)
      }, 0)
    } catch (error) {
      console.error('Error handling comment addition:', error)
    }
  }

  const handleHeadingChange = (level: 1 | 2 | 3) => {
    editor.chain().focus().toggleHeading({ level }).run()
  }

  const handleTextAlign = (alignment: 'left' | 'center' | 'right' | 'justify') => {
    editor.chain().focus().setTextAlign(alignment).run()
  }

  const handleHighlight = (color?: string) => {
    if (color) {
      editor.chain().focus().toggleHighlight({ color }).run()
    } else {
      editor.chain().focus().toggleHighlight().run()
    }
  }

  const isTextSelected = !editor.state.selection.empty

  // Don't render if editor is destroyed or not available
  if (!editor || editor.isDestroyed) {
    return null
  }

  return (
    <BubbleMenuErrorBoundary>
      <BubbleMenu
        key={editorKey}
        editor={editor}
        tippyOptions={{
          duration: 100,
          placement: 'top',
          interactive: true,
          hideOnClick: false, // Don't hide on click to prevent DOM conflicts
          appendTo: () => document.body,
          zIndex: 9999,
          onShow: (instance) => {
            // Ensure DOM is ready before showing
            try {
              // Validate that the editor is still available
              if (!editor || editor.isDestroyed) {
                instance.hide()
                return false
              }
            } catch (error) {
              console.error('Error in BubbleMenu onShow:', error)
              instance.hide()
              return false
            }
          },
          onHide: () => {
            // Clean up any pending operations
            try {
              // Clear any timeouts or pending operations
            } catch (error) {
              console.error('Error in BubbleMenu onHide:', error)
            }
          }
        }}
        shouldShow={({ editor, view, state, oldState, from, to }) => {
          try {
            // Show only when text is selected and editor is not destroyed
            return from !== to && !editor.isDestroyed && editor.view && editor.view.dom.isConnected
          } catch (error) {
            console.error('Error in BubbleMenu shouldShow:', error)
            return false
          }
        }}
        className="flex items-center gap-1 p-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
      >
      {/* Basic Clipboard Operations */}
      <button
        onClick={handleCopy}
        disabled={!isTextSelected}
        className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Copy"
      >
        <Copy className="w-4 h-4" />
      </button>
      <button
        onClick={handleCut}
        disabled={!isTextSelected}
        className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Cut"
      >
        <Scissors className="w-4 h-4" />
      </button>
      <button
        onClick={handlePaste}
        className="p-1 rounded hover:bg-gray-100"
        title="Paste"
      >
        <Clipboard className="w-4 h-4" />
      </button>

      <div className="w-px h-6 bg-gray-200 mx-1" />

      {/* Text Formatting */}
      <button
        onClick={() => editor.chain().focus().toggleBold().run()}
        disabled={!isTextSelected}
        className={`p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed ${
          editor.isActive('bold') ? 'bg-blue-100 text-blue-600' : ''
        }`}
        title="Bold"
      >
        <Bold className="w-4 h-4" />
      </button>
      <button
        onClick={() => editor.chain().focus().toggleItalic().run()}
        disabled={!isTextSelected}
        className={`p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed ${
          editor.isActive('italic') ? 'bg-blue-100 text-blue-600' : ''
        }`}
        title="Italic"
      >
        <Italic className="w-4 h-4" />
      </button>
      <button
        onClick={() => editor.chain().focus().toggleUnderline().run()}
        disabled={!isTextSelected}
        className={`p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed ${
          editor.isActive('underline') ? 'bg-blue-100 text-blue-600' : ''
        }`}
        title="Underline"
      >
        <Underline className="w-4 h-4" />
      </button>

      <div className="w-px h-6 bg-gray-200 mx-1" />

      {/* Block Types */}
      <button
        onClick={() => handleHeadingChange(1)}
        className={`p-1 rounded hover:bg-gray-100 ${
          editor.isActive('heading', { level: 1 }) ? 'bg-blue-100 text-blue-600' : ''
        }`}
        title="Heading 1"
      >
        <Heading1 className="w-4 h-4" />
      </button>
      <button
        onClick={() => handleHeadingChange(2)}
        className={`p-1 rounded hover:bg-gray-100 ${
          editor.isActive('heading', { level: 2 }) ? 'bg-blue-100 text-blue-600' : ''
        }`}
        title="Heading 2"
      >
        <Heading2 className="w-4 h-4" />
      </button>
      <button
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        className={`p-1 rounded hover:bg-gray-100 ${
          editor.isActive('bulletList') ? 'bg-blue-100 text-blue-600' : ''
        }`}
        title="Bullet List"
      >
        <List className="w-4 h-4" />
      </button>

      <div className="w-px h-6 bg-gray-200 mx-1" />

      {/* Links and Comments */}
      <button
        onClick={handleAddLink}
        disabled={!isTextSelected}
        className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Add Link"
      >
        <Link className="w-4 h-4" />
      </button>
      {onAddComment && (
        <button
          onClick={handleAddComment}
          disabled={!isTextSelected}
          className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
          title="Add Comment"
        >
          <MessageSquare className="w-4 h-4" />
        </button>
      )}

      <div className="w-px h-6 bg-gray-200 mx-1" />

      {/* Advanced Features */}
      <button
        onClick={() => editor.chain().focus().setInlineMath('').run()}
        className="p-1 rounded hover:bg-gray-100"
        title="Math Expression"
      >
        <Calculator className="w-4 h-4" />
      </button>
      <button
        onClick={() => {
          // Trigger emoji picker by inserting colon
          editor.chain().focus().insertContent(':').run()
        }}
        className="p-1 rounded hover:bg-gray-100"
        title="Emoji"
      >
        <Smile className="w-4 h-4" />
      </button>
      </BubbleMenu>
    </BubbleMenuErrorBoundary>
  )
}
