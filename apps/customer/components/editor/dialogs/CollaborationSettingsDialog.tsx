'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@ui/components/ui/button'
import { Input } from '@ui/components/ui/input'
import { Label } from '@ui/components/ui/label'
import { Switch } from '@ui/components/ui/switch'
import { Separator } from '@ui/components/ui/separator'
import { Badge } from '@ui/components/ui/badge'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@ui/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/ui/select'
import { 
  Globe, 
  Lock, 
  Settings, 
  Bell, 
  BellOff,
  Users,
  Eye,
  Edit3
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { createClient } from '@/app/supabase/client'
import { useToast } from '@ui/hooks/use-toast'

interface CollaborationSettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  documentId: string
  currentUser?: {
    id: string
    name: string
    email?: string
  }
  isOwner?: boolean
}

interface DocumentSettings {
  isPublic: boolean
  defaultPermission: 'read' | 'write' | 'admin'
  allowComments: boolean
  allowSuggestions: boolean
  notifyOnComments: boolean
  notifyOnShares: boolean
  notifyOnEdits: boolean
}

export function CollaborationSettingsDialog({
  open,
  onOpenChange,
  documentId,
  currentUser,
  isOwner = false
}: CollaborationSettingsDialogProps) {
  const [settings, setSettings] = useState<DocumentSettings>({
    isPublic: false,
    defaultPermission: 'read',
    allowComments: true,
    allowSuggestions: true,
    notifyOnComments: true,
    notifyOnShares: true,
    notifyOnEdits: false
  })
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  const { toast } = useToast()
  const supabase = createClient()

  // Load current settings
  useEffect(() => {
    if (open && documentId) {
      loadSettings()
    }
  }, [open, documentId])

  const loadSettings = async () => {
    setLoading(true)
    try {
      // Load document settings from Supabase
      const { data: document, error } = await supabase
        .from('collaborative_documents')
        .select('metadata')
        .eq('id', documentId)
        .single()

      if (error) throw error

      if (document?.metadata && typeof document.metadata === 'object' && document.metadata !== null) {
        const metadata = document.metadata as { settings?: Partial<DocumentSettings> }
        if (metadata.settings) {
          setSettings(prev => ({
            ...prev,
            ...metadata.settings
          }))
        }
      }
    } catch (error) {
      console.error('Error loading settings:', error)
      toast({
        title: 'Error',
        description: 'Failed to load document settings',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    setSaving(true)
    try {
      // Update document settings in Supabase
      const { error } = await supabase
        .from('collaborative_documents')
        .update({
          metadata: {
            settings: settings as any
          }
        })
        .eq('id', documentId)

      if (error) throw error

      toast({
        title: 'Settings saved',
        description: 'Document collaboration settings have been updated'
      })

      onOpenChange(false)
    } catch (error) {
      console.error('Error saving settings:', error)
      toast({
        title: 'Error',
        description: 'Failed to save document settings',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const updateSetting = <K extends keyof DocumentSettings>(
    key: K,
    value: DocumentSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="sm:max-w-[500px]" 
        data-testid="collaboration-settings"
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Document Settings
          </DialogTitle>
          <DialogDescription>
            Configure collaboration and sharing settings for this document.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Document Permissions */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <h4 className="font-medium">Document Permissions</h4>
            </div>
            
            <div className="space-y-3 pl-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="flex items-center gap-2">
                    <Globe className="w-3 h-3" />
                    Public
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Anyone with the link can access this document
                  </p>
                </div>
                <Switch
                  checked={settings.isPublic}
                  onCheckedChange={(checked) => updateSetting('isPublic', checked)}
                  disabled={!isOwner}
                  data-testid="public-access-switch"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="flex items-center gap-2">
                    <Lock className="w-3 h-3" />
                    Private
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Only invited users can access this document
                  </p>
                </div>
                <Switch
                  checked={!settings.isPublic}
                  onCheckedChange={(checked) => updateSetting('isPublic', !checked)}
                  disabled={!isOwner}
                />
              </div>

              {settings.isPublic && (
                <div className="space-y-2">
                  <Label htmlFor="default-permission">Default Permission</Label>
                  <Select
                    value={settings.defaultPermission}
                    onValueChange={(value: 'read' | 'write' | 'admin') => 
                      updateSetting('defaultPermission', value)
                    }
                    disabled={!isOwner}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="read">
                        <div className="flex items-center gap-2">
                          <Eye className="w-3 h-3" />
                          Can view
                        </div>
                      </SelectItem>
                      <SelectItem value="write">
                        <div className="flex items-center gap-2">
                          <Edit3 className="w-3 h-3" />
                          Can edit
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Collaboration Features */}
          <div className="space-y-4">
            <h4 className="font-medium">Collaboration Features</h4>
            
            <div className="space-y-3 pl-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Allow Comments</Label>
                  <p className="text-xs text-muted-foreground">
                    Users can add comments to the document
                  </p>
                </div>
                <Switch
                  checked={settings.allowComments}
                  onCheckedChange={(checked) => updateSetting('allowComments', checked)}
                  disabled={!isOwner}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Allow Suggestions</Label>
                  <p className="text-xs text-muted-foreground">
                    Users can suggest edits to the document
                  </p>
                </div>
                <Switch
                  checked={settings.allowSuggestions}
                  onCheckedChange={(checked) => updateSetting('allowSuggestions', checked)}
                  disabled={!isOwner}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Notifications */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Bell className="w-4 h-4" />
              <h4 className="font-medium">Notifications</h4>
            </div>
            
            <div className="space-y-3 pl-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Comments</Label>
                  <p className="text-xs text-muted-foreground">
                    Get notified when someone comments
                  </p>
                </div>
                <Switch
                  checked={settings.notifyOnComments}
                  onCheckedChange={(checked) => updateSetting('notifyOnComments', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Shares</Label>
                  <p className="text-xs text-muted-foreground">
                    Get notified when document is shared
                  </p>
                </div>
                <Switch
                  checked={settings.notifyOnShares}
                  onCheckedChange={(checked) => updateSetting('notifyOnShares', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Edits</Label>
                  <p className="text-xs text-muted-foreground">
                    Get notified when document is edited
                  </p>
                </div>
                <Switch
                  checked={settings.notifyOnEdits}
                  onCheckedChange={(checked) => updateSetting('notifyOnEdits', checked)}
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} data-testid="cancel-settings-button">
            Cancel
          </Button>
          <Button onClick={saveSettings} disabled={saving || !isOwner} data-testid="save-settings-button">
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
