'use client'

import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@ui/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/ui/select'
import { Button } from '@ui/components/ui/button'
import { Input } from '@ui/components/ui/input'
import { Label } from '@ui/components/ui/label'
import { Textarea } from '@ui/components/ui/textarea'
import { Switch } from '@ui/components/ui/switch'
import { Badge } from '@ui/components/ui/badge'
import { X } from 'lucide-react'
import { useDocumentContext } from '@/components/editor/context/DocumentContext'
// Real data hooks
import { useAuth } from '@/components/context/auth/auth-context'
import { createClient } from '@/app/supabase/client'

export interface ReportComponentConfig {
  id: string
  title: string
  type: 'report-section' | 'report-group' | 'report-summary'
  endpoint?: string
  prompt?: string
  summarize?: string[] // For summaries - array of component IDs
  entity?: string
  model?: string
  section?: string
  level?: string
  disclosures?: boolean
  runId?: string
  headings?: 'keep' | 'remove' | 'reset' // Heading transformation option
  depth?: number // Depth override for heading reset
}

interface ReportComponentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (config: ReportComponentConfig) => void
  type: 'report-section' | 'report-group' | 'report-summary'
  initialConfig?: Partial<ReportComponentConfig>
  availableComponents?: Array<{ id: string; title: string; type: string }> // For summary dependencies
}

interface EntityOption {
  id: string
  name: string
}

interface ModelOption {
  id: string
  name: string
}

interface SectionOption {
  id: string
  name: string
}

interface RunOption {
  id: string
  name: string
}

// Hook to fetch entities
function useEntities() {
  const [entities, setEntities] = useState<EntityOption[]>([])
  const [loading, setLoading] = useState(true)
  const auth = useAuth()
  const supabase = createClient()

  useEffect(() => {
    async function fetchEntities() {
      try {
        const userId = auth.user?.id
        if (!userId) return

        const { data, error } = await supabase
          .from('view_my_companies')
          .select('entity_xid, name')
          .eq('profile_id', userId)

        if (error) {
          console.error('Error fetching entities:', error)
          return
        }

        setEntities(data.map((item: any) => ({
          id: item.entity_xid as string,
          name: item.name as string
        })))
      } catch (error) {
        console.error('Error fetching entities:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchEntities()
  }, [auth.user?.id, supabase])

  return { entities, loading }
}

// Hook to fetch models
function useModels() {
  const models: ModelOption[] = [
    { id: 'eko', name: 'ekoIntelligence' },
    { id: 'sdg', name: 'Sustainable Development Goals' },
    { id: 'doughnut', name: 'Doughnut Economics' },
    { id: 'plant_based_treaty', name: 'Plant Based Treaty' },
  ]

  return { models, loading: false }
}

// Hook to fetch sections for a model
function useSections(model: string) {
  const [sections, setSections] = useState<SectionOption[]>([])
  const [loading, setLoading] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    if (!model) {
      setSections([])
      return
    }

    async function fetchSections() {
      setLoading(true)
      try {
        const { data, error } = await supabase
          .from('xfer_model_sections_v2')
          .select('section, title')
          .eq('model', model)
          .order('section', { ascending: true })

        if (error) {
          console.error('Error fetching sections:', error)
          return
        }

        setSections(data.map((item: any) => ({
          id: item.section as string,
          name: item.title as string || item.section.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())
        })))
      } catch (error) {
        console.error('Error fetching sections:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSections()
  }, [model, supabase])

  return { sections, loading }
}

// Hook to fetch levels for a specific model
function useLevels(model: string) {
  const [levels, setLevels] = useState<Array<{ id: string; name: string }>>([])
  const [loading, setLoading] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    if (!model) {
      setLevels([])
      return
    }

    async function fetchLevels() {
      setLoading(true)
      try {
        const { data, error } = await supabase
          .from('xfer_model_sections_v2')
          .select('level, data')
          .eq('model', model)
          .not('level', 'is', null)

        if (error) {
          console.error('Error fetching levels:', error)
          return
        }

        // Extract unique levels
        const levelSet = new Set<string>()
        data.forEach((item: any) => {
          // Handle both direct level property and data.level
          const level = item.level || (item.data?.level as string | undefined);
          if (level) {
            levelSet.add(level)
          }
        })

        setLevels(Array.from(levelSet).map(level => ({
          id: level,
          name: level.charAt(0).toUpperCase() + level.slice(1)
        })))
      } catch (error) {
        console.error('Error fetching levels:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchLevels()
  }, [model, supabase])

  return { levels, loading }
}

// Hook to fetch runs for an entity
function useRuns(entityId: string) {
  const [runs, setRuns] = useState<RunOption[]>([])
  const [loading, setLoading] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    if (!entityId) {
      setRuns([{ id: 'latest', name: 'Latest Run' }])
      return
    }

    async function fetchRuns() {
      setLoading(true)
      try {
        const { data, error } = await supabase
          .from('xfer_runs_v2')
          .select('run_id, run_date')
          .eq('scope', 'entity')
          .eq('target', entityId)
          .order('run_date', { ascending: false })
          .limit(10)

        if (error) {
          console.error('Error fetching runs:', error)
          return
        }

        const runOptions = [
          { id: 'latest', name: 'Latest Run' },
          ...data.map((run: any) => ({
            id: run.run_id.toString(),
            name: `Run ${run.run_id} (${new Date(run.run_date).toLocaleDateString()})`
          }))
        ]

        setRuns(runOptions)
      } catch (error) {
        console.error('Error fetching runs:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchRuns()
  }, [entityId, supabase])

  return { runs, loading }
}

export function ReportComponentDialog({
  open,
  onOpenChange,
  onConfirm,
  type,
  initialConfig,
  availableComponents = [],
}: ReportComponentDialogProps) {
  // Try to use document context, but make it optional for backward compatibility
  let documentContext: any = null
  try {
    documentContext = useDocumentContext()
  } catch {
    // Document context not available, continue without it
  }

  const [config, setConfig] = useState<ReportComponentConfig>({
    id: '',
    title: '',
    type,
    model: '',
    section: '',
    level: '',
    endpoint: '',
    prompt: '',
    summarize: [],
    disclosures: documentContext?.includeDisclosures ?? true,
    headings: 'remove', // Default to remove headings
    depth: 1, // Default depth
    ...initialConfig,
  })

  // Use real data hooks
  const { models, loading: modelsLoading } = useModels()
  const { sections, loading: sectionsLoading } = useSections(config.model || '')
  const { levels, loading: levelsLoading } = useLevels(config.model || '')

  // Generate default values when dialog opens
  useEffect(() => {
    if (open && !initialConfig) {
      const timestamp = Date.now()
      setConfig(prev => ({
        ...prev,
        id: `${type.replace('report-', '')}-${timestamp}`,
        title: type === 'report-section' ? 'Report Section' 
              : type === 'report-group' ? 'Report Group' 
              : 'Summary',
      }))
    }
  }, [open, type, initialConfig])

  // Update endpoint when model, section, or level changes (use placeholders for entity/run)
  useEffect(() => {
    if (type === 'report-section' && config.model) {
      let endpoint = `/report/entity/[ENTITY_ID]/[RUN_ID]/harm/model/${config.model}`

      if (config.section) {
        endpoint += `/section/${config.section}`
      } else if (config.level) {
        endpoint += `/level/${config.level}`
      }

      const params = new URLSearchParams()
      if (config.disclosures) params.append('includeDisclosures', 'true')

      if (params.toString()) {
        endpoint += `?${params.toString()}`
      }

      setConfig(prev => ({ ...prev, endpoint }))
    }
  }, [config.model, config.section, config.level, config.disclosures, type])

  const handleSubmit = () => {
    console.log('handleSubmit called with config:', config)
    console.log('Validation check - id:', config.id, 'title:', config.title)

    if (!config.id || !config.title) {
      console.log('Validation failed - missing id or title')
      return // Basic validation
    }

    console.log('Calling onConfirm with config:', config)
    try {
      onConfirm(config)
      console.log('onConfirm completed successfully')
    } catch (error) {
      console.error('Error in onConfirm:', error)
    }

    console.log('Calling onOpenChange(false)')
    onOpenChange(false)
    console.log('Dialog should now be closed')
  }

  const addSummarizeComponent = (componentId: string) => {
    if (!config.summarize?.includes(componentId)) {
      setConfig(prev => ({
        ...prev,
        summarize: [...(prev.summarize || []), componentId]
      }))
    }
  }

  const removeSummarizeComponent = (componentId: string) => {
    setConfig(prev => ({
      ...prev,
      summarize: prev.summarize?.filter(id => id !== componentId) || []
    }))
  }



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            Configure {type === 'report-section' ? 'Report Section' 
                     : type === 'report-group' ? 'Report Group' 
                     : 'Report Summary'}
          </DialogTitle>
          <DialogDescription>
            {type === 'report-section' && 'Configure a report section that loads content from an API endpoint.'}
            {type === 'report-group' && 'Configure a report group to organize related sections.'}
            {type === 'report-summary' && 'Configure a summary that consolidates content from other components.'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Basic Configuration */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="id">ID</Label>
              <Input
                id="id"
                value={config.id}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setConfig(prev => ({ ...prev, id: e.target.value }))}
                placeholder="component-id"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={config.title}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setConfig(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Component Title"
              />
            </div>
          </div>

          {/* Report Section Specific */}
          {type === 'report-section' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Select value={config.model} onValueChange={(value: string) => setConfig(prev => ({ ...prev, model: value, section: '', level: '' }))}>
                  <SelectTrigger>
                    <SelectValue placeholder={modelsLoading ? "Loading models..." : "Select model"} />
                  </SelectTrigger>
                  <SelectContent>
                    {models.map(model => (
                      <SelectItem key={model.id} value={model.id}>
                        {model.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="section">Section</Label>
                  <Select
                    value={config.section}
                    onValueChange={(value: string) => setConfig(prev => ({ ...prev, section: value, level: '' }))}
                    disabled={!config.model || sectionsLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={sectionsLoading ? "Loading sections..." : "Select section"} />
                    </SelectTrigger>
                    <SelectContent>
                      {sections.map(section => (
                        <SelectItem key={section.id} value={section.id}>
                          {section.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="level">Level (alternative to section)</Label>
                  <Select
                    value={config.level}
                    onValueChange={(value: string) => setConfig(prev => ({ ...prev, level: value, section: '' }))}
                    disabled={!config.model || levelsLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={levelsLoading ? "Loading levels..." : "Select level"} />
                    </SelectTrigger>
                    <SelectContent>
                      {levels.map(level => (
                        <SelectItem key={level.id} value={level.id}>
                          {level.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="disclosures"
                  checked={config.disclosures}
                  onCheckedChange={(checked) => setConfig(prev => ({ ...prev, disclosures: checked }))}
                />
                <Label htmlFor="disclosures">Include Disclosures</Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="endpoint">Generated Endpoint</Label>
                <Input
                  id="endpoint"
                  value={config.endpoint}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setConfig(prev => ({ ...prev, endpoint: e.target.value }))}
                  placeholder="Endpoint will be generated automatically"
                  className="font-mono text-sm"
                />
              </div>
            </>
          )}

          {/* Report Summary Specific */}
          {type === 'report-summary' && (
            <div className="space-y-2">
              <Label>Components to Summarize</Label>
              <div className="space-y-2">
                <Select onValueChange={addSummarizeComponent}>
                  <SelectTrigger>
                    <SelectValue placeholder="Add component to summarize" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableComponents
                      .filter(comp => !config.summarize?.includes(comp.id))
                      .map(comp => (
                        <SelectItem key={comp.id} value={comp.id}>
                          {comp.title} ({comp.type})
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                <div className="flex flex-wrap gap-2">
                  {config.summarize?.map(componentId => {
                    const comp = availableComponents.find(c => c.id === componentId)
                    return (
                      <Badge key={componentId} variant="secondary" className="flex items-center gap-1">
                        {comp?.title || componentId}
                        <X 
                          className="w-3 h-3 cursor-pointer" 
                          onClick={() => removeSummarizeComponent(componentId)}
                        />
                      </Badge>
                    )
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Common Prompt Field */}
          {(type === 'report-section' || type === 'report-summary') && (
            <div className="space-y-2">
              <Label htmlFor="prompt">Additional Prompt</Label>
              <Textarea
                id="prompt"
                value={config.prompt}
                onChange={(e) => setConfig(prev => ({ ...prev, prompt: e.target.value }))}
                placeholder="Additional instructions for content generation..."
                rows={3}
              />
            </div>
          )}

          {/* Headings Configuration */}
          {(type === 'report-section' || type === 'report-summary') && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="headings">Headings Behaviour</Label>
                  <Select
                    value={config.headings}
                    onValueChange={(value: 'keep' | 'remove' | 'reset') => setConfig(prev => ({ ...prev, headings: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select headings behaviour" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="keep">Keep Headings</SelectItem>
                      <SelectItem value="remove">Remove Headings</SelectItem>
                      <SelectItem value="reset">Reset Heading Depth</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="depth">Depth Override</Label>
                  <Input
                    id="depth"
                    type="number"
                    min="1"
                    max="6"
                    value={config.depth}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setConfig(prev => ({ ...prev, depth: parseInt(e.target.value) || 1 }))}
                    placeholder="Auto-calculated from parent groups"
                  />
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                Depth is auto-calculated based on parent report groups. Override only if needed.
                {config.headings === 'reset' && ' Used for "Reset Heading Depth" option.'}
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!config.id || !config.title}
            type="button"
            data-testid="create-component-button"
          >
            {initialConfig ? 'Update' : 'Create'} Component
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
