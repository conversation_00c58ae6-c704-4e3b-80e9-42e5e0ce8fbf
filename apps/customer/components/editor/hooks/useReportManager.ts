import { useCallback } from 'react'
import { ReportComponent, useDocumentContext } from '../context/DocumentContext'
import { CitationType } from '@/components/citation'

/**
 * Hook that provides report-specific functionality using the unified DocumentContext
 * This is now the single source of truth for all report state management
 */
export function useReportManager() {
  const {
    state,
    dispatch,
    registerComponent,
    updateComponent,
    removeComponent,
    registerCitations,
    addCitation,
    setEntityRun,
    areDependenciesReady,
    waitForDependencies,
    addEntityChangeListener,
    notifyEntityChange,
    updateGroupStatus,
    updateAllGroupStatuses,
    refreshAllDescendants,
    triggerAutoSaveVersion,
    areAllComponentsLoaded,
  } = useDocumentContext()

  // Dependencies are now handled by the unified DocumentContext
  
  // Get children of a component (for groups)
  const getChildren = useCallback((parentId: string): ReportComponent[] => {
    return Array.from(state.components.values()).filter(
      component => component.parentId === parentId
    )
  }, [state.components])

  // Get all descendants of a component (recursive)
  const getAllDescendants = useCallback((parentId: string): ReportComponent[] => {
    const directChildren = Array.from(state.components.values()).filter(
      component => component.parentId === parentId,
    )

    const allDescendants: ReportComponent[] = [...directChildren]

    // Recursively get descendants of each child group
    directChildren.forEach(child => {
      if (child.type === 'report-group') {
        allDescendants.push(...getAllDescendants(child.id))
      }
    })

    return allDescendants
  }, [state.components])

  // Register a component - parent group updates are now handled automatically by DocumentContext
  const registerReportComponent = useCallback((component: ReportComponent) => {
    registerComponent(component)
    // Note: Parent group status updates are now handled automatically by DocumentContext
  }, [registerComponent])

  // Update a component - the DocumentContext will handle parent group updates automatically
  const updateReportComponent = useCallback((id: string, updates: Partial<ReportComponent>) => {
    const component = state.components.get(id)
    if (!component) {
      console.log(`updateReportComponent: Component ${id} not found`)
      return
    }

    console.log(`updateReportComponent: Updating ${id} (${component.type}) with:`, updates)
    updateComponent(id, updates)
    // Note: Parent group status updates are now handled automatically by DocumentContext
  }, [state.components, updateComponent])
  
  // Get ordered citations based on document position
  const getOrderedCitations = useCallback((): CitationType[] => {
    if (!state.editor) return state.citations
    
    // For now, return citations in the order they were added
    // TODO: Implement position-based ordering if needed
    return state.citations
  }, [state.editor, state.citations])
  
  // Set editor reference (for citation position tracking)
  const setEditor = useCallback((editor: any) => {
    dispatch({ type: 'EDITOR_CREATED', editor })
  }, [dispatch])

  // Entity change listeners are now handled by the unified DocumentContext
  
  return {
    // State
    components: state.components,
    citations: state.citations,
    currentEntity: state.currentEntity,
    currentRun: state.currentRun,
    editor: state.editor,

    // Component management
    registerComponent: registerReportComponent,
    updateComponent: updateReportComponent,
    removeComponent,
    getChildren,
    getAllDescendants,
    refreshAllDescendants,
    
    // Dependencies
    areDependenciesReady,
    waitForDependencies,

    // Citations
    registerCitations,
    addCitation,
    getOrderedCitations,

    // Editor
    setEditor,

    // Entity changes
    addEntityChangeListener,
    notifyEntityChange,

    // Group status management
    updateGroupStatus,
    updateAllGroupStatuses,

    // Auto-saving (EKO-130)
    triggerAutoSaveVersion,
    areAllComponentsLoaded,
  }
}
