'use client'

import React, { useEffect, useState, useRef } from 'react'

export interface AriaLiveRegionProps {
  /** The politeness level for announcements */
  politeness?: 'polite' | 'assertive'
  /** Additional CSS classes */
  className?: string
}

export interface AriaLiveMessage {
  id: string
  message: string
  politeness: 'polite' | 'assertive'
  timestamp: number
}

/**
 * AriaLiveRegion component for screen reader announcements
 * Provides accessible status updates for editor actions and errors
 */
export const AriaLiveRegion: React.FC<AriaLiveRegionProps> = ({
  politeness = 'polite',
  className = '',
}) => {
  const [politeMessages, setPoliteMessages] = useState<string[]>([])
  const [assertiveMessages, setAssertiveMessages] = useState<string[]>([])
  const messageTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)

  // Clear messages after a delay to prevent accumulation
  const clearMessages = (type: 'polite' | 'assertive') => {
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current)
    }
    
    messageTimeoutRef.current = setTimeout(() => {
      if (type === 'polite') {
        setPoliteMessages([])
      } else {
        setAssertiveMessages([])
      }
    }, 3000) // Clear after 3 seconds
  }

  // Global function to announce messages
  useEffect(() => {
    const announceMessage = (message: string, level: 'polite' | 'assertive' = 'polite') => {
      if (level === 'polite') {
        setPoliteMessages(prev => [...prev, message])
        clearMessages('polite')
      } else {
        setAssertiveMessages(prev => [...prev, message])
        clearMessages('assertive')
      }
    }

    // Attach to window for global access
    ;(window as any).announceToScreenReader = announceMessage

    return () => {
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current)
      }
      delete (window as any).announceToScreenReader
    }
  }, [])

  return (
    <>
      {/* Polite announcements for non-critical updates */}
      <div
        aria-live="polite"
        aria-atomic="true"
        className={`sr-only ${className}`}
        role="status"
      >
        {politeMessages.map((message, index) => (
          <span key={`polite-${index}`}>{message}</span>
        ))}
      </div>

      {/* Assertive announcements for critical updates and errors */}
      <div
        aria-live="assertive"
        aria-atomic="true"
        className={`sr-only ${className}`}
        role="alert"
      >
        {assertiveMessages.map((message, index) => (
          <span key={`assertive-${index}`}>{message}</span>
        ))}
      </div>
    </>
  )
}

/**
 * Hook to provide easy access to screen reader announcements
 */
export const useAriaLiveAnnouncer = () => {
  const announce = (message: string, level: 'polite' | 'assertive' = 'polite') => {
    if (typeof window !== 'undefined' && (window as any).announceToScreenReader) {
      ;(window as any).announceToScreenReader(message, level)
    }
  }

  return { announce }
}

/**
 * Utility functions for common editor announcements
 */
export const EditorAnnouncements = {
  formatApplied: (format: string) => `${format} applied`,
  formatRemoved: (format: string) => `${format} removed`,
  headingChanged: (level: number) => `Heading level ${level} applied`,
  listCreated: (type: 'bullet' | 'ordered') => `${type === 'bullet' ? 'Bullet' : 'Numbered'} list created`,
  tableInserted: () => 'Table inserted',
  linkAdded: () => 'Link added',
  imageInserted: () => 'Image inserted',
  textAligned: (alignment: string) => `Text aligned ${alignment}`,
  undoPerformed: () => 'Undone',
  redoPerformed: () => 'Redone',
  saveSuccess: () => 'Document saved successfully',
  saveError: () => 'Error saving document',
  contentLoaded: () => 'Document content loaded',
  focusEntered: () => 'Entered document editor',
  focusExited: () => 'Exited document editor',
}
