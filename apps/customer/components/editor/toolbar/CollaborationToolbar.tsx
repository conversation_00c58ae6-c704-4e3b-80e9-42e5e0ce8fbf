'use client'

import React from 'react'
import { Editor } from '@tiptap/react'
import { TiptapCollabProvider } from '@hocuspocus/provider'
import { Button } from '@ui/components/ui/button'
import { Separator } from '@ui/components/ui/separator'
import { Badge } from '@ui/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/ui/avatar'
import {
  History,
  MessageSquare,
  Settings,
  Share2,
  Users,
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'


interface CollaborationUser {
  id: string
  name: string
  email?: string
  avatar?: string
  color?: string
}

interface CollaborationToolbarProps {
  editor: Editor
  provider?: TiptapCollabProvider | null
  currentUser?: CollaborationUser
  activeUsers?: CollaborationUser[]
  isConnected?: boolean
  showComments?: boolean
  showHistory?: boolean
  onToggleComments?: () => void
  onToggleHistory?: () => void
  onShare?: () => void
  onSettings?: () => void

}

export function CollaborationToolbar({
  editor,
  provider,
  currentUser,
  activeUsers = [],
  isConnected = false,
  showComments = false,
  showHistory = false,
  onToggleComments,
  onToggleHistory,
  onShare,
  onSettings
}: CollaborationToolbarProps) {
  // Filter out current user from active users and limit display
  const otherUsers = activeUsers
    .filter(user => user.id !== currentUser?.id)
    .slice(0, 5) // Show max 5 users

  const totalOtherUsers = activeUsers.filter(user => user.id !== currentUser?.id).length
  const hasMoreUsers = totalOtherUsers > 5

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }



  return (
    <div
      className="border-b bg-muted/30 backdrop-blur supports-[backdrop-filter]:bg-muted/20"
      data-testid="collaboration-toolbar"
    >
      <div className="flex items-center justify-between p-2">
        {/* Left side - Collaboration status and users */}
        <div className="flex items-center gap-3">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            <div className={cn(
              'w-2 h-2 rounded-full',
              isConnected ? 'bg-green-500' : 'bg-red-500'
            )} />
            <span className="text-sm text-muted-foreground">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>

          <Separator orientation="vertical" className="h-4" />

          {/* Active Users */}
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4 text-muted-foreground" />
            <div className="flex items-center gap-1">
              <TooltipProvider>
                {otherUsers.map((user) => (
                  <Tooltip key={user.id}>
                    <TooltipTrigger>
                      <Avatar
                        className="w-6 h-6 border-2"
                        style={{ borderColor: user.color || '#3B82F6' }}
                        data-testid="collaborator-avatar"
                      >
                        <AvatarImage src={user.avatar} alt={user.name} />
                        <AvatarFallback className="text-xs" style={{ backgroundColor: user.color || '#3B82F6' }}>
                          {getInitials(user.name)}
                        </AvatarFallback>
                      </Avatar>
                    </TooltipTrigger>
                    <TooltipContent data-testid="user-tooltip">
                      <p>{user.name}</p>
                      {user.email && <p className="text-xs text-muted-foreground">{user.email}</p>}
                    </TooltipContent>
                  </Tooltip>
                ))}
                {hasMoreUsers && (
                  <Tooltip>
                    <TooltipTrigger>
                      <Badge variant="secondary" className="w-6 h-6 rounded-full p-0 text-xs">
                        +{totalOtherUsers - 5}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{totalOtherUsers - 5} more users</p>
                    </TooltipContent>
                  </Tooltip>
                )}
                {/* Current User Avatar */}
                {currentUser && (
                  <Tooltip>
                    <TooltipTrigger>
                      <Avatar
                        className="w-6 h-6 border-2 border-primary"
                        data-testid="current-user-avatar"
                      >
                        <AvatarImage src={currentUser.avatar} alt={currentUser.name} />
                        <AvatarFallback className="text-xs bg-primary text-primary-foreground">
                          {getInitials(currentUser.name)}
                        </AvatarFallback>
                      </Avatar>
                    </TooltipTrigger>
                    <TooltipContent data-testid="user-tooltip">
                      <p>{currentUser.name} (You)</p>
                      {currentUser.email && <p className="text-xs text-muted-foreground">{currentUser.email}</p>}
                    </TooltipContent>
                  </Tooltip>
                )}
              </TooltipProvider>
            </div>
            {totalOtherUsers === 0 && (
              <span className="text-xs text-muted-foreground">Only you</span>
            )}
          </div>
        </div>

        {/* Right side - Collaboration tools */}
        <div className="flex items-center gap-2">
          {/* Comments */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleComments}
            className={showComments ? 'bg-muted' : ''}
            title="Toggle Comments"
            data-testid="comments-button"
          >
            <MessageSquare className="w-4 h-4 mr-1" />
            Comments
          </Button>

          {/* History */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleHistory}
            className={showHistory ? 'bg-muted' : ''}
            title="Version History"
            data-testid="history-button"
          >
            <History className="w-4 h-4 mr-1" />
            History
          </Button>

          <Separator orientation="vertical" className="h-6" />

          {/* Share */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onShare}
            title="Share Document"
            data-testid="share-button"
          >
            <Share2 className="w-4 h-4 mr-1" />
            Share
          </Button>

          {/* Settings */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onSettings}
            title="Document Settings"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
