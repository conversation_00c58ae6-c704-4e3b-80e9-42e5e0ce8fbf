'use client'

import React, { useState } from 'react'
import { Editor } from '@tiptap/react'
import { Button } from '@ui/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@ui/components/ui/dropdown-menu'
import { Badge } from '@ui/components/ui/badge'
import { 
  Sparkles, 
  Wand2, 
  FileText, 
  Languages, 
  CheckCircle, 
  Lightbulb,
  MessageSquare,
  Loader2,
  ChevronDown,
} from 'lucide-react'
import { CustomAIProvider } from '../providers/CustomAIProvider'
import { cn } from '@utils/lib/utils'

interface AIToolbarProps {
  editor: Editor | null
  aiProvider: CustomAIProvider
  onOpenChat?: () => void
  className?: string
}

interface AICommand {
  id: string
  label: string
  icon: React.ReactNode
  prompt: string
  description: string
  requiresSelection?: boolean
}

const AI_COMMANDS: AICommand[] = [
  {
    id: 'improve',
    label: 'Improve Writing',
    icon: <Wand2 className="h-4 w-4" />,
    prompt: 'Improve the writing quality, clarity, and flow of this text while maintaining its original meaning and tone.',
    description: 'Enhance clarity and flow',
    requiresSelection: true,
  },
  {
    id: 'fix-grammar',
    label: 'Fix Grammar',
    icon: <CheckCircle className="h-4 w-4" />,
    prompt: 'Fix any grammar, spelling, and punctuation errors in this text.',
    description: 'Correct grammar and spelling',
    requiresSelection: true,
  },
  {
    id: 'make-shorter',
    label: 'Make Shorter',
    icon: <FileText className="h-4 w-4" />,
    prompt: 'Make this text more concise while preserving all key information and meaning.',
    description: 'Reduce length while keeping meaning',
    requiresSelection: true,
  },
  {
    id: 'make-longer',
    label: 'Expand',
    icon: <FileText className="h-4 w-4" />,
    prompt: 'Expand this text with more detail, examples, and explanation while maintaining the same tone and style.',
    description: 'Add more detail and examples',
    requiresSelection: true,
  },
  {
    id: 'change-tone',
    label: 'Change Tone',
    icon: <Languages className="h-4 w-4" />,
    prompt: 'Rewrite this text in a more professional and formal tone.',
    description: 'Adjust writing tone',
    requiresSelection: true,
  },
  {
    id: 'summarize',
    label: 'Summarize',
    icon: <FileText className="h-4 w-4" />,
    prompt: 'Create a concise summary of this text, highlighting the main points.',
    description: 'Create a summary',
    requiresSelection: true,
  },
  {
    id: 'continue',
    label: 'Continue Writing',
    icon: <Lightbulb className="h-4 w-4" />,
    prompt: 'Continue writing from where this text ends, maintaining the same style and tone.',
    description: 'Continue from current position',
    requiresSelection: false,
  },
  {
    id: 'brainstorm',
    label: 'Brainstorm Ideas',
    icon: <Lightbulb className="h-4 w-4" />,
    prompt: 'Generate creative ideas and suggestions related to this topic.',
    description: 'Generate related ideas',
    requiresSelection: false,
  },
]

export function AIToolbar({ editor, aiProvider, onOpenChat, className }: AIToolbarProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [activeCommand, setActiveCommand] = useState<string | null>(null)
  const [streamingContent, setStreamingContent] = useState('')

  const handleAICommand = async (command: AICommand) => {
    if (!editor || isGenerating) return

    const selection = editor.state.selection
    const selectedText = editor.state.doc.textBetween(selection.from, selection.to)

    // Check if command requires selection
    if (command.requiresSelection && !selectedText.trim()) {
      // Could show a toast or alert here
      console.warn('This command requires text selection')
      return
    }

    setIsGenerating(true)
    setActiveCommand(command.id)
    setStreamingContent('')

    try {
      // Get context around the selection or cursor
      const doc = editor.state.doc
      const contextStart = Math.max(0, selection.from - 500)
      const contextEnd = Math.min(doc.content.size, selection.to + 500)
      const context = doc.textBetween(contextStart, contextEnd)

      // Enable change tracking for AI edits
      editor.commands.setTrackChangeStatus(true)

      // Store the original selection position for applying changes
      const originalFrom = selection.from
      const originalTo = selection.to

      // Use streaming text generation for real-time feedback
      const stream = aiProvider.streamText({
        prompt: command.prompt,
        context,
        selection: selectedText,
        command: command.id,
      })

      let accumulatedText = ''
      for await (const chunk of stream) {
        accumulatedText += chunk
        setStreamingContent(accumulatedText)
      }

      // Apply the final result using change tracking
      if (accumulatedText.trim()) {
        // Create a simple JSON patch for the change
        // For now, we'll use a simplified approach that works with the current document structure
        const currentDoc = editor.getJSON()

        if (selectedText.trim()) {
          // For text replacement, we'll use the sophisticated change tracking
          // First, select the text to be replaced
          editor.chain()
            .focus()
            .setTextSelection({ from: originalFrom, to: originalTo })
            .run()

          // Then apply the change with tracking by directly manipulating the content
          // This will mark the old text as deleted and new text as inserted
          const tr = editor.state.tr

          // Mark the selected text for deletion
          tr.addMark(originalFrom, originalTo, editor.state.schema.marks.deletion.create({
            'data-op-user-id': 'ai',
            'data-op-user-nickname': 'AI Assistant',
            'data-op-date': new Date().toISOString(),
          }))

          // Insert the new text at the end of the selection
          tr.insertText(accumulatedText, originalTo)

          // Mark the new text as an insertion
          tr.addMark(
            originalTo,
            originalTo + accumulatedText.length,
            editor.state.schema.marks.insertion.create({
              'data-op-user-id': 'ai',
              'data-op-user-nickname': 'AI Assistant',
              'data-op-date': new Date().toISOString(),
            })
          )

          // Apply the transaction
          editor.view.dispatch(tr)
        } else {
          // For insertion at cursor, just insert with insertion mark
          const tr = editor.state.tr

          // Insert the new text
          tr.insertText(accumulatedText, originalFrom)

          // Mark it as an insertion
          tr.addMark(
            originalFrom,
            originalFrom + accumulatedText.length,
            editor.state.schema.marks.insertion.create({
              'data-op-user-id': 'ai',
              'data-op-user-nickname': 'AI Assistant',
              'data-op-date': new Date().toISOString(),
            })
          )

          // Apply the transaction
          editor.view.dispatch(tr)
        }
      }
    } catch (error) {
      console.error('AI command failed:', error)
      // Could show error toast here
    } finally {
      setIsGenerating(false)
      setActiveCommand(null)
      setStreamingContent('')
    }
  }

  const getAvailableCommands = () => {
    if (!editor) return []

    const selection = editor.state.selection
    const hasSelection = selection.from !== selection.to

    return AI_COMMANDS.filter(command => {
      if (command.requiresSelection) {
        return hasSelection
      }
      return true
    })
  }

  const availableCommands = getAvailableCommands()

  return (
    <div className={cn('flex items-center gap-2', className)} data-testid="ai-toolbar">
      {/* Individual AI Command Buttons */}
      {AI_COMMANDS.map((command) => (
        <Button
          key={command.id}
          variant="ghost"
          size="sm"
          onClick={() => handleAICommand(command)}
          disabled={isGenerating || (command.requiresSelection && (!editor?.state.selection || editor?.state.selection.from === editor?.state.selection.to))}
          className="flex items-center gap-2"
          title={command.label}
          data-testid={`ai-command-${command.id}`}
        >
          {command.icon}
          {activeCommand === command.id && isGenerating && (
            <Loader2 className="h-3 w-3 animate-spin ml-1" />
          )}
        </Button>
      ))}

      {/* AI Generation Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            disabled={!editor || isGenerating}
            className="gap-2"
          >
            {isGenerating ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Sparkles className="h-4 w-4" />
            )}
            AI Tools
            <ChevronDown className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-64">
          {availableCommands.map((command) => (
            <DropdownMenuItem
              key={command.id}
              onClick={() => handleAICommand(command)}
              disabled={isGenerating}
              className="flex flex-col items-start gap-1 p-3"
            >
              <div className="flex items-center gap-2 w-full">
                {command.icon}
                <span className="font-medium">{command.label}</span>
                {activeCommand === command.id && (
                  <Loader2 className="h-3 w-3 animate-spin ml-auto" />
                )}
              </div>
              <span className="text-xs text-gray-500">{command.description}</span>
              {command.requiresSelection && (
                <Badge variant="outline" className="text-xs">
                  Requires selection
                </Badge>
              )}
            </DropdownMenuItem>
          ))}
          
          {availableCommands.length === 0 && (
            <DropdownMenuItem disabled className="text-center text-gray-500">
              Select text to use AI tools
            </DropdownMenuItem>
          )}
          
          <DropdownMenuSeparator />
          
          {onOpenChat && (
            <DropdownMenuItem onClick={onOpenChat} className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Open AI Chat
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Quick AI Chat Button */}
      {onOpenChat && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onOpenChat}
          className="gap-2"
          disabled={!editor}
          title="AI Chat"
          data-testid="ai-chat-button"
        >
          <MessageSquare className="h-4 w-4" />
          AI Chat
        </Button>
      )}

      {/* Status indicator */}
      {isGenerating && (
        <div className="flex items-center gap-2 text-sm text-gray-600" data-testid="ai-processing">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>
            {streamingContent ? 'Streaming...' : 'Generating...'}
            {streamingContent && (
              <span className="ml-1 text-xs opacity-75">
                ({streamingContent.length} chars)
              </span>
            )}
          </span>
        </div>
      )}
    </div>
  )
}
