'use client'

import React, { useEffect, useState } from 'react'
import { Button } from '@ui/components/ui/button'
import { ChevronUp } from 'lucide-react'
import { cn } from '@utils/lib/utils'

interface BackToTopProps {
  /**
   * Optional custom class name
   */
  className?: string

  /**
   * Threshold in pixels to show the button (default: viewport height)
   */
  threshold?: number

  /**
   * Position of the button (default: 'bottom-right')
   */
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center'

  /**
   * Z-index for the button (default: 50)
   */
  zIndex?: number
}

/**
 * A reusable "Back to Top" button component that appears when scrolling past the threshold
 */
export function BackToTop({
  className,
  threshold,
  position = 'bottom-right',
  zIndex = 50
}: BackToTopProps) {
  const [isVisible, setIsVisible] = useState(false)

  // Set default threshold to viewport height on client side
  const [scrollThreshold, setScrollThreshold] = useState(threshold || 0)

  useEffect(() => {
    // Set default threshold to viewport height if not provided
    if (!threshold) {
      setScrollThreshold(window.innerHeight)
    }

    // Function to handle scroll event
    const handleScroll = () => {
      setIsVisible(window.scrollY > scrollThreshold)
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true })

    // Initial check
    handleScroll()

    // Clean up event listener
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [threshold, scrollThreshold])

  // Function to scroll back to top
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'bottom-center': 'bottom-6 left-1/2 transform -translate-x-1/2'
  }

  return isVisible ? (
    <Button
      onClick={scrollToTop}
      variant="outline"
      size="icon"
      className={cn(
        'fixed glass-effect-subtle rounded-full w-12 h-12 shadow-md transition-all duration-300',
        'hover:translate-y-[-4px] hover:shadow-lg',
        'focus:outline-none focus:ring-2 focus:ring-primary',
        positionClasses[position],
        className,
        // Apply z-index as a style prop instead of a class to avoid template literal in className
      )}
      style={{ zIndex }}
      aria-label="Back to top"
    >
      <ChevronUp className="h-6 w-6" />
    </Button>
  ) : null
}
