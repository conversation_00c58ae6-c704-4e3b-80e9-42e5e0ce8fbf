'use client';

import { type ElementRef, useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createPortal } from 'react-dom'
import { Button } from '@ui/components/ui/button'
import { AnimatePresence, motion } from 'framer-motion'

interface SimpleModalProps {
    children: React.ReactNode;
    title?: string;
}

export function SimpleModal({ children, title }: SimpleModalProps) {
    const router = useRouter();
    const dialogRef = useRef<ElementRef<'dialog'>>(null);
    const previouslyFocusedElement = useRef<HTMLElement | null>(null);
    const [isOpen, setIsOpen] = useState(true);

    useEffect(() => {
        const dialog = dialogRef.current;

        if (dialog && !dialog.open) {
            previouslyFocusedElement.current = document.activeElement as HTMLElement;
            dialog.showModal();
        }

        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                onDismiss();
            } else if (e.key === 'Tab') {
                const focusableElements = dialog?.querySelectorAll<HTMLElement>(
                    'a[href], button:not([disabled]), textarea, input, select, [tabindex]:not([tabindex="-1"])'
                );
                if (focusableElements && focusableElements.length > 0) {
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];

                    if (e.shiftKey) {
                        if (document.activeElement === firstElement) {
                            e.preventDefault();
                            lastElement.focus();
                        }
                    } else {
                        if (document.activeElement === lastElement) {
                            e.preventDefault();
                            firstElement.focus();
                        }
                    }
                }
            }
        };

        dialog?.addEventListener('keydown', handleKeyDown);

        const originalOverflow = document.body.style.overflow;
        document.body.style.overflow = 'hidden';

        return () => {
            dialog?.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = originalOverflow;
            previouslyFocusedElement.current?.focus();
        };
    }, []);

    function onDismiss() {
        setIsOpen(false); // Trigger exit animation
        setTimeout(() => {
            const dialog = dialogRef.current;
            if (dialog?.open) {
                dialog.close();
                router.back();
            }
        }, 300); // Match this duration with the `exit` animation duration
    }

    return createPortal(
        <AnimatePresence>
            {isOpen && (
                <motion.div
                  className="fixed inset-0 z-50 grid place-items-center  bg-black/50 dark:bg-black/70 backdrop-blur-sm"
                    aria-hidden="true"
                    onClick={onDismiss}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                >
                    <motion.dialog
                        ref={dialogRef}
                        className="relative block mx-auto glass-effect-strong-lit shadow-xl p-6 max-w-4xl mx-4"
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.8, opacity: 0 }}
                        transition={{ duration: 0.25 }}
                        onClose={onDismiss}
                        aria-labelledby="modal-title"
                        role="dialog"
                    >
                        <div className="flex items-start justify-between">
                            {title && (
                                <h2
                                    id="modal-title"
                                    className="text-lg font-semibold text-zinc-900 dark:text-zinc-100"
                                >
                                    {title}
                                </h2>
                            )}
                        </div>
                        <div className="absolute top-0 right-0 p-2 text-zinc-500 dark:text-zinc-400 focus:outline-none">
                            <button
                                onClick={onDismiss}
                                className="text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-300 focus:outline-none"
                                aria-label="Close modal"
                            >
                                ✕
                            </button>
                        </div>
                        <div className="mt-4 z-100 text-zinc-700 dark:text-zinc-300 max-h-[80vh] overflow-scroll max-height-[80vh]">{children}</div>
                        <div className="mt-6 flex justify-end space-x-4">
                            <Button
                                onClick={onDismiss}
                                className="px-4 py-2 text-sm font-medium text-zinc-700 bg-zinc-100 rounded hover:bg-zinc-200 dark:text-zinc-200 dark:bg-zinc-700 dark:hover:bg-zinc-600"
                                data-testid="modal-close-button"
                            >
                                Close
                            </Button>
                        </div>
                    </motion.dialog>
                </motion.div>
            )}
        </AnimatePresence>,
        document.getElementById('simple-modal-root')!
    );
}
