// Interface to match the Python XferClaimModel class
import { CitationType } from '@/components/citation'

export interface XferClaimModel {
  id: number;
  statement_id: number;
  entity_xid: string;
  valid_claim: boolean;
  greenwashing: boolean;
  verdict: string;
  summary: string;
  conclusion: string;
  confidence: number;
  text: string;
  statement_text?: string; // The text of the statement the claim is about
  context?: string;
  claim_doc: string;
  claim_doc_year: number;
  counters: any;
  citations: CitationType[];
  llm_greenwashing: boolean;
  importance: number; // Importance of the claim (0-100)
  company: string;
  company_id: number;
  esg_claim: boolean;
  created_at?: string; // ISO date string
}

// Interface for the xfer_gw_claims_v2 table row
export interface ClaimTypeV2 {
  id: number;
  entity_xid: string;
  run_id: number;
  statement_id: number;
  verified: boolean | null;
  model: XferClaimModel;
}
