// Statement and metadata types
export interface StatementMetadata {
  impact_value?: number;
  impact_text?: string;
  sentiment?: number;
  sentiment_text?: string;
  credibility?: number;
  year?: number;
  domain?: string;
  [key: string]: any; // Allow for additional metadata fields
}

export interface StatementAndMetadata {
  statement_id: number;
  statement_text: string;
  source_text?: string;  // Added source_text field
  metadata?: StatementMetadata;
  citations?: any[];
  [key: string]: any; // Allow for additional fields
}

// Other common types can be added here
