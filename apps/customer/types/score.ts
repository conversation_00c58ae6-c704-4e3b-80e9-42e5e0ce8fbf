import { Json } from '@/database.types';

// Interface to match the Python XferScoreModel class
export interface XferScoreModel {
  entity_xid: string;
  score: number;
  rating_text: string;
  minor_major_text: string;
  red_flags_count: number;
  green_flags_count: number;
  red_flags_score: number;
  green_flags_score: number;
  average_red: number;
  average_green: number;
  median_red: number;
  median_green: number;
  created_at?: string; // ISO date string
}

// Interface for the xfer_score_v2 table row
export interface ScoreTypeV2 {
  entity_xid: string;
  run_id: number;
  score: number;
  model: XferScoreModel;
}
