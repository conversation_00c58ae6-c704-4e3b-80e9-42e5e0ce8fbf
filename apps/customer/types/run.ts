import { Database } from '@/database.types'

// Interface to match the Python XferRunModel class
export interface XferRunModel {
  id: number;
  run_type: string;
  scope: string;
  target?: string;
  start_year: number;
  end_year?: number;
  models: string[];
  status: string;
  created_at: string; // ISO date string
  updated_at?: string; // ISO date string
  completed_at?: string; // ISO date string
  prev_run_id?: number;
  version?: string;
}

type BaseType=Database["public"]["Tables"]["xfer_runs_v2"]["Row"];

// @ts-ignore
export interface V2RunType  extends BaseType{
  model: XferRunModel;
}
