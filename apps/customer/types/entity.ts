// Enum to match the Python EntityType enum
export enum EntityType {
  COMPANY = 'COMPANY',
  ORGANIZATION = 'ORGANIZATION',
  PERSON = 'PERSON',
  PRODUCT = 'PRODUCT',
  LOCATION = 'LOCATION',
  EVENT = 'EVENT',
  OTHER = 'OTHER'
}

// Base entity interface to match the Python Entity class
export interface Entity {
  id: number;
  name: string;
  short_id: string;
  eko_id: string;
  type: EntityType;
}

// Interface to match the Python VirtualEntityModel class
export interface VirtualEntityModel {
  id: number;
  name: string;
  eko_id: string;
  type: EntityType;
  created_at: string; // ISO date string
  short_id: string;
  common_names?: string[];
  leis?: string[];
  legal_names?: string[];
  lei_records?: any;
  title?: string;
  aka?: string[];
  description?: string;
  updated_at?: string; // ISO date string
  entity_regex?: string;
  search_phrase?: string;
}

// Interface to match the Python VirtualEntityExpandedModel class
export interface VirtualEntityExpandedModel extends VirtualEntityModel {
  base_entities: Entity[];
}

// Interface for the xfer_entities_v2 table row
export interface V2EntityType {
  id: number;
  entity_type: string;
  entity_xid: string;
  name: string;
  short_id: string;
  model: VirtualEntityExpandedModel;
  run_id: number;
  type: string | null;
}
