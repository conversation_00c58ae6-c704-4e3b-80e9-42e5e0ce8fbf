/**
 * Types for prediction-v2 data
 */

export enum ComponentType {
  MOTIVATION = 'motivation',
  STATEMENT_TYPE = 'statement_type',
  ENGAGEMENT = 'engagement',
  IMPACT = 'impact'
}

/**
 * Component-level prediction analysis
 */
export interface PredictiveComponentAnalysis {
  id: number;
  run_id: number;
  prediction_id: number;
  virtual_entity_id: number;
  cluster_id: number;
  component_type: ComponentType;
  year: number;
  summary: string;
  detailed_analysis: string;
  potential_risks: string[];
  potential_opportunities: string[];
  confidence: number;
  created_at: string;
}

/**
 * Cluster-level combined analysis
 */
export interface ClusterAnalysis {
  id: number;
  run_id: number;
  virtual_entity_id: number;
  cluster_id: number;
  year: number;
  summary: string;
  detailed_analysis: string;
  motivation_summary: string;
  statement_type_summary: string;
  engagement_summary: string;
  impact_summary: string;
  potential_risks: string[];
  potential_opportunities: string[];
  confidence: number;
  created_at: string;
}

/**
 * Entity-year combined analysis
 */
export interface EntityYearAnalysis {
  id: number;
  run_id: number;
  virtual_entity_id: number;
  year: number;
  summary: string;
  detailed_analysis: string;
  cluster_summaries: Record<string, string>;
  potential_risks: string[];
  potential_opportunities: string[];
  confidence: number;
  created_at: string;
}

/**
 * Response from the API for component analysis
 */
export interface PredictiveComponentResponse {
  id: number;
  entity_xid: string;
  run_id: number;
  cluster_id: number;
  component_type: ComponentType;
  year: number;
  model: PredictiveComponentAnalysis;
  created_at: string;
}

/**
 * Response from the API for cluster analysis
 */
export interface ClusterAnalysisResponse {
  id: number;
  entity_xid: string;
  run_id: number;
  cluster_id: number;
  year: number;
  model: ClusterAnalysis;
  created_at: string;
}

/**
 * Response from the API for entity-year analysis
 */
export interface EntityYearAnalysisResponse {
  id: number;
  entity_xid: string;
  run_id: number;
  year: number;
  model: EntityYearAnalysis;
  created_at: string;
}
