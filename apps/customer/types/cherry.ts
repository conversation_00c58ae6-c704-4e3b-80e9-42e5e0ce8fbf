import { StatementAndMetadata } from './types'
import { CitationType } from '@/components/citation'

// Interface for the model field in XferCherryTypeV2
export interface CherryModel {
  entity_xid: string;
  label: string;
  negative_statements: StatementAndMetadata[];
  positive_statements: StatementAndMetadata[];
  model: 'cherry_picking' | 'flooding';
  score: number;
  explanation: string;
  analysis: string;
  reason?: string;
  citations: CitationType[];
  analysis_model?: string;
  // LLM-generated metrics
  severity: number;
  confidence: number;
  authenticity: number;
  created_at?: string; // ISO date string
  domain_vector?: any;
}

// Interface to match the Python XferCherryModel class
export interface CherryTypeV2 {
  id: number;
  run_id: number;
  entity_xid: string;
  label: string;
  model: CherryModel;
  created_at?: string; // ISO date string
}
