# Test info

- Name: EKO-135: Move charts to Shadcn/Recharts >> should render Recharts-based charts when chart data is present
- Location: /Users/<USER>/claude_workdirs/issue-test-135/repo/apps/customer/tests/issues/issue-eko-135.spec.ts:12:7

# Error details

```
TimeoutError: page.waitForURL: Timeout 60000ms exceeded.
=========================== logs ===========================
waiting for navigation until "load"
============================================================
    at TestUtils.createDocumentFromTemplate (/Users/<USER>/claude_workdirs/issue-test-135/repo/apps/customer/tests/helpers/tests/test-utils.ts:101:21)
    at /Users/<USER>/claude_workdirs/issue-test-135/repo/apps/customer/tests/issues/issue-eko-135.spec.ts:13:5
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link:
      - /url: /
      - img
      - img
- text: Analysis
- list:
  - listitem:
    - img
    - text: Dashboard
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Dashboard":
          - /url: /customer/dashboard
          - img
          - text: Dashboard
      - listitem:
        - link "Flags":
          - /url: /customer/dashboard/flags
          - img
          - text: Flags
      - listitem:
        - link "Cherry Picking":
          - /url: /customer/dashboard/gw/cherry
          - img
          - text: Cherry Picking
      - listitem:
        - link "Claims":
          - /url: /customer/dashboard/gw/claims
          - img
          - text: Claims
      - listitem:
        - link "Promises":
          - /url: /customer/dashboard/gw/promises
          - img
          - text: Promises
  - listitem:
    - img
    - text: Documents
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Create":
          - /url: /customer/documents/new
          - img
          - text: Create
      - listitem:
        - link "View":
          - /url: /customer/documents
          - img
          - text: View
  - listitem:
    - img
    - text: Analyse
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Company Analysis":
          - /url: /customer/analysis/companies
          - img
          - text: Company Analysis
      - listitem:
        - link "Usage":
          - /url: /customer/analysis/usage
          - img
          - text: Usage
  - listitem:
    - img
    - text: Models
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Doughnut Economics":
          - /url: /customer/models/doughnut
          - img
          - text: Doughnut Economics
      - listitem:
        - link "UN SDG":
          - /url: /customer/models/sdg
          - img
          - text: UN SDG
      - listitem:
        - link "Plant Based Treaty":
          - /url: /customer/models/plant_based_treaty
          - img
          - text: Plant Based Treaty
  - listitem:
    - img
    - text: Documentation
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Introduction":
          - /url: "#"
      - listitem:
        - link "Get Started":
          - /url: "#"
      - listitem:
        - link "Tutorials":
          - /url: "#"
      - listitem:
        - link "Changelog":
          - /url: "#"
- list:
  - listitem:
    - link "Support":
      - /url: /customer/account/contact/support
      - img
      - text: Support
  - listitem:
    - link "Feedback":
      - /url: /customer/account/contact/feedback
      - img
      - text: Feedback
- list:
  - listitem:
    - button "t <EMAIL> <EMAIL>":
      - text: t <EMAIL> <EMAIL>
      - img
- main:
  - region "Notifications (F8)":
    - list
  - main:
    - heading "Documents" [level=1]
    - paragraph: Create and manage your documents
    - button "New Document":
      - img
      - text: New Document
    - img
    - textbox "Search documents..."
    - button "My Documents"
    - button "Shared"
    - img
    - heading "No documents yet" [level=3]
    - paragraph: Create your first document to get started
    - button "Create Document":
      - img
      - text: Create Document
  - paragraph: © 2024 Eko Intelligence Ltd.
  - link "Privacy Policy":
    - /url: /policy/privacy
  - link "Terms of Service":
    - /url: /policy/tos
- alert
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 2 Issue
- button "Collapse issues badge":
  - img
```

# Test source

```ts
   1 | import { expect, Page } from '@playwright/test'
   2 | import { getAuthCredentials, getTestTemplate } from '../../test-config'
   3 |
   4 | /**
   5 |  * Common test utilities for the report system
   6 |  */
   7 |
   8 | export class TestUtils {
   9 |   constructor(private page: Page) {}
   10 |
   11 |   /**
   12 |    * Login with test credentials
   13 |    */
   14 |   async login(email?: string, password?: string) {
   15 |     const credentials = getAuthCredentials();
   16 |     const loginEmail = email || credentials.email;
   17 |     const loginPassword = password || credentials.password;
   18 |
   19 |     // Retry navigation if server is still starting up
   20 |     let retries = 5;
   21 |     while (retries > 0) {
   22 |       try {
   23 |         await this.page.goto('/login?next=%2Fcustomer', { timeout: 30000 });
   24 |         await this.page.waitForLoadState('networkidle', { timeout: 30000 });
   25 |         break;
   26 |       } catch (error) {
   27 |         retries--;
   28 |         if (retries === 0) throw error;
   29 |         console.log(`Login navigation failed, retrying... (${retries} attempts left)`);
   30 |         await this.page.waitForTimeout(2000);
   31 |       }
   32 |     }
   33 |
   34 |     await this.page.fill('#email', loginEmail);
   35 |     await this.page.fill('#password', loginPassword);
   36 |     await this.page.click('button[type="submit"]');
   37 |     
   38 |     // Wait for either /customer or /customer/dashboard - be more flexible
   39 |     try {
   40 |       await this.page.waitForURL(/\/customer/, { timeout: 30000 });
   41 |     } catch (error) {
   42 |       // If direct URL wait fails, check if we're actually on a customer page
   43 |       const currentUrl = this.page.url();
   44 |       if (!currentUrl.includes('/customer')) {
   45 |         throw new Error(`Login failed - expected customer URL but got: ${currentUrl}`);
   46 |       }
   47 |       // If we're on a customer page, consider it successful
   48 |       console.log(`Login successful, redirected to: ${currentUrl}`);
   49 |     }
   50 |   }
   51 |
   52 |   /**
   53 |    * Create a new document from template
   54 |    */
   55 |   async createDocumentFromTemplate(templateName?: string) {
   56 |     const template = templateName || getTestTemplate('primary') || 'Blank Document';
   57 |     console.log(`Creating document from template: ${template}`)
   58 |
   59 |     await this.page.goto('/customer/documents', { timeout: 60000 });
   60 |
   61 |     // Wait for page to load completely
   62 |     await this.page.waitForLoadState('networkidle', { timeout: 5000 })
   63 |
   64 |     // Wait for authentication and data loading
   65 |     await this.page.waitForFunction(() => {
   66 |       // Use vanilla JS instead of Playwright selectors
   67 |       const buttons = Array.from(document.querySelectorAll('button, a'))
   68 |       return buttons.some(btn => btn.textContent && btn.textContent.includes('New Document'))
   69 |     }, { timeout: 30000 })
   70 |
   71 |     // Click New Document button
   72 |     await this.page.click('[data-testid="new-document-button"]')
   73 |     console.log('Clicked New Document button')
   74 |
   75 |     // Wait for template dialog to appear with better selectors
   76 |     await this.page.waitForSelector('[role="dialog"], .template-dialog, .modal, [data-testid="template-dialog"]', { timeout: 20000 })
   77 |     console.log('Template dialog appeared')
   78 |
   79 |     // Wait for template data to load (entities and model sections)
   80 |     await this.page.waitForFunction(() => {
   81 |       const dialog = document.querySelector('[role="dialog"], .template-dialog, .modal')
   82 |       return dialog && dialog.textContent && dialog.textContent.includes('EKO Report')
   83 |     }, { timeout: 30000 })
   84 |
   85 |     // Use data-testid for template selection - map template names to actual IDs
   86 |     const templateNameToId: Record<string, string> = {
   87 |       'blank document': 'blank',
   88 |       'meeting notes': 'meeting-notes',
   89 |       'project report': 'project-report',
   90 |       'eko report': 'eko-report'
   91 |     };
   92 |     
   93 |     const templateId = templateNameToId[template.toLowerCase()] || template.toLowerCase().replace(/\s+/g, '-');
   94 |     const templateSelector = `[data-testid="template-${templateId}"]`;
   95 |
   96 |     // Wait for template to be available and click it
   97 |     await this.page.click(templateSelector)
   98 |     console.log(`Clicked template: ${template}`)
   99 |
  100 |     // Wait for navigation to document editor
> 101 |     await this.page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 60000 })
      |                     ^ TimeoutError: page.waitForURL: Timeout 60000ms exceeded.
  102 |     console.log('Navigated to document editor')
  103 |
  104 |     // Wait for editor to load
  105 |     await this.waitForEditor(30000)
  106 |     console.log('Editor loaded successfully')
  107 |
  108 |     return this.getDocumentIdFromUrl();
  109 |   }
  110 |
  111 |   /**
  112 |    * Get document ID from current URL
  113 |    */
  114 |   getDocumentIdFromUrl(): string {
  115 |     const url = this.page.url();
  116 |     const match = url.match(/\/documents\/([a-f0-9-]+)/);
  117 |     if (!match) {
  118 |       throw new Error('Could not extract document ID from URL');
  119 |     }
  120 |     return match[1];
  121 |   }
  122 |
  123 |   /**
  124 |    * Wait for report component to finish loading
  125 |    */
  126 |   async waitForComponentLoading(componentSelector = '.report-section') {
  127 |     const component = this.page.locator(componentSelector).first();
  128 |     
  129 |     // Wait for loading to start (spinner appears)
  130 |     await expect(component.locator('.animate-spin')).toBeVisible({ timeout: 5000 });
  131 |     
  132 |     // Wait for loading to complete (spinner disappears)
  133 |     await expect(component.locator('.animate-spin')).not.toBeVisible({ timeout: 30000 });
  134 |   }
  135 |
  136 |   /**
  137 |    * Open component configuration dialog
  138 |    */
  139 |   async openComponentConfig(componentSelector = '.report-section') {
  140 |     const component = this.page.locator(componentSelector).first();
  141 |     
  142 |     // Use specific test ID based on component type
  143 |     let testId: string;
  144 |     if (componentSelector.includes('summary')) {
  145 |       testId = 'report-summary-menu-trigger';
  146 |     } else {
  147 |       testId = 'report-section-menu-trigger';
  148 |     }
  149 |     
  150 |     const menuTrigger = component.locator(`[data-testid="${testId}"]`);
  151 |     await menuTrigger.click();
  152 |     await this.page.click('text=Configure');
  153 |
  154 |     // Wait for dialog to open
  155 |     await expect(this.page.locator('[role="dialog"]')).toBeVisible();
  156 |   }
  157 |
  158 |   /**
  159 |    * Fill component configuration form
  160 |    */
  161 |   async fillComponentConfig(config: {
  162 |     id?: string;
  163 |     title?: string;
  164 |     prompt?: string;
  165 |     entity?: string;
  166 |     model?: string;
  167 |     section?: string;
  168 |   }) {
  169 |     if (config.id) {
  170 |       await this.page.fill('input[placeholder="component-id"]', config.id);
  171 |     }
  172 |     if (config.title) {
  173 |       await this.page.fill('input[placeholder="Component Title"]', config.title);
  174 |     }
  175 |     if (config.prompt) {
  176 |       await this.page.fill('textarea[placeholder*="Additional instructions"]', config.prompt);
  177 |     }
  178 |     if (config.entity) {
  179 |       await this.page.click('text=Select Entity');
  180 |       await this.page.click(`text=${config.entity}`);
  181 |     }
  182 |     if (config.model) {
  183 |       await this.page.click('text=Select Model');
  184 |       await this.page.click(`text=${config.model}`);
  185 |     }
  186 |     if (config.section) {
  187 |       await this.page.click('text=Select Section');
  188 |       await this.page.click(`text=${config.section}`);
  189 |     }
  190 |   }
  191 |
  192 |   /**
  193 |    * Confirm component configuration
  194 |    */
  195 |   async confirmComponentConfig() {
  196 |     // Use data-testid for Create/Update Component button
  197 |     const confirmButton = this.page.locator('[data-testid="create-component-button"]');
  198 |     await confirmButton.click();
  199 |     await expect(this.page.locator('[role="dialog"]')).not.toBeVisible();
  200 |   }
  201 |
```