# Test info

- Name: EKO-135: Move charts to Shadcn/Recharts >> should render different chart types correctly
- Location: /Users/<USER>/claude_workdirs/issue-test-135/repo/apps/customer/tests/issues/issue-eko-135.spec.ts:109:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toHaveCount(expected)

Locator: locator('[data-testid="chart-container"]')
Expected: 3
Received: 0
Call log:
  - expect.toHaveCount with timeout 5000ms
  - waiting for locator('[data-testid="chart-container"]')
    8 × locator resolved to 0 elements
      - unexpected value "0"

    at /Users/<USER>/claude_workdirs/issue-test-135/repo/apps/customer/tests/issues/issue-eko-135.spec.ts:161:67
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link:
      - /url: /
      - img
      - img
- text: Analysis
- list:
  - listitem:
    - img
    - text: Dashboard
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Dashboard":
          - /url: /customer/dashboard
          - img
          - text: Dashboard
      - listitem:
        - link "Flags":
          - /url: /customer/dashboard/flags
          - img
          - text: Flags
      - listitem:
        - link "Cherry Picking":
          - /url: /customer/dashboard/gw/cherry
          - img
          - text: Cherry Picking
      - listitem:
        - link "Claims":
          - /url: /customer/dashboard/gw/claims
          - img
          - text: Claims
      - listitem:
        - link "Promises":
          - /url: /customer/dashboard/gw/promises
          - img
          - text: Promises
  - listitem:
    - img
    - text: Documents
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Create":
          - /url: /customer/documents/new
          - img
          - text: Create
      - listitem:
        - link "View":
          - /url: /customer/documents
          - img
          - text: View
  - listitem:
    - img
    - text: Analyse
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Company Analysis":
          - /url: /customer/analysis/companies
          - img
          - text: Company Analysis
      - listitem:
        - link "Usage":
          - /url: /customer/analysis/usage
          - img
          - text: Usage
  - listitem:
    - img
    - text: Models
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Doughnut Economics":
          - /url: /customer/models/doughnut
          - img
          - text: Doughnut Economics
      - listitem:
        - link "UN SDG":
          - /url: /customer/models/sdg
          - img
          - text: UN SDG
      - listitem:
        - link "Plant Based Treaty":
          - /url: /customer/models/plant_based_treaty
          - img
          - text: Plant Based Treaty
  - listitem:
    - img
    - text: Documentation
    - button "Toggle" [expanded]:
      - img
      - text: Toggle
    - list:
      - listitem:
        - link "Introduction":
          - /url: "#"
      - listitem:
        - link "Get Started":
          - /url: "#"
      - listitem:
        - link "Tutorials":
          - /url: "#"
      - listitem:
        - link "Changelog":
          - /url: "#"
- list:
  - listitem:
    - link "Support":
      - /url: /customer/account/contact/support
      - img
      - text: Support
  - listitem:
    - link "Feedback":
      - /url: /customer/account/contact/feedback
      - img
      - text: Feedback
- list:
  - listitem:
    - button "t <EMAIL> <EMAIL>":
      - text: t <EMAIL> <EMAIL>
      - img
- main:
  - region "Notifications (F8)":
    - list
  - main:
    - button "Back":
      - img
      - text: Back
    - heading "EKO Report" [level=1]
    - text: John Lewis Latest Run
    - status: Document content loaded
    - alert
    - button "Undo":
      - img
    - button "Redo" [disabled]:
      - img
    - combobox: Heading 2
    - button "Bold":
      - img
    - button "Italic":
      - img
    - button "Underline":
      - img
    - button "Strikethrough":
      - img
    - button "Inline Code":
      - img
    - button "Highlight":
      - img
    - button "Superscript":
      - img
    - button "Subscript":
      - img
    - button "Align Left":
      - img
    - button "Align Center":
      - img
    - button "Align Right":
      - img
    - button "Justify":
      - img
    - button "Bullet List":
      - img
    - button "Numbered List":
      - img
    - button "Quote":
      - img
    - button "Horizontal Rule":
      - img
    - button "Insert Table":
      - img
    - button "Insert Image":
      - img
    - button "Insert Link":
      - img
    - button "Insert Table of Contents":
      - img
    - button "Insert Report Section":
      - img
    - button "Insert Report Group":
      - img
    - button "Insert Report Summary":
      - img
    - button "Save":
      - img
      - text: Save
    - button "Print":
      - img
      - text: Print
    - button "Export":
      - img
      - text: Export
    - img
    - text: Connected
    - img
    - text: 0 online
    - button "Comments":
      - img
      - text: Comments
    - button "History":
      - img
      - text: History
    - button "Share":
      - img
      - text: Share
    - button "Export":
      - img
      - text: Export
    - button "Settings":
      - img
    - button "Improve Writing" [disabled]:
      - img
    - button "Fix Grammar" [disabled]:
      - img
    - button "Make Shorter" [disabled]:
      - img
    - button "Expand" [disabled]:
      - img
    - button "Change Tone" [disabled]:
      - img
    - button "Summarize" [disabled]:
      - img
    - button "Continue Writing":
      - img
    - button "Brainstorm Ideas":
      - img
    - button "AI Tools":
      - img
      - text: AI Tools
      - img
    - button "AI Chat":
      - img
      - text: AI Chat
    - textbox "Document editor":
      - heading "Table of Contents" [level=3]
      - navigation:
        - button "Ecological Impact"
        - button "Social Impact"
        - button "Governance"
        - button "Data Reliability"
        - button "Transparency & Disclosure"
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - heading "Ecological Impact" [level=2]
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - heading "Social Impact" [level=2]
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - heading "Governance" [level=2]
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - heading "Data Reliability" [level=2]
      - img
      - img
      - button:
        - img
      - img
      - img
      - button:
        - img
      - heading "Transparency & Disclosure" [level=2]
      - img
      - img
      - button:
        - img
  - paragraph: © 2024 Eko Intelligence Ltd.
  - link "Privacy Policy":
    - /url: /policy/privacy
  - link "Terms of Service":
    - /url: /policy/tos
- alert: ekoIntelligence - shining a light on investments
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- button "Collapse issues badge":
  - img
```

# Test source

```ts
   61 |     }
   62 |     
   63 |     // Insert chart using TipTap commands
   64 |     await page.evaluate((data) => {
   65 |       const editor = (window as any).testEditor
   66 |       const encodedData = btoa(JSON.stringify(data))
   67 |       editor.commands.insertContent(`<chart data-json="${encodedData}"></chart>`)
   68 |     }, legacyChartData)
   69 |     
   70 |     // Wait for chart container to appear
   71 |     await expect(page.locator('[data-testid="chart-container"]')).toBeVisible()
   72 |     
   73 |     // Verify eCharts renderer is used (legacy format)
   74 |     await expect(page.locator('[data-testid="echarts-chart"]')).toBeVisible()
   75 |     
   76 |     // Verify this is a legacy chart by checking for absence of Recharts elements
   77 |     await expect(page.locator('[data-testid="recharts-chart"]')).toHaveCount(0)
   78 |     
   79 |     // Verify no chart errors
   80 |     await expect(page.locator('[data-testid="chart-error"]')).toHaveCount(0)
   81 |   })
   82 |
   83 |   test('should display error for invalid chart data', async ({ page }) => {
   84 |     await testUtils.createDocumentFromTemplate('EKO Report')
   85 |     await testUtils.waitForEditor()
   86 |     
   87 |     // Create invalid chart data
   88 |     const invalidChartData = 'invalid json{'
   89 |     
   90 |     // Insert chart using TipTap commands
   91 |     await page.evaluate((data) => {
   92 |       const editor = (window as any).testEditor
   93 |       const encodedData = btoa(data)
   94 |       editor.commands.insertContent(`<chart data-json="${encodedData}"></chart>`)
   95 |     }, invalidChartData)
   96 |     
   97 |     // Wait for error message to appear
   98 |     await expect(page.locator('[data-testid="chart-error"]')).toBeVisible()
   99 |     
  100 |     // Verify error message content
  101 |     const errorMessage = page.locator('[data-testid="chart-error"]')
  102 |     await expect(errorMessage).toContainText('Invalid chart JSON')
  103 |     
  104 |     // Verify no chart containers or other chart elements appear
  105 |     await expect(page.locator('[data-testid="recharts-chart"]')).toHaveCount(0)
  106 |     await expect(page.locator('[data-testid="echarts-chart"]')).toHaveCount(0)
  107 |   })
  108 |
  109 |   test('should render different chart types correctly', async ({ page }) => {
  110 |     await testUtils.createDocumentFromTemplate('EKO Report')
  111 |     await testUtils.waitForEditor()
  112 |     
  113 |     const chartTypes = [
  114 |       {
  115 |         type: 'area',
  116 |         title: 'Area Chart',
  117 |         data: [
  118 |           { month: 'Jan', value: 100 },
  119 |           { month: 'Feb', value: 150 },
  120 |           { month: 'Mar', value: 120 }
  121 |         ],
  122 |         config: { value: { label: 'Value', color: 'hsl(210, 70%, 50%)' } }
  123 |       },
  124 |       {
  125 |         type: 'line',
  126 |         title: 'Line Chart', 
  127 |         data: [
  128 |           { day: 'Mon', temp: 20 },
  129 |           { day: 'Tue', temp: 22 },
  130 |           { day: 'Wed', temp: 18 }
  131 |         ],
  132 |         config: { temp: { label: 'Temperature', color: 'hsl(0, 70%, 50%)' } }
  133 |       },
  134 |       {
  135 |         type: 'pie',
  136 |         title: 'Pie Chart',
  137 |         data: [
  138 |           { category: 'A', value: 30 },
  139 |           { category: 'B', value: 45 },
  140 |           { category: 'C', value: 25 }
  141 |         ],
  142 |         config: {
  143 |           A: { label: 'Category A', color: 'hsl(0, 70%, 50%)' },
  144 |           B: { label: 'Category B', color: 'hsl(120, 70%, 50%)' },
  145 |           C: { label: 'Category C', color: 'hsl(240, 70%, 50%)' }
  146 |         }
  147 |       }
  148 |     ]
  149 |     
  150 |     // Insert charts one by one using TipTap commands
  151 |     for (const chartConfig of chartTypes) {
  152 |       await page.evaluate((data) => {
  153 |         const editor = (window as any).testEditor
  154 |         const encodedData = btoa(JSON.stringify(data))
  155 |         editor.commands.insertContent(`<chart data-json="${encodedData}"></chart>`)
  156 |         editor.commands.insertContent('<p></p>') // Add paragraph between charts
  157 |       }, chartConfig)
  158 |     }
  159 |     
  160 |     // Wait for all charts to render
> 161 |     await expect(page.locator('[data-testid="chart-container"]')).toHaveCount(3)
      |                                                                   ^ Error: Timed out 5000ms waiting for expect(locator).toHaveCount(expected)
  162 |     
  163 |     // Verify all charts are using Recharts (new format)
  164 |     await expect(page.locator('[data-testid="recharts-chart"]')).toHaveCount(3)
  165 |     
  166 |     // Verify chart titles
  167 |     await expect(page.locator('[data-testid="chart-title"]').nth(0)).toHaveText('Area Chart')
  168 |     await expect(page.locator('[data-testid="chart-title"]').nth(1)).toHaveText('Line Chart')
  169 |     await expect(page.locator('[data-testid="chart-title"]').nth(2)).toHaveText('Pie Chart')
  170 |     
  171 |     // Verify no chart errors
  172 |     await expect(page.locator('[data-testid="chart-error"]')).toHaveCount(0)
  173 |   })
  174 | })
```