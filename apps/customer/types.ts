import { Database } from '@/database.types'
import { V2EntityType } from './types/entity'
import { V2RunType } from '@/types/run'
import { PromiseTypeV2 as ImportedPromiseTypeV2 } from './types/promise'
import { CherryTypeV2 as ImportedCherryTypeV2 } from './types/cherry'
import { VagueTypeV2 as ImportedVagueTypeV2 } from './types/vague'
import { ScoreTypeV2 as ImportedScoreTypeV2 } from './types/score'
import { ModelSectionTypeV2 as ImportedModelSectionTypeV2 } from './types/model-section'
import { CitationType } from '@/components/citation'

// VagueType is now using the V2 version
export type VagueType = VagueTypeV2;
export type VagueTypeV2 = ImportedVagueTypeV2;
export type ScoreTypeV2 = ImportedScoreTypeV2;
export type EntityTypeV2 = V2EntityType;
export type FlagTypeV2Base = Database['public']['Tables']['xfer_flags_v2']['Row'];
// IssueType is deprecated as model sections are now directly attached to each flag
export type PromiseTypeV2 = ImportedPromiseTypeV2;
export type CherryTypeV2 = ImportedCherryTypeV2;
// Using imported type instead of direct database reference
export type ModelSectionType = ModelSectionTypeV2;
export type ModelSectionTypeV2 = ImportedModelSectionTypeV2;
// Extended RunType to include all properties used in entity-analysis-report.tsx
export interface RunType {
  id: number;
  completed_at: string | null;
  model?: any;
  run_type: string;
  scope: string;
  target?: string | null;
  end_year?: number | null;
  models?: string[] | null;
  start_year?: number | null;
};
export type RunTypeV2 = V2RunType;
export type ProfileType = Database["public"]["Tables"]["profiles"]["Row"];
export type SingleDocAnalysesType = Database["public"]["Views"]["view_single_doc_runs"]["Row"];
export type EntityAnalysesRunType = Database["public"]["Views"]["view_entity_analysis_runs"]["Row"];
export type MyCompaniesType = Database["public"]["Views"]["view_my_companies"]["Row"];
export type QuotaUsedType = Database["public"]["Views"]["view_quota_used"]["Row"];
export type APIQueueType = Database["public"]["Tables"]["api_queue"]["Row"];
// Note: view_api_queue_expanded doesn't exist in current database schema
// export type APIQueueExpandedType = Database["public"]["Views"]["view_api_queue_expanded"]["Row"];
export type AccountMessageType = Database["public"]["Tables"]["acc_messages"]["Row"];
export type AccountMessageEnumType= Database["public"]["Enums"]["acc_message_type"]


// DEMISE model structure from Python's DEMISEModel
export type DEMISEModel = {
  demise_embedding?: number[];
  domain_embedding?: number[];
  effect_embedding?: number[];
};

// Statement metadata structure
export type StatementAndMetadata = {
  statement_text: string;
  source_text?: string;  // Added source_text field
  metadata: {
    statement_category: string;
    // Other metadata fields
  };
};

// Impact measurement types from Python backend
export type TemporalBreakdown = {
  immediate: string;
  medium_term: string;
  long_term: string;
};

export type DimensionAssessment = {
  score: number; // 0.0 to 1.0
  reasoning: string;
  confidence: 'high' | 'medium' | 'low';
  temporal_breakdown: TemporalBreakdown;
};

export type ImpactAssessment = {
  animals: DimensionAssessment;
  humans: DimensionAssessment;
  environment: DimensionAssessment;
};

export type EventImpactMeasurement = {
  id?: number;
  run_id?: number;
  event_id?: string;
  event_summary: string;
  event_description: string;
  harm_assessment: ImpactAssessment;
  benefit_assessment: ImpactAssessment;
  key_uncertainties: string[];
  ethical_considerations: string[];
  assessed_at: string;
  model_used: string;
  prompt_version: string;
  harm_score: number; // 0.0 to 1.0
  benefit_score: number; // 0.0 to 1.0
  net_impact_score: number; // -1.0 to 1.0
};

export type ConsistencyChecks = {
  score_alignment: boolean;
  temporal_consistency: boolean;
  dimensional_balance: boolean;
  issues: string[];
};

export type ConfidenceAnalysis = {
  confidence_distribution: Record<string, number>;
  avg_confidence: number;
  low_confidence_high_impact: string[];
};

export type BiasDetection = {
  anthropocentric_bias: boolean;
  optimism_bias: boolean;
  recency_bias: boolean;
  detected_issues: string[];
};

export type EventImpactEvaluation = {
  measurement_id?: string;
  consistency_checks: ConsistencyChecks;
  confidence_analysis: ConfidenceAnalysis;
  bias_detection: BiasDetection;
  completeness_score: number; // 0.0 to 1.0
  overall_quality: 'poor' | 'fair' | 'good' | 'excellent';
  overall_quality_score: number; // 0.0 to 1.0
};

export type ImpactValueAnalysis = {
  impact_measurement?: EventImpactMeasurement;
  impact_evaluation?: EventImpactEvaluation;
};

// Structure that matches the XferEffectFlagModel Python class
export interface FlagModelData {
  id: number;
  entity_xid: string;
  flag_type: string;
  flag_title: string;
  flag_short_title: string;
  year: number;
  start_year?: number;
  end_year?: number;
  score: number;
  impact: number;
  authentic: number;
  contribution: number;
  confidence: number;
  flag_summary?: string;
  flag_analysis?: string;
  domains: string[];
  citations: CitationType[];
  flag_statements: StatementAndMetadata[];
  impact_value_analysis: ImpactValueAnalysis;
  model_sections: Record<string, string>; // Map of model name to section ID
  full_demise_centroid: DEMISEModel;
  is_disclosure_only: boolean; // Flag indicating if all source documents are disclosures
}

// Complete FlagTypeV2 with model data
// @ts-ignore
export interface FlagTypeV2 extends FlagTypeV2Base {
  model: FlagModelData;
  // Add issue field which is in the table but not in the model
  issue?: string;
}
