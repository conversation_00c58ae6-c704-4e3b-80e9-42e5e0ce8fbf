/**
 * Verification script for EKO-88: Summary sections in generated templates
 * This script verifies that the dynamic template generator correctly adds
 * summary sections to each level group.
 */

// Mock the Supabase client since we're running this as a standalone script
const mockSupabaseClient = {
  from: () => mockSupabaseClient,
  select: () => mockSupabaseClient,
  eq: () => mockSupabaseClient,
  order: () => mockSupabaseClient,
  then: (callback) => {
    // Mock data representing model sections
    const mockData = [
      {
        model: 'sdg',
        section: 'sdg_1',
        title: 'No Poverty',
        level: 'social',
        status: 'active'
      },
      {
        model: 'sdg',
        section: 'sdg_13',
        title: 'Climate Action',
        level: 'ecological',
        status: 'active'
      },
      {
        model: 'sdg',
        section: 'sdg_16',
        title: 'Peace and Justice',
        level: 'governance',
        status: 'active'
      }
    ]
    
    return callback({ data: mockData, error: null })
  }
}

// Mock the createClient function
global.createClient = () => mockSupabaseClient

// Import the function we want to test
const path = require('path')
const fs = require('fs')

// Read the dynamic template generator file
const templateGeneratorPath = path.join(__dirname, '../components/editor/templates/dynamic-template-generator.ts')
const templateGeneratorCode = fs.readFileSync(templateGeneratorPath, 'utf8')

// Extract the functions we need (simplified approach)
function extractFunction(code, functionName) {
  const regex = new RegExp(`function ${functionName}\\([^{]*\\{[\\s\\S]*?^}`, 'm')
  const match = code.match(regex)
  return match ? match[0] : null
}

// Verify the structure manually
function verifyTemplateStructure() {
  console.log('🔍 Verifying EKO-88 implementation...\n')
  
  // Check if createLevelSections returns both sections and sectionIds
  const createLevelSectionsMatch = templateGeneratorCode.match(/function createLevelSections[\s\S]*?return \{ sections: reportSections, sectionIds \}/)
  
  if (createLevelSectionsMatch) {
    console.log('✅ createLevelSections() correctly returns both sections and sectionIds')
  } else {
    console.log('❌ createLevelSections() does not return the expected structure')
    return false
  }
  
  // Check if ecological group has summary section
  const ecologicalSummaryMatch = templateGeneratorCode.match(/type: 'reportSummary'[\s\S]*?id: 'ecological-summary'/)
  
  if (ecologicalSummaryMatch) {
    console.log('✅ Ecological group has summary section')
  } else {
    console.log('❌ Ecological group missing summary section')
    return false
  }
  
  // Check if social group has summary section
  const socialSummaryMatch = templateGeneratorCode.match(/type: 'reportSummary'[\s\S]*?id: 'social-summary'/)
  
  if (socialSummaryMatch) {
    console.log('✅ Social group has summary section')
  } else {
    console.log('❌ Social group missing summary section')
    return false
  }
  
  // Check if governance group has summary section
  const governanceSummaryMatch = templateGeneratorCode.match(/type: 'reportSummary'[\s\S]*?id: 'governance-summary'/)
  
  if (governanceSummaryMatch) {
    console.log('✅ Governance group has summary section')
  } else {
    console.log('❌ Governance group missing summary section')
    return false
  }
  
  // Check if summarize attribute uses section IDs
  const summarizeMatch = templateGeneratorCode.match(/summarize: \w+SectionIds\.join\(','\)/)
  
  if (summarizeMatch) {
    console.log('✅ Summary sections correctly use section IDs in summarize attribute')
  } else {
    console.log('❌ Summary sections do not use section IDs correctly')
    return false
  }
  
  console.log('\n🎉 All checks passed! EKO-88 implementation is correct.')
  return true
}

// Run the verification
const success = verifyTemplateStructure()

if (success) {
  console.log('\n📋 Summary of changes:')
  console.log('- Modified createLevelSections() to return both sections and section IDs')
  console.log('- Added reportSummary components at the beginning of each level group')
  console.log('- Each summary depends on all report sections within that level group')
  console.log('- Summary sections provide high-level overviews for each ESG dimension')
  process.exit(0)
} else {
  console.log('\n❌ Verification failed. Please check the implementation.')
  process.exit(1)
}
