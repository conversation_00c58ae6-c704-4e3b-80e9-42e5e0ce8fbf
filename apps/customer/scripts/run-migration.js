const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function runMigration() {
  try {
    console.log('Reading migration file...')
    
    const migrationPath = path.join(__dirname, '../supabase/migrations/20250127_collaborative_documents.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    console.log('Running migration...')
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`Found ${statements.length} SQL statements to execute`)
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';'
      console.log(`Executing statement ${i + 1}/${statements.length}...`)
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          console.error(`Error in statement ${i + 1}:`, error)
          // Continue with other statements
        } else {
          console.log(`Statement ${i + 1} executed successfully`)
        }
      } catch (err) {
        console.error(`Exception in statement ${i + 1}:`, err.message)
        // Continue with other statements
      }
    }
    
    console.log('Migration completed!')
    
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  }
}

// Alternative approach: Try to create tables directly using Supabase client
async function createTablesDirectly() {
  try {
    console.log('Creating tables directly...')
    
    // Create collaborative_documents table
    console.log('Creating collaborative_documents table...')
    const { error: docError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS collaborative_documents (
          id TEXT PRIMARY KEY,
          content TEXT,
          title TEXT,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          metadata JSONB DEFAULT '{}'::jsonb
        );
      `
    })
    
    if (docError) {
      console.log('collaborative_documents table might already exist or using direct SQL...')
    }
    
    console.log('Tables creation completed!')
    
  } catch (error) {
    console.error('Direct table creation failed:', error)
  }
}

// Run the migration
if (process.argv.includes('--direct')) {
  createTablesDirectly()
} else {
  runMigration()
}
