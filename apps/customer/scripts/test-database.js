const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testDatabase() {
  try {
    console.log('Testing database connection...')
    
    // Test basic connection
    const { data: testData, error: testError } = await supabase
      .from('collaborative_documents')
      .select('count')
      .limit(1)
    
    if (testError) {
      console.log('collaborative_documents table does not exist or is not accessible')
      console.log('Error:', testError.message)
      
      // Try to create a simple test document to see if we can insert
      console.log('Attempting to create table...')
      
      // This won't work with <PERSON><PERSON>, but let's see what happens
      const { data: insertData, error: insertError } = await supabase
        .from('collaborative_documents')
        .insert({
          id: 'test-doc-' + Date.now(),
          title: 'Test Document',
          content: '<p>This is a test document</p>',
          created_by: '00000000-0000-0000-0000-000000000000', // Dummy UUID
          updated_by: '00000000-0000-0000-0000-000000000000'
        })
        .select()
      
      if (insertError) {
        console.log('Insert failed:', insertError.message)
      } else {
        console.log('Insert successful:', insertData)
      }
    } else {
      console.log('collaborative_documents table exists and is accessible')
      console.log('Test data:', testData)
    }
    
    // Test auth
    console.log('\nTesting auth...')
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError) {
      console.log('Auth error:', authError.message)
    } else if (user) {
      console.log('User authenticated:', user.email)
    } else {
      console.log('No user authenticated')
    }
    
  } catch (error) {
    console.error('Database test failed:', error)
  }
}

// Run the test
testDatabase()
