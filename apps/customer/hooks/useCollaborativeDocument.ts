import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/app/supabase/client'
import { useAuth } from '@/components/context/auth/auth-context'
import { Database } from '@/database.types'

type CollaborativeDocumentRow = Database['public']['Tables']['collaborative_documents']['Row']

export interface CollaborativeDocument extends CollaborativeDocumentRow {
  user_permission?: 'read' | 'write' | 'admin'
}

export interface UseCollaborativeDocumentOptions {
  documentId?: string
  autoCreate?: boolean
  defaultTitle?: string
  defaultContent?: string
}

export interface UseCollaborativeDocumentReturn {
  document: CollaborativeDocument | null
  isLoading: boolean
  error: string | null
  createDocument: (title: string, content?: string) => Promise<CollaborativeDocument | null>
  updateDocument: (updates: Partial<CollaborativeDocument>) => Promise<boolean>
  deleteDocument: () => Promise<boolean>
  refreshDocument: () => Promise<void>
}

export function useCollaborativeDocument({
  documentId,
  autoCreate = false,
  defaultTitle = 'Untitled Document',
  defaultContent = '',
}: UseCollaborativeDocumentOptions = {}): UseCollaborativeDocumentReturn {
  const { user } = useAuth()
  const supabase = createClient()

  const [document, setDocument] = useState<CollaborativeDocument | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch document by ID
  const fetchDocument = useCallback(async (id: string) => {
    if (!user) {
      setError('User not authenticated')
      return null
    }

    setIsLoading(true)
    setError(null)

    try {
      const { data, error: fetchError } = await supabase
        .from('collaborative_documents')
        .select('*')
        .eq('id', id)
        .single()

      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          // Document not found
          if (autoCreate) {
            return await createDocument(defaultTitle, defaultContent)
          } else {
            setError('Document not found')
            return null
          }
        } else {
          throw fetchError
        }
      }

      // Add user permission based on ownership
      const documentWithPermission: CollaborativeDocument = {
        ...data,
        user_permission: data.created_by === user.id ? 'admin' : 'read'
      }

      setDocument(documentWithPermission)
      return documentWithPermission
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch document'
      setError(errorMessage)
      console.error('Error fetching document:', err)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [user, supabase, autoCreate, defaultTitle, defaultContent])

  // Create a new document
  const createDocument = useCallback(async (title: string, content: string = '') => {
    if (!user) {
      setError('User not authenticated')
      return null
    }

    setIsLoading(true)
    setError(null)

    try {
      const newDocumentId = documentId || `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      const { data, error: createError } = await supabase
        .from('collaborative_documents')
        .insert({
          id: newDocumentId,
          title,
          content: content || '',
          created_by: user.id,
          updated_by: user.id,
        })
        .select()
        .single()

      if (createError) {
        throw createError
      }

      const newDocument: CollaborativeDocument = {
        ...data,
        user_permission: 'admin'
      }

      setDocument(newDocument)
      return newDocument
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create document'
      setError(errorMessage)
      console.error('Error creating document:', err)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [user, supabase, documentId])

  // Update document
  const updateDocument = useCallback(async (updates: Partial<CollaborativeDocument>) => {
    if (!user || !document) {
      setError('User not authenticated or no document loaded')
      return false
    }

    setError(null)

    try {
      const { error: updateError } = await supabase
        .from('collaborative_documents')
        .update({
          ...updates,
          updated_by: user.id,
        })
        .eq('id', document.id)

      if (updateError) {
        throw updateError
      }

      // Update local state
      setDocument(prev => prev ? { ...prev, ...updates, updated_by: user.id } : null)
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update document'
      setError(errorMessage)
      console.error('Error updating document:', err)
      return false
    }
  }, [user, supabase, document])

  // Delete document
  const deleteDocument = useCallback(async () => {
    if (!user || !document) {
      setError('User not authenticated or no document loaded')
      return false
    }

    setError(null)

    try {
      const { error: deleteError } = await supabase
        .from('collaborative_documents')
        .delete()
        .eq('id', document.id)

      if (deleteError) {
        throw deleteError
      }

      setDocument(null)
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete document'
      setError(errorMessage)
      console.error('Error deleting document:', err)
      return false
    }
  }, [user, supabase, document])

  // Refresh document
  const refreshDocument = useCallback(async () => {
    if (documentId) {
      await fetchDocument(documentId)
    }
  }, [documentId, fetchDocument])

  // Load document on mount or when documentId changes
  useEffect(() => {
    if (documentId && user) {
      fetchDocument(documentId)
    }
  }, [documentId, user, fetchDocument])

  return {
    document,
    isLoading,
    error,
    createDocument,
    updateDocument,
    deleteDocument,
    refreshDocument,
  }
}
