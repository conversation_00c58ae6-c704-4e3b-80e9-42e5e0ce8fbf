import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/components/context/auth/auth-context'

export interface TiptapAuthResponse {
  token: string
  user: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  permissions: {
    allowedDocumentNames: string[]
    readonlyDocumentNames: string[]
  }
}

export interface UseTiptapAuthOptions {
  documentId: string
  permissions?: 'read' | 'write'
  autoRefresh?: boolean
}

export interface UseTiptapAuthReturn {
  token: string | null
  isLoading: boolean
  error: string | null
  refreshToken: () => Promise<void>
  authData: TiptapAuthResponse | null
}

export function useTiptapAuth({
  documentId,
  permissions = 'write',
  autoRefresh = true
}: UseTiptapAuthOptions): UseTiptapAuthReturn {
  const { user } = useAuth()
  const [token, setToken] = useState<string | null>(null)
  const [authData, setAuthData] = useState<TiptapAuthResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchToken = useCallback(async () => {
    if (!user || !documentId) {
      setError('User not authenticated or document ID missing')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/tiptap/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId,
          permissions,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to get authentication token')
      }

      const data: TiptapAuthResponse = await response.json()
      setToken(data.token)
      setAuthData(data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to authenticate'
      setError(errorMessage)
      console.error('TipTap authentication error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [user, documentId, permissions])

  // Fetch token when dependencies change
  useEffect(() => {
    if (user && documentId) {
      fetchToken()
    }
  }, [user, documentId, permissions, fetchToken])

  // Auto-refresh token before expiration (if enabled)
  useEffect(() => {
    if (!autoRefresh || !token) return

    // Refresh token 5 minutes before expiration (55 minutes after creation)
    const refreshInterval = setInterval(() => {
      fetchToken()
    }, 55 * 60 * 1000) // 55 minutes

    return () => clearInterval(refreshInterval)
  }, [token, autoRefresh, fetchToken])

  return {
    token,
    isLoading,
    error,
    refreshToken: fetchToken,
    authData,
  }
}
