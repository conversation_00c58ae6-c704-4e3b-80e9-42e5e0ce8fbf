# Customer App Style Guidelines

This document outlines the style guidelines and design system for the Customer application, ensuring consistency with the CMS while adapting for an application context.

## Design Philosophy

The Customer app follows a glass-morphism design language with heavily rounded elements, adapted for an application context:

1. **Glass-Morphism**: Translucent, frosted glass-like surfaces with subtle backdrop blur effects
2. **Rounded Corners**: Generous border radii (standard: 1.5rem) for a modern, approachable feel
3. **Subtle Shadows**: Light shadows to create depth without heaviness
4. **Layered Elements**: Creating depth through overlapping translucent elements
5. **Clean Typography**: Clear, readable text with proper spacing and hierarchy
6. **Application Focus**: Optimized for data-dense interfaces while maintaining visual appeal

## Color System

The color system uses a neutral palette based on slate colors with brand accent colors:

- **Primary**: Brand green (`hsl(145, 27%, 45%)`)
- **Accent**: Brand contrast (`hsl(4, 27%, 51%)`)
- **Neutrals**: Slate-based grayscale for UI elements
- **Semantic Colors**: Success, warning, error, and info colors for status indicators

## Components

### Cards

Cards are a fundamental building block of the UI. They use glass-morphism effects with rounded corners and subtle hover animations.

#### Standard Card

```jsx
<Card className="rounded-xl shadow-soft hover:shadow-medium transition-all duration-300">
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Card content goes here</p>
  </CardContent>
</Card>
```

#### Glass Card

```jsx
<Card className="glass-effect rounded-xl shadow-soft hover:shadow-medium transition-all duration-300">
  <CardHeader>
    <CardTitle>Glass Card Title</CardTitle>
    <CardDescription>Glass card with backdrop blur</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Card content goes here</p>
  </CardContent>
</Card>
```

#### Interactive Card

```jsx
<Card className="glass-effect-lit rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 hover:-translate-y-1 cursor-pointer">
  <CardHeader>
    <CardTitle>Interactive Card</CardTitle>
    <CardDescription>Card with hover effects</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Card content goes here</p>
  </CardContent>
</Card>
```

### Buttons

Buttons use the same rounded design language with glass effects for consistency.

```jsx
<Button className="rounded-xl glass-effect-subtle transition-all duration-300 hover:-translate-y-1">
  Click Me
</Button>
```

### Headers and Navigation

The application header uses a glass-effect with backdrop blur for a modern look:

```jsx
<header className="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-border/20 backdrop-blur-xl">
  {/* Header content */}
</header>
```

### Data Visualization

Data visualization components should use the same design language:

- Rounded corners for charts and graphs
- Consistent color palette
- Glass-effect containers
- Subtle animations for transitions

## Typography

Typography follows a clear hierarchy:

- **Headings**: Use larger sizes with tighter line heights
- **Body Text**: Optimized for readability with proper line height
- **Small Text**: Used for labels and secondary information

## Spacing

Consistent spacing creates rhythm in the UI:

- **Extra Small**: 0.25rem (4px)
- **Small**: 0.5rem (8px)
- **Medium**: 1rem (16px)
- **Large**: 1.5rem (24px)
- **Extra Large**: 2rem (32px)
- **2XL**: 3rem (48px)

## Responsive Design

The application is designed to work across all device sizes:

- **Mobile First**: Core functionality works on small screens
- **Progressive Enhancement**: Additional features and layouts on larger screens
- **Breakpoints**: Standard Tailwind breakpoints (sm, md, lg, xl, 2xl)

## Accessibility

All components must be accessible:

- Sufficient color contrast
- Keyboard navigation
- Screen reader support
- Focus states

## Dark Mode

The application supports both light and dark modes:

- Dark mode uses deeper colors with reduced brightness
- Glass effects are adjusted for dark mode
- Text contrast is maintained in both modes

## Animation

Subtle animations enhance the user experience:

- Hover effects for interactive elements
- Transition animations for state changes
- Loading states and indicators

## Implementation Notes

- Use the provided glass-effect utility classes
- Maintain consistent border-radius with the `rounded-xl` or `rounded-2xl` classes
- Apply consistent shadows with the shadow utility classes
- Use transition utilities for interactive elements
