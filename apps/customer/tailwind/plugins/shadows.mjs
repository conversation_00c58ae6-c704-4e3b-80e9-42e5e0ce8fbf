/**
 * Shadow utilities plugin for Tailwind CSS
 */
export default function shadowsPlugin({ addUtilities }) {
  // Shadow utilities
  const shadowUtilities = {
    '.shadow-subtle': {
      boxShadow: '0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.03)',
    },
    '.shadow-soft': {
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    },
    '.shadow-medium': {
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.08)',
    },
    '.shadow-strong': {
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1)',
    },
    '[data-theme="dark"] .shadow-subtle, [data-theme="dark"] .shadow-soft, [data-theme="dark"] .shadow-medium, [data-theme="dark"] .shadow-strong': {
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)',
    },
  };
  
  addUtilities(shadowUtilities, ['responsive', 'hover']);
}
