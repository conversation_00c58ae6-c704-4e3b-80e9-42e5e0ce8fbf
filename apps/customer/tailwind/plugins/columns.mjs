/**
 * TipTap Columns Extension utilities plugin for Tailwind CSS
 */
export default function columnsPlugin({ addUtilities }) {
  // Column block utilities
  const columnUtilities = {
    '.column-block': {
      width: '100%',
      padding: '1rem 0',
      position: 'relative',
      minHeight: '2em',
      borderRadius: '0.5rem',
      transition: 'all 0.2s ease',
    },
    '.column-block:hover': {
      backgroundColor: 'rgba(59, 130, 246, 0.05)',
    },
    '.column-block.column-block-hovered': {
      backgroundColor: 'rgba(59, 130, 246, 0.05)',
      border: '1px dashed rgba(59, 130, 246, 0.3)',
    },
    '.column': {
      minHeight: '2em',
      borderRadius: '0.375rem',
      transition: 'all 0.2s ease',
      padding: '0.5rem',
      margin: '-0.5rem',
    },
    '.ProseMirror-focused .column': {
      border: '1px dashed rgba(156, 163, 175, 0.5)',
      borderRadius: '0.5rem',
    },
    '.column:hover': {
      backgroundColor: 'rgba(59, 130, 246, 0.02)',
    },

    // Column layout specific styles - target the actual container that holds columns
    // This works with TipTap's NodeViewRenderer wrapper structure
    '.column-block[data-layout="equal-2"] [data-node-view-content-react]': {
      display: 'grid',
      gridTemplateColumns: '1fr 1fr',
      gap: '1.5rem',
      gridAutoFlow: 'column',
    },
    '.column-block[data-layout="equal-3"] [data-node-view-content-react]': {
      display: 'grid',
      gridTemplateColumns: '1fr 1fr 1fr',
      gap: '1.5rem',
      gridAutoFlow: 'column',
    },
    '.column-block[data-layout="equal-4"] [data-node-view-content-react]': {
      display: 'grid',
      gridTemplateColumns: '1fr 1fr 1fr 1fr',
      gap: '1.5rem',
      gridAutoFlow: 'column',
    },
    '.column-block[data-layout="ratio-1-2"] [data-node-view-content-react]': {
      display: 'grid',
      gridTemplateColumns: '1fr 2fr',
      gap: '1.5rem',
      gridAutoFlow: 'column',
    },
    '.column-block[data-layout="ratio-2-1"] [data-node-view-content-react]': {
      display: 'grid',
      gridTemplateColumns: '2fr 1fr',
      gap: '1.5rem',
      gridAutoFlow: 'column',
    },
    '.column-block[data-layout="centered"] [data-node-view-content-react]': {
      display: 'grid',
      gridTemplateColumns: '1fr',
      maxWidth: '66.67%',
      margin: '0 auto',
    },
    // Layout selector styles
    '.column-block .layout-selector': {
      position: 'absolute',
      top: '-2rem',
      left: '0',
      display: 'flex',
      gap: '0.25rem',
      background: 'white',
      border: '1px solid rgba(229, 231, 235, 1)',
      borderRadius: '0.375rem',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      padding: '0.25rem',
      zIndex: '10',
    },
    '.column-block .layout-selector button': {
      padding: '0.25rem',
      fontSize: '0.75rem',
      borderRadius: '0.25rem',
      border: 'none',
      background: 'transparent',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
    },
    '.column-block .layout-selector button:hover': {
      backgroundColor: 'rgba(243, 244, 246, 1)',
    },
    '.column-block .layout-selector button.active': {
      backgroundColor: 'rgba(219, 234, 254, 1)',
      color: 'rgba(37, 99, 235, 1)',
    },
    '.column-block .layout-selector button.delete': {
      color: 'rgba(220, 38, 38, 1)',
    },
    '.column-block .layout-selector button.delete:hover': {
      backgroundColor: 'rgba(254, 226, 226, 1)',
    },
    // Responsive design
    '@media (max-width: 768px)': {
      '.column-block[data-layout="equal-3"] [data-node-view-content-react], .column-block[data-layout="equal-4"] [data-node-view-content-react], .column-block[data-layout="ratio-1-2"] [data-node-view-content-react], .column-block[data-layout="ratio-2-1"] [data-node-view-content-react]': {
        display: 'grid',
        gridTemplateColumns: '1fr',
        gap: '1rem',
        gridAutoFlow: 'row',
      },
      '.column-block[data-layout="centered"] [data-node-view-content-react]': {
        maxWidth: '100%',
      },
    },
    // Print styles
    '@media print': {
      '.column-block .layout-selector': {
        display: 'none',
      },
      '.column-block:hover, .column-block.column-block-hovered': {
        backgroundColor: 'transparent',
        border: 'none',
      },
    },
  };

  addUtilities(columnUtilities, ['responsive', 'print']);
}
