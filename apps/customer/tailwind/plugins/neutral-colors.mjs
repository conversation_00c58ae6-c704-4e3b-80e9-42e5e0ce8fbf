/**
 * Neutral color utilities plugin for Tailwind CSS
 * Provides standardized semantic color classes for text, backgrounds, and borders
 * Currently using slate colors as the base, but can be changed to any color palette
 */
export default function neutralColorsPlugin({ addUtilities }) {
  // Text color utilities
  const textColorUtilities = {
    '.text-neutral-dark': {
      color: 'var(--slate-900)',
    },
    '.text-neutral-medium': {
      color: 'var(--slate-700)',
    },
    '.text-neutral-light': {
      color: 'var(--slate-500)',
    },
    '.text-neutral-subtle': {
      color: 'var(--slate-400)',
    },
    '[data-theme="dark"] .text-neutral-dark': {
      color: 'var(--slate-50)',
    },
    '[data-theme="dark"] .text-neutral-medium': {
      color: 'var(--slate-300)',
    },
    '[data-theme="dark"] .text-neutral-light': {
      color: 'var(--slate-400)',
    },
    '[data-theme="dark"] .text-neutral-subtle': {
      color: 'var(--slate-500)',
    },
  };

  // Background color utilities
  const backgroundColorUtilities = {
    '.bg-neutral-dark': {
      backgroundColor: 'var(--slate-900)',
    },
    '.bg-neutral-medium': {
      backgroundColor: 'var(--slate-700)',
    },
    '.bg-neutral-light': {
      backgroundColor: 'var(--slate-200)',
    },
    '.bg-neutral-subtle': {
      backgroundColor: 'var(--slate-100)',
    },
    '[data-theme="dark"] .bg-neutral-dark': {
      backgroundColor: 'var(--slate-900)',
    },
    '[data-theme="dark"] .bg-neutral-medium': {
      backgroundColor: 'var(--slate-800)',
    },
    '[data-theme="dark"] .bg-neutral-light': {
      backgroundColor: 'var(--slate-700)',
    },
    '[data-theme="dark"] .bg-neutral-subtle': {
      backgroundColor: 'var(--slate-800)',
    },
  };

  // Border color utilities
  const borderColorUtilities = {
    '.border-neutral-dark': {
      borderColor: 'var(--slate-900)',
    },
    '.border-neutral-medium': {
      borderColor: 'var(--slate-700)',
    },
    '.border-neutral-light': {
      borderColor: 'var(--slate-300)',
    },
    '.border-neutral-subtle': {
      borderColor: 'var(--slate-200)',
    },
    '[data-theme="dark"] .border-neutral-dark': {
      borderColor: 'var(--slate-700)',
    },
    '[data-theme="dark"] .border-neutral-medium': {
      borderColor: 'var(--slate-600)',
    },
    '[data-theme="dark"] .border-neutral-light': {
      borderColor: 'var(--slate-700)',
    },
    '[data-theme="dark"] .border-neutral-subtle': {
      borderColor: 'var(--slate-800)',
    },
  };

  // Add all utilities
  addUtilities(textColorUtilities, ['responsive', 'hover']);
  addUtilities(backgroundColorUtilities, ['responsive', 'hover']);
  addUtilities(borderColorUtilities, ['responsive', 'hover']);
}
