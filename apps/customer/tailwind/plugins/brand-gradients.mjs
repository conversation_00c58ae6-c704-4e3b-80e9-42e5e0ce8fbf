/**
 * Brand gradient utilities plugin for Tailwind CSS
 */
export default function brandGradientsPlugin({ addUtilities }) {
  // Brand gradient utilities
  const brandGradientUtilities = {
    // Background gradient utilities
    '.bg-brand-gradient': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 45%, 1) 0%, hsla(144, 40%, 40%, 1) 35%, hsla(144, 40%, 45%, 1) 100%)',
    },
    '.bg-brand-gradient-dark': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 35%, 1) 0%, hsla(144, 40%, 30%, 1) 35%, hsla(144, 40%, 35%, 1) 100%)',
    },
    '.bg-brand-gradient-compliment': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 56%, 1) 0%, hsla(0, 55%, 60%, 1) 35%, hsla(0, 55%, 58%, 1) 100%)',
    },
    '.bg-brand-gradient-compliment-dark': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 46%, 1) 0%, hsla(0, 55%, 50%, 1) 35%, hsla(0, 55%, 48%, 1) 100%)',
    },
    '.bg-brand-gradient-accent': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 56%, 1) 0%, hsla(55, 55%, 60%, 1) 35%, hsla(55, 55%, 58%, 1) 100%)',
    },
    '.bg-brand-gradient-accent-dark': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 46%, 1) 0%, hsla(55, 55%, 50%, 1) 35%, hsla(55, 55%, 48%, 1) 100%)',
    },
    '.bg-brand-glass': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 45%, 0.08) 0%, hsla(144, 40%, 40%, 0.1) 35%, hsla(144, 40%, 45%, 0.08) 100%)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    },
    '.bg-brand-glass-accent': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 56%, 0.08) 0%, hsla(55, 55%, 60%, 0.08) 35%, hsla(55, 55%, 58%, 0.08) 100%)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    },
    '.bg-brand-glass-compliment': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 56%, 0.08) 0%, hsla(0, 55%, 60%, 0.08) 35%, hsla(0, 55%, 58%, 0.08) 100%)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    },
    '.bg-brand': {
      backgroundColor: 'hsl(145, 40%, 45%)',
    },
    '.bg-brand-dark': {
      backgroundColor: 'hsl(145, 40%, 30%)',
    },
    '.bg-brand-accent': {
      backgroundColor: 'hsl(55, 55%, 58%)',
    },
    '.bg-brand-compliment': {
      backgroundColor: 'hsl(0, 55%, 58%)',
    },

    // Text color utilities
    '.text-brand': {
      color: 'hsl(145, 40%, 45%)',
    },
    '.text-brand-dark': {
      color: 'hsl(145, 40%, 30%)',
    },
    '.text-brand-light': {
      color: 'hsl(145, 40%, 60%)',
    },
    '.text-brand-accent': {
      color: 'hsl(55, 55%, 58%)',
    },
    '.text-brand-accent-dark': {
      color: 'hsl(55, 55%, 40%)',
    },
    '.text-brand-compliment': {
      color: 'hsl(0, 55%, 58%)',
    },
    '.text-brand-compliment-dark': {
      color: 'hsl(0, 55%, 40%)',
    },

    // Dark mode variants for backgrounds
    '[data-theme="dark"] .bg-brand-gradient': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 25%, 1) 0%, hsla(144, 40%, 20%, 1) 35%, hsla(144, 40%, 25%, 1) 100%)',
    },
    '[data-theme="dark"] .bg-brand-gradient-dark': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 15%, 1) 0%, hsla(144, 40%, 10%, 1) 35%, hsla(144, 40%, 15%, 1) 100%)',
    },
    '[data-theme="dark"] .bg-brand-gradient-compliment': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 36%, 1) 0%, hsla(0, 55%, 40%, 1) 35%, hsla(0, 55%, 38%, 1) 100%)',
    },
    '[data-theme="dark"] .bg-brand-gradient-compliment-dark': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 26%, 1) 0%, hsla(0, 55%, 30%, 1) 35%, hsla(0, 55%, 28%, 1) 100%)',
    },
    '[data-theme="dark"] .bg-brand-gradient-accent': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 36%, 1) 0%, hsla(55, 55%, 40%, 1) 35%, hsla(55, 55%, 38%, 1) 100%)',
    },
    '[data-theme="dark"] .bg-brand-gradient-accent-dark': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 26%, 1) 0%, hsla(55, 55%, 30%, 1) 35%, hsla(55, 55%, 28%, 1) 100%)',
    },
    '[data-theme="dark"] .bg-brand-glass': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 15%, 0.15) 0%, hsla(144, 40%, 10%, 0.25) 35%, hsla(144, 40%, 15%, 0.15) 100%)',
      backdropFilter: 'blur(14px)',
      border: '1px solid rgba(255, 255, 255, 0.05)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.2)',
    },
    '[data-theme="dark"] .bg-brand-glass-accent': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 15%, 0.15) 0%, hsla(55, 55%, 10%, 0.25) 35%, hsla(55, 55%, 15%, 0.15) 100%)',
      backdropFilter: 'blur(14px)',
      border: '1px solid rgba(255, 255, 255, 0.05)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.2)',
    },
    '[data-theme="dark"] .bg-brand-glass-compliment': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 15%, 0.15) 0%, hsla(0, 55%, 10%, 0.25) 35%, hsla(0, 55%, 15%, 0.15) 100%)',
      backdropFilter: 'blur(14px)',
      border: '1px solid rgba(255, 255, 255, 0.05)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.2)',
    },
    '[data-theme="dark"] .bg-brand': {
      backgroundColor: 'hsl(145, 40%, 25%)',
    },
    '[data-theme="dark"] .bg-brand-dark': {
      backgroundColor: 'hsl(145, 40%, 15%)',
    },
    '[data-theme="dark"] .bg-brand-accent': {
      backgroundColor: 'hsl(55, 55%, 38%)',
    },
    '[data-theme="dark"] .bg-brand-compliment': {
      backgroundColor: 'hsl(0, 55%, 38%)',
    },

    // Dark mode variants for text colors
    '[data-theme="dark"] .text-brand': {
      color: 'hsl(145, 40%, 50%)',
    },
    '[data-theme="dark"] .text-brand-dark': {
      color: 'hsl(145, 40%, 35%)',
    },
    '[data-theme="dark"] .text-brand-light': {
      color: 'hsl(145, 40%, 65%)',
    },
    '[data-theme="dark"] .text-brand-accent': {
      color: 'hsl(55, 55%, 63%)',
    },
    '[data-theme="dark"] .text-brand-accent-dark': {
      color: 'hsl(55, 55%, 45%)',
    },
    '[data-theme="dark"] .text-brand-compliment': {
      color: 'hsl(0, 55%, 63%)',
    },
    '[data-theme="dark"] .text-brand-compliment-dark': {
      color: 'hsl(0, 55%, 45%)',
    },
  };

  addUtilities(brandGradientUtilities, ['responsive', 'hover']);
}
