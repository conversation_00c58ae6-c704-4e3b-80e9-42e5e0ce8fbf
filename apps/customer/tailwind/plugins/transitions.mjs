/**
 * Transition utilities plugin for Tailwind CSS
 */
export default function transitionsPlugin({ addUtilities }) {
  // Transition utilities
  const transitionUtilities = {
    '.transition-fast': {
      transition: 'all 150ms ease-in-out',
    },
    '.transition-standard': {
      transition: 'all 250ms ease-in-out',
    },
    '.transition-slow': {
      transition: 'all 350ms ease-in-out',
    },
    '.hover-lift': {
      transition: 'transform 250ms ease-in-out',
    },
    '.hover-lift:hover': {
      transform: 'translateY(-4px)',
    },
    '.hover-lift-subtle': {
      transition: 'transform 250ms ease-in-out',
    },
    '.hover-lift-subtle:hover': {
      transform: 'translateY(-2px)',
    },
    '.hover-scale': {
      transition: 'transform 250ms ease-in-out',
    },
    '.hover-scale:hover': {
      transform: 'scale(1.05)',
    },
    '.hover-scale-subtle': {
      transition: 'transform 250ms ease-in-out',
    },
    '.hover-scale-subtle:hover': {
      transform: 'scale(1.02)',
    },
  };
  
  addUtilities(transitionUtilities, ['responsive', 'hover']);
}
