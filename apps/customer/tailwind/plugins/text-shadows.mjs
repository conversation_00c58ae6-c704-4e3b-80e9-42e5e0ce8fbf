/**
 * Text shadow utilities plugin for Tailwind CSS
 */
export default function textShadowsPlugin({ addUtilities }) {
  // Text shadow utilities
  const textShadowUtilities = {
    '.text-shadow-sm': {
      textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
    },
    '.text-shadow': {
      textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
    },
    '.text-shadow-md': {
      textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
    },
    '.text-shadow-lg': {
      textShadow: '0 8px 16px rgba(0, 0, 0, 0.3)',
    },
    '.text-shadow-glow': {
      textShadow: '0 0 5px rgba(255, 255, 255, 0.5), 0 0 10px rgba(255, 255, 255, 0.3)',
    },
    '.text-shadow-none': {
      textShadow: 'none',
    },
  };

  addUtilities(textShadowUtilities, ['responsive']);
}
