/**
 * Glass effect utilities plugin for Tailwind CSS
 */
export default function glassEffectsPlugin({ addUtilities }) {
  // Glass effect utilities
  const glassEffectUtilities = {
    '.glass-effect': {
      background: 'rgba(230, 230, 230, 0.1)',
      backdropFilter: 'blur(12px)',
      borderBottom: '1px solid rgba(150, 150, 150, 0.3)',
      borderLeft: '1px solid rgba(150, 150, 150, 0.3)',
      borderTop: '1px solid rgba(240, 240, 240, 0.2)',
      borderRight: '1px solid rgba(240, 240, 240, 0.2)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.1))',
      overflow: 'visible'
    },
    '.glass-effect-lit': {
      background: 'rgba(230, 230, 230, 0.1)',
      backdropFilter: 'blur(12px)',
      borderBottom: '1px solid rgba(150, 150, 150, 0.3)',
      borderLeft: '1px solid rgba(150, 150, 150, 0.3)',
      borderTop: '1px solid rgba(240, 240, 240, 0.2)',
      borderRight: '1px solid rgba(240, 240, 240, 0.2)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), inset 0 0 20px rgba(255, 255, 255, 0.15), inset 0 0 10px rgba(255, 255, 255, 0.1)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 70%)',
        opacity: '0.7',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-subtle': {
      background: 'rgba(255, 255, 255, 0.05)',
      backdropFilter: 'blur(8px)',
      border: '1px solid rgba(255, 255, 255, 0.08)',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.03)',
    },
    '.glass-effect-subtle-lit': {
      background: 'rgba(255, 255, 255, 0.05)',
      backdropFilter: 'blur(8px)',
      border: '1px solid rgba(255, 255, 255, 0.08)',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.03), inset 0 0 15px rgba(255, 255, 255, 0.1), inset 0 0 5px rgba(255, 255, 255, 0.05)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 70%)',
        opacity: '0.5',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-strong': {
      background: 'rgba(255, 255, 255, 0.12)',
      backdropFilter: 'blur(16px)',
      border: '1px solid rgba(255, 255, 255, 0.15)',
      boxShadow: '0 8px 12px rgba(0, 0, 0, 0.08)',
    },
    '.glass-effect-strong-lit': {
      background: 'rgba(255, 255, 255, 0.12)',
      backdropFilter: 'blur(16px)',
      border: '1px solid rgba(255, 255, 255, 0.15)',
      boxShadow: '0 8px 12px rgba(0, 0, 0, 0.08), inset 0 0 25px rgba(255, 255, 255, 0.2), inset 0 0 15px rgba(255, 255, 255, 0.15)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 70%)',
        opacity: '0.8',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-brand': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 45%, 0.08) 0%, hsla(144, 40%, 40%, 0.1) 35%, hsla(144, 40%, 45%, 0.08) 100%)',
      color: 'var(--slate-900)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    },
    '.glass-effect-brand-lit': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 45%, 0.08) 0%, hsla(144, 40%, 40%, 0.1) 35%, hsla(144, 40%, 45%, 0.08) 100%)',
      color: 'var(--slate-900)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1), inset 0 0 25px hsla(145, 40%, 80%, 0.15), inset 0 0 15px hsla(145, 40%, 80%, 0.1)',
      borderBottom: '1px solid hsla(145, 40%, 40%, 0.2)',
      borderLeft: '1px solid hsla(145, 40%, 40%, 0.2)',
      borderTop: '1px solid hsla(145, 40%, 70%, 0.2)',
      borderRight: '1px solid hsla(145, 40%, 70%, 0.2)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(145, 40%, 80%, 0.15) 0%, transparent 70%)',
        opacity: '0.8',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-brand-medium-lit': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 45%, 0.4) 0%, hsla(144, 40%, 40%, 0.5) 35%, hsla(144, 40%, 45%, 0.4) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05), inset 0 0 20px hsla(145, 40%, 80%, 0.1), inset 0 0 10px hsla(145, 40%, 80%, 0.05)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(145, 40%, 80%, 0.1) 0%, transparent 70%)',
        opacity: '0.6',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-brand-alt-lit': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 56%, 0.08) 0%, hsla(55, 55%, 60%, 0.08) 35%, hsla(55, 55%, 58%, 0.08) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05), inset 0 0 20px hsla(145, 40%, 80%, 0.1), inset 0 0 10px hsla(145, 40%, 80%, 0.05)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(145, 40%, 80%, 0.1) 0%, transparent 70%)',
        opacity: '0.6',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-brand-strong': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 45%, 0.7) 0%, hsla(144, 40%, 40%, 0.7) 35%, hsla(144, 40%, 45%, 0.7) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.4))',
      borderBottom: '1px solid  hsla(145, 40%, 40%, 0.3)',
      borderLeft: '1px solid  hsla(145, 40%, 40%, 0.3)',
      borderTop: '1px solid  hsla(145, 40%, 70%, 0.3)',
      borderRight: '1px solid  hsla(145, 40%, 70%, 0.3)',
    },
    '.glass-effect-brand-strong-lit': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 45%, 0.7) 0%, hsla(144, 40%, 40%, 0.7) 35%, hsla(144, 40%, 45%, 0.7) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1), inset 0 0 25px hsla(145, 40%, 80%, 0.15), inset 0 0 15px hsla(145, 40%, 80%, 0.1)',
      borderBottom: '1px solid  hsla(145, 40%, 40%, 0.3)',
      borderLeft: '1px solid  hsla(145, 40%, 40%, 0.3)',
      borderTop: '1px solid  hsla(145, 40%, 70%, 0.3)',
      borderRight: '1px solid  hsla(145, 40%, 70%, 0.3)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(145, 40%, 80%, 0.15) 0%, transparent 70%)',
        opacity: '0.8',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-brand-alt-strong': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 56%, 0.7) 0%, hsla(55, 55%, 60%, 0.7) 35%, hsla(55, 55%, 58%, 0.7) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.4))',
      borderBottom: '1px solid hsla(55, 55%, 40%, 0.2)',
      borderLeft: '1px solid hsla(55, 55%, 40%, 0.2)',
      borderTop: '1px solid hsla(55, 55%, 70%, 0.2)',
      borderRight: '1px solid hsla(55, 55%, 70%, 0.2)',
    },
    '.glass-effect-brand-alt-strong-lit': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 56%, 0.7) 0%, hsla(55, 55%, 60%, 0.7) 35%, hsla(55, 55%, 58%, 0.7) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1), inset 0 0 25px hsla(55, 55%, 80%, 0.15), inset 0 0 15px hsla(55, 55%, 80%, 0.1)',
      borderBottom: '1px solid hsla(55, 55%, 40%, 0.2)',
      borderLeft: '1px solid hsla(55, 55%, 40%, 0.2)',
      borderTop: '1px solid hsla(55, 55%, 70%, 0.2)',
      borderRight: '1px solid hsla(55, 55%, 70%, 0.2)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(55, 55%, 80%, 0.15) 0%, transparent 70%)',
        opacity: '0.8',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-brand-compliment': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 56%, 0.08) 0%, hsla(0, 55%, 60%, 0.08) 35%, hsla(0, 55%, 58%, 0.08) 100%)',
      color: 'var(--slate-900)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.4))',
      borderBottom: '1px solid hsla(0, 55%, 40%, 0.2)',
      borderLeft: '1px solid hsla(0, 55%, 40%, 0.2)',
      borderTop: '1px solid hsla(0, 55%, 70%, 0.2)',
      borderRight: '1px solid hsla(0, 55%, 70%, 0.2)',
    },
    '.glass-effect-brand-compliment-lit': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 56%, 0.08) 0%, hsla(0, 55%, 60%, 0.08) 35%, hsla(0, 55%, 58%, 0.08) 100%)',
      color: 'var(--slate-900)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1), inset 0 0 25px hsla(0, 55%, 80%, 0.15), inset 0 0 15px hsla(0, 55%, 80%, 0.1)',
      borderBottom: '1px solid hsla(0, 55%, 40%, 0.2)',
      borderLeft: '1px solid hsla(0, 55%, 40%, 0.2)',
      borderTop: '1px solid hsla(0, 55%, 70%, 0.2)',
      borderRight: '1px solid hsla(0, 55%, 70%, 0.2)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(0, 55%, 80%, 0.15) 0%, transparent 70%)',
        opacity: '0.8',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-brand-compliment-strong': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 56%, 0.6) 0%, hsla(0, 55%, 60%, 0.6) 35%, hsla(0, 55%, 58%, 0.6) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.4))',
      borderBottom: '1px solid hsla(0, 55%, 40%, 0.3)',
      borderLeft: '1px solid hsla(0, 55%, 40%, 0.3)',
      borderTop: '1px solid hsla(0, 55%, 70%, 0.3)',
      borderRight: '1px solid hsla(0, 55%, 70%, 0.3)',
    },
    '.glass-effect-brand-compliment-strong-lit': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 56%, 0.6) 0%, hsla(0, 55%, 60%, 0.6) 35%, hsla(0, 55%, 58%, 0.6) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1), inset 0 0 25px hsla(0, 55%, 80%, 0.15), inset 0 0 15px hsla(0, 55%, 80%, 0.1)',
      borderBottom: '1px solid hsla(0, 55%, 40%, 0.3)',
      borderLeft: '1px solid hsla(0, 55%, 40%, 0.3)',
      borderTop: '1px solid hsla(0, 55%, 70%, 0.3)',
      borderRight: '1px solid hsla(0, 55%, 70%, 0.3)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(0, 55%, 80%, 0.15) 0%, transparent 70%)',
        opacity: '0.8',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-neutral-strong': {
      background: 'linear-gradient(197deg, hsla(215, 10%, 45%, 0.7) 0%, hsla(215, 10%, 40%, 0.7) 35%, hsla(215, 10%, 45%, 0.7) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.4))',
      borderBottom: '1px solid hsla(215, 20%, 40%, 0.2)',
      borderLeft: '1px solid hsla(215, 20%, 40%, 0.2)',
      borderTop: '1px solid hsla(215, 20%, 70%, 0.2)',
      borderRight: '1px solid hsla(215, 20%, 70%, 0.2)',
    },
    '.glass-effect-neutral-strong-lit': {
      background: 'linear-gradient(197deg, hsla(215, 10%, 45%, 0.7) 0%, hsla(215, 10%, 40%, 0.7) 35%, hsla(215, 10%, 45%, 0.7) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1), inset 0 0 25px hsla(215, 10%, 80%, 0.15), inset 0 0 15px hsla(215, 10%, 80%, 0.1)',
      borderBottom: '1px solid hsla(215, 20%, 40%, 0.2)',
      borderLeft: '1px solid hsla(215, 20%, 40%, 0.2)',
      borderTop: '1px solid hsla(215, 20%, 70%, 0.2)',
      borderRight: '1px solid hsla(215, 20%, 70%, 0.2)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(215, 20%, 80%, 0.15) 0%, transparent 70%)',
        opacity: '0.8',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '.glass-effect-neutral-light-strong': {
      background: 'linear-gradient(197deg, hsla(215, 10%, 95%, 0.7) 0%, hsla(215, 10%, 90%, 0.7) 35%, hsla(215, 10%, 95%, 0.7) 100%)',
      color: 'var(--slate-900)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.4))',
      borderBottom: '1px solid hsla(215, 20%, 40%, 0.2)',
      borderLeft: '1px solid hsla(215, 20%, 40%, 0.2)',
      borderTop: '1px solid hsla(215, 20%, 70%, 0.2)',
      borderRight: '1px solid hsla(215, 20%, 70%, 0.2)',
    },
    '.glass-effect-neutral-light-strong-lit': {
      background: 'linear-gradient(197deg, hsla(215, 10%, 95%, 0.7) 0%, hsla(215, 10%, 90%, 0.7) 35%, hsla(215, 10%, 95%, 0.7) 100%)',
      color: 'var(--slate-900)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1), inset 0 0 25px hsla(215, 10%, 98%, 0.15), inset 0 0 15px hsla(215, 10%, 99%, 0.1)',
      borderBottom: '1px solid hsla(215, 20%, 40%, 0.2)',
      borderLeft: '1px solid hsla(215, 20%, 40%, 0.2)',
      borderTop: '1px solid hsla(215, 20%, 70%, 0.2)',
      borderRight: '1px solid hsla(215, 20%, 70%, 0.2)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(215, 20%, 80%, 0.15) 0%, transparent 70%)',
        opacity: '0.8',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '[data-theme="dark"] .glass-effect': {
      backdropFilter: 'blur(12px)',
      background: 'rgba(0, 0, 0, 0.15)',
      color: 'white',
      border: '1px solid rgba(255, 255, 255, 0.05)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.2)',
      borderBottom: '1px solid rgba(50, 50, 50, 0.3)',
      borderLeft: '1px solid rgba(50, 50, 50, 0.3)',
      borderTop: '1px solid rgba(80, 80, 80, 0.4)',
      borderRight: '1px solid rgba(80, 80, 80, 0.4)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.2))',
    },
    '[data-theme="dark"] .glass-effect-lit': {
      backdropFilter: 'blur(12px)',
      background: 'rgba(0, 0, 0, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.05)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1), inset 0 0 20px rgba(255, 255, 255, 0.05), inset 0 0 10px rgba(255, 255, 255, 0.03)',
      borderBottom: '1px solid rgba(50, 50, 50, 0.3)',
      borderLeft: '1px solid rgba(50, 50, 50, 0.3)',
      borderTop: '1px solid rgba(80, 80, 80, 0.4)',
      borderRight: '1px solid rgba(80, 80, 80, 0.4)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, rgba(100, 100, 100, 0.1) 0%, transparent 70%)',
        opacity: '0.5',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '[data-theme="dark"] .glass-effect-subtle': {
      background: 'rgba(0, 0, 0, 0.15)',
      color: 'white',
      border: '1px solid rgba(255, 255, 255, 0.03)',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15)',
    },
    '[data-theme="dark"] .glass-effect-subtle-lit': {
      background: 'rgba(0, 0, 0, 0.15)',
      color: 'white',
      border: '1px solid rgba(255, 255, 255, 0.03)',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.03), inset 0 0 5px rgba(255, 255, 255, 0.02)',
      backdropFilter: 'blur(8px)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, rgba(100, 100, 100, 0.08) 0%, transparent 70%)',
        opacity: '0.4',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '[data-theme="dark"] .glass-effect-strong': {
      background: 'rgba(0, 0, 0, 0.35)',
      color: 'white',
      border: '1px solid rgba(255, 255, 255, 0.08)',
      boxShadow: '0 8px 12px rgba(0, 0, 0, 0.25)',
    },
    '[data-theme="dark"] .glass-effect-strong-lit': {
      background: 'rgba(0, 0, 0, 0.35)',
      color: 'white',
      border: '1px solid rgba(255, 255, 255, 0.08)',
      boxShadow: '0 8px 12px rgba(0, 0, 0, 0.25), inset 0 0 20px rgba(255, 255, 255, 0.05), inset 0 0 10px rgba(255, 255, 255, 0.03)',
      backdropFilter: 'blur(16px)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, rgba(100, 100, 100, 0.1) 0%, transparent 70%)',
        opacity: '0.5',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '[data-theme="dark"] .glass-effect-brand': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 15%, 0.15) 0%, hsla(144, 40%, 10%, 0.25) 35%, hsla(144, 40%, 15%, 0.15) 100%)',
      color: 'white',
      backdropFilter: 'blur(14px)',
      border: '1px solid rgba(255, 255, 255, 0.05)',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.2)',
    },
    '[data-theme="dark"] .glass-effect-brand-lit': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 15%, 0.5) 0%, hsla(144, 40%, 10%, 0.6) 35%, hsla(144, 40%, 15%, 0.5) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2), inset 0 0 20px hsla(145, 40%, 40%, 0.1), inset 0 0 10px hsla(145, 40%, 40%, 0.05)',
      borderBottom: '1px solid hsla(145, 40%, 10%, 0.2)',
      borderLeft: '1px solid hsla(145, 40%, 10%, 0.2)',
      borderTop: '1px solid hsla(145, 40%, 30%, 0.2)',
      borderRight: '1px solid hsla(145, 40%, 30%, 0.2)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(145, 40%, 40%, 0.1) 0%, transparent 70%)',
        opacity: '0.6',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '[data-theme="dark"] .glass-effect-brand-strong': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 25%, 0.5) 0%, hsla(144, 40%, 20%, 0.5) 35%, hsla(144, 40%, 25%, 0.5) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.8))',
      borderBottom: '1px solid  hsla(145, 40%, 40%, 0.3)',
      borderLeft: '1px solid  hsla(145, 40%, 40%, 0.3)',
      borderTop: '1px solid  hsla(145, 40%, 70%, 0.3)',
      borderRight: '1px solid  hsla(145, 40%, 70%, 0.3)',
    },
    '[data-theme="dark"] .glass-effect-brand-strong-lit': {
      background: 'linear-gradient(197deg, hsla(145, 40%, 25%, 0.5) 0%, hsla(144, 40%, 20%, 0.5) 35%, hsla(144, 40%, 25%, 0.5) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2), inset 0 0 20px hsla(145, 40%, 40%, 0.1), inset 0 0 10px hsla(145, 40%, 40%, 0.05)',
      borderBottom: '1px solid  hsla(145, 40%, 40%, 0.3)',
      borderLeft: '1px solid  hsla(145, 40%, 40%, 0.3)',
      borderTop: '1px solid  hsla(145, 40%, 70%, 0.3)',
      borderRight: '1px solid  hsla(145, 40%, 70%, 0.3)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(145, 40%, 40%, 0.1) 0%, transparent 70%)',
        opacity: '0.6',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '[data-theme="dark"] .glass-effect-brand-alt-strong': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 56%, 0.4) 0%, hsla(55, 55%, 60%, 0.5) 35%, hsla(55, 55%, 58%, 0.4) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.7))',
      borderBottom: '1px solid hsla(55, 55%, 10%, 0.2)',
      borderLeft: '1px solid hsla(55, 55%, 10%, 0.2)',
      borderTop: '1px solid hsla(55, 55%, 30%, 0.2)',
      borderRight: '1px solid hsla(55, 55%, 30%, 0.2)',
    },
    '[data-theme="dark"] .glass-effect-brand-alt-strong-lit': {
      background: 'linear-gradient(197deg, hsla(55, 55%, 56%, 0.4) 0%, hsla(55, 55%, 60%, 0.5) 35%, hsla(55, 55%, 58%, 0.4) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2), inset 0 0 20px hsla(55, 55%, 40%, 0.1), inset 0 0 10px hsla(55, 55%, 40%, 0.05)',
      borderBottom: '1px solid hsla(55, 55%, 10%, 0.2)',
      borderLeft: '1px solid hsla(55, 55%, 10%, 0.2)',
      borderTop: '1px solid hsla(55, 55%, 30%, 0.2)',
      borderRight: '1px solid hsla(55, 55%, 30%, 0.2)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(55, 55%, 40%, 0.1) 0%, transparent 70%)',
        opacity: '0.6',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '[data-theme="dark"] .glass-effect-brand-compliment': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 36%, 0.5) 0%, hsla(0, 55%, 40%, 0.6) 35%, hsla(0, 55%, 38%, 0.5) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.7))',
      borderBottom: '1px solid hsla(0, 55%, 10%, 0.2)',
      borderLeft: '1px solid hsla(0, 55%, 10%, 0.2)',
      borderTop: '1px solid hsla(0, 55%, 30%, 0.2)',
      borderRight: '1px solid hsla(0, 55%, 30%, 0.2)',
    },
    '[data-theme="dark"] .glass-effect-brand-compliment-lit': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 36%, 0.5) 0%, hsla(0, 55%, 40%, 0.6) 35%, hsla(0, 55%, 38%, 0.5) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2), inset 0 0 20px hsla(0, 55%, 40%, 0.1), inset 0 0 10px hsla(0, 55%, 40%, 0.05)',
      borderBottom: '1px solid hsla(0, 55%, 10%, 0.2)',
      borderLeft: '1px solid hsla(0, 55%, 10%, 0.2)',
      borderTop: '1px solid hsla(0, 55%, 30%, 0.2)',
      borderRight: '1px solid hsla(0, 55%, 30%, 0.2)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(0, 55%, 40%, 0.1) 0%, transparent 70%)',
        opacity: '0.6',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '[data-theme="dark"] .glass-effect-brand-compliment-strong': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 36%, 0.7) 0%, hsla(0, 55%, 40%, 0.8) 35%, hsla(0, 55%, 38%, 0.7) 100%)',
      backdropFilter: 'blur(12px)',
      color: 'white',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.8))',
      borderBottom: '1px solid hsla(0, 55%, 10%, 0.3)',
      borderLeft: '1px solid hsla(0, 55%, 10%, 0.3)',
      borderTop: '1px solid hsla(0, 55%, 30%, 0.3)',
      borderRight: '1px solid hsla(0, 55%, 30%, 0.3)',
    },
    '[data-theme="dark"] .glass-effect-brand-compliment-strong-lit': {
      background: 'linear-gradient(197deg, hsla(0, 55%, 36%, 0.7) 0%, hsla(0, 55%, 40%, 0.8) 35%, hsla(0, 55%, 38%, 0.7) 100%)',
      color: 'white',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2), inset 0 0 20px hsla(0, 55%, 40%, 0.15), inset 0 0 10px hsla(0, 55%, 40%, 0.1)',
      borderBottom: '1px solid hsla(0, 55%, 10%, 0.3)',
      borderLeft: '1px solid hsla(0, 55%, 10%, 0.3)',
      borderTop: '1px solid hsla(0, 55%, 30%, 0.3)',
      borderRight: '1px solid hsla(0, 55%, 30%, 0.3)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(0, 55%, 40%, 0.15) 0%, transparent 70%)',
        opacity: '0.7',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
    '[data-theme="dark"] .glass-effect-neutral': {
      background: 'linear-gradient(197deg, hsla(215, 20%, 25%, 0.5) 0%, hsla(215, 20%, 20%, 0.5) 35%, hsla(215, 20%, 25%, 0.5) 100%)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      filter: 'drop-shadow(0 0 12px rgba(100,100,100,0.7))',
      borderBottom: '1px solid hsla(215, 20%, 10%, 0.2)',
      borderLeft: '1px solid hsla(215, 20%, 10%, 0.2)',
      borderTop: '1px solid hsla(215, 20%, 30%, 0.2)',
      borderRight: '1px solid hsla(215, 20%, 30%, 0.2)',
    },
    '[data-theme="dark"] .glass-effect-neutral-lit': {
      background: 'linear-gradient(197deg, hsla(215, 20%, 25%, 0.5) 0%, hsla(215, 20%, 20%, 0.5) 35%, hsla(215, 20%, 25%, 0.5) 100%)',
      backdropFilter: 'blur(12px)',
      border: '1px solid rgba(255, 255, 255, 0.12)',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2), inset 0 0 20px hsla(215, 20%, 40%, 0.1), inset 0 0 10px hsla(215, 20%, 40%, 0.05)',
      borderBottom: '1px solid hsla(215, 20%, 10%, 0.2)',
      borderLeft: '1px solid hsla(215, 20%, 10%, 0.2)',
      borderTop: '1px solid hsla(215, 20%, 30%, 0.2)',
      borderRight: '1px solid hsla(215, 20%, 30%, 0.2)',
      position: 'relative',
      
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'radial-gradient(circle at 30% 30%, hsla(215, 20%, 40%, 0.1) 0%, transparent 70%)',
        opacity: '0.6',
        pointerEvents: 'none',
        mixBlendMode: 'overlay'
      }
    },
  };

  addUtilities(glassEffectUtilities, ['responsive']);
}
