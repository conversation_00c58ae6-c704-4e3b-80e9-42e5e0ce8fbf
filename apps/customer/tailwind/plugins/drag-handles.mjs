/**
 * Drag handle utilities plugin for TipTap editor blocks
 */
export default function dragHandlesPlugin({ addUtilities, addComponents, theme }) {
  // Base drag handle component
  const dragHandleComponents = {
    '.drag-handle': {
      position: 'absolute',
      left: '-2rem',
      top: '50%',
      transform: 'translateY(-50%)',
      width: '1.5rem',
      height: '1.5rem',
      cursor: 'grab',
      opacity: '0',
      transition: 'all 0.2s ease-in-out',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: '10',
      userSelect: 'none',
      pointerEvents: 'auto',
      borderRadius: '0.75rem',
      
      // Glass effect styling
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      
      // Grip dots using CSS
      '&::before': {
        content: '""',
        width: '0.25rem',
        height: '0.25rem',
        background: 'currentColor',
        borderRadius: '50%',
        boxShadow: `
          0 0.375rem 0 currentColor,
          0.375rem 0 0 currentColor,
          0.375rem 0.375rem 0 currentColor
        `,
        opacity: '0.6',
      },
      
      '&:hover': {
        background: 'rgba(255, 255, 255, 0.15)',
        borderColor: 'rgba(255, 255, 255, 0.3)',
        transform: 'translateY(-50%) scale(1.1)',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      
      '&:active': {
        cursor: 'grabbing',
        background: 'rgba(255, 255, 255, 0.2)',
        transform: 'translateY(-50%) scale(0.95)',
      },
    },
    
    // Report component specific styling
    '.drag-handle-report': {
      left: '-2.5rem',
      background: 'rgba(34, 197, 94, 0.1)',
      borderColor: 'rgba(34, 197, 94, 0.2)',
      color: 'rgb(34, 197, 94)',
      
      '&:hover': {
        background: 'rgba(34, 197, 94, 0.15)',
        borderColor: 'rgba(34, 197, 94, 0.3)',
        transform: 'translateY(-50%) scale(1.1)',
      },
    },
    
    // Dragging state - consolidated opacity, cursor, and animation
    '.drag-handle-dragging': {
      opacity: '1',
      cursor: 'grabbing',
      animation: 'dragPulse 0.6s ease-in-out infinite alternate',
    },
  };

  // Utilities for drag handle visibility and positioning
  const dragHandleUtilities = {
    // Show drag handle on hover of parent block
    '.ProseMirror > *:hover .drag-handle': {
      opacity: '1',
    },
    '.report-group:hover .drag-handle': {
      opacity: '1',
    },
    '.report-section:hover .drag-handle': {
      opacity: '1',
    },
    '.report-summary:hover .drag-handle': {
      opacity: '1',
    },
    
    // Always show when dragging - use consolidated class
    '.drag-handle.is-dragging, .drag-handle.drag-handle-dragging': {
      opacity: '1',
      cursor: 'grabbing',
    },
    
    // Ensure proper positioning for different block types
    '.ProseMirror h1, .ProseMirror h2, .ProseMirror h3, .ProseMirror h4, .ProseMirror h5, .ProseMirror h6': {
      position: 'relative',
    },
    '.ProseMirror p, .ProseMirror ul, .ProseMirror ol, .ProseMirror blockquote, .ProseMirror pre, .ProseMirror table': {
      position: 'relative',
    },
    '.report-group, .report-section, .report-summary': {
      position: 'relative',
    },
    
    // Adjust margin for drag handle space
    '.ProseMirror': {
      paddingLeft: '2rem',
    },
    
    // Style for when blocks are being dragged over
    '.ProseMirror .drag-over': {
      background: 'rgba(59, 130, 246, 0.1)',
      borderRadius: '0.5rem',
      transition: 'background-color 0.2s ease-in-out',
    },
    
    // Dark mode adjustments
    '[data-theme="dark"] .drag-handle': {
      background: 'rgba(0, 0, 0, 0.1)',
      borderColor: 'rgba(0, 0, 0, 0.2)',
      color: 'rgba(255, 255, 255, 0.7)',
      
      '&:hover': {
        background: 'rgba(0, 0, 0, 0.15)',
        borderColor: 'rgba(0, 0, 0, 0.3)',
        color: 'rgba(255, 255, 255, 0.9)',
      },
      
      '&:active': {
        background: 'rgba(0, 0, 0, 0.2)',
      },
    },
    
    // Print mode - hide drag handles
    '@media print': {
      '.drag-handle': {
        display: 'none !important',
      },
      '.ProseMirror': {
        paddingLeft: '0',
      },
    },
    
    // Responsive adjustments
    '@media (max-width: 768px)': {
      '.drag-handle': {
        left: '-1.5rem',
        width: '1.25rem',
        height: '1.25rem',
      },
      '.ProseMirror': {
        paddingLeft: '1.5rem',
      },
    },
  };

  // Keyframe animations
  const animations = {
    '@keyframes dragPulse': {
      'from': {
        transform: 'translateY(-50%) scale(0.95)',
        opacity: '0.8',
      },
      'to': {
        transform: 'translateY(-50%) scale(1.05)',
        opacity: '1',
      },
    },
  };

  // Add all utilities and components
  addComponents(dragHandleComponents);
  addUtilities({
    ...dragHandleUtilities,
    ...animations,
  }, ['responsive', 'hover', 'focus', 'active']);
}
