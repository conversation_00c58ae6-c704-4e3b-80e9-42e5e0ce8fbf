/**
 * Layout utilities plugin for Tailwind CSS
 */
export default function layoutPlugin({ addUtilities }) {
  // Grid utilities
  const gridUtilities = {
    '.grid-2-cols': {
      display: 'grid',
      gridTemplateColumns: '1fr',
      gap: '2rem',
      '@media (min-width: 640px)': {
        gridTemplateColumns: 'repeat(2, 1fr)',
      },
    },
    '.grid-3-cols': {
      display: 'grid',
      gridTemplateColumns: '1fr',
      gap: '2rem',
      '@media (min-width: 768px)': {
        gridTemplateColumns: 'repeat(3, 1fr)',
      },
    },
    '.grid-4-cols': {
      display: 'grid',
      gridTemplateColumns: '1fr',
      gap: '2rem',
      '@media (min-width: 1024px)': {
        gridTemplateColumns: 'repeat(4, 1fr)',
      },
    },
  };
  
  addUtilities(gridUtilities, ['responsive']);
}
