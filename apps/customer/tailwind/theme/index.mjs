/**
 * Theme configuration for Tailwind CSS
 */
import { colors } from './colors.mjs'

export const theme = {
  container: {
    center: true,
    padding: {
      '2xl': '2rem',
      DEFAULT: '1rem',
      lg: '2rem',
      md: '2rem',
      sm: '1rem',
      xl: '2rem',
    },
    screens: {
      '2xl': '86rem',
      lg: '64rem',
      md: '48rem',
      sm: '40rem',
      xl: '80rem',
    },
  },
  colors: {
    ...colors,
    background: 'hsl(var(--background))',
    foreground: 'hsl(var(--foreground))',
    border: 'hsl(var(--border))',
    popover: {
      DEFAULT: "hsl(var(--popover))",
      foreground: "hsl(var(--popover-foreground))",
    },
    card: {
      DEFAULT: 'hsl(var(--card))',
      foreground: 'hsl(var(--card-foreground))',
    },
    primary: {
      DEFAULT: 'hsl(var(--primary))',
      foreground: 'hsl(var(--primary-foreground))',
    },
    secondary: {
      DEFAULT: 'hsl(var(--secondary))',
      foreground: 'hsl(var(--secondary-foreground))',
    },
    muted: {
      DEFAULT: 'hsl(var(--muted))',
      foreground: 'hsl(var(--muted-foreground))',
    },
    accent: {
      DEFAULT: 'hsl(var(--accent))',
      foreground: 'hsl(var(--accent-foreground))',
    },
    destructive: {
      DEFAULT: 'hsl(var(--destructive))',
      foreground: 'hsl(var(--destructive-foreground))',
    },
  },
  extend: {
    animation: {
      'accordion-down': 'accordion-down 0.2s ease-out',
      'accordion-up': 'accordion-up 0.2s ease-out',
      aurora: 'aurora 120s cubic-bezier(0.2, 0, 0.2, 1) infinite',
      'aurora-slow': 'aurora 240s cubic-bezier(0.2, 0, 0.2, 1) infinite',
      'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      wiggle: 'wiggle 0.4s ease-in-out 3',
      'float': 'float 6s ease-in-out infinite',
      'float-slow': 'float 8s ease-in-out infinite alternate',
      'scroll': 'scroll 25s linear infinite',
      'scroll-reverse': 'scroll-reverse 25s linear infinite',
    },
    keyframes: {
      'accordion-down': {
        from: { height: '0' },
        to: { height: 'var(--radix-accordion-content-height)' },
      },
      'accordion-up': {
        from: { height: 'var(--radix-accordion-content-height)' },
        to: { height: '0' },
      },
      aurora: {
        '0%': {
          backgroundPosition: '0% 50%, 0% 50%',
          transform: 'translate3d(0, 0, 0)',
        },
        '50%': {
          backgroundPosition: '100% 50%, 100% 50%',
          transform: 'translate3d(0, 0, 0)',
        },
        '100%': {
          backgroundPosition: '200% 50%, 200% 50%',
          transform: 'translate3d(0, 0, 0)',
        },
      },
      wiggle: {
        '0%, 100%': { transform: 'rotate(-3deg) translate3d(0, 0, 0)' },
        '50%': { transform: 'rotate(3deg) translate3d(0, 0, 0)' },
      },
      float: {
        '0%, 100%': { transform: 'translate3d(0, 0, 0)' },
        '50%': { transform: 'translate3d(0, -10px, 0)' },
      },
      scroll: {
        to: {
          transform: 'translate3d(-100%, 0, 0)',
        },
      },
      'scroll-reverse': {
        to: {
          transform: 'translate3d(100%, 0, 0)',
        },
      },
    },
    backgroundImage: {
      'brand-gradient': 'linear-gradient(197deg, hsl(145, 35%, 50%) 0%, hsl(144, 35%, 54%) 35%, hsl(144, 35%, 52%) 100%)',
      'brand-glass': 'linear-gradient(197deg, hsla(145, 40%, 50%, 0.4) 0%, hsla(144, 40%, 56%, 0.4) 35%, hsla(144, 40%, 52%, 0.4) 100%)',
      'brand-gradient-dark': 'linear-gradient(197deg, hsl(145, 40%, 20%) 0%, hsl(144, 40%, 25%) 35%, hsl(144, 40%, 24%) 100%)',
      'brand-gradient-compliment': 'linear-gradient(197deg, hsl(55, 55%, 56%) 0%, hsl(55, 55%, 60%) 35%, hsl(55, 55%, 58%) 100%)',
      'brand-gradient-compliment-dark': 'linear-gradient(197deg, hsl(55, 55%, 36%) 0%, hsl(55, 55%, 34%) 35%, hsl(55, 55%, 36%) 100%)',
      'brand-gradient-accent': 'linear-gradient(197deg, hsl(25, 40%, 50%) 0%, hsl(25, 45%, 53%) 35%, hsl(25, 40%, 52%) 100%)',
      'brand-gradient-accent-dark': 'linear-gradient(197deg, hsl(25, 40%, 50%) 0%, hsl(25, 45%, 53%) 35%, hsl(25, 40%, 52%) 100%)',
    },
    // Shadow utilities
    boxShadow: {
      'subtle': '0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.03)',
      'soft': '0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      'medium': '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.08)',
      'strong': '0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1)',
    },
    // Transition utilities
    transitionProperty: {
      'standard': 'all',
    },
    transitionDuration: {
      'fast': '150ms',
      'standard': '250ms',
      'slow': '350ms',
      '1000': '1000ms',
      '1500': '1500ms',
      '2000': '2000ms',
    },
    transitionTimingFunction: {
      'standard': 'ease-in-out',
    },
    // Border radius utilities
    borderRadius: {
      'standard': '1.5rem',
      'small': '0.5rem',
      'medium': '0.75rem',
      'large': '1rem',
      'full': '9999px',
    },
    // Spacing utilities
    spacing: {
      // Custom spacing values
    },
    // Typography configuration
    typography: () => ({
      DEFAULT: {
        css: [
          {
            '--tw-prose-body': 'var(--text)',
            '--tw-prose-headings': 'var(--text)',
            h1: {
              fontWeight: '600',
              fontSize: '2.5rem',
              lineHeight: '1.2',
              letterSpacing: '-0.015em',
              marginBottom: '0.5em',
              '@screen md': {
                fontSize: '3.5rem',
              },
            },
            h2: {
              fontWeight: '600',
              fontSize: '2rem',
              lineHeight: '1.3',
              letterSpacing: '-0.015em',
              marginBottom: '0.5em',
              '@screen md': {
                fontSize: '2.5rem',
              },
            },
            h3: {
              fontWeight: '600',
              fontSize: '1.5rem',
              lineHeight: '1.3',
              letterSpacing: '-0.01em',
              marginBottom: '0.5em',
              '@screen md': {
                fontSize: '2rem',
              },
            },
            h4: {
              fontWeight: '600',
              fontSize: '1.25rem',
              lineHeight: '1.4',
              marginBottom: '0.5em',
              '@screen md': {
                fontSize: '1.5rem',
              },
            },
            h5: {
              fontWeight: '600',
              fontSize: '1.125rem',
              lineHeight: '1.4',
              marginBottom: '0.5em',
            },
            h6: {
              fontWeight: '600',
              fontSize: '1rem',
              lineHeight: '1.5',
              marginBottom: '0.5em',
            },
            p: {
              fontSize: '1rem',
              lineHeight: '1.65',
              marginBottom: '1.25em',
              maxWidth: '70ch',
              '@screen md': {
                fontSize: '1.125rem',
              },
            },
            a: {
              textDecoration: 'underline',
              textDecorationStyle: 'dotted',
              textDecorationThickness: '1px',
              textDecorationColor: 'color-mix(in srgb, currentColor 50%, transparent)',
              textUnderlineOffset: '2px',
              transition: 'all 250ms ease-in-out',
              '&:hover': {
                textDecoration: 'underline',
                textDecorationStyle: 'solid',
              },
            },
            ul: {
              marginBottom: '1.5rem',
              marginLeft: '1.5rem',
              listStyleType: 'disc',
            },
            ol: {
              marginBottom: '1.5rem',
              marginLeft: '1.5rem',
              listStyleType: 'decimal',
            },
            li: {
              marginBottom: '0.5rem',
            },
            blockquote: {
              borderLeftColor: 'hsl(var(--brand-primary))',
              fontStyle: 'italic',
              marginTop: '1.5rem',
              marginBottom: '1.5rem',
            },
            code: {
              backgroundColor: 'hsl(var(--muted))',
              borderRadius: '0.25rem',
              padding: '0.125rem 0.25rem',
              fontSize: '0.875rem',
            },
            pre: {
              backgroundColor: 'hsl(var(--muted))',
              borderRadius: '0.5rem',
              padding: '1rem',
              marginTop: '1.5rem',
              marginBottom: '1.5rem',
              overflowX: 'auto',
            },
            img: {
              borderRadius: '0.75rem',
              marginTop: '1.5rem',
              marginBottom: '1.5rem',
            },
            hr: {
              borderColor: 'hsl(var(--border))',
              marginTop: '2rem',
              marginBottom: '2rem',
            },
            table: {
              width: '100%',
              borderCollapse: 'collapse',
              marginTop: '1.5rem',
              marginBottom: '1.5rem',
            },
            'th, td': {
              border: '1px solid hsl(var(--border))',
              padding: '0.5rem',
            },
            th: {
              backgroundColor: 'hsl(var(--muted))',
              fontWeight: '600',
            },
          },
        ],
      },
      md: {
        css: [
          {
            h1: {
              fontSize: '3.5rem',
            },
            h2: {
              fontSize: '1.5rem',
            },
          },
        ],
      },
      sm: {
        css: [
          {
            h1: {
              fontSize: '2.5rem',
            },
            h2: {
              fontSize: '1.5rem',
            },
          },
        ],
      },
    }),
    fontFamily: {
      mono: ['var(--font-geist-mono)'],
      sans: ['var(--font-geist-sans)'],
    },
  },
};
