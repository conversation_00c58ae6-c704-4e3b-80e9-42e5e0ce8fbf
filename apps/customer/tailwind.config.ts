import tailwindcssAnimate from 'tailwindcss-animate'
import typography from '@tailwindcss/typography'
import { customPlugins } from './tailwind/plugins/index.mjs'
import { theme } from './tailwind/theme/index.mjs'

const {
  default: flattenColorPalette,
} = require('tailwindcss/lib/util/flattenColorPalette')

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    '../../packages/ui/src/**/*{.js,.ts,.jsx,.tsx}',
  ],
  darkMode: ['selector', '[data-theme="dark"]'],
  plugins: [
    tailwindcssAnimate,
    typography,
    addVariablesForColors,
    require('tailwindcss-textshadow'),
    ...customPlugins,
  ],
  prefix: '',
  safelist: [
    'lg:col-span-4',
    'lg:col-span-6',
    'lg:col-span-8',
    'lg:col-span-12',
    'border-border',
    'bg-card',
    'border-error',
    'bg-error/30',
    'border-success',
    'bg-success/30',
    'border-warning',
    'bg-warning/30',
    'animate-scroll',
    'animate-scroll-reverse',
    'bg-brand-gradient',
    'bg-brand-glass',
    'bg-brand-gradient-dark',
    'bg-brand-gradient-compliment',
    'bg-brand-gradient-compliment-dark',
    'bg-brand-gradient-accent',
    'bg-brand-gradient-accent-dark',
    'backdrop-blur-md',
    // HighlightedText block colors
    'from-brand/5', 'from-brand/10', 'from-brand/15', 'from-brand/30',
    'from-blue-500/5', 'from-blue-500/10', 'from-blue-500/15', 'from-blue-500/30',
    'from-purple-500/5', 'from-purple-500/10', 'from-purple-500/15', 'from-purple-500/30',
    'from-amber-500/5', 'from-amber-500/10', 'from-amber-500/15', 'from-amber-500/30',
    'from-teal-500/5', 'from-teal-500/10', 'from-teal-500/15', 'from-teal-500/30',
    'from-rose-500/5', 'from-rose-500/10', 'from-rose-500/15', 'from-rose-500/30',
    'to-brand/5', 'to-brand/10', 'via-brand/5', 'via-brand/10',
    'to-blue-500/5', 'to-blue-500/10', 'via-blue-500/5', 'via-blue-500/10',
    'to-purple-500/5', 'to-purple-500/10', 'via-purple-500/5', 'via-purple-500/10',
    'to-amber-500/5', 'to-amber-500/10', 'via-amber-500/5', 'via-amber-500/10',
    'to-teal-500/5', 'to-teal-500/10', 'via-teal-500/5', 'via-teal-500/10',
    'to-rose-500/5', 'to-rose-500/10', 'via-rose-500/5', 'via-rose-500/10',
    'to-brand-light/5', 'to-brand-light/10', 'from-brand-light/15',
    'to-blue-300/5', 'to-blue-300/10', 'from-blue-300/15',
    'to-purple-300/5', 'to-purple-300/10', 'from-purple-300/15',
    'to-amber-300/5', 'to-amber-300/10', 'from-amber-300/15',
    'to-teal-300/5', 'to-teal-300/10', 'from-teal-300/15',
    'to-rose-300/5', 'to-rose-300/10', 'from-rose-300/15',
    'border-brand/10', 'border-brand/20', 'border-brand/30',
    'border-blue-500/10', 'border-blue-500/20', 'border-blue-500/30',
    'border-purple-500/10', 'border-purple-500/20', 'border-purple-500/30',
    'border-amber-500/10', 'border-amber-500/20', 'border-amber-500/30',
    'border-teal-500/10', 'border-teal-500/20', 'border-teal-500/30',
    'border-rose-500/10', 'border-rose-500/20', 'border-rose-500/30',
    'bg-brand/5', 'bg-blue-500/5', 'bg-purple-500/5', 'bg-amber-500/5', 'bg-teal-500/5', 'bg-rose-500/5',
    'bg-brand/10', 'bg-blue-500/10', 'bg-purple-500/10', 'bg-amber-500/10', 'bg-teal-500/10', 'bg-rose-500/10',
    'bg-brand/15', 'bg-blue-500/15', 'bg-purple-500/15', 'bg-amber-500/15', 'bg-teal-500/15', 'bg-rose-500/15',
    'border-l-brand', 'border-l-blue-500', 'border-l-purple-500', 'border-l-amber-500', 'border-l-teal-500', 'border-l-rose-500',
    'border-l-brand/40', 'border-l-blue-500/40', 'border-l-purple-500/40', 'border-l-amber-500/40', 'border-l-teal-500/40', 'border-l-rose-500/40',
    'border-l-brand/60', 'border-l-blue-500/60', 'border-l-purple-500/60', 'border-l-amber-500/60', 'border-l-teal-500/60', 'border-l-rose-500/60',
    'text-brand/20', 'text-blue-500/20', 'text-purple-500/20', 'text-amber-500/20', 'text-teal-500/20', 'text-rose-500/20',
    'prose-strong:text-brand', 'prose-strong:text-blue-500', 'prose-strong:text-purple-500', 'prose-strong:text-amber-500', 'prose-strong:text-teal-500', 'prose-strong:text-rose-500',
    'prose-headings:text-brand-dark', 'prose-headings:text-blue-700', 'prose-headings:text-purple-700', 'prose-headings:text-amber-700', 'prose-headings:text-teal-700', 'prose-headings:text-rose-700',
    'dark:prose-headings:text-brand-light', 'dark:prose-headings:text-blue-300', 'dark:prose-headings:text-purple-300', 'dark:prose-headings:text-amber-300', 'dark:prose-headings:text-teal-300', 'dark:prose-headings:text-rose-300',
    // Animation classes for HighlightedText - mostly handled by Framer Motion now
    'transition-all', 'duration-300', 'duration-500', 'duration-700',
    'hover:scale-[1.02]', 'hover:translate-x-1', 'hover:translate-y-[-5px]',
    'animate-float', 'animate-float-slow', 'animate-pulse-slow',
    'hover:bg-gradient-to-tr', 'hover:bg-foreground/10',
    'group-hover:opacity-30', 'group-hover:opacity-40', 'group-hover:opacity-100',
    'group-hover:w-1/2', 'group-hover:h-1/2',
    'group-hover:bg-brand/10', 'group-hover:bg-blue-500/10', 'group-hover:bg-purple-500/10', 'group-hover:bg-amber-500/10', 'group-hover:bg-teal-500/10', 'group-hover:bg-rose-500/10',
    'group-hover:bg-brand/15', 'group-hover:bg-blue-500/15', 'group-hover:bg-purple-500/15', 'group-hover:bg-amber-500/15', 'group-hover:bg-teal-500/15', 'group-hover:bg-rose-500/15',
    'group-hover:bg-foreground/10', 'hover:shadow-lg',
    'bg-[radial-gradient(circle,_rgba(var(--brand)_/_0.1)_0%,_transparent_25%)]',
    'bg-[radial-gradient(circle,_rgba(var(--blue-500)_/_0.1)_0%,_transparent_25%)]',
    'bg-[radial-gradient(circle,_rgba(var(--purple-500)_/_0.1)_0%,_transparent_25%)]',
    'bg-[radial-gradient(circle,_rgba(var(--amber-500)_/_0.1)_0%,_transparent_25%)]',
    'bg-[radial-gradient(circle,_rgba(var(--teal-500)_/_0.1)_0%,_transparent_25%)]',
    'bg-[radial-gradient(circle,_rgba(var(--rose-500)_/_0.1)_0%,_transparent_25%)]',
    'via-brand-accent/5', 'via-cyan-400/5', 'via-fuchsia-400/5', 'via-orange-400/5', 'via-emerald-400/5', 'via-pink-400/5',
    // Neutral color utility classes
    'text-neutral-dark', 'text-neutral-medium', 'text-neutral-light', 'text-neutral-subtle',
    'bg-neutral-dark', 'bg-neutral-medium', 'bg-neutral-light', 'bg-neutral-subtle',
    'border-neutral-dark', 'border-neutral-medium', 'border-neutral-light', 'border-neutral-subtle',
    'column-block', 'column-block-hovered', 'column', 'layout-selector',
    // Drag handle classes
    'drag-handle', 'drag-handle-report', 'drag-handle-dragging', 'is-dragging', 'drag-over',
  ],
  theme: {
    ...theme,
    extend: {
      ...theme.extend,
      screens: {
        print: { raw: 'print' },
        screen: { raw: 'screen' },
      },
    },
  },
}

// This plugin adds each Tailwind color as a global CSS variable, e.g. var(--gray-200).
function addVariablesForColors({ addBase, theme }:{addBase: any, theme: any}) {
  let allColors = flattenColorPalette(theme('colors'))
  let newVars = Object.fromEntries(
    Object.entries(allColors).map(([key, val]) => [`--${key}`, val]),
  )

  addBase({
    ':root': newVars,
  })
}

export default config
