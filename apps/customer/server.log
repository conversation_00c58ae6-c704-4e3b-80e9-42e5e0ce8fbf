
> dev
> next dev --port 3000

   ▲ Next.js 15.3.2
   - Local:        http://localhost:3000
   - Network:      http://**********:3000
   - Environments: .env.development.local
   - Experiments (use with caution):
     · clientTraceMetadata

 ✓ Starting...
 ○ Compiling /instrumentation ...
 ✓ Compiled /instrumentation in 4.1s (973 modules)
 ✓ Ready in 8.4s
 ○ Compiling /middleware ...
 ✓ Compiled /middleware in 1515ms (599 modules)
 ○ Compiling / ...

warn - The `min-*` and `max-*` variants are not supported with a `screens` configuration containing objects.
 ⚠ ./app/globals.css.webpack[javascript/auto]!=!../../node_modules/.pnpm/next@15.3.2_@babel+core@7.27.1_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19._ji42l52e4uqhoe4xlmjgtal2xu/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../../node_modules/.pnpm/next@15.3.2_@babel+core@7.27.1_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19._ji42l52e4uqhoe4xlmjgtal2xu/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css
Warning

(565:5) Nested CSS was detected, but CSS nesting has not been configured correctly.
Please enable a CSS nesting plugin *before* Tailwind in your configuration.
See how here: https://tailwindcss.com/docs/using-with-preprocessors#nesting

Import trace for requested module:
./app/globals.css.webpack[javascript/auto]!=!../../node_modules/.pnpm/next@15.3.2_@babel+core@7.27.1_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19._ji42l52e4uqhoe4xlmjgtal2xu/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../../node_modules/.pnpm/next@15.3.2_@babel+core@7.27.1_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19._ji42l52e4uqhoe4xlmjgtal2xu/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css
./app/globals.css
 HEAD / 307 in 15296ms
 ⚠ ./app/globals.css.webpack[javascript/auto]!=!../../node_modules/.pnpm/next@15.3.2_@babel+core@7.27.1_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19._ji42l52e4uqhoe4xlmjgtal2xu/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../../node_modules/.pnpm/next@15.3.2_@babel+core@7.27.1_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19._ji42l52e4uqhoe4xlmjgtal2xu/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css
Warning

(565:5) Nested CSS was detected, but CSS nesting has not been configured correctly.
Please enable a CSS nesting plugin *before* Tailwind in your configuration.
See how here: https://tailwindcss.com/docs/using-with-preprocessors#nesting

Import trace for requested module:
./app/globals.css.webpack[javascript/auto]!=!../../node_modules/.pnpm/next@15.3.2_@babel+core@7.27.1_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19._ji42l52e4uqhoe4xlmjgtal2xu/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../../node_modules/.pnpm/next@15.3.2_@babel+core@7.27.1_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19._ji42l52e4uqhoe4xlmjgtal2xu/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css
./app/globals.css
