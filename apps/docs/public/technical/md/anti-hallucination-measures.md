### Evaluations

When a call is made to an LLM an optional evaluation can be provided. Evaluations are simply tests that check the data
for possible errors. These evaluations are called after the LLM returns and are used to check if the results makes
sense. By checking for obvious mistakes (invalid citations, non-existant enumeration values) we can improve the chances
of getting a sensible answer from the LLM. Retry logic is supported as well as the option to feedback to the LLM what
the problem was allowing it to correct it’s answer.

### Escalation

After half of the possible retries of an evaluation have been performed an optional escalation LLM will be called (if
provided) - this will typically be a frontier model - it will then be used for the remaining half of retries. This is a
little like asking your boss when you can’t solve a problem, assuming your boss is smarter than you.

### Parent Document Retriever

Matched embeddings are converted into several pages from the parent document to give better context.
https://community.fullstackretrieval.com/indexing/parent-document-retriever

### Chain of Thought

The LLM is encouraged to use <thoughts> tags to think through before providing an analysis.

https://arxiv.org/abs/2201.11903

### Reflection

Reflection is used to get an LLLM to check it's own analysis and evaluate whether it still thinks the response is
appropriate, this helps to weed out hallucinated reponses and inaccurate ratings.

### Mixture of Models

The mixture of models approach is used to get a high quality response from low quality LLMs. We use this primarily in
reflection (see above) to get a confidence rating for each flag. It is cheaper than a frontier model and offers
different ‘opinions’ to reduce outlying values.

### Citation Based Verification

This is similar to the Parent Document Retriever strategy, but is used to decide if an analysis needs to be redone.

Various forms of analysis produce citations. Our process uses them as a means to verify the analysis, much in the same
way a peer reviewer would review a paper. Simply put any text we produce that has a citation has a link to the specific
page of a specific document. The verification process loads those pages (and the previous and next ones) into the LLM
context along with the original analysis. One of three possible options are then supplied by the LLM ‘reviewer’,
CORRECT, REDO or INVALID. CORRECT requires no action, REDO means there is some discrepancy between the text and the
cited sources so pass this data to a better (frontier) model to rewrite the analysis. Finally INVALID means that due to
hallucinations or introduction of noise by multiple summations the text is no longer related at all to the citations and
can not be deduced from them. In which case there is no choice but to exclude the analysis completely.
