{"vertex_ai/gemini/2877576961029308416": {"total_statements": 10, "successful_extractions": 10, "failed_extractions": 0, "field_metrics": {}, "overall_accuracy": 0.6615476190476189, "field_accuracy": {"locations": 0.8, "impact": 0.3, "is_impactful_action": 0.7, "is_governance": 0.8, "is_environmental": 0.9, "impact_value": 0.6875, "is_disclosure": 0.6, "object_entities": 0.2, "authors": 0.7, "statement_category": 0.4, "quantities": 0.5599999999999999, "action": 0.8, "is_animal_welfare": 0.9, "company": 0.7, "subject_entities": 0.7, "time": 0.265, "is_social": 0.8, "domain": 0.78, "is_vague": 0.9, "start_year": 0.7, "end_year": 0.7}, "statement_metrics": [{"statement_id": 1561537, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.2500000000000001, "is_disclosure": 0.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.7, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.0, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1558373, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 0.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.75, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.1, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.2, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 0.0, "end_year": 1.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1630246, "field_matches": {"locations": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.0, "is_disclosure": 1.0, "object_entities": 0.5, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 0.0, "end_year": 0.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1234567, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.875, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.7, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.2, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8571428571428571}, {"statement_id": 1603975, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 1.0, "is_governance": 0.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.1, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.25, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 0.0}, "overall_match": 0.6190476190476191}, {"statement_id": 1580715, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 0.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 0.0, "object_entities": 0.5, "authors": 1.0, "statement_category": 0.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 0.0, "time": 0.0, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.5714285714285714}, {"statement_id": 1558383, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 0.0, "statement_category": 1.0, "quantities": 0.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 0.0, "time": 1.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.6666666666666666}, {"statement_id": 1559888, "field_matches": {"locations": 0.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 0.0, "object_entities": 0.0, "authors": 0.0, "statement_category": 1.0, "quantities": 0.9999999999999999, "action": 0.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 1.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.6666666666666666}, {"statement_id": 1634385, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.0, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.0, "is_social": 0.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8095238095238095}, {"statement_id": 1558364, "field_matches": {}, "overall_match": 0.0}], "model_name": "vertex_ai/gemini/2877576961029308416", "gold_standard_model": "gemini-2.5-pro-preview-05-06", "raw_results": [{"statement_id": 1630246, "metadata": {"company": "Athleta", "subject_entities": [{"name": "<PERSON><PERSON><PERSON>", "entity_type": "company", "proper_noun": true}, {"name": "Impetus", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "Athleta", "entity_type": "company", "proper_noun": true}], "authors": [{"name": "<PERSON>", "entity_type": "person", "proper_noun": true}], "domain": "industry.commerce", "impact": null, "impact_value": 0.0, "action": "shipped", "locations": [{"name": "US", "entity_type": "country", "proper_noun": true, "location_type": "country"}], "time": {"from_": {"year": 2024, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2024, "end_year": 2024, "quantities": [], "is_environmental": false, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.commerce": 1.0, "domain.industry.supply_chain": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0}, "success": true}, {"statement_id": 1580715, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "Partnership Finance team", "entity_type": "organisation", "proper_noun": true}, {"name": "E&S team", "entity_type": "organisation", "proper_noun": true}], "object_entities": [{"name": "E&S strategy", "entity_type": "strategy", "proper_noun": true}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "governance.stakeholder_engagement", "impact": null, "impact_value": 0.0, "action": "works closely with", "locations": [], "time": {"from_": {"year": 2022, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2022, "end_year": 2022, "quantities": [], "is_environmental": false, "is_social": false, "is_governance": true, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.governance.stakeholder_engagement": 1.0, "domain.governance.supply_chain_governance": 1.0, "domain.governance.workplace_wellbeing": 1.0, "statement.descriptive": 1.0}, "success": true}, {"statement_id": 1234567, "metadata": {"company": "Example Corporation", "subject_entities": [{"name": "Example Corporation", "entity_type": "company", "proper_noun": true}], "object_entities": [], "authors": [{"name": "Example Corporation", "entity_type": "company", "proper_noun": true}], "domain": "environment.climate_change_c_o2", "impact": "reduced carbon emissions", "impact_value": 0.3, "action": "reduced", "locations": [], "time": {"from_": {"year": 2015, "month": null, "day": null}, "to": {"year": 2023, "month": null, "day": null}, "precision": "year"}, "start_year": 2015, "end_year": 2023, "quantities": [{"amount": 45.0, "entity": {"name": "carbon emissions", "entity_type": "environment.climate_change_c_o2", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "percentage"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "fact"}, "demise": {"domain.environment.climate_change_c_o2": 1.0, "domain.environment.sustainability": 1.0, "impact.benefit_to_environment": 0.5, "impact.duration": 0.5, "impact.proportion": 0.45, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1634385, "metadata": {"company": "<PERSON><PERSON>", "subject_entities": [{"name": "<PERSON><PERSON>", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "employees", "entity_type": "group_of_people", "proper_noun": false}], "authors": [{"name": "<PERSON>", "entity_type": "person", "proper_noun": true}, {"name": "<PERSON>", "entity_type": "person", "proper_noun": true}], "domain": "society.community_development", "impact": null, "impact_value": 0.0, "action": "included photographs", "locations": [], "time": {"from_": {"year": 2018, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2018, "end_year": 2018, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.society.community_development": 0.5, "domain.society.social_good": 0.5, "impact.benefit_to_human_life": 0.3, "impact.duration": 0.2, "object.entity_type.group": 0.5, "statement.descriptive": 0.5, "statement.past_statement": 0.5, "subject.entity_type.organizations": 0.5}, "success": true}, {"statement_id": 1603975, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "object_entities": [], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "equality.socio_economic", "impact": null, "impact_value": 0.0, "action": "invested", "locations": [], "time": {"from_": {"year": 2016, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2016, "end_year": 2017, "quantities": [{"amount": 4.36, "entity": {"name": "total spend", "entity_type": "monetary", "proper_noun": false}, "delta": "inc", "unit": "%", "quantity_type": "percentage"}, {"amount": 2.56, "entity": {"name": "total spend", "entity_type": "monetary", "proper_noun": false}, "delta": null, "unit": "%", "quantity_type": "percentage"}, {"amount": 36.0, "entity": {"name": "additional annualised pay costs", "entity_type": "monetary", "proper_noun": false}, "delta": "inc", "unit": "m", "quantity_type": "currency"}, {"amount": 3.0, "entity": {"name": "NLW", "entity_type": "monetary", "proper_noun": true}, "delta": "inc", "unit": "m", "quantity_type": "currency"}], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "fact"}, "demise": {"domain.equality.socio_economic": 1.0, "domain.governance.compensation": 1.0, "domain.governance.profit_sharing": 1.0, "domain.human_needs.food": 1.0, "domain.human_needs.housing": 1.0, "domain.human_needs.land": 1.0, "domain.human_needs.reproduction": 1.0, "domain.human_needs.sanitation": 1.0, "domain.society.employment": 1.0, "domain.society.wealth": 1.0, "impact.benefit_to_human_life": 0.5, "impact.duration": 0.5, "impact.proportion": 0.5, "motivation.compliant": 1.0, "motivation.genuine": 1.0, "object.entity_type.group": 1.0, "object.entity_type.individual": 1.0, "statement.assertive": 1.0, "statement.beneficial_statement": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1559888, "metadata": {"company": "The Crown Estate", "subject_entities": [{"name": "Rushden Lakes", "entity_type": "facility", "proper_noun": true}], "object_entities": [{"name": "Nene Wetlands", "entity_type": "location", "proper_noun": true}], "authors": [{"name": "The Crown Estate", "entity_type": "company", "proper_noun": true}], "domain": "environment.conservation", "impact": null, "impact_value": 0.0, "action": null, "locations": [{"name": "Rushden Lakes", "entity_type": "facility", "proper_noun": true, "location_type": "place"}, {"name": "Nene Wetlands", "entity_type": "location", "proper_noun": true, "location_type": "natural"}], "time": null, "start_year": 2022, "end_year": 2022, "quantities": [{"amount": 200.0, "entity": {"name": "acres", "entity_type": "number", "proper_noun": false}, "delta": null, "unit": "acres", "quantity_type": "area"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.environment.conservation": 1.0, "domain.industry.real_estate": 1.0, "domain.industry.tourism": 1.0, "statement.descriptive": 1.0}, "success": true}, {"statement_id": 1561537, "metadata": {"company": "<PERSON><PERSON>", "subject_entities": [{"name": "<PERSON><PERSON>", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "single use packaging", "entity_type": "material", "proper_noun": false}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "environment.circular_economy", "impact": "reduced packaging waste", "impact_value": 0.5, "action": "reduced", "locations": [], "time": {"from_": {"year": 2023, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2023, "end_year": 2023, "quantities": [{"amount": 98.0, "entity": {"name": "single use packaging", "entity_type": "material", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "percentage"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.environment.circular_economy": 1.0, "domain.environment.sustainability": 1.0, "impact.benefit_to_environment": 0.5, "impact.proportion": 0.98, "statement.descriptive": 1.0, "statement.past_statement": 1.0}, "success": true}, {"statement_id": 1558364, "metadata": {}, "demise": {}, "success": true}, {"statement_id": 1558373, "metadata": {"company": null, "subject_entities": [{"name": "US", "entity_type": "country", "proper_noun": true}], "object_entities": [{"name": "offline sales", "entity_type": "market", "proper_noun": false}], "authors": [{"name": "Retail Research Institute", "entity_type": "organisation", "proper_noun": true}], "domain": "industry.commerce", "impact": "drop", "impact_value": -0.25, "action": "see", "locations": [{"name": "US", "entity_type": "country", "proper_noun": true, "location_type": "country"}], "time": {"from_": {"year": 2021, "month": null, "day": null}, "to": {"year": 2021, "month": null, "day": null}, "precision": "year"}, "start_year": 2021, "end_year": 2021, "quantities": [{"amount": 22.0, "entity": {"name": "percent", "entity_type": "number", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "percentage"}, {"amount": 27.0, "entity": {"name": "percent", "entity_type": "number", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "percentage"}], "is_environmental": false, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.commerce": 0.8, "domain.industry.retail": 0.9, "domain.society.wealth": 0.7, "impact.harm_to_human_life": 0.5, "impact.proportion": 0.6, "statement.descriptive": 0.8, "statement.future_statement": 0.7}, "success": true}, {"statement_id": 1558383, "metadata": {"company": null, "subject_entities": [{"name": "Brands", "entity_type": "company", "proper_noun": false}], "object_entities": [{"name": "low wages", "entity_type": "monetary", "proper_noun": false}], "authors": [{"name": "Fashion Magazine", "entity_type": "media", "proper_noun": true}], "domain": "society.workers_rights", "impact": null, "impact_value": 0.0, "action": "working to improve", "locations": [], "time": null, "start_year": 2020, "end_year": 2020, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "directive"}, "demise": {"domain.equality.socio_economic": 0.8, "domain.society.workers_rights": 0.9, "statement.directive": 1.0}, "success": true}]}, "vertex_ai/gemini/4972735950175076352": {"total_statements": 10, "successful_extractions": 10, "failed_extractions": 0, "field_metrics": {}, "overall_accuracy": 0.7837244897959185, "field_accuracy": {"locations": 0.8833333333333332, "impact": 0.5, "is_impactful_action": 0.8, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.8332142857142857, "is_disclosure": 1.0, "object_entities": 0.6166666666666666, "authors": 0.9, "statement_category": 0.5, "quantities": 0.5599999999999999, "action": 0.9, "is_animal_welfare": 1.0, "company": 0.7, "subject_entities": 0.7, "time": 0.16499999999999998, "is_social": 0.8, "domain": 0.9, "is_vague": 1.0, "start_year": 0.9, "end_year": 0.8}, "statement_metrics": [{"statement_id": 1561537, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.2500000000000001, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.7, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.0, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8571428571428571}, {"statement_id": 1558373, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 0.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.5, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.1, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.2, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1630246, "field_matches": {"locations": 0.5, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.0, "is_disclosure": 1.0, "object_entities": 0.5, "authors": 1.0, "statement_category": 1.0, "quantities": 0.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 0.0, "end_year": 0.0}, "overall_match": 0.6666666666666666}, {"statement_id": 1234567, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.875, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.7, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.2, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.9047619047619048}, {"statement_id": 1603975, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.0, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.1, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.25, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 0.0}, "overall_match": 0.8095238095238095}, {"statement_id": 1580715, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 0.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 1.0, "object_entities": 0.3333333333333333, "authors": 1.0, "statement_category": 0.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 0.0, "time": 0.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.6190476190476191}, {"statement_id": 1558383, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.8571428571428572, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 1.0, "quantities": 0.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 0.0, "time": 1.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8095238095238095}, {"statement_id": 1559888, "field_matches": {"locations": 0.3333333333333333, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 0.0, "statement_category": 1.0, "quantities": 0.9999999999999999, "action": 0.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 0.0, "time": 0.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.6190476190476191}, {"statement_id": 1634385, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.0, "is_disclosure": 1.0, "object_entities": 0.3333333333333333, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.0, "is_social": 0.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8095238095238095}, {"statement_id": 1558364, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.85, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 1.0, "time": 0.0, "is_social": 0.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8571428571428571}], "model_name": "vertex_ai/gemini/4972735950175076352", "gold_standard_model": "gemini-2.5-pro-preview-05-06", "raw_results": [{"statement_id": 1630246, "metadata": {"company": "Athleta", "subject_entities": [{"name": "<PERSON><PERSON><PERSON>", "entity_type": "company", "proper_noun": true}, {"name": "Impetus", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "Athleta", "entity_type": "company", "proper_noun": true}], "authors": [{"name": "<PERSON>", "entity_type": "person", "proper_noun": true}], "domain": "industry.commerce", "impact": null, "impact_value": 0.0, "action": "shipped", "locations": [{"name": "Portugal", "entity_type": "country", "proper_noun": true, "location_type": "country"}, {"name": "US", "entity_type": "country", "proper_noun": true, "location_type": "country"}], "time": {"from_": {"year": 2024, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2024, "end_year": 2024, "quantities": [{"amount": 180.0, "entity": {"name": "tops", "entity_type": "product", "proper_noun": false}, "delta": null, "unit": "tops", "quantity_type": "count"}, {"amount": 200.0, "entity": {"name": "pullovers", "entity_type": "product", "proper_noun": false}, "delta": null, "unit": "pullovers", "quantity_type": "count"}], "is_environmental": false, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.commerce": 1.0, "domain.industry.supply_chain": 1.0, "statement.describes_action": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1580715, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "Partnership Finance team", "entity_type": "department", "proper_noun": true}], "object_entities": [{"name": "E&S team", "entity_type": "department", "proper_noun": true}, {"name": "E&S strategy", "entity_type": "strategy", "proper_noun": false}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "governance.general", "impact": null, "impact_value": 0.0, "action": "works closely with", "locations": [], "time": {"from_": {"year": 2022, "month": null, "day": null}, "to": {"year": 2022, "month": null, "day": null}, "precision": "year"}, "start_year": 2022, "end_year": 2022, "quantities": [], "is_environmental": false, "is_social": false, "is_governance": true, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": true, "statement_category": "fact"}, "demise": {"domain.governance.communication": 1.0, "domain.governance.stakeholder_engagement": 1.0, "domain.governance.supply_chain_governance": 1.0, "domain.governance.workplace_wellbeing": 1.0, "domain.industry.finance": 1.0, "domain.industry.general": 1.0, "domain.industry.professional_services": 1.0, "domain.society.employment": 1.0, "domain.society.workers_rights": 1.0, "engagement.proactive": 1.0, "engagement.responsive": 1.0, "impact.benefit_to_human_life": 0.5, "motivation.compliant": 1.0, "motivation.genuine": 1.0, "object.entity_type.organizations": 1.0, "statement.assertive": 1.0, "statement.beneficial_statement": 1.0, "statement.describes_action": 1.0, "statement.descriptive": 1.0, "statement.present_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1234567, "metadata": {"company": "Example Corporation", "subject_entities": [{"name": "Example Corporation", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "Scope 1 and 2 carbon emissions", "entity_type": "concept", "proper_noun": false}], "authors": [{"name": "Example Corporation", "entity_type": "company", "proper_noun": true}], "domain": "environment.climate_change_c_o2", "impact": "reduced", "impact_value": 0.5, "action": "reduced", "locations": [], "time": {"from_": {"year": 2015, "month": null, "day": null}, "to": {"year": 2023, "month": null, "day": null}, "precision": "year"}, "start_year": 2015, "end_year": 2023, "quantities": [{"amount": 45.0, "entity": {"name": "carbon emissions", "entity_type": "concept", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "percentage"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "fact"}, "demise": {"domain.environment.climate_change_c_o2": 1.0, "domain.environment.sustainability": 1.0, "domain.industry.energy": 1.0, "domain.industry.general": 1.0, "engagement.proactive": 1.0, "impact.benefit_to_environment": 0.5, "impact.duration": 0.5, "motivation.genuine": 1.0, "object.entity_type.environment": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.neutral_statement": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1634385, "metadata": {"company": "<PERSON><PERSON>", "subject_entities": [{"name": "<PERSON><PERSON>", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "employees", "entity_type": "group_of_people", "proper_noun": false}, {"name": "Sue <PERSON> Charity Cycle Event", "entity_type": "event", "proper_noun": true}], "authors": [{"name": "<PERSON>", "entity_type": "person", "proper_noun": true}, {"name": "<PERSON>", "entity_type": "person", "proper_noun": true}], "domain": "society.social_good", "impact": null, "impact_value": 0.0, "action": "included photographs", "locations": [], "time": {"from_": {"year": 2018, "month": null, "day": null}, "to": {"year": 2018, "month": null, "day": null}, "precision": "year"}, "start_year": 2018, "end_year": 2018, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.society.community_development": 0.8, "domain.society.social_good": 0.7, "engagement.proactive": 0.6, "engagement.responsive": 0.5, "impact.benefit_to_human_life": 0.6, "motivation.genuine": 0.7, "object.entity_type.demographic": 0.8, "statement.describes_action": 0.7, "statement.describes_fact": 0.6, "statement.descriptive": 0.8, "subject.entity_type.organizations": 0.9}, "success": true}, {"statement_id": 1603975, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "Partners", "entity_type": "group_of_people", "proper_noun": false}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "governance.compensation", "impact": "positive", "impact_value": 0.3, "action": "invested", "locations": [], "time": {"from_": {"year": 2016, "month": null, "day": null}, "to": {"year": 2016, "month": null, "day": null}, "precision": "year"}, "start_year": 2016, "end_year": 2017, "quantities": [{"amount": 4.36, "entity": {"name": "total spend", "entity_type": "monetary", "proper_noun": false}, "delta": "inc", "unit": "percent", "quantity_type": "percentage"}, {"amount": 2.56, "entity": {"name": "total spend", "entity_type": "monetary", "proper_noun": false}, "delta": "inc", "unit": "percent", "quantity_type": "percentage"}, {"amount": 36.0, "entity": {"name": "annualised pay costs", "entity_type": "monetary", "proper_noun": false}, "delta": "inc", "unit": "million", "quantity_type": "currency"}, {"amount": 3.0, "entity": {"name": "NLW", "entity_type": "monetary", "proper_noun": false}, "delta": "inc", "unit": "million", "quantity_type": "currency"}], "is_environmental": false, "is_social": true, "is_governance": true, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "fact"}, "demise": {"domain.governance.compensation": 1.0, "domain.governance.profit_sharing": 1.0, "domain.society.employment": 1.0, "domain.society.workers_rights": 1.0, "impact.benefit_to_human_life": 0.5, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1559888, "metadata": {"company": "The Crown Estate", "subject_entities": [{"name": "The Crown Estate", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "Rushden Lakes", "entity_type": "facility", "proper_noun": true}, {"name": "Nene Wetlands", "entity_type": "location", "proper_noun": true}], "authors": [{"name": "The Crown Estate", "entity_type": "company", "proper_noun": true}], "domain": "environment.conservation", "impact": null, "impact_value": 0.0, "action": null, "locations": [{"name": "Rushden Lakes", "entity_type": "facility", "proper_noun": true, "location_type": "place"}, {"name": "Northamptonshire", "entity_type": "region", "proper_noun": true, "location_type": "region"}, {"name": "Nene Wetlands", "entity_type": "natural", "proper_noun": true, "location_type": "natural"}], "time": {"from_": {"year": 2022, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2022, "end_year": 2022, "quantities": [{"amount": 200.0, "entity": {"name": "acres", "entity_type": "number", "proper_noun": false}, "delta": null, "unit": "acres", "quantity_type": "area"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": true, "statement_category": "fact"}, "demise": {"domain.environment.conservation": 1.0, "domain.environment.general": 1.0, "domain.environment.rewilding": 1.0, "domain.environment.urban_conservation": 1.0, "domain.industry.real_estate": 1.0, "object.entity_type.environment": 1.0, "object.entity_type.place": 1.0, "statement.descriptive": 1.0, "statement.neutral_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1561537, "metadata": {"company": "<PERSON><PERSON>", "subject_entities": [{"name": "<PERSON><PERSON>", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "single use packaging", "entity_type": "product", "proper_noun": false}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "environment.circular_economy", "impact": "reduced", "impact_value": 0.5, "action": "reduced", "locations": [], "time": {"from_": {"year": 2023, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2023, "end_year": 2023, "quantities": [{"amount": 98.0, "entity": {"name": "single use packaging", "entity_type": "product", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "percentage"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "fact"}, "demise": {"domain.environment.circular_economy": 1.0, "domain.environment.plastic_waste": 1.0, "domain.industry.consumer": 1.0, "domain.industry.food": 1.0, "impact.benefit_to_environment": 0.7, "object.entity_type.immaterial": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.neutral_statement": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1558364, "metadata": {"company": null, "subject_entities": [{"name": "pandemic", "entity_type": "event", "proper_noun": false}], "object_entities": [{"name": "physical retail", "entity_type": "sector", "proper_noun": false}], "authors": [{"name": "Retail Research Institute", "entity_type": "organisation", "proper_noun": true}], "domain": "industry.retail", "impact": "stress", "impact_value": -0.3, "action": "source of", "locations": [], "time": {"from_": {"year": 2020, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2020, "end_year": 2020, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.retail": 1.0, "domain.society.disease": 1.0, "domain.society.public_health": 1.0, "impact.harm_to_human_life": 0.5, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.present_statement": 1.0}, "success": true}, {"statement_id": 1558373, "metadata": {"company": null, "subject_entities": [{"name": "US", "entity_type": "country", "proper_noun": true}], "object_entities": [{"name": "offline sales", "entity_type": "market", "proper_noun": false}], "authors": [{"name": "Retail Research Institute", "entity_type": "organisation", "proper_noun": true}], "domain": "industry.commerce", "impact": null, "impact_value": 0.0, "action": "see a drop", "locations": [{"name": "US", "entity_type": "country", "proper_noun": true, "location_type": "country"}], "time": {"from_": {"year": 2019, "month": null, "day": null}, "to": {"year": 2021, "month": null, "day": null}, "precision": "year"}, "start_year": 2019, "end_year": 2021, "quantities": [{"amount": 22.0, "entity": {"name": "offline sales", "entity_type": "market", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "percentage"}, {"amount": 27.0, "entity": {"name": "offline sales", "entity_type": "market", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "percentage"}], "is_environmental": false, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.commerce": 1.0, "domain.industry.retail": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.future_statement": 1.0, "statement.neutral_statement": 1.0, "statement.predictive": 1.0}, "success": true}, {"statement_id": 1558383, "metadata": {"company": null, "subject_entities": [{"name": "Brands", "entity_type": "company", "proper_noun": false}], "object_entities": [{"name": "millions of workers in their supply chains", "entity_type": "group_of_people", "proper_noun": false}], "authors": [{"name": "Fashion Magazine", "entity_type": "organisation", "proper_noun": true}], "domain": "society.workers_rights", "impact": "improve the low wages they have helped to create", "impact_value": 0.5, "action": "working to improve", "locations": [], "time": null, "start_year": 2020, "end_year": 2020, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "directive"}, "demise": {"domain.equality.socio_economic": 1.0, "domain.industry.fashion": 1.0, "domain.industry.supply_chain": 1.0, "domain.legal.labor_law": 1.0, "domain.society.workers_rights": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.directive": 1.0, "statement.present_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}]}, "vertex_ai/gemini/1286610023916503040": {"total_statements": 10, "successful_extractions": 10, "failed_extractions": 0, "field_metrics": {}, "overall_accuracy": 0.814733560090703, "field_accuracy": {"locations": 1.0, "impact": 0.5, "is_impactful_action": 0.8, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.8210714285714286, "is_disclosure": 0.9, "object_entities": 0.5533333333333335, "authors": 0.9, "statement_category": 0.8, "quantities": 0.69, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.6, "subject_entities": 0.6, "time": 0.665, "is_social": 0.9, "domain": 0.8800000000000001, "is_vague": 1.0, "start_year": 0.7, "end_year": 0.8}, "statement_metrics": [{"statement_id": 1561537, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.75, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 1.0, "quantities": 0.8999999999999999, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 1.0, "time": 1.0, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.9523809523809523}, {"statement_id": 1558373, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 0.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 2.0, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.1, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.2, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 0.0, "end_year": 1.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1630246, "field_matches": {"locations": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.0, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 1.0, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 0.0, "end_year": 0.0}, "overall_match": 0.8571428571428571}, {"statement_id": 1234567, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.875, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 1.0, "quantities": 0.8999999999999999, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 0.0, "time": 0.2, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8571428571428571}, {"statement_id": 1603975, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 1.0, "quantities": 0.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 0.0, "time": 0.25, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 0.0, "end_year": 0.0}, "overall_match": 0.6190476190476191}, {"statement_id": 1580715, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 0.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 1.0, "object_entities": 0.3333333333333333, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 0.0, "time": 1.0, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.7142857142857143}, {"statement_id": 1558383, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.2857142857142857, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 1.0, "quantities": 0.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 0.0, "time": 1.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1559888, "field_matches": {"locations": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 0.0, "object_entities": 1.0, "authors": 0.0, "statement_category": 1.0, "quantities": 0.9999999999999999, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 1.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8095238095238095}, {"statement_id": 1634385, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.95, "is_disclosure": 1.0, "object_entities": 0.2, "authors": 1.0, "statement_category": 0.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 1.0, "is_social": 0.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8095238095238095}, {"statement_id": 1558364, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.85, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 1.0, "time": 0.0, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.9047619047619048}], "model_name": "vertex_ai/gemini/1286610023916503040", "gold_standard_model": "gemini-2.5-pro-preview-05-06", "raw_results": [{"statement_id": 1630246, "metadata": {"company": "Athleta", "subject_entities": [{"name": "<PERSON><PERSON><PERSON>", "entity_type": "company", "proper_noun": true}, {"name": "Impetus", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "ATHLETA-branded clothing", "entity_type": "product", "proper_noun": true}, {"name": "Athleta", "entity_type": "company", "proper_noun": true}], "authors": [{"name": "<PERSON>", "entity_type": "person", "proper_noun": true}], "domain": "industry.fashion", "impact": null, "impact_value": 0.0, "action": "shipped", "locations": [{"name": "US", "entity_type": "country", "proper_noun": true, "location_type": "country"}], "time": null, "start_year": 2024, "end_year": 2024, "quantities": [], "is_environmental": false, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.fashion": 1.0, "engagement.passive": 1.0, "impact.harm_to_environment": 0.1, "impact.harm_to_human_life": 0.1, "impact.harm_to_object_entity": 0.1, "motivation.compliant": 0.2, "motivation.genuine": 0.3, "motivation.opportunistic": 0.5, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.describes_action": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1580715, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "Partnership Finance team", "entity_type": "department", "proper_noun": true}], "object_entities": [{"name": "E&S team", "entity_type": "department", "proper_noun": true}, {"name": "E&S strategy", "entity_type": "strategy", "proper_noun": true}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "governance.stakeholder_engagement", "impact": null, "impact_value": 0.0, "action": "works closely with", "locations": [], "time": null, "start_year": 2022, "end_year": 2022, "quantities": [], "is_environmental": false, "is_social": false, "is_governance": true, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": true, "statement_category": "action"}, "demise": {"domain.governance.stakeholder_engagement": 1.0, "domain.industry.finance": 0.2, "domain.society.social_good": 0.1, "engagement.proactive": 0.7, "engagement.responsive": 0.3, "impact.benefit_to_environment": 0.1, "impact.benefit_to_human_life": 0.1, "impact.benefit_to_object_entity": 0.2, "impact.duration": 0.6, "motivation.compliant": 0.6, "motivation.genuine": 0.7, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.descriptive": 1.0, "statement.present_statement": 1.0, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1234567, "metadata": {"company": "Example Corporation", "subject_entities": [{"name": "We", "entity_type": "group_of_people", "proper_noun": false}], "object_entities": [{"name": "Scope 1 and 2 carbon emissions", "entity_type": "other", "proper_noun": false}], "authors": [{"name": "Example Corporation", "entity_type": "company", "proper_noun": true}], "domain": "environment.climate_change_c_o2", "impact": "reduced", "impact_value": 0.3, "action": "reduced", "locations": [], "time": {"from_": {"year": 2015, "month": null, "day": null}, "to": {"year": 2023, "month": null, "day": null}, "precision": "year"}, "start_year": 2015, "end_year": 2023, "quantities": [{"amount": 45.0, "entity": {"name": "carbon emissions", "entity_type": "other", "proper_noun": false}, "delta": "dec", "unit": "%", "quantity_type": "percentage"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "action"}, "demise": {"domain.environment.climate_change_c_o2": 1.0, "domain.environment.energy_efficiency": 0.2, "domain.environment.sustainability": 0.3, "domain.governance.stakeholder_engagement": 0.1, "engagement.proactive": 0.8, "engagement.responsive": 0.2, "impact.benefit_to_environment": 0.3, "impact.benefit_to_human_life": 0.1, "impact.duration": 0.6, "impact.proportion": 0.4, "motivation.compliant": 0.2, "motivation.genuine": 0.7, "motivation.opportunistic": 0.1, "object.entity_type.chemical": 1.0, "object.entity_type.environment": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.chemical": 1.0, "subject.entity_type.environment": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1634385, "metadata": {"company": "<PERSON><PERSON>", "subject_entities": [{"name": "<PERSON><PERSON>", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "employees", "entity_type": "group_of_people", "proper_noun": false}, {"name": "Sue <PERSON> Charity Cycle Event", "entity_type": "event", "proper_noun": true}, {"name": "exercise bikes", "entity_type": "product", "proper_noun": false}, {"name": "collecting buckets", "entity_type": "product", "proper_noun": false}], "authors": [{"name": "<PERSON>", "entity_type": "person", "proper_noun": true}, {"name": "<PERSON>", "entity_type": "person", "proper_noun": true}], "domain": "society.social_good", "impact": null, "impact_value": 0.1, "action": "included", "locations": [], "time": null, "start_year": 2018, "end_year": 2018, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "action"}, "demise": {"domain.industry.retail": 0.2, "domain.society.community_development": 0.8, "domain.society.social_good": 0.7, "engagement.altruistic": 0.8, "engagement.proactive": 0.7, "impact.benefit_to_human_life": 0.1, "impact.benefit_to_object_entity": 0.1, "impact.duration": 0.3, "motivation.genuine": 0.7, "motivation.opportunistic": 0.3, "object.entity_type.group": 1.0, "object.entity_type.individual": 1.0, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.describes_action": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1603975, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "Partnership", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "Pay Review", "entity_type": "event", "proper_noun": true}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "equality.socio_economic", "impact": null, "impact_value": 0.0, "action": "invested", "locations": [], "time": {"from_": {"year": 2016, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2017, "end_year": 2017, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": true, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "action"}, "demise": {"domain.equality.socio_economic": 1.0, "domain.governance.compensation": 1.0, "domain.governance.workplace_wellbeing": 0.5, "domain.industry.retail": 0.2, "domain.society.employment": 0.7, "domain.society.wealth": 0.6, "engagement.proactive": 0.7, "impact.benefit_to_human_life": 0.3, "impact.benefit_to_object_entity": 0.3, "impact.duration": 0.6, "motivation.compliant": 0.2, "motivation.genuine": 0.8, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1559888, "metadata": {"company": "The Crown Estate", "subject_entities": [{"name": "Rushden Lakes", "entity_type": "facility", "proper_noun": true}], "object_entities": [{"name": "Nene Wetlands", "entity_type": "biome", "proper_noun": true}], "authors": [{"name": "The Crown Estate", "entity_type": "company", "proper_noun": true}], "domain": "environment.conservation", "impact": null, "impact_value": 0.0, "action": "set within", "locations": [{"name": "Northamptonshire", "entity_type": "region", "proper_noun": true, "location_type": "region"}], "time": null, "start_year": 2022, "end_year": 2022, "quantities": [{"amount": 200.0, "entity": {"name": "acres", "entity_type": "resource", "proper_noun": false}, "delta": null, "unit": "acres", "quantity_type": "area"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.environment.conservation": 1.0, "domain.environment.rewilding": 0.5, "domain.realms.overground": 1.0, "engagement.passive": 1.0, "impact.benefit_to_environment": 0.3, "impact.benefit_to_object_entity": 0.3, "impact.duration": 0.6, "motivation.genuine": 0.6, "motivation.superficial": 0.4, "object.entity_type.environment": 1.0, "object.entity_type.place": 1.0, "object.qualities.quantity": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "subject.entity_type.environment": 1.0, "subject.entity_type.place": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1561537, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "<PERSON><PERSON>", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "single use packaging", "entity_type": "product", "proper_noun": false}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "environment.plastic_waste", "impact": "reduced", "impact_value": 0.3, "action": "reduced", "locations": [], "time": null, "start_year": 2023, "end_year": 2023, "quantities": [{"amount": 98.0, "entity": {"name": "single use packaging", "entity_type": "product", "proper_noun": false}, "delta": "dec", "unit": "%", "quantity_type": "percentage"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "action"}, "demise": {"domain.environment.circular_economy": 0.8, "domain.environment.plastic_waste": 1.0, "domain.environment.sustainability": 0.7, "domain.industry.retail": 0.8, "engagement.preventative": 0.7, "engagement.proactive": 0.8, "engagement.responsive": 0.6, "impact.benefit_to_environment": 0.6, "impact.benefit_to_object_entity": 0.7, "impact.duration": 0.6, "impact.harm_to_environment": 0.1, "impact.harm_to_object_entity": 0.1, "motivation.compliant": 0.2, "motivation.genuine": 0.7, "motivation.opportunistic": 0.3, "object.entity_type.immaterial_systems": 1.0, "object.entity_type.multiple_immaterials": 1.0, "object.entity_type.software_system": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.beneficial_statement": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.immaterial_systems": 1.0, "subject.entity_type.multiple_immaterials": 1.0, "subject.entity_type.software_system": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1558364, "metadata": {"company": "Retail Research Institute", "subject_entities": [{"name": "pandemic", "entity_type": "event", "proper_noun": true}], "object_entities": [{"name": "physical retail", "entity_type": "sector", "proper_noun": false}], "authors": [{"name": "Retail Research Institute", "entity_type": "organisation", "proper_noun": true}], "domain": "industry.retail", "impact": "negative", "impact_value": -0.3, "action": "source of stress", "locations": [], "time": {"from_": {"year": 2020, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2020, "end_year": 2020, "quantities": [], "is_environmental": false, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.retail": 1.0, "engagement.reactive": 0.8, "impact.harm_to_environment": 0.1, "impact.harm_to_human_life": 0.2, "impact.harm_to_object_entity": 0.3, "motivation.pressured": 0.7, "object.entity_type.immaterial_systems": 1.0, "object.qualities.quantity": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.present_statement": 1.0, "subject.entity_type.immaterial_systems": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1558373, "metadata": {"company": null, "subject_entities": [{"name": "US", "entity_type": "country", "proper_noun": true}], "object_entities": [{"name": "offline sales", "entity_type": "market", "proper_noun": false}], "authors": [{"name": "Retail Research Institute", "entity_type": "organisation", "proper_noun": true}], "domain": "industry.retail", "impact": "drop", "impact_value": -0.3, "action": "see", "locations": [{"name": "US", "entity_type": "country", "proper_noun": true, "location_type": "country"}], "time": {"from_": {"year": 2021, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2021, "end_year": 2021, "quantities": [{"amount": 22.0, "entity": {"name": "offline sales", "entity_type": "market", "proper_noun": false}, "delta": "dec", "unit": "%", "quantity_type": "percentage"}, {"amount": 27.0, "entity": {"name": "offline sales", "entity_type": "market", "proper_noun": false}, "delta": "dec", "unit": "%", "quantity_type": "percentage"}], "is_environmental": false, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.commerce": 1.0, "domain.industry.retail": 1.0, "engagement.dismissive": 0.2, "engagement.passive": 0.8, "impact.harm_to_environment": 0.1, "impact.harm_to_human_life": 0.1, "impact.harm_to_object_entity": 0.3, "impact.proportion": 0.3, "motivation.compliant": 0.1, "motivation.opportunistic": 0.2, "motivation.superficial": 0.1, "object.entity_type.cities_provinces": 1.0, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.future_statement": 1.0, "statement.harmful_statement": 1.0, "statement.predictive": 1.0, "subject.entity_type.cities_provinces": 1.0, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1558383, "metadata": {"company": null, "subject_entities": [{"name": "Brands", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "low wages", "entity_type": "monetary", "proper_noun": false}], "authors": [{"name": "Fashion Magazine", "entity_type": "organisation", "proper_noun": true}], "domain": "society.workers_rights", "impact": "harm", "impact_value": -0.3, "action": "improve", "locations": [], "time": null, "start_year": 2020, "end_year": 2020, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "directive"}, "demise": {"domain.equality.socio_economic": 0.8, "domain.governance.supply_chain_governance": 0.7, "domain.human_needs.food": 0.2, "domain.human_needs.housing": 0.2, "domain.human_needs.sanitation": 0.2, "domain.industry.fashion": 1.0, "domain.industry.supply_chain": 1.0, "domain.legal.labor_law": 0.8, "domain.society.employment": 0.8, "domain.society.social_equity": 0.8, "domain.society.workers_rights": 1.0, "engagement.proactive": 0.7, "engagement.responsive": 0.6, "impact.benefit_to_human_life": 0.3, "impact.benefit_to_object_entity": 0.3, "impact.duration": 0.6, "impact.harm_to_human_life": 0.2, "impact.harm_to_object_entity": 0.2, "motivation.compliant": 0.6, "motivation.genuine": 0.7, "motivation.pressured": 0.6, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 0.8, "statement.describes_action": 0.8, "statement.descriptive": 0.7, "statement.directive": 1.0, "statement.harmful_statement": 0.8, "statement.present_statement": 0.8, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}]}, "vertex_ai/gemini/8567804721467752448": {"total_statements": 10, "successful_extractions": 10, "failed_extractions": 0, "field_metrics": {}, "overall_accuracy": 0.8120124716553286, "field_accuracy": {"locations": 0.95, "impact": 0.7, "is_impactful_action": 0.7, "is_governance": 0.9, "is_environmental": 0.9, "impact_value": 0.8539285714285715, "is_disclosure": 0.8, "object_entities": 0.7333333333333333, "authors": 0.9, "statement_category": 0.8, "quantities": 0.67, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.6, "subject_entities": 0.6, "time": 0.745, "is_social": 0.7, "domain": 0.9, "is_vague": 1.0, "start_year": 0.8, "end_year": 0.8}, "statement_metrics": [{"statement_id": 1561537, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.75, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 1.0, "quantities": 0.8999999999999999, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 1.0, "time": 1.0, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.9523809523809523}, {"statement_id": 1558373, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 0.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.5, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 0.0, "quantities": 0.1, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 0.0, "end_year": 1.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1630246, "field_matches": {"locations": 0.5, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.0, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 1.0, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 0.0, "end_year": 0.0}, "overall_match": 0.8095238095238095}, {"statement_id": 1234567, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.875, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 1.0, "quantities": 0.7, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 0.2, "is_social": 1.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.9523809523809523}, {"statement_id": 1603975, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 1.0, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 1.0, "quantities": 0.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 0.0, "time": 0.25, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 0.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1580715, "field_matches": {"locations": 1.0, "impact": 0.0, "is_impactful_action": 0.0, "is_governance": 1.0, "is_environmental": 0.0, "impact_value": 0.5, "is_disclosure": 1.0, "object_entities": 0.3333333333333333, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 0.0, "time": 1.0, "is_social": 0.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.6190476190476191}, {"statement_id": 1558383, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 0.0, "is_environmental": 1.0, "impact_value": 0.7142857142857143, "is_disclosure": 1.0, "object_entities": 0.0, "authors": 1.0, "statement_category": 1.0, "quantities": 0.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 0.0, "time": 1.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1559888, "field_matches": {"locations": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.5, "is_disclosure": 0.0, "object_entities": 1.0, "authors": 0.0, "statement_category": 1.0, "quantities": 0.9999999999999999, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 0.0, "time": 1.0, "is_social": 1.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.7619047619047619}, {"statement_id": 1634385, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 0.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.85, "is_disclosure": 0.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 0.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 1.0, "subject_entities": 1.0, "time": 1.0, "is_social": 0.0, "domain": 1.0, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.8095238095238095}, {"statement_id": 1558364, "field_matches": {"locations": 1.0, "impact": 1.0, "is_impactful_action": 1.0, "is_governance": 1.0, "is_environmental": 1.0, "impact_value": 0.85, "is_disclosure": 1.0, "object_entities": 1.0, "authors": 1.0, "statement_category": 1.0, "quantities": 1.0, "action": 1.0, "is_animal_welfare": 1.0, "company": 0.0, "subject_entities": 1.0, "time": 1.0, "is_social": 0.0, "domain": 0.8, "is_vague": 1.0, "start_year": 1.0, "end_year": 1.0}, "overall_match": 0.9047619047619048}], "model_name": "vertex_ai/gemini/8567804721467752448", "gold_standard_model": "gemini-2.5-pro-preview-05-06", "raw_results": [{"statement_id": 1630246, "metadata": {"company": "Athleta", "subject_entities": [{"name": "<PERSON><PERSON><PERSON>", "entity_type": "company", "proper_noun": true}, {"name": "Impetus", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "ATHLETA-branded clothing", "entity_type": "product", "proper_noun": true}, {"name": "Athleta", "entity_type": "company", "proper_noun": true}], "authors": [{"name": "<PERSON>", "entity_type": "person", "proper_noun": true}], "domain": "industry.fashion", "impact": null, "impact_value": 0.0, "action": "shipped", "locations": [{"name": "Portugal", "entity_type": "country", "proper_noun": true, "location_type": "country"}, {"name": "US", "entity_type": "country", "proper_noun": true, "location_type": "country"}], "time": null, "start_year": 2023, "end_year": 2023, "quantities": [], "is_environmental": false, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.fashion": 1.0, "domain.industry.supply_chain": 1.0, "domain.legal.intellectual_property": 0.2, "domain.legal.transnational_law": 0.2, "domain.society.consumer_protection": 0.1, "engagement.passive": 0.8, "engagement.proactive": 0.2, "impact.benefit_to_object_entity": 0.1, "impact.duration": 0.3, "impact.harm_to_object_entity": 0.1, "impact.proportion": 0.1, "motivation.compliant": 0.7, "motivation.opportunistic": 0.3, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1580715, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "Partnership Finance team", "entity_type": "department", "proper_noun": true}], "object_entities": [{"name": "E&S team", "entity_type": "department", "proper_noun": true}, {"name": "E&S strategy", "entity_type": "strategy", "proper_noun": true}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "environment.sustainability", "impact": null, "impact_value": 0.0, "action": "works closely with", "locations": [], "time": null, "start_year": 2022, "end_year": 2022, "quantities": [], "is_environmental": true, "is_social": true, "is_governance": true, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": true, "statement_category": "action"}, "demise": {"domain.environment.sustainability": 0.8, "domain.governance.stakeholder_engagement": 0.7, "domain.governance.supply_chain_governance": 0.3, "domain.governance.workplace_wellbeing": 0.2, "domain.industry.finance": 0.8, "domain.industry.retail": 0.7, "domain.society.social_good": 0.6, "engagement.altruistic": 0.6, "engagement.proactive": 0.7, "engagement.responsive": 0.6, "impact.benefit_to_environment": 0.3, "impact.benefit_to_human_life": 0.3, "impact.benefit_to_object_entity": 0.4, "impact.duration": 0.6, "motivation.compliant": 0.3, "motivation.genuine": 0.7, "motivation.opportunistic": 0.2, "object.entity_type.immaterial_systems": 1.0, "object.entity_type.organizations": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.present_statement": 1.0, "subject.entity_type.immaterial_systems": 1.0, "subject.entity_type.organizations": 1.0}, "success": true}, {"statement_id": 1234567, "metadata": {"company": "Example Corporation", "subject_entities": [{"name": "Example Corporation", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "Scope 1 and 2 carbon emissions", "entity_type": "concept", "proper_noun": false}], "authors": [{"name": "Example Corporation", "entity_type": "company", "proper_noun": true}], "domain": "environment.climate_change_c_o2", "impact": "reduced", "impact_value": 0.3, "action": "reduced", "locations": [], "time": {"from_": {"year": 2015, "month": null, "day": null}, "to": null, "precision": "year"}, "start_year": 2015, "end_year": 2023, "quantities": [{"amount": 45.0, "entity": {"name": "carbon emissions", "entity_type": "concept", "proper_noun": false}, "delta": "dec", "unit": "%", "quantity_type": "mass"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "action"}, "demise": {"domain.energy.consumption": 0.2, "domain.energy.efficiency": 0.2, "domain.energy.fossil_fuel": 0.1, "domain.energy.production": 0.1, "domain.energy.renewable": 0.1, "domain.environment.air_purification": 0.2, "domain.environment.carbon_capture": 0.1, "domain.environment.climate": 0.8, "domain.environment.climate_change": 0.8, "domain.environment.climate_change_c_o2": 1.0, "domain.environment.energy_efficiency": 0.2, "domain.environment.general": 0.2, "domain.environment.sustainability": 0.2, "domain.industry.energy": 0.2, "domain.industry.general": 0.1, "domain.industry.manufacturing": 0.1, "domain.industry.utilities": 0.1, "domain.pollution.air": 0.2, "domain.pollution.general": 0.1, "engagement.preventative": 0.6, "engagement.proactive": 0.7, "engagement.responsive": 0.3, "impact.benefit_to_environment": 0.3, "impact.benefit_to_human_life": 0.1, "impact.duration": 0.6, "impact.harm_to_environment": 0.1, "impact.harm_to_human_life": 0.1, "impact.proportion": 0.45, "motivation.compliant": 0.2, "motivation.genuine": 0.7, "motivation.opportunistic": 0.1, "motivation.pressured": 0.2, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1634385, "metadata": {"company": "<PERSON><PERSON>", "subject_entities": [{"name": "<PERSON><PERSON>", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "group of employees", "entity_type": "group_of_people", "proper_noun": false}, {"name": "Sue <PERSON> Charity Cycle Event", "entity_type": "event", "proper_noun": true}], "authors": [{"name": "<PERSON>", "entity_type": "person", "proper_noun": true}, {"name": "<PERSON>", "entity_type": "person", "proper_noun": true}], "domain": "society.social_good", "impact": "positive", "impact_value": 0.3, "action": "included photographs", "locations": [], "time": null, "start_year": 2018, "end_year": 2018, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "action"}, "demise": {"domain.industry.retail": 0.2, "domain.society.community_development": 0.8, "domain.society.social_good": 1.0, "engagement.altruistic": 0.8, "engagement.proactive": 0.7, "engagement.responsive": 0.6, "impact.benefit_to_human_life": 0.3, "impact.benefit_to_object_entity": 0.2, "impact.duration": 0.4, "impact.proportion": 0.3, "motivation.genuine": 0.7, "motivation.opportunistic": 0.3, "object.entity_type.individual": 1.0, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.individual": 1.0, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1603975, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "The Partnership", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "Pay Review", "entity_type": "event", "proper_noun": true}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "society.employment", "impact": "benefit", "impact_value": 0.3, "action": "invested", "locations": [], "time": {"from_": {"year": 2016, "month": null, "day": null}, "to": {"year": 2016, "month": null, "day": null}, "precision": "year"}, "start_year": 2016, "end_year": 2017, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": true, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "action"}, "demise": {"domain.governance.compensation": 0.8, "domain.governance.general": 0.2, "domain.society.employment": 0.8, "domain.society.workers_rights": 0.7, "engagement.proactive": 0.7, "engagement.responsive": 0.6, "impact.benefit_to_human_life": 0.3, "impact.duration": 0.6, "impact.proportion": 0.6, "motivation.compliant": 0.2, "motivation.genuine": 0.7, "motivation.opportunistic": 0.3, "object.entity_type.organizations": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1559888, "metadata": {"company": "The Crown Estate", "subject_entities": [{"name": "Rushden Lakes", "entity_type": "location", "proper_noun": true}], "object_entities": [{"name": "Nene Wetlands", "entity_type": "biome", "proper_noun": true}], "authors": [{"name": "The Crown Estate", "entity_type": "company", "proper_noun": true}], "domain": "environment.conservation", "impact": null, "impact_value": 0.0, "action": "set within", "locations": [{"name": "Northamptonshire", "entity_type": "region", "proper_noun": true, "location_type": "region"}], "time": null, "start_year": 2022, "end_year": 2022, "quantities": [{"amount": 200.0, "entity": {"name": "acres", "entity_type": "resource", "proper_noun": false}, "delta": null, "unit": "acres", "quantity_type": "area"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.environment.conservation": 1.0, "domain.environment.general": 0.2, "domain.environment.sustainability": 0.2, "domain.realms.overground": 1.0, "engagement.passive": 0.8, "engagement.preventative": 0.2, "impact.benefit_to_environment": 0.3, "impact.duration": 0.6, "impact.proportion": 0.2, "motivation.compliant": 0.7, "motivation.genuine": 0.3, "object.entity_type.environment": 1.0, "object.entity_type.place": 1.0, "object.qualities.quantity": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.neutral_statement": 1.0, "statement.present_statement": 1.0, "subject.entity_type.environment": 1.0, "subject.entity_type.place": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1561537, "metadata": {"company": "John Lewis Partnership plc", "subject_entities": [{"name": "<PERSON><PERSON>", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "single use packaging", "entity_type": "product", "proper_noun": false}], "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company", "proper_noun": true}], "domain": "environment.plastic_waste", "impact": "reduced", "impact_value": 0.3, "action": "reduced", "locations": [], "time": null, "start_year": 2023, "end_year": 2023, "quantities": [{"amount": 98.0, "entity": {"name": "single use packaging", "entity_type": "product", "proper_noun": false}, "delta": "dec", "unit": "%", "quantity_type": "percentage"}], "is_environmental": true, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": true, "is_disclosure": true, "statement_category": "action"}, "demise": {"domain.environment.circular_economy": 0.8, "domain.environment.plastic_waste": 1.0, "domain.environment.sustainability": 0.7, "domain.industry.food": 0.2, "domain.industry.retail": 0.2, "engagement.preventative": 0.7, "engagement.proactive": 0.8, "engagement.responsive": 0.6, "impact.benefit_to_environment": 0.7, "impact.benefit_to_human_life": 0.1, "impact.duration": 0.6, "impact.harm_to_environment": 0.1, "impact.proportion": 0.8, "motivation.compliant": 0.2, "motivation.genuine": 0.7, "motivation.opportunistic": 0.3, "object.entity_type.chemical": 0.5, "object.entity_type.immaterial": 0.5, "object.entity_type.organic": 0.5, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.describes_action": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.past_statement": 1.0, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1558364, "metadata": {"company": null, "subject_entities": [{"name": "pandemic", "entity_type": "event", "proper_noun": false}], "object_entities": [{"name": "physical retail", "entity_type": "sector", "proper_noun": false}], "authors": [{"name": "Retail Research Institute", "entity_type": "organisation", "proper_noun": true}], "domain": "industry.retail", "impact": "stress", "impact_value": -0.3, "action": "source of stress", "locations": [], "time": null, "start_year": 2020, "end_year": 2020, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.retail": 1.0, "domain.society.consumer_protection": 0.2, "domain.society.employment": 0.2, "domain.society.general": 0.2, "domain.society.social_networks": 0.1, "domain.society.urbanization": 0.1, "engagement.dismissive": 0.1, "engagement.passive": 0.8, "engagement.reactive": 0.7, "impact.duration": 0.6, "impact.harm_to_human_life": 0.3, "impact.harm_to_object_entity": 0.5, "impact.proportion": 0.7, "motivation.compliant": 0.1, "motivation.genuine": 0.7, "motivation.opportunistic": 0.2, "object.entity_type.immaterial_systems": 1.0, "object.qualities.quantity": 1.0, "statement.assertive": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.present_statement": 1.0, "subject.entity_type.immaterial_systems": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1558373, "metadata": {"company": null, "subject_entities": [{"name": "US", "entity_type": "country", "proper_noun": true}], "object_entities": [{"name": "offline sales", "entity_type": "market", "proper_noun": false}], "authors": [{"name": "Retail Research Institute", "entity_type": "organisation", "proper_noun": true}], "domain": "industry.retail", "impact": "drop", "impact_value": -0.2, "action": "see", "locations": [{"name": "US", "entity_type": "country", "proper_noun": true, "location_type": "country"}], "time": null, "start_year": 2021, "end_year": 2021, "quantities": [{"amount": 22.0, "entity": {"name": "offline sales", "entity_type": "market", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "number"}, {"amount": 27.0, "entity": {"name": "offline sales", "entity_type": "market", "proper_noun": false}, "delta": "dec", "unit": "percent", "quantity_type": "number"}], "is_environmental": false, "is_social": false, "is_governance": false, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "fact"}, "demise": {"domain.industry.commerce": 1.0, "domain.industry.retail": 1.0, "domain.society.wealth": 0.2, "engagement.passive": 0.8, "impact.benefit_to_human_life": 0.1, "impact.duration": 0.6, "impact.harm_to_human_life": 0.1, "impact.harm_to_object_entity": 0.2, "impact.proportion": 0.25, "motivation.opportunistic": 0.2, "motivation.pressured": 0.1, "object.entity_type.immaterial_systems": 1.0, "object.qualities.quantity": 1.0, "statement.describes_fact": 1.0, "statement.descriptive": 1.0, "statement.future_statement": 1.0, "statement.predictive": 1.0, "subject.entity_type.immaterial_systems": 1.0, "subject.qualities.quantity": 1.0}, "success": true}, {"statement_id": 1558383, "metadata": {"company": null, "subject_entities": [{"name": "Brands", "entity_type": "company", "proper_noun": true}], "object_entities": [{"name": "low wages", "entity_type": "monetary", "proper_noun": false}], "authors": [{"name": "Fashion Magazine", "entity_type": "organisation", "proper_noun": true}], "domain": "society.workers_rights", "impact": "improve", "impact_value": 0.3, "action": "working to improve", "locations": [], "time": null, "start_year": 2020, "end_year": 2020, "quantities": [], "is_environmental": false, "is_social": true, "is_governance": true, "is_animal_welfare": false, "is_vague": false, "is_impactful_action": false, "is_disclosure": false, "statement_category": "directive"}, "demise": {"domain.equality.socio_economic": 0.8, "domain.governance.supply_chain_governance": 0.7, "domain.industry.fashion": 0.8, "domain.industry.supply_chain": 0.7, "domain.legal.labor_law": 0.6, "domain.society.consumer_protection": 0.3, "domain.society.employment": 0.8, "domain.society.fair_trade": 0.7, "domain.society.modern_slavery": 0.3, "domain.society.workers_rights": 0.9, "engagement.proactive": 0.7, "engagement.responsive": 0.6, "impact.benefit_to_human_life": 0.6, "impact.duration": 0.6, "impact.proportion": 0.7, "motivation.compliant": 0.3, "motivation.genuine": 0.7, "motivation.pressured": 0.6, "object.entity_type.individual": 1.0, "object.qualities.quantity": 1.0, "statement.describes_action": 0.8, "statement.describes_fact": 0.7, "statement.descriptive": 0.8, "statement.directive": 0.9, "statement.present_statement": 0.8, "subject.entity_type.organizations": 1.0, "subject.qualities.quantity": 1.0}, "success": true}]}}