'use client'

import { cn } from '@/utilities/ui'
import React, { useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

export type SortOption = {
  label: string
  value: string
}

export type SortDropdownProps = {
  className?: string
  options: SortOption[]
  defaultValue?: string
}

export const SortDropdown: React.FC<SortDropdownProps> = ({ className, options, defaultValue }) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const currentSort = searchParams.get('sort') || defaultValue || options[0]?.value

  const handleSortChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value
    const params = new URLSearchParams(searchParams.toString())
    
    if (value) {
      params.set('sort', value)
    } else {
      params.delete('sort')
    }
    
    router.push(`?${params.toString()}`)
  }, [router, searchParams])

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <label htmlFor="sort-select" className="text-sm font-medium">
        Sort by:
      </label>
      <select
        id="sort-select"
        className="glass-effect-lit px-3 py-1.5 rounded-lg text-sm border border-border/30 bg-transparent focus:outline-none focus:ring-1 focus:ring-brand"
        value={currentSort}
        onChange={handleSortChange}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  )
}
