import { MigrateUpArgs, MigrateDownArgs } from '@payloadcms/db-postgres'

export async function up({ payload }: MigrateUpArgs): Promise<void> {
  await payload.db.drizzle.execute(/*sql*/ `
    ALTER TABLE "posts" ADD COLUMN IF NOT EXISTS "pinned" boolean DEFAULT false;
    ALTER TABLE "_posts_v" ADD COLUMN IF NOT EXISTS "version_pinned" boolean DEFAULT false;
  `)
}

export async function down({ payload }: MigrateDownArgs): Promise<void> {
  await payload.db.drizzle.execute(/*sql*/ `
    ALTER TABLE "posts" DROP COLUMN IF EXISTS "pinned";
    ALTER TABLE "_posts_v" DROP COLUMN IF EXISTS "version_pinned";
  `)
}
