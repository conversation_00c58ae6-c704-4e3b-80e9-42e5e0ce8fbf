'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import type { Header } from '@/payload-types'

import { Logo } from '@/components/Logo/Logo'
import { HeaderNav } from './Nav'
import { useScroll } from 'framer-motion'
import { cn } from '@utils/lib/utils'
import { AdminBar } from '@/components/AdminBar'
import { useSessionStorage } from 'usehooks-ts'

interface HeaderClientProps {
  data: Header
  isPreviewEnabled?: boolean
}

export const HeaderClient: React.FC<HeaderClientProps> = ({ data, isPreviewEnabled }) => {
  /* Storing the value in a useState to avoid hydration errors */
  const [theme, setTheme] = useState<string | null>(null)
  const { headerTheme, setHeaderTheme } = useHeaderTheme()
  const pathname = usePathname()
  const [scrolled, setScrolled] = useSessionStorage("fp.scrolled", false);
  const [showHeader, setShowHeader] = useState(true);
  const { scrollY } = useScroll()

  const [linksCount, setLinksCount] = useState(0)
  useEffect(() => {
    setLinksCount(window.innerWidth < 1280 ? 3 : 5)
  }, [])

  useEffect(() => {
    setShowHeader(scrolled)
  }, [scrolled]);

  useEffect(() => {
    setHeaderTheme(null)
    // eslint-disable-next-line react-hooks/exhaustive-deps
    if(pathname !== '/') {
      setShowHeader(true)
    }
  }, [pathname])

  useEffect(() => {
    if (headerTheme && headerTheme !== theme) setTheme(headerTheme)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [headerTheme])



  return showHeader && (
    <>
      <header
        className={cn(
          "absolute left-0 right-0 z-50 overflow-visible transition-all duration-300",
          showHeader
            ? "translate-y-0 fixed backdrop-blur-xl bg-background/90 dark:bg-background/85 border-b border-foreground/10 shadow-soft"
            : "-translate-y-32 bg-transparent"
        )}
        {...(theme ? { 'data-theme': theme } : {})}
      >
        <div className="py-4 flex container justify-between items-center">
          <Link href="/" className="flex items-center transition-all duration-300 hover:scale-105 hover:brightness-110 relative group">
            <div className="absolute -inset-3 bg-gradient-to-r from-primary/15 to-transparent rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="absolute -inset-3 bg-primary/5 rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-300"></div>
            <Logo height={32} className="relative"/>
          </Link>
          <HeaderNav data={data} visibleLinksCount={linksCount} theme={theme!}/>
        </div>
        <AdminBar
          adminBarProps={{
            preview: isPreviewEnabled,
          }}
        />
      </header>

      {/* Spacer for fixed header when it's showing - only add on non-home pages */}
      {showHeader && pathname !== '/' && <div className="h-[72px]"></div>}
    </>
  )
}
