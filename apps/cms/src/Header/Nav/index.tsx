'use client'

import React, { useState } from 'react'
import type { Header as HeaderType } from '@/payload-types'
import { CMSLink } from '@/components/Link'
import Link from 'next/link'
import { ChevronDown, Menu as MenuIcon, SearchIcon } from 'lucide-react'
import { Sheet, SheetContent, SheetTitle, SheetTrigger } from '@ui/components/ui/sheet'
import { Button } from '@ui/components/ui/button'
import { usePathname } from 'next/navigation'
import { cn } from '@utils/lib/utils'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'

export const HeaderNav: React.FC<{ data: HeaderType, visibleLinksCount?: number, theme?: string }> = ({ data, visibleLinksCount = 2, theme }) => {
  const navItems = data?.navItems || []
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  // Number of links to show directly in the navbar is passed as a prop with default value of 3

  // Check if a link is active
  const isActive = (linkUrl: string): boolean => {
    if (!linkUrl) return false
    // Remove trailing slash for comparison
    const currentPath = pathname.endsWith('/') ? pathname.slice(0, -1) : pathname
    const targetPath = linkUrl.endsWith('/') ? linkUrl.slice(0, -1) : linkUrl
    return currentPath === targetPath
  }

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden lg:flex items-center">
        <div className="flex gap-8 mr-8 items-center">
          {/* Show first n links directly */}
          {navItems.slice(0, visibleLinksCount).map(({ link }, i) => {
            const active = isActive(link?.url || '')
            return (
              <CMSLink
                key={i}
                {...link}
                appearance="link"
                className={cn(
                  "hover:no-underline text-sm font-medium relative px-1 py-2 transition-all duration-300 group",
                  "hover:text-brand hover:scale-105",
                  active
                    ? "text-brand"
                    : "text-foreground dark:text-white"
                )}
              >
                {/* Underline effect - more polished with animation */}
                <span className={cn(
                  "absolute -bottom-0.5 left-0 h-0.5 bg-brand rounded-full transition-all duration-300",
                  active ? "w-full" : "w-0 group-hover:w-full"
                )}></span>

                {/* Subtle glow effect on hover */}
                <span className="absolute inset-0 -z-10 rounded-full bg-brand/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              </CMSLink>
            )
          })}

          {/* Show dropdown for remaining links if there are more than visibleLinksCount */}
          {navItems.length > visibleLinksCount && (
            <DropdownMenu >
              <DropdownMenuTrigger className="hover:no-underline text-sm font-medium relative px-1 py-2 transition-all duration-300 group hover:text-brand hover:scale-105 text-foreground dark:text-white flex items-center gap-1">
                More <ChevronDown className="h-4 w-4" />
                {/* Underline effect - more polished with animation */}
                <span className="absolute -bottom-0.5 left-0 h-0.5 bg-brand rounded-full transition-all duration-300 w-0 group-hover:w-full"></span>

                {/* Subtle glow effect on hover */}
                <span className="absolute inset-0 -z-10 rounded-full bg-brand/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              </DropdownMenuTrigger>
              <DropdownMenuContent  data-theme={theme} align="end" className="min-w-[150px] bg-background">
                {navItems.slice(visibleLinksCount).map(({ link }, i) => {
                  const active = isActive(link?.url || '')
                  return (
                    <DropdownMenuItem key={i} asChild className="p-0 focus:bg-transparent hover:bg-transparent">
                      <CMSLink
                        {...link}
                        appearance="link"
                        className={cn(
                          "w-full hover:no-underline text-sm font-medium relative transition-all duration-300 px-3 py-2 block group",
                          active
                            ? "text-brand"
                            : "text-foreground dark:text-white hover:text-brand"
                        )}
                      >
                        {/* Underline effect - more polished with animation */}
                        <span className={cn(
                          "absolute -bottom-0.5 left-0 h-0.5 bg-brand rounded-full transition-all duration-300",
                          active ? "w-full" : "w-0 group-hover:w-full"
                        )}></span>

                        {/* Subtle glow effect on hover */}
                        <span className="absolute inset-0 -z-10 rounded-md bg-brand/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      </CMSLink>
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        <div className="flex items-center gap-4">
          <Link href="/search"
            className="p-2.5 rounded-full bg-foreground/5 hover:bg-foreground/10
                    transition-all duration-300 border border-foreground/10
                    hover:border-foreground/20 hover:scale-105 hover:shadow-md
                    relative group overflow-hidden">
            <span className="sr-only">Search</span>
            <SearchIcon className="w-4 h-4 text-foreground/80 group-hover:text-foreground transition-colors duration-300" />
            <span className="absolute inset-0 bg-brand/5 transform scale-0 opacity-0 group-hover:scale-100 group-hover:opacity-100 transition-all duration-500 rounded-full"></span>
          </Link>

          <Button asChild
            className="bg-gradient-to-r from-brand to-brand-dark text-white
                     font-medium shadow-md hover:shadow-lg transition-all duration-300
                     transform hover:-translate-y-0.5 rounded-full px-6 py-2.5 relative group overflow-hidden">
            <Link href="/contact" className="flex items-center gap-1.5">
              <span className="relative z-10">Contact Us</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16" height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-0.5 transition-transform duration-300 relative z-10 group-hover:translate-x-1">
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
              {/* Button animated bg */}
              <span className="absolute inset-0 bg-gradient-to-r from-brand-dark to-brand opacity-0 group-hover:opacity-100 transition-opacity duration-500"></span>
            </Link>
          </Button>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <button className="p-2.5 rounded-full bg-foreground/5 hover:bg-foreground/10
                     transition-all duration-300 border border-foreground/10
                     hover:border-foreground/20 focus:outline-none hover:scale-105
                     hover:shadow-md relative overflow-hidden group">
              <MenuIcon className="w-5 h-5 text-foreground/80 group-hover:text-foreground transition-colors duration-300 relative z-10" />
              <span className="absolute inset-0 bg-brand/5 transform scale-0 opacity-0 group-hover:scale-100 group-hover:opacity-100 transition-all duration-500 rounded-full"></span>
            </button>
          </SheetTrigger>
          <SheetContent side="right" className="bg-background/95 backdrop-blur-xl border-l border-foreground/10 p-8 shadow-xl">
            <SheetTitle className="text-xl font-semibold text-foreground mb-8 flex items-center">
              <span className="text-brand mr-2">●</span> Navigation
            </SheetTitle>

            <div className="flex flex-col gap-5">
              {navItems.map(({ link }, i) => {
                const active = isActive(link?.url || '')
                return (
                  <CMSLink
                    key={i}
                    {...link}
                    appearance="link"
                    className={cn(
                      "text-foreground text-lg font-medium py-3 border-b border-foreground/10 transition-all duration-300 relative group",
                      active
                        ? "text-brand border-brand pl-4"
                        : "hover:text-brand hover:border-brand/50 border-foreground/10 hover:pl-4"
                    )}
                    onClick={() => {
                      setIsOpen(false)
                    }}
                  >
                    {/* Active indicator */}
                    {active && (
                      <span className="absolute left-0 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-brand"></span>
                    )}
                    {/* Hover indicator */}
                    {!active && (
                      <span className="absolute left-0 top-1/2 -translate-y-1/2 w-0 h-2 rounded-full bg-brand/70 transition-all duration-300 group-hover:w-2"></span>
                    )}

                    {/* Background highlight effect */}
                    <span className={cn(
                      "absolute inset-0 bg-brand/5 opacity-0 transition-opacity duration-300 -z-10",
                      active ? "opacity-30" : "group-hover:opacity-10"
                    )}></span>
                  </CMSLink>
                )
              })}

              <div className="mt-6 pt-6 border-t border-foreground/10">
                <Link href="/search" className="flex items-center gap-3 text-foreground hover:text-brand text-lg transition-all duration-300 py-3 group"
                      onClick={() => {
                        setIsOpen(false)
                      }}
                >
                  <div className="p-2 rounded-full bg-foreground/5 group-hover:bg-brand/10 transition-all duration-300">
                    <SearchIcon className="w-5 h-5 group-hover:text-brand transition-colors duration-300" />
                  </div>
                  <span>Search</span>
                </Link>
              </div>

              <Button asChild className="mt-8 w-full bg-gradient-to-r from-brand to-brand-dark text-white
                      font-medium shadow-md hover:shadow-lg transition-all duration-300
                      transform hover:-translate-y-0.5 rounded-full py-6 text-lg relative overflow-hidden group"

              >
                <Link href="/contact" className="flex items-center justify-center gap-2"
                      onClick={() => {
                        setIsOpen(false)
                      }}
                >
                  <span className="relative z-10">Contact Us</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18" height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="transition-transform duration-300 group-hover:translate-x-1 relative z-10 ">
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                  {/* Button animated bg */}
                  <span className="absolute inset-0 bg-gradient-to-r from-brand-dark to-brand opacity-0 group-hover:opacity-100 transition-opacity duration-500"></span>
                </Link>
              </Button>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </>
  )
}
