import { getPayload } from 'payload';
import path from 'path';

async function debugPosts() {
  // Load the config dynamically
  const configPromise = (await import(path.resolve(process.cwd(), 'src/payload.config.ts'))).default;
  const payload = await getPayload({ config: configPromise });

  console.log('Fetching posts with pinned field...');

  const posts = await payload.find({
    collection: 'posts',
    depth: 1,
    limit: 20,
    sort: [
      'pinned', // Sort pinned posts first (true values before false)
      '-publishedAt', // Then sort by publish date (newest first)
    ],
  });

  console.log('Posts found:', posts.docs.length);

  posts.docs.forEach((post, index) => {
    console.log(`Post ${index + 1}:`);
    console.log(`  ID: ${post.id}`);
    console.log(`  Title: ${post.title}`);
    console.log(`  Pinned: ${post.pinned}`);
    console.log(`  Published At: ${post.publishedAt}`);
    console.log('---');
  });
}

debugPosts().catch(console.error);
