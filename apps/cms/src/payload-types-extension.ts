/**
 * This file contains manual extensions to the auto-generated payload-types.ts file.
 * These interfaces will be automatically generated by Payload when the CMS is built,
 * but we need them for development.
 */

export interface IQuadrantBlock {
  inset?: boolean | null;
  title: string;
  background?: boolean | null;
  backgroundMedia?: any | null;
  darkMode?: boolean | null;
  xAxisTitle: string;
  yAxisTitle: string;
  currency?: 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CNY' | null;
  quadrantLabels: {
    topRight: string;
    topLeft: string;
    bottomRight: string;
    bottomLeft: string;
  };

  companies?: {
    name: string;
    x: number;
    y: number;
    logo?: any;
    logoUrl?: string | null;
    color?: string | null;
    marketCap?: number | null;
    description?: string | null;
    isYourCompany?: boolean | null;
  }[] | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'quadrant';
}
