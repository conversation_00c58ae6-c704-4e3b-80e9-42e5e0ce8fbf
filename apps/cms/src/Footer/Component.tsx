import { getCachedGlobal } from '@/utilities/getGlobals'
import Link from 'next/link'
import React from 'react'
import { ThemeSelector } from '@/providers/Theme/ThemeSelector'
import { Facebook, Instagram, Linkedin } from 'lucide-react'
import { EkoLogoText, EkoSymbolBrand } from '@utils/images'

const XIcon = ({className}:{className:string}) => (
  <svg
    width="251px"
    height="256px"
    viewBox="0 0 251 256"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    preserveAspectRatio="xMidYMid"
    className={className}
  >
    <title>X</title>
    <g>
      <path
        d="M149.078767,108.398529 L242.331303,0 L220.233437,0 L139.262272,94.1209195 L74.5908396,0 L0,0 L97.7958952,142.3275 L0,256 L22.0991185,256 L107.606755,156.605109 L175.904525,256 L250.495364,256 L149.07334,108.398529 L149.078767,108.398529 Z M118.810995,143.581438 L108.902233,129.408828 L30.0617399,16.6358981 L64.0046968,16.6358981 L127.629893,107.647252 L137.538655,121.819862 L220.243874,240.120681 L186.300917,240.120681 L118.810995,143.586865 L118.810995,143.581438 Z"
        fill="#FFFFFF"
      />
    </g>
  </svg>
);

// Define types for our data structures
type LinkData = {
  link: {
    type?: 'reference' | 'custom' | null;
    url?: string | null;
    label: string;
    reference?: {
      relationTo: 'pages' | 'posts';
      value: { slug?: string } | number;
    } | null;
    newTab?: boolean | null;
  };
  id?: string | null;
};

type SocialData = {
  platform: 'facebook' | 'twitter' | 'linkedin' | 'instagram';
  url: string;
  id?: string | null;
};

// Helper function to resolve links with enhanced styling
const renderLink = (linkData: LinkData) => {
  if (!linkData?.link) return null;

  const { type, url, label, reference, newTab } = linkData.link;

  let href = '#';
  if (type === 'custom' && url) {
    href = url;
  } else if (type === 'reference' && reference) {
    const relativeUrl =
      typeof reference.value === 'object' && reference.value?.slug
        ? `/${reference.value.slug}`
        : '#';
    href = reference.relationTo === 'pages'
      ? relativeUrl
      : `/posts/${typeof reference.value === 'object' && reference.value?.slug ? reference.value.slug : ''}`;
  }

  return (
    <Link
      href={href}
      target={newTab ? '_blank' : undefined}
      rel={newTab ? 'noopener noreferrer' : undefined}
      className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group"
    >
      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
      {label}
      {newTab && (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12" height="12"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="ml-1.5 opacity-60 group-hover:opacity-100 transition-opacity duration-200"
        >
          <path d="M7 7h10v10"></path>
          <path d="M7 17 17 7"></path>
        </svg>
      )}
    </Link>
  );
};

// Helper to get the social icon component
const getSocialIcon = (platform: string, className: string) => {
  switch (platform) {
    case 'facebook':
      return <Facebook className={className} />;
    case 'twitter':
      return <XIcon className={className} />;
    case 'linkedin':
      return <Linkedin className={className} />;
    case 'instagram':
      return <Instagram className={className} />;
    default:
      return null;
  }
};

type FooterData = {
  companyInfo?: {
    description: string
  };
  quickLinks?: LinkData[];
  resourceLinks?: LinkData[];
  solutionLinks?: LinkData[];
  companyLinks?: LinkData[];
  socialLinks?: SocialData[];
  copyrightText?: string;
};

export async function Footer() {
  // Use type assertion to specify the correct interface
  const footerData = await getCachedGlobal('footer', 1)() as FooterData;

  // Set defaults or use data from CMS
  const companyInfo = footerData?.companyInfo || {
    description: 'ekoIntelligence – Behaviour Driven ESG Analytics. Discover how our single tool provides in-depth, foolproof analysis of your or your competitors\' ESG behaviour.'
  };
  const quickLinks = footerData?.quickLinks || [] as LinkData[];
  const resourceLinks = footerData?.resourceLinks || [] as LinkData[];
  const solutionLinks = footerData?.solutionLinks || [] as LinkData[];
  const companyLinks = footerData?.companyLinks || [] as LinkData[];
  const socialLinks = footerData?.socialLinks || [] as SocialData[];

  // Format copyright text with current year
  const currentYear = new Date().getFullYear();
  const copyrightText = footerData?.copyrightText?.replace('{year}', currentYear.toString()) ||
    `© ${currentYear} ekoIntelligence. All rights reserved.`;

  return (
    <footer className="mt-auto border-t border-neutral-900/10 bg-gradient-to-b from-neutral-900 to-neutral-950 text-neutral-100 relative overflow-hidden">
      {/* Enhanced visual effects */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-brand/50 to-transparent"></div>
      <div className="absolute top-0 right-0 w-96 h-96 rounded-full blur-3xl bg-brand/10 -mr-20 -mt-20 "></div>
      <div className="absolute bottom-0 left-0 w-96 h-96 rounded-full blur-3xl bg-brand-accent/10 -ml-20 -mb-20 animate-pulse-slow animation-delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-brand/5 via-transparent to-transparent opacity-50"></div>
      <div className="absolute inset-0 bg-noise opacity-[0.03] mix-blend-soft-light pointer-events-none"></div>

      {/* Main content */}
      <div className="relative z-10">
        <div className="container py-20 relative">
          <div className="grid grid-cols-1 gap-y-12 md:grid-cols-2 lg:grid-cols-6 gap-x-8 xl:gap-x-12">
            {/* Column 1: Company Overview (wider) */}
            <div className="lg:col-span-2">
              <Link href="/" className="block mb-8 transition-all duration-300 hover:scale-105 hover:brightness-110 group mx-auto">
                <div className="flex items-center justify-center relative" >
                  <div className="absolute -inset-4 bg-gradient-to-r from-brand/20 to-transparent rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <EkoSymbolBrand height={28*1.1} className="relative" />
                  <EkoLogoText height={32} className="ml-4 relative" ekoColor="var(--slate-50)" intelligenceColor="var(--slate-200)" />
                </div>
              </Link>
              <p className="mt-4 text-neutral-300 text-sm leading-relaxed max-w-md font-light">
                {companyInfo.description}
              </p>

              {/* Enhanced social links */}
              <div className="flex gap-4 mt-8">
                {socialLinks.length > 0 ? (
                  socialLinks.map((social: SocialData, i: number) => (
                    <Link
                      key={`social-${i}`}
                      href={social.url}
                      aria-label={social.platform}
                      className="p-2.5 rounded-full bg-white/5 hover:bg-brand/20 border border-white/10 hover:border-brand/30
                               transition-all duration-300 hover:scale-110 hover:-translate-y-1 shadow-sm hover:shadow-md group"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <div className="relative">
                        {getSocialIcon(social.platform, "w-5 h-5 group-hover:text-white transition-colors duration-300")}
                        <span className="absolute inset-0 bg-gradient-to-tr from-brand/30 to-transparent rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      </div>
                    </Link>
                  ))
                ) : (
                  // Default social links with enhanced styling
                  <>
                    <Link href="https://www.facebook.com" aria-label="Facebook"
                      className="p-2.5 rounded-full bg-white/5 hover:bg-brand/20 border border-white/10 hover:border-brand/30
                               transition-all duration-300 hover:scale-110 hover:-translate-y-1 shadow-sm hover:shadow-md group">
                      <div className="relative">
                        <Facebook className="w-5 h-5 group-hover:text-white transition-colors duration-300" />
                        <span className="absolute inset-0 bg-gradient-to-tr from-brand/30 to-transparent rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      </div>
                    </Link>
                    <Link href="https://www.twitter.com" aria-label="Twitter"
                      className="p-2.5 rounded-full bg-white/5 hover:bg-brand/20 border border-white/10 hover:border-brand/30
                               transition-all duration-300 hover:scale-110 hover:-translate-y-1 shadow-sm hover:shadow-md group">
                      <div className="relative">
                        <XIcon className="w-5 h-5 group-hover:text-white transition-colors duration-300" />
                        <span className="absolute inset-0 bg-gradient-to-tr from-brand/30 to-transparent rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      </div>
                    </Link>
                    <Link href="https://www.linkedin.com" aria-label="LinkedIn"
                      className="p-2.5 rounded-full bg-white/5 hover:bg-brand/20 border border-white/10 hover:border-brand/30
                               transition-all duration-300 hover:scale-110 hover:-translate-y-1 shadow-sm hover:shadow-md group">
                      <div className="relative">
                        <Linkedin className="w-5 h-5 group-hover:text-white transition-colors duration-300" />
                        <span className="absolute inset-0 bg-gradient-to-tr from-brand/30 to-transparent rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      </div>
                    </Link>
                    <Link href="https://www.instagram.com" aria-label="Instagram"
                      className="p-2.5 rounded-full bg-white/5 hover:bg-brand/20 border border-white/10 hover:border-brand/30
                               transition-all duration-300 hover:scale-110 hover:-translate-y-1 shadow-sm hover:shadow-md group">
                      <div className="relative">
                        <Instagram className="w-5 h-5 group-hover:text-white transition-colors duration-300" />
                        <span className="absolute inset-0 bg-gradient-to-tr from-brand/30 to-transparent rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      </div>
                    </Link>
                  </>
                )}
              </div>

            </div>

            {/* Column 2: Quick Links */}
            <div className="lg:col-span-1">
              <h3 className="text-lg font-semibold mb-6 border-b border-neutral-700/20 pb-2 flex items-center text-neutral-200">
                <span className="inline-block w-1.5 h-1.5 rounded-full bg-brand mr-2"></span>
                Quick Links
              </h3>
              <nav className="flex flex-col gap-3">
                {quickLinks.length > 0 ? (
                  quickLinks.map((item: LinkData, i: number) => (
                    <div key={`quick-link-${i}`} className="group relative">
                      {renderLink(item)}
                    </div>
                  ))
                ) : (
                  // Enhanced default links
                  <>
                    <Link href="/posts" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Posts
                    </Link>
                    <Link href="/contact" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Contact
                    </Link>
                    <Link href="/company-reports" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Reports
                    </Link>
                    <Link href="/search" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Search
                    </Link>
                    <Link href="/#features" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Features
                    </Link>
                  </>
                )}
              </nav>
            </div>

            {/* Column 3: Resources */}
            <div className="lg:col-span-1">
              <h3 className="text-lg font-semibold mb-6 border-b border-neutral-700/20 pb-2 flex items-center text-neutral-200">
                <span className="inline-block w-1.5 h-1.5 rounded-full bg-brand mr-2"></span>
                Resources
              </h3>
              <nav className="flex flex-col gap-3">
                {resourceLinks.length > 0 ? (
                  resourceLinks.map((item: LinkData, i: number) => (
                    <div key={`resource-link-${i}`} className="group relative">
                      {renderLink(item)}
                    </div>
                  ))
                ) : (
                  // Enhanced default links
                  <>
                    <Link href="/#privacy" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Privacy Policy
                    </Link>
                    <Link href="/#terms" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Terms of Service
                    </Link>
                    <Link href="/#support" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Support
                    </Link>
                    <Link href="/#faq" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      FAQ
                    </Link>
                    <Link href="/posts" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Blog
                    </Link>
                  </>
                )}
              </nav>
            </div>

            {/* Column 4: Solutions */}
            <div className="lg:col-span-1">
              <h3 className="text-lg font-semibold mb-6 border-b border-neutral-700/20 pb-2 flex items-center text-neutral-200">
                <span className="inline-block w-1.5 h-1.5 rounded-full bg-brand mr-2"></span>
                Solutions
              </h3>
              <nav className="flex flex-col gap-3">
                {solutionLinks.length > 0 ? (
                  solutionLinks.map((item: LinkData, i: number) => (
                    <div key={`solution-link-${i}`} className="group relative">
                      {renderLink(item)}
                    </div>
                  ))
                ) : (
                  // Enhanced default links
                  <>
                    <Link href="/#supply-chain" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Supply Chain Validation
                    </Link>
                    <Link href="/#greenwashing" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Greenwashing Detection
                    </Link>
                    <Link href="/behaviour" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Behaviour Driven ESG
                    </Link>
                    <Link href="/#app" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      The App
                    </Link>
                    <Link href="/#sustainable" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Sustainable Practices
                    </Link>
                  </>
                )}
              </nav>
            </div>

            {/* Column 5: Company */}
            <div className="lg:col-span-1">
              <h3 className="text-lg font-semibold mb-6 border-b border-neutral-700/20 pb-2 flex items-center text-neutral-200">
                <span className="inline-block w-1.5 h-1.5 rounded-full bg-brand mr-2"></span>
                Company
              </h3>
              <nav className="flex flex-col gap-3">
                {companyLinks.length > 0 ? (
                  companyLinks.map((item: LinkData, i: number) => (
                    <div key={`company-link-${i}`} className="group relative">
                      {renderLink(item)}
                    </div>
                  ))
                ) : (
                  // Enhanced default links
                  <>
                    <Link href="/#team" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Our Team
                    </Link>
                    <Link href="/#careers" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Careers
                    </Link>
                    <Link href="/#investors" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Investors
                    </Link>
                    <Link href="/#press" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Press
                    </Link>
                    <Link href="/#events" className="text-neutral-200 hover:text-neutral-100 transition-all duration-200 hover:translate-x-1.5 inline-flex items-center group">
                      <span className="w-0 h-0.5 bg-brand mr-0 rounded-full group-hover:w-2 group-hover:mr-1.5 transition-all duration-200"></span>
                      Events
                    </Link>
                  </>
                )}
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced bottom section with copyright */}
      <div className="relative overflow-hidden border-t border-neutral-900/10">
        {/* Enhanced gradient background */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-brand/5 via-transparent to-transparent opacity-40"></div>
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

        <div className="container py-6 relative z-10">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-sm text-neutral-300 hover:text-neutral-200 transition-colors duration-300 flex items-center">
              <span className="inline-block w-1 h-1 rounded-full bg-brand mr-2 opacity-70"></span>
              {copyrightText}
            </p>
            <div className="flex items-center gap-3 mt-4 md:mt-0">
              <span className="text-xs text-neutral-400 uppercase tracking-wider mr-2 font-medium">Appearance</span>
              <ThemeSelector />
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
