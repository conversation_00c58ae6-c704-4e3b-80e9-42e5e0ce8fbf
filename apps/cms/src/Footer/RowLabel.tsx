'use client'
import { Footer } from '@/payload-types'
import { RowLabelProps, useRowLabel } from '@payloadcms/ui'

export const RowLabel: React.FC<RowLabelProps> = () => {
  const data = useRowLabel<any>()

  // For links with a link property
  const label = data?.data?.link?.label
    ? `Link ${data.rowNumber !== undefined ? data.rowNumber + 1 : ''}: ${data?.data?.link?.label}`
    : undefined

  // For social links with a platform property
  const socialLabel = data?.data?.platform
    ? `${data.data.platform} (${data.data.url})` 
    : undefined

  const displayLabel = label || socialLabel || `Row ${data.rowNumber !== undefined ? data.rowNumber + 1 : ''}`

  return <div>{displayLabel}</div>
}
