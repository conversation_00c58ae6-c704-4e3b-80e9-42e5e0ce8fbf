// src/app/company-reports/[slug]/page.tsx
import type { Metadata } from 'next'
import { PayloadRedirects } from '@/components/PayloadRedirects'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import { draftMode } from 'next/headers'
import React from 'react'
import { LivePreviewListener } from '@/components/LivePreviewListener'
import CompanyReportBlocks from '@/blocks/CompanyReports/CompanyReportBlocks'
import PageClient from '@/app/(frontend)/company-reports/[slug]/page.client'
import { mergeOpenGraph } from '@/utilities/mergeOpenGraph'


export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })
  const reports = await payload.find({
    collection: 'company-reports',
    draft: false,
    limit: 1000,
    overrideAccess: false,
    pagination: false,
    select: {
      slug: true,
    },
  })

  const params = reports.docs.map(({ slug }: { slug: string }) => {
    return { slug }
  })

  return params
}

type Args = {
  params: Promise<{
    slug?: string
  }>
}

export default async function CompanyReportPage({ params: paramsPromise }: Args) {
  const { isEnabled: draft } = await draftMode()
  const { slug = '' } = await paramsPromise
  const url = '/company-reports/' + slug
  const report = await queryReportBySlug({ slug })
  if (!report) return <PayloadRedirects url={url} />
  const payload = await getPayload({ config: configPromise })



  return (
    <article className="pt-0 pb-16">
      <PageClient slug={slug} />
      {/* Enables redirects even for valid pages */}
      <PayloadRedirects disableNotFound url={url} />

      {draft && <LivePreviewListener />}

      {/*/!* Render a hero section for the report, if defined *!/*/}
      {/*<CompanyReportHero report={report} />*/}

      <div className="flex flex-col items-center gap-4 pt-8">
        <div className="container">
          {/* Render your blocks field */}
          {report.blocks && <CompanyReportBlocks blocks={report.blocks} />}
        </div>

      </div>
    </article>
  )
}

export async function generateMetadata({ params: paramsPromise }: Args): Promise<Metadata> {
  const { slug = '' } = await paramsPromise
  const report = await queryReportBySlug({ slug })
  return {
    description: report?.extract,
    openGraph: mergeOpenGraph({
      description: report?.extract || '',
      title: report?.title || '',
    }),
    title:report?.title,
  }
}

const queryReportBySlug = async ({ slug }: { slug: string }) => {
  const { isEnabled: draft } = await draftMode()
  const payload = await getPayload({ config: configPromise })

  const result = await payload.find({
    collection: 'company-reports',
    draft,
    limit: 1,
    overrideAccess: true,
    pagination: false,
    where: {
      slug: {
        equals: slug,
      },
    },
  })

  return result.docs?.[0] || null
}
