'use client'

import React, { useEffect, useState } from 'react'
import { Media } from '@/payload-types'
import { IQuadrantBlock } from '@/payload-types-extension'
import { BlockWrapper } from '@/components/BlockWrapper'
import { cn } from '@utils/lib/utils'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'
import dynamic from 'next/dynamic'

// Import the SVG download button with no SSR
const SVGDownloadButton = dynamic(
  () => import('./SVGDownloadButton'),
  { ssr: false }
)

interface CompanyData {
  name: string;
  x: number;
  y: number;
  logo?: string | undefined;
  logoUrl?: string | undefined;
  logoMedia?: Media | undefined;
  color: string;
  marketCap?: number;
  size: number;
  description: string;
  isYourCompany?: boolean;
}

export function QuadrantBlock({
  title,
  xAxisTitle,
  yAxisTitle,
  quadrantLabels,
  companies = [],
  background,
  backgroundMedia,
  darkMode,
  inset,
  currency = 'USD',
}: IQuadrantBlock) {
  // Currency symbols mapping
  const currencySymbols: Record<string, string> = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CNY': '¥',
  };
  const [parsedCompanies, setParsedCompanies] = useState<CompanyData[]>([]);
  // Reference to the chart container for measuring width
  const chartRef = React.useRef<HTMLDivElement>(null);
  const [chartWidth, setChartWidth] = useState(1000);

  // Update chart width on resize
  useEffect(() => {
    const updateChartWidth = () => {
      if (chartRef.current) {
        setChartWidth(chartRef.current.offsetWidth);
      }
    };

    // Initial measurement
    updateChartWidth();

    // Set up resize listener
    window.addEventListener('resize', updateChartWidth);
    return () => window.removeEventListener('resize', updateChartWidth);
  }, []);

  // Map companies data for display
  useEffect(() => {
    if (!companies || companies.length === 0) {
      setParsedCompanies([]);
      return;
    }

    // Find the max market cap to scale the circles
    const marketCaps = companies
      .map(company => company.marketCap ?? 0)
      .filter(cap => cap > 0);

    const maxMarketCap = marketCaps.length > 0
      ? Math.max(...marketCaps)
      : 1000;

    // Calculate the maximum circle diameter (15% of chart width)
    const maxDiameter = Math.round(chartWidth * 0.15);
    const minDiameter = 12; // Minimum circle size in pixels

    // Map the companies from the CMS format to the component format
    const mappedCompanies = companies.map(company => {
      // Get the logo URL safely
      let logoUrl: string | undefined = undefined;
      if (company.logoUrl) {
        logoUrl = company.logoUrl;
      } else if (company.logo && typeof company.logo === 'object') {
        logoUrl = (company.logo as Media).url || undefined;
      }

      // Calculate size based on market cap if available
      const marketCap = company.marketCap ?? 0;

      // Calculate the circle size proportionally to market cap
      // For zero market cap, use the minimum size
      let calculatedSize = minDiameter;

      if (marketCap > 0 && maxMarketCap > 0) {
        // Square root scaling for better visual representation
        // (area of circle is proportional to market cap)
        const scaleFactor = Math.sqrt(marketCap / maxMarketCap);
        calculatedSize = Math.max(
          minDiameter,
          Math.round(minDiameter + scaleFactor * (maxDiameter - minDiameter))
        );
      }

      return {
        name: company.name,
        x: company.x,
        y: company.y,
        logo: logoUrl,
        logoMedia: company.logo as Media | undefined,
        color: company.color || '#4682B4',
        marketCap: company.marketCap ?? 0,
        size: calculatedSize,
        description: company.description || '',
        isYourCompany: company.isYourCompany || false,
      };
    }) || [];

    setParsedCompanies(mappedCompanies);
  }, [companies, chartWidth]);

  return (
    <BlockWrapper
      background={background}
      backgroundMedia={backgroundMedia as Media}
      darkMode={darkMode}
      inset={inset}
    >
      <div className={cn(
        "w-full max-w-[1000px] mx-auto p-5",
        darkMode ? "text-neutral-200" : "text-neutral-800",
        "rounded-xl"
      )}>
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-center text-2xl font-bold flex-grow">{title}</h2>
          <SVGDownloadButton
            title={title}
            chartRef={chartRef}
            companies={parsedCompanies}
            currency={currency}
            currencySymbol={currencySymbols[currency as keyof typeof currencySymbols]}
            darkMode={darkMode}
          />
        </div>
        <div className="text-center text-sm opacity-70 mb-6">
          Market cap in {currencySymbols[currency as keyof typeof currencySymbols]} millions • Circle area proportional to market cap
        </div>

        <div
          ref={chartRef}
          className={cn(
          "glass-effect relative rounded-xl overflow-hidden",
          "w-full h-[650px] p-8 box-border",
          darkMode ? "shadow-[0_8px_32px_rgba(0,0,0,0.3)]" : "shadow-[0_8px_32px_rgba(0,0,0,0.1)]"
        )}>
          {/* Quadrant labels */}
          <div className={cn(
            "border-2 border-slate-400/20 border-solid  absolute font-bold p-4 rounded-lg min-w-[120px] text-center transform -translate-x-1/2 -translate-y-1/2",
            "top-1/4 left-1/4",
            darkMode ? "text-white" : "text-neutral-900"
          )}>
            {quadrantLabels.topLeft}
          </div>
          <div className={cn(
            "border-2 border-slate-400/20 border-solid absolute font-bold p-4 rounded-lg min-w-[120px] text-center transform -translate-x-1/2 -translate-y-1/2",
            "top-1/4 left-3/4",
            darkMode ? "text-white" : "text-neutral-900"
          )}>
            {quadrantLabels.topRight}
          </div>
          <div className={cn(
            "border-2 border-slate-400/20 border-solid  absolute font-bold p-4 rounded-lg min-w-[120px] text-center transform -translate-x-1/2 -translate-y-1/2",
            "top-3/4 left-1/4",
            darkMode ? "text-white" : "text-neutral-900"
          )}>
            {quadrantLabels.bottomLeft}
          </div>
          <div className={cn(
            "border-2 border-slate-400/20 border-solid  absolute font-bold p-4 rounded-lg min-w-[120px] text-center transform -translate-x-1/2 -translate-y-1/2",
            "top-3/4 left-3/4",
            darkMode ? "text-white" : "text-neutral-900"
          )}>
            {quadrantLabels.bottomRight}
          </div>

          {/* Axis labels */}
          <div className={cn(
            "absolute bottom-[2%] left-1/2 transform -translate-x-1/2 font-bold w-fit",
            darkMode ? "text-neutral-400" : "text-neutral-600"
          )}>
            <div className="flex items-center justify-center space-x-6">
              <span className="font-bold px-3">{xAxisTitle}</span>
            </div>
          </div>
          <div className={cn(
            "absolute top-1/2 left-[2%] transform rotate-[-90deg] origin-center whitespace-nowrap font-bold w-fit",
            darkMode ? "text-neutral-400" : "text-neutral-600"
          )}>
            <div className="flex items-center justify-center space-x-6">
              <span className="font-bold px-3">{yAxisTitle}</span>
            </div>
          </div>

          {/* Axis lines */}
          <div className={cn(
            "absolute left-[10%] top-1/2 w-[80%] h-[2px] transform -translate-y-[1px]",
            darkMode ? "bg-white/20 shadow-[0_0_8px_rgba(255,255,255,0.1)]" : "bg-slate-900/20 shadow-[0_0_8px_rgba(0,0,0,0.1)]"
          )}></div>
          <div className={cn(
            "absolute left-1/2 top-[10%] w-[2px] h-[80%] transform -translate-x-[1px]",
            darkMode ? "bg-white/20 shadow-[0_0_8px_rgba(255,255,255,0.1)]" : "bg-slate-900/20 shadow-[0_0_8px_rgba(0,0,0,0.1)]"
          )}></div>

          {/* Grid lines - horizontal */}
          <div className={cn(
            "absolute left-[10%] top-[30%] w-[80%] h-[1px]",
            darkMode ? "bg-white/5" : "bg-black/5"
          )}></div>
          <div className={cn(
            "absolute left-[10%] top-[70%] w-[80%] h-[1px]",
            darkMode ? "bg-white/5" : "bg-black/5"
          )}></div>

          {/* Grid lines - vertical */}
          <div className={cn(
            "absolute left-[30%] top-[10%] w-[1px] h-[80%]",
            darkMode ? "bg-white/5" : "bg-black/5"
          )}></div>
          <div className={cn(
            "absolute left-[70%] top-[10%] w-[1px] h-[80%]",
            darkMode ? "bg-white/5" : "bg-black/5"
          )}></div>

          {/* Companies */}
          {parsedCompanies.map((company, index) => {
            const posX = 10 + (company.x / 100) * 80; // Scale from 0-100 to 10%-90% of container
            const posY = 10 + ((100 - company.y) / 100) * 80; // Invert Y-axis for correct positioning

            return (
              <TooltipProvider key={index}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className={cn(
                        "company-bubble absolute flex flex-col items-center cursor-pointer z-[5]",
                        "transition-all duration-300 ease-[cubic-bezier(0.175,0.885,0.32,1.275)]",
                        "hover:scale-110"
                      )}
                      style={{
                        left: `${posX}%`,
                        top: `${posY}%`,
                        transform: 'translate(-50%, -50%)'
                      }}
                    >
                      <div
                        className={cn(
                          "flex items-center justify-center rounded-full overflow-hidden",
                          "transition-all duration-300 shadow-md hover:shadow-lg",
                          company.isYourCompany ? "border-[3px] border-brand" : "border-2 border-white/30"
                        )}
                        style={{
                          width: `${company.size}px`,
                          height: `${company.size}px`,
                          backgroundColor: company.color
                        }}
                      >
                        {company.logo && (
                          <img
                            src={company.logo}
                            alt={company.name}
                            width={company.size}
                            height={company.size}
                            className="w-[90%] h-[90%] object-contain transition-opacity duration-300"
                          />
                        )}
                      </div>
                      <div
                        className={cn(
                          "mt-[10px] text-[13px] font-bold text-center whitespace-nowrap py-[6px] px-[10px] rounded-md",
                          "",
                          company.isYourCompany
                            ? (darkMode ? "glass-effect-brand-strong-lit" : "glass-effect-brand-strong-lit")
                            : (darkMode ? "border-2 border-slate-400/20 border-solid bg-background" : "border-2 border-slate-400/20 border-solid bg-background"),
                          "border-2 border-slate-400/20 border-solid"
                        )}
                      >
                        {company.name}
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent
                    side="right"
                    align="start"
                    className={cn(
                      "glass-effect-strong p-4 rounded-xl max-w-[280px] text-sm z-50",
                      "shadow-[0_8px_24px_rgba(0,0,0,0.3)] leading-relaxed",
                      darkMode ? "text-white border border-white/10" : "text-neutral-800 border border-black/10"
                    )}
                  >
                    <h4 className="text-lg font-bold mb-2">{company.name}</h4>
                    {(company.marketCap ?? 0) > 0 && (
                      <div className="mb-2 text-sm font-medium">
                        <span className="opacity-70">Market Cap: </span>
                        <span className="font-bold">
                          {currencySymbols[currency as keyof typeof currencySymbols]}
                          {(company.marketCap ?? 0).toLocaleString()} million
                        </span>
                      </div>
                    )}
                    <p className="leading-relaxed">{company.description}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </div>

        <div className={cn("mt-10 grid grid-cols-1 md:grid-cols-2 gap-6", darkMode ? "text-white" : "text-black")}>
          {parsedCompanies.map((company, index) => (
            <div
              key={index}
              className="glass-effect p-5 rounded-xl flex items-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
            >
              <div
                className={cn(
                  "w-[70px] h-[70px] rounded-full mr-4 flex items-center justify-center overflow-hidden flex-shrink-0",
                  "shadow-[0_4px_16px_rgba(0,0,0,0.2)]",
                  company.isYourCompany ? "border-[3px] border-[#00FFCC]" : "border-2 border-white/20"
                )}
                style={{ backgroundColor: company.color }}
              >
                {company.logo && (
                  <img
                    src={company.logo}
                    alt={company.name}
                    width={70}
                    height={70}
                    className="w-[90%] h-[90%] object-contain transition-opacity duration-300"
                  />
                )}
              </div>
              <div className="ml-5 flex-1">
                <div className="font-bold text-lg mb-1 flex items-center">
                  {company.name}
                  {company.isYourCompany && (
                    <span className="ml-2 text-xs bg-opacity-20 bg-teal-400 text-teal-400 px-2 py-1 rounded-full uppercase tracking-wider">
                      Your Company
                    </span>
                  )}
                </div>
                {(company.marketCap ?? 0) > 0 && (
                  <div className="text-sm mb-1 font-medium">
                    <span className="opacity-70">Market Cap: </span>
                    <span className="font-bold">
                      {currencySymbols[currency as keyof typeof currencySymbols]}
                      {(company.marketCap ?? 0).toLocaleString()} million
                    </span>
                  </div>
                )}
                <div className="text-sm leading-relaxed opacity-80">{company.description}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </BlockWrapper>
  );
}

export default QuadrantBlock;
