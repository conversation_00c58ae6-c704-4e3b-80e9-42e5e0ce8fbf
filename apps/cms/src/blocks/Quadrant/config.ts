import { Block } from 'payload'

const Quadrant: Block = {
  slug: 'quadrant',
  interfaceName: "IQuadrantBlock",
  labels: {
    singular: 'Quadrant Chart',
    plural: 'Quadrant Charts',
  },
  fields: [
    {
      name: 'inset',
      type: 'checkbox',
      label: 'Add shadow inset',
      defaultValue: false,
    },
    {
      name: 'title',
      type: 'text',
      label: 'Chart Title',
      required: true,
    },
    {
      name: 'background',
      type: 'checkbox',
      label: 'Background Effect',
      defaultValue: true,
    },
    {
      name: 'backgroundMedia',
      type: 'upload',
      label: 'Background Media',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'darkMode',
      type: 'checkbox',
      label: 'Dark Mode',
      defaultValue: true,
    },
    {
      name: 'xAxisTitle',
      type: 'text',
      label: 'X-Axis Title',
      required: true,
    },
    {
      name: 'yAxisTitle',
      type: 'text',
      label: 'Y-Axis Title',
      required: true,
    },
    {
      name: 'currency',
      type: 'select',
      label: 'Currency for Market Cap',
      defaultValue: 'USD',
      options: [
        {
          label: 'US Dollar ($)',
          value: 'USD',
        },
        {
          label: 'Euro (€)',
          value: 'EUR',
        },
        {
          label: 'British Pound (£)',
          value: 'GBP',
        },
        {
          label: 'Japanese Yen (¥)',
          value: 'JPY',
        },
        {
          label: 'Chinese Yuan (¥)',
          value: 'CNY',
        },
      ],
    },
    {
      name: 'quadrantLabels',
      type: 'group',
      label: 'Quadrant Labels',
      fields: [
        {
          name: 'topRight',
          type: 'text',
          label: 'Top Right Quadrant',
          required: true,
        },
        {
          name: 'topLeft',
          type: 'text',
          label: 'Top Left Quadrant',
          required: true,
        },
        {
          name: 'bottomRight',
          type: 'text',
          label: 'Bottom Right Quadrant',
          required: true,
        },
        {
          name: 'bottomLeft',
          type: 'text',
          label: 'Bottom Left Quadrant',
          required: true,
        },
      ],
    },

    {
      name: 'companies',
      type: 'array',
      label: 'Companies',
      admin: {
        initCollapsed: true,
      },
      fields: [
        {
          name: 'name',
          type: 'text',
          label: 'Company Name',
          required: true,
        },
        {
          name: 'x',
          type: 'number',
          label: 'X Position (0-100)',
          required: true,
          min: 0,
          max: 100,
        },
        {
          name: 'y',
          type: 'number',
          label: 'Y Position (0-100)',
          required: true,
          min: 0,
          max: 100,
        },
        {
          name: 'logo',
          type: 'upload',
          label: 'Company Logo',
          relationTo: 'media',
          required: false,
        },
        {
          name: 'logoUrl',
          type: 'text',
          label: 'External Logo URL (if not uploading)',
          required: false,
        },
        {
          name: 'color',
          type: 'text',
          label: 'Brand Color (hex)',
          required: false,
        },
        {
          name: 'marketCap',
          type: 'number',
          label: 'Market Cap (in millions)',
          min: 0,
          defaultValue: 1000,
        },
        {
          name: 'description',
          type: 'textarea',
          label: 'Company Description',
          required: false,
        },
        {
          name: 'isYourCompany',
          type: 'checkbox',
          label: 'Highlight as Your Company',
          defaultValue: false,
        },
      ],
    },
  ],
};

export default Quadrant;
