'use client'

import React, { useState } from 'react'
import { Button } from '@ui/components/ui/button'
import { Download } from 'lucide-react'
import { cn } from '@utils/lib/utils'
import html2canvas from 'html2canvas'

interface CompanyData {
  name: string;
  x: number;
  y: number;
  logo?: string;
  color: string;
  marketCap?: number;
  size: number;
  description: string;
  isYourCompany?: boolean;
}

interface SVGDownloadButtonProps {
  title: string;
  chartRef: React.RefObject<HTMLDivElement | null>;
  companies: CompanyData[];
  currency: string | null;
  currencySymbol: string | undefined;
  darkMode?: boolean | null;
}

export function SVGDownloadButton({
  title,
  chartRef,
  companies,
  currency,
  currencySymbol,
  darkMode
}: SVGDownloadButtonProps) {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadAsSVG = async () => {
    if (!chartRef.current) return;

    try {
      setIsDownloading(true);

      // Use html2canvas to capture the chart as an image
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: null,
        scale: 2, // Higher resolution
        logging: false,
        useCORS: true, // Enable CORS for images
      });

      // Convert canvas to PNG (SVG conversion is not directly supported by html2canvas)
      const imgData = canvas.toDataURL('image/png');

      // Create a download link
      const downloadLink = document.createElement('a');
      downloadLink.href = imgData;
      downloadLink.download = `${title.replace(/[^a-zA-Z0-9]/g, '_')}_Quadrant.png`;

      // Trigger the download
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    } catch (error) {
      console.error('Error generating image:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className={cn(
        "flex items-center gap-2 ml-4",
        darkMode ? "text-white border-white/20 hover:bg-white/10" : "text-neutral-800 border-black/20 hover:bg-black/5"
      )}
      onClick={downloadAsSVG}
      disabled={isDownloading}
    >
      <Download size={16} />
      {isDownloading ? 'Generating...' : 'Download Image'}
    </Button>
  );
}

export default SVGDownloadButton;
