'use client';

import React, { useState } from 'react';
import { Button, useField } from '@payloadcms/ui';

// Define the expected company data structure
interface CompanyData {
  name: string;
  x: number;
  y: number;
  logo?: string;
  color?: string;
  size?: number;
  description?: string;
  isYourCompany?: boolean;
}

// Define the expected JSON structure
interface QuadrantData {
  xAxisTitle?: string;
  yAxisTitle?: string;
  quadrantLabels?: {
    topRight: string;
    topLeft: string;
    bottomRight: string;
    bottomLeft: string;
  };
  companies: CompanyData[];
}

export const ImportJSONButton: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [jsonInput, setJsonInput] = useState('');

  // Get access to the form fields we need to update
  const { value: xAxisValue, setValue: setXAxisValue } = useField<string>({ path: 'xAxisTitle' });
  const { value: yAxisValue, setValue: setYAxisValue } = useField<string>({ path: 'yAxisTitle' });

  // Quadrant labels
  const { value: topRightValue, setValue: setTopRightValue } = useField<string>({ path: 'quadrantLabels.topRight' });
  const { value: topLeftValue, setValue: setTopLeftValue } = useField<string>({ path: 'quadrantLabels.topLeft' });
  const { value: bottomRightValue, setValue: setBottomRightValue } = useField<string>({ path: 'quadrantLabels.bottomRight' });
  const { value: bottomLeftValue, setValue: setBottomLeftValue } = useField<string>({ path: 'quadrantLabels.bottomLeft' });

  // Companies array
  const { value: companiesValue, setValue: setCompaniesValue } = useField<any[]>({ path: 'companies' });

  const handleImport = () => {
    try {
      const parsedData: QuadrantData = JSON.parse(jsonInput);

      // Update axis titles if provided
      if (parsedData.xAxisTitle) {
        setXAxisValue(parsedData.xAxisTitle);
      }

      if (parsedData.yAxisTitle) {
        setYAxisValue(parsedData.yAxisTitle);
      }

      // Update quadrant labels if provided
      if (parsedData.quadrantLabels) {
        setTopRightValue(parsedData.quadrantLabels.topRight);
        setTopLeftValue(parsedData.quadrantLabels.topLeft);
        setBottomRightValue(parsedData.quadrantLabels.bottomRight);
        setBottomLeftValue(parsedData.quadrantLabels.bottomLeft);
      }

      // Update companies array
      if (parsedData.companies && Array.isArray(parsedData.companies)) {
        // Transform the companies data to match the expected format in the CMS
        const formattedCompanies = parsedData.companies.map(company => ({
          name: company.name,
          x: company.x,
          y: company.y,
          logoUrl: company.logo || '',
          color: company.color || '#4682B4',
          size: company.size || 24,
          description: company.description || '',
          isYourCompany: company.isYourCompany || false,
        }));

        setCompaniesValue(formattedCompanies);
      }

      console.log('JSON data imported successfully');
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error parsing JSON:', error);
    }
  };

  return (
    <div className="import-json-button">
      <Button
        buttonStyle="primary"
        onClick={() => setIsModalOpen(true)}
      >
        Import from JSON
      </Button>

      {isModalOpen && (
        <div className="payload-modal-overlay">
          <div className="payload-modal">
            <div className="payload-modal__header">
              <h3>Import Quadrant Data from JSON</h3>
            </div>
            <div className="payload-modal__body">
              <p className="payload-modal__instructions">
                Paste your JSON data below. This will populate the form fields for further editing.
              </p>

              <textarea
                className="payload-modal__textarea"
                value={jsonInput}
                onChange={(e) => setJsonInput(e.target.value)}
                placeholder='{"xAxisTitle": "Analysis Depth", "yAxisTitle": "Analysis Approach", "quadrantLabels": {"topRight": "DEEP BEHAVIORAL ANALYSIS", "topLeft": "SUPERFICIAL BEHAVIORAL ANALYSIS", "bottomRight": "DEEP METRICS", "bottomLeft": "SUPERFICIAL METRICS"}, "companies": [{"name": "Company1", "x": 85, "y": 90, "logo": "https://example.com/logo.png", "color": "#008080", "size": 30, "description": "Description", "isYourCompany": true}]}'
                rows={10}
              />
            </div>
            <div className="payload-modal__footer">
              <Button
                buttonStyle="secondary"
                onClick={() => setIsModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                buttonStyle="primary"
                onClick={handleImport}
              >
                Import
              </Button>
            </div>
          </div>

          <style jsx>{`
            .payload-modal-overlay {
              position: fixed;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background-color: var(--theme-elevation-800);
              background-color: rgba(0, 0, 0, 0.5);
              display: flex;
              justify-content: center;
              align-items: center;
              z-index: 1000;
            }

            .payload-modal {
              background-color: var(--theme-elevation-0);
              border-radius: var(--style-radius-m);
              width: 90%;
              max-width: 600px;
              max-height: 90vh;
              overflow-y: auto;
              box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
            }

            .payload-modal__header {
              padding: 20px;
              border-bottom: 1px solid var(--theme-elevation-100);
            }

            .payload-modal__header h3 {
              margin: 0;
              font-size: 1.3rem;
              font-weight: 500;
            }

            .payload-modal__body {
              padding: 20px;
            }

            .payload-modal__instructions {
              margin-bottom: 15px;
            }

            .payload-modal__textarea {
              width: 100%;
              min-height: 200px;
              padding: 10px;
              border: 1px solid var(--theme-elevation-200);
              border-radius: var(--style-radius-s);
              font-family: monospace;
              margin-bottom: 15px;
              background-color: var(--theme-input-bg);
              color: var(--theme-text);
            }

            .payload-modal__footer {
              padding: 15px 20px;
              border-top: 1px solid var(--theme-elevation-100);
              display: flex;
              justify-content: flex-end;
              gap: 10px;
            }
          `}</style>
        </div>
      )}
    </div>
  );
};

export default ImportJSONButton;
