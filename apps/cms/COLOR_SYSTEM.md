# Color System Documentation

This document outlines the standardized color system used in the CMS application. The system is designed to be flexible and maintainable, with semantic color names that can be mapped to different color palettes as needed.

## Base Colors

The application currently uses the Slate color palette from Tailwind CSS as the base for neutral colors. This provides a cool, professional look with blue-tinted grays that complement the natural green brand color.

All black and gray colors have been standardized to use the Slate palette, which provides a consistent look across the application. This standardization makes it easy to switch to a different gray palette in the future if needed.

## Semantic Color Classes

Instead of using specific color names directly in components (like `text-black`, `bg-gray-900`, etc.), we use semantic color classes that describe the purpose of the color rather than the specific color itself.

### Text Colors

```jsx
// Use these classes for text colors
<p className="text-neutral-dark">Dark text for headings and important content</p>
<p className="text-neutral-medium">Medium text for body content</p>
<p className="text-neutral-light">Light text for less important content</p>
<p className="text-neutral-subtle">Subtle text for captions and hints</p>
```

### Background Colors

```jsx
// Use these classes for background colors
<div className="bg-neutral-dark">Dark background</div>
<div className="bg-neutral-medium">Medium background</div>
<div className="bg-neutral-light">Light background</div>
<div className="bg-neutral-subtle">Subtle background</div>
```

### Border Colors

```jsx
// Use these classes for border colors
<div className="border border-neutral-dark">Dark border</div>
<div className="border border-neutral-medium">Medium border</div>
<div className="border border-neutral-light">Light border</div>
<div className="border border-neutral-subtle">Subtle border</div>
```

## CSS Variables

The application uses CSS variables for colors, which are defined in `globals.css`. These variables are used by the Tailwind theme and can be accessed directly in CSS if needed.

```css
:root {
  /* Light mode variables */
  --foreground: 240 10% 3.9%; /* Zinc-based text color */
  --background: 0 0% 100%;    /* White background */
  /* ... other variables ... */
}

[data-theme='dark'] {
  /* Dark mode variables */
  --foreground: 0 0% 95%;     /* Light text for dark mode */
  --background: 240 3.7% 15.9%; /* Zinc-based dark background */
  /* ... other variables ... */
}
```

## Theme Colors

In addition to neutral colors, the application uses theme colors for branding and semantic meaning:

- **Brand Colors**: `--brand-primary`, `--brand-primary-dark`, `--brand-contrast`
- **Semantic Colors**: `--success`, `--warning`, `--error`
- **UI Colors**: `--background`, `--foreground`, `--card`, `--border`, etc.

## Dark Mode Support

All color classes automatically adjust for dark mode. The application uses the `[data-theme='dark']` selector for dark mode styles.

## Implementation Details

The neutral color system is implemented as a Tailwind plugin in `tailwind/plugins/neutral-colors.mjs`. This plugin defines the semantic color classes and maps them to the appropriate CSS variables.

## Slate Color Palette

The Slate color palette is a set of cool grays with a blue undertone that provide a professional, modern look. The palette ranges from very light (50) to very dark (950):

```
slate-50:  #f8fafc  - Very light gray, almost white
slate-100: #f1f5f9  - Light gray
slate-200: #e2e8f0  - Light gray
slate-300: #cbd5e1  - Medium-light gray
slate-400: #94a3b8  - Medium gray
slate-500: #64748b  - Medium gray
slate-600: #475569  - Medium-dark gray
slate-700: #334155  - Dark gray
slate-800: #1e293b  - Very dark gray
slate-900: #0f172a  - Almost black
slate-950: #020617  - Black
```

## Changing the Color Palette

To change the underlying color palette (e.g., from Slate to Zinc, Stone, or another color):

1. Update the CSS variables in `globals.css` to use the new color values
2. Update the `--slate-*` variables in the `neutral-colors.mjs` plugin to point to the new color palette
3. The semantic color classes will automatically use the new colors

This approach allows for easy theme switching without having to update component code.
