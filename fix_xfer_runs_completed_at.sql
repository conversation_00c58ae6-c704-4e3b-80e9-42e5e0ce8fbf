-- <PERSON><PERSON>t to fix the completed_at column in xfer_runs_v2 table
-- This updates the completed_at column with the value from the model JSON

-- First, let's see what we're dealing with
SELECT id, run_type, model->>'completed_at' as model_completed_at, completed_at as table_completed_at
FROM xfer_runs_v2
LIMIT 10;

-- Now update the completed_at column with the value from the model JSON
UPDATE xfer_runs_v2
SET completed_at = (model->>'completed_at')::timestamp with time zone
WHERE model->>'completed_at' IS NOT NULL
  AND (completed_at IS NULL OR completed_at = '1970-01-01T00:00:00Z');

-- Check the results
SELECT id, run_type, model->>'completed_at' as model_completed_at, completed_at as table_completed_at
FROM xfer_runs_v2
LIMIT 10;
