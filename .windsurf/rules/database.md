---
trigger: manual
---

## Database 


There are two databases in this application the analytics database `./bin/run_in_db.sh` that is connected to with `get_bo_conn()` and the customer database `./bin/run_in_customer_db.sh` `get_cus_conn()`. The python application in `backofffice` does almost all it's work in the analytics database. The final results are placed in the xfer_ tables. These are then synced to the customer database in `/Users/<USER>/IdeaProjects/mono-repo/backoffice/src/eko/db/sync.py`

The customer application is in apps/customer and makes use of those copied xfer_ tables to power the web application from it's database.

### Analytics Database

All ana_ tables should include a run_id as analysis runs are seperate from each other. See RunData DAO.

**Schemas**: `public` (main), `dash` (metrics),

Don't store ids in array columns, always use a join table. Always add foreign key constraints. If an ana_ table maps onto a kg_ table then deletions and updates in the kg_ table should cascade to the ana_ table.

#### Write Acccess
use the run_in_db.sh script for read/write access to the database.

```
./bin/run_in_db.sh "SQL CODE"
```
or
```./bin/run_in_db.sh  < file.sql```

### Customer Database

#### Write Acccess
Use the run_in_.sh script for read/write access to the database.

```
./bin/run_in_customer_db.sh "SQL CODE"
```
or
```./bin/run_in_customer_db.sh  < file.sql```

The xfer_ tables are the link between customer and analytics databases. They are intentionally without any real schema, constraints or fkeys.

The acc_ and cus_ tables are normal tables and have constraints, foreign keys etc.

The api_queue table is only for front_end to back_end communication.
