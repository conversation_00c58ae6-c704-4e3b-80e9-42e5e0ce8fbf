---
trigger: always_on
---

# Global Preferences

* Please commit changes when finished.
* I use zsh shell.
* Fail Fast, don't add fallbacks or backwards compatability unless explicitly asked to.

# Coding Philosophy
- Max 300-400 lines per file, please split your output into multiple files, creating packages as you need.
- Defensive validation of function parameters, throw ValueErrors if incorrect. Add explicit error logging and exception propagation.
- Do not put in runtime checks for things you are uncertain of at compile time (such as getattr("xyz")) instead FAIL FAST. Avoid silent degradation, fail fast.
- Fail fast, don't try to work out how to continue raise an Error
- When data and functionality are closely related use classes not functions. No need to be Object Oriented about everything, just when it makes sense.
- If a list can only contain unique values consider using a set instead.
- Don't 'swallow' exceptions, log them and raise
