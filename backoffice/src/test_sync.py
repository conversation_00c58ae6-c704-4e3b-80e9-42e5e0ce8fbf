"""
Test script for syncing prediction-v2 data to xfer tables.
"""

from eko.db import get_bo_conn
from eko.db.data.prediction_sync import sync_prediction_v2_to_xfer
from loguru import logger

def main():
    """Run the test."""
    run_id = 3131

    logger.info(f"Testing sync_prediction_v2_to_xfer for run {run_id}")

    with get_bo_conn() as conn:
        try:
            synced_counts = sync_prediction_v2_to_xfer(conn, run_id)
            logger.info(f"Synced data to xfer tables: {synced_counts}")
        except Exception as e:
            logger.error(f"Error syncing prediction-v2 data: {e}")
            logger.exception(e)

if __name__ == "__main__":
    main()
