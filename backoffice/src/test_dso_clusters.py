"""
Test script to debug DSO clustering issues.
"""

from loguru import logger
from eko.db import get_bo_conn
from eko.entities.virtual_queries import get_virtual_entity_for_analytics
from eko.prediction_v2.clustering import create_dso_clusters

def test_create_dso_clusters():
    """Test the create_dso_clusters function."""
    # Get a virtual entity
    virtual_entity = get_virtual_entity_for_analytics("Colgate")
    if not virtual_entity:
        logger.error("Virtual entity not found")
        return
    
    logger.info(f"Testing with virtual entity: {virtual_entity.name} (ID: {virtual_entity.id})")
    
    # Test the function
    try:
        clusters = create_dso_clusters(
            virtual_entity=virtual_entity,
            start_year=2019,
            end_year=2025
        )
        
        logger.info(f"Successfully created {len(clusters)} DSO clusters")
        
        # Print details of each cluster
        for i, cluster in enumerate(clusters):
            logger.info(f"Cluster {i+1}: Year {cluster.year}, Size {cluster.size}, Coherence {cluster.coherence:.4f}")
        
        logger.info("Test completed successfully")
        
    except Exception as e:
        logger.exception(f"Error in create_dso_clusters: {e}")

if __name__ == "__main__":
    test_create_dso_clusters()
