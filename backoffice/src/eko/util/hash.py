import os

import diskcache
import hashlib
from typing import Union

from eko import eko_var_path

hash_cache = diskcache.Cache(os.path.join(eko_var_path,"cache/hash_cache"))

@hash_cache.memoize(expire=60 * 60 * 24)
def sha256_hash_str(input_str):
    hasher = hashlib.sha256()
    # Encode the string to bytes if it's not already encoded
    if isinstance(input_str, str):
        input_str = input_str.encode('utf-8')
    hasher.update(input_str)
    return hasher.hexdigest()


@hash_cache.memoize(expire=60 * 60 * 24)
def sha256_hash_file(file_path):
    return hash_file(file_path, hashlib.sha256)

@hash_cache.memoize(expire=60 * 60 * 24)
def md5_hash_file(file_path):
    return hash_file(file_path)


@hash_cache.memoize(expire=60 * 60 * 24)
def hash_file(file_path, hash_func=hashlib.md5):
    """
    Computes the hash of the specified file using the provided hash function (default: MD5).

    Args:
    file_path (str): Path to the file to hash.
    hash_func: Hash function to use (default: hashlib.md5).

    Returns:
    str: The hash of the file in hexadecimal format.
    """
    hasher = hash_func()

    try:
        with open(file_path, "rb") as f:
            # Read the file in chunks to avoid memory issues with large files
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        return hasher.hexdigest()
    except Exception as e:
        # If there's an error reading the file, log it and return a default hash
        from loguru import logger
        logger.error(f"Error hashing file {file_path}: {str(e)}")
        # Return a hash of the file path as a fallback
        return hash_func(file_path.encode('utf-8')).hexdigest()


@hash_cache.memoize(expire=60 * 60 * 24)
def md5_hash_url(url):
    # Encode the URL as bytes (UTF-8 is a common encoding for URLs)
    url_bytes = url.encode('utf-8')

    # Create an MD5 hash object
    md5_hash = hashlib.md5()

    # Update the hash object with the URL bytes
    md5_hash.update(url_bytes)

    # Get the hexadecimal representation of the hash
    return md5_hash.hexdigest()


@hash_cache.memoize(expire=60 * 60 * 24)
def md5_hash_str(text: str):
    # Encode the text as bytes (UTF-8 is a common encoding)
    text_bytes = text.encode('utf-8')

    # Create an MD5 hash object
    md5_hash = hashlib.md5()

    # Update the hash object with the text bytes
    md5_hash.update(text_bytes)

    # Get the hexadecimal representation of the hash
    return md5_hash.hexdigest()

def avg_non_zero(numbers:list[Union[int,float]]):
    """
    Calculate the average of a list of numbers, ignoring zeros.

    Args:
        numbers (list): List of numbers.

    Returns:
        float: Average of non-zero numbers, or 0 if all are zero.
    """
    non_zero_numbers = [num for num in numbers if num != 0]
    return sum(non_zero_numbers) / len(non_zero_numbers) if non_zero_numbers else 0
