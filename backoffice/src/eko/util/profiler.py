import functools
import time
from contextlib import contextmanager
from typing import Callable, Dict, Any, Optional

from loguru import logger

# Global storage for profiling data
_profiling_stats: Dict[str, Dict[str, Any]] = {}

@contextmanager
def profile_section(section_name: str):
    """
    Context manager to profile a section of code.
    Usage:
        with profile_section("section_name"):
            # code to profile
    """
    start_time = time.time()
    try:
        yield
    finally:
        elapsed = time.time() - start_time
        if section_name not in _profiling_stats:
            _profiling_stats[section_name] = {
                "total_time": 0,
                "call_count": 0,
                "max_time": 0
            }
        
        _profiling_stats[section_name]["total_time"] += elapsed
        _profiling_stats[section_name]["call_count"] += 1
        _profiling_stats[section_name]["max_time"] = max(
            _profiling_stats[section_name]["max_time"], elapsed
        )
        
        # Log the elapsed time
        logger.info(f"PROFILE - {section_name}: {elapsed:.2f}s")

def profile(func: Optional[Callable] = None, section_name: Optional[str] = None):
    """
    Decorator to profile a function.
    Usage:
        @profile
        def my_function():
            pass
        
        # Or with custom section name
        @profile(section_name="custom_name")
        def my_function():
            pass
    """
    def decorator(f):
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            nonlocal section_name
            name = section_name or f.__name__
            with profile_section(name):
                return f(*args, **kwargs)
        return wrapper
    
    if func is None:
        return decorator
    return decorator(func)

def get_profiling_stats():
    """Get the current profiling statistics"""
    return _profiling_stats

def print_profiling_summary():
    """Print a summary of all profiling stats"""
    if not _profiling_stats:
        logger.info("No profiling data collected.")
        return
    
    logger.info("========== PROFILING SUMMARY ==========")
    
    # Sort sections by total time (descending)
    sorted_sections = sorted(
        _profiling_stats.items(), 
        key=lambda x: x[1]["total_time"], 
        reverse=True
    )
    
    for section_name, stats in sorted_sections:
        total = stats["total_time"]
        count = stats["call_count"]
        avg = total / count if count > 0 else 0
        max_time = stats["max_time"]
        
        logger.info(f"{section_name}:")
        logger.info(f"  Total: {total:.2f}s, Calls: {count}, Avg: {avg:.2f}s, Max: {max_time:.2f}s")
    
    logger.info("========== END SUMMARY ==========")

def reset_profiling_stats():
    """Reset all profiling statistics"""
    global _profiling_stats
    _profiling_stats = {}
