from time import sleep
import inspect
import re
import threading
from loguru import logger
from typing import Callable


def timeout_handler(signum, frame):
    raise TimeoutError("Request timed out")


class TimeoutRequest:
    def __init__(self, timeout=2):
        self.timeout = timeout
        self.response = None
        self.error = None

    def make_request(self, func:Callable):
        try:
            self.response = func()
        except Exception as e:
            self.error = e

    def run(self, func:Callable):
        # Create the request thread
        request_thread = threading.Thread(target=self.make_request, args=(func,), name=f"TimeoutRequest {inspect.getsource(func)}")
        request_thread.daemon = True

        # Start the request
        request_thread.start()
        for i in range(self.timeout * 10):
            if i > self.timeout * 5 :
                logger.info(f"Waiting for {inspect.getsource(func)} request")
            if self.response is not None or self.error is not None:
                return self.response, self.error
            sleep(0.1)

        clean_source = re.sub(r'\s+', ' ', inspect.getsource(func))
        logger.warning(f"Timeout for {clean_source} request")
        return None, TimeoutError("Request timed out after {} seconds".format(self.timeout))
