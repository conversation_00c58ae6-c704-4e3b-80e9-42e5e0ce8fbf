"""
CLI commands for predictive analytics.
"""

import os
import webbrowser
from typing import Optional

import click
from loguru import logger

from eko import eko_var_path
from eko.analysis_v2.pipeline_tracker_extended import get_traceability_tracker
from eko.db import get_bo_conn
from eko.entities.virtual_queries import get_virtual_entity_for_analytics
from eko.predictive.analysis import generate_predictive_analysis
from eko.predictive.models import RegressionModelType
from eko.predictive.regression import predict_future_clusters
from eko.predictive.temporal_clustering import create_temporal_clusters
from eko.predictive.utils import generate_predictive_report

from eko.db.data.run import RunData


@click.command("analyze-predictive-trends")
@click.option("--entity", required=True, help="Entity short ID or name")
@click.option("--start-year", type=int, required=True, help="Start year for analysis")
@click.option("--end-year", type=int, required=True, help="End year for analysis")
@click.option("--future-years", type=int, default=3, help="Number of future years to predict")
@click.option("--model-type", type=click.Choice([m.value for m in RegressionModelType]),
              default=RegressionModelType.ENSEMBLE.value, help="Regression model type")
@click.option("--report", is_flag=True, help="Generate a human-readable report")
@click.option("--report-path", help="Path to save the report (default: var/reports/predictive)")
def analyze_predictive_trends(
    entity: str,
    start_year: int,
    end_year: int,
    future_years: int,
    model_type: str,
    report: bool,
    report_path: Optional[str] = None
):
    """
    Run the full predictive analytics pipeline.

    This command:
    1. Creates temporal clusters for historical years
    2. Predicts future clusters
    3. Generates human-readable analysis
    4. Optionally generates a report
    """
    logger.info(f"Starting predictive analytics for {entity} ({start_year}-{end_year})")

    # Get entity
    virtual_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not virtual_entity:
        # Try with short ID
        virtual_entity = get_virtual_entity_for_analytics(virtual_entity_short_id=entity)
        if not virtual_entity:
            logger.error(f"Virtual entity not found: {entity}")
            return

    # Create a new run for this analysis
    with get_bo_conn() as conn:
        # Create a run with models populated from kg_model_sections
        run = RunData.create_historical_run(
            conn=conn,
            start_year=start_year,
            end_year=end_year,
            models=None,  # Will be populated from kg_model_sections
            target=virtual_entity.short_id
        )
        run_id = run.id
        logger.info(f"Created new run with ID {run_id} for predictive analysis")

        # Create traceability tracker
        if run_id is not None:
            tracker = get_traceability_tracker(conn, run_id=run_id)
        else:
            tracker = None

    # Step 1: Create temporal clusters
    logger.info("Step 1: Creating temporal clusters")
    clusters = create_temporal_clusters(
        virtual_entity,
        start_year,
        end_year,
        tracker,
        run_id=run_id
    )
    logger.info(f"Created {len(clusters)} temporal clusters")

    # Step 2: Predict future clusters
    logger.info("Step 2: Predicting future clusters")
    historical_years = list(range(start_year, end_year + 1))
    future_year_list = list(range(end_year + 1, end_year + future_years + 1))

    if run_id is not None:
        predictions = predict_future_clusters(
            virtual_entity,
            historical_years,
            future_year_list,
            run_id,
            RegressionModelType(model_type),
            tracker
        )
    else:
        logger.error("Run ID is None, cannot predict future clusters")
        return
    logger.info(f"Created {len(predictions)} future cluster predictions")

    # Step 3: Generate human-readable analysis
    logger.info("Step 3: Generating human-readable analysis")
    analyses = generate_predictive_analysis(
        virtual_entity,
        predictions,
        tracker
    )
    logger.info(f"Created {len(analyses)} predictive analyses")

    # Step 4: Generate report if requested
    if report:
        logger.info("Step 4: Generating report")
        if not report_path:
            report_path = os.path.join(eko_var_path, "reports/predictive")
            os.makedirs(report_path, exist_ok=True)

        # Retrieve analyses and predictions from the database for the current run only
        logger.info(f"Retrieving analyses and predictions from database for run {run_id}")
        with get_bo_conn() as conn:
            with conn.cursor() as cursor:
                from eko.predictive.dao import PredictiveAnalysisDAO, ClusterPredictionDAO

                # Get all analyses for this run
                all_analyses = PredictiveAnalysisDAO.list_by_run_id(cursor, run_id)
                logger.info(f"Retrieved {len(all_analyses)} analyses from database for run {run_id}")

                # Get all predictions for this run
                predictions = ClusterPredictionDAO.list_by_run_id(cursor, run_id)
                logger.info(f"Retrieved {len(predictions)} predictions from database for run {run_id}")

        report_file_path = os.path.join(report_path, f"{virtual_entity.short_id}_{end_year + 1}_{end_year + future_years}_run{run_id}.html")
        report_file = generate_predictive_report(
            virtual_entity,
            all_analyses,  # Use all analyses from the database
            predictions,
            report_file_path
        )
        logger.info(f"Report generated: {report_file}")

        # Open the report in the browser
        if report_file:
            logger.info(f"Opening report in browser: {report_file}")
            webbrowser.open(f"file://{report_file}")
        RunData.mark_completed(conn, run_id)

    logger.info("Predictive analytics completed successfully")


@click.command("create-temporal-clusters")
@click.option("--entity", required=True, help="Entity short ID or name")
@click.option("--start-year", type=int, required=True, help="Start year for analysis")
@click.option("--end-year", type=int, required=True, help="End year for analysis")
def create_temporal_clusters_command(
    entity: str,
    start_year: int,
    end_year: int
):
    """
    Create temporal clusters for an entity.

    This command groups statements by year and applies clustering to identify
    patterns in entity behavior over time.
    """
    logger.info(f"Creating temporal clusters for {entity} ({start_year}-{end_year})")

    # Get entity
    virtual_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not virtual_entity:
        # Try with short ID
        virtual_entity = get_virtual_entity_for_analytics(virtual_entity_short_id=entity)
        if not virtual_entity:
            logger.error(f"Virtual entity not found: {entity}")
            return

    # Create a new run for this analysis
    with get_bo_conn() as conn:
        from eko.db.data.run import RunData
        run = RunData.create_predictive_run(
            conn=conn,
            start_year=start_year,
            end_year=end_year,
            models=None,  # Will be populated from kg_model_sections
            target=virtual_entity.short_id
        )
        run_id = run.id
        logger.info(f"Created new run with ID {run_id} for predictive analysis")

        # Create traceability tracker
        if run_id is not None:
            tracker = get_traceability_tracker(conn, run_id=run_id)
        else:
            tracker = None

    # Create temporal clusters
    clusters = create_temporal_clusters(
        virtual_entity,
        start_year,
        end_year,
        tracker,
        run_id=run_id
    )

    logger.info(f"Created {len(clusters)} temporal clusters")


@click.command("predict-future-clusters")
@click.option("--entity", required=True, help="Entity short ID or name")
@click.option("--start-year", type=int, required=True, help="Start year for historical data")
@click.option("--end-year", type=int, required=True, help="End year for historical data")
@click.option("--future-years", type=int, default=3, help="Number of future years to predict")
@click.option("--model-type", type=click.Choice([m.value for m in RegressionModelType]),
              default=RegressionModelType.ENSEMBLE.value, help="Regression model type")
def predict_future_clusters_command(
    entity: str,
    start_year: int,
    end_year: int,
    future_years: int,
    model_type: str
):
    """
    Predict future clusters for an entity.

    This command uses regression models to predict how clusters will evolve
    in future years based on historical data.
    """
    logger.info(f"Predicting future clusters for {entity} ({start_year}-{end_year}, +{future_years} years)")

    # Get entity
    virtual_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not virtual_entity:
        # Try with short ID
        virtual_entity = get_virtual_entity_for_analytics(virtual_entity_short_id=entity)
        if not virtual_entity:
            logger.error(f"Virtual entity not found: {entity}")
            return

    # Create a new run for this analysis
    with get_bo_conn() as conn:
        from eko.db.data.run import RunData
        run = RunData.create_predictive_run(
            conn=conn,
            start_year=start_year,
            end_year=end_year,
            models=None,  # Will be populated from kg_model_sections
            target=virtual_entity.short_id
        )
        run_id = run.id
        logger.info(f"Created new run with ID {run_id} for predictive analysis")

        # Create traceability tracker
        if run_id is not None:
            tracker = get_traceability_tracker(conn, run_id=run_id)
        else:
            tracker = None

    # Predict future clusters
    historical_years = list(range(start_year, end_year + 1))
    future_year_list = list(range(end_year + 1, end_year + future_years + 1))

    if run_id is not None:
        predictions = predict_future_clusters(
            virtual_entity,
            historical_years,
            future_year_list,
            run_id,
            RegressionModelType(model_type),
            tracker
        )
    else:
        logger.error("Run ID is None, cannot predict future clusters")
        return

    logger.info(f"Created {len(predictions)} future cluster predictions")


@click.command("generate-predictive-analysis")
@click.option("--entity", required=True, help="Entity short ID or name")
@click.option("--future-years", type=int, default=3, help="Number of future years to analyze")
@click.option("--report", is_flag=True, help="Generate a human-readable report")
@click.option("--report-path", help="Path to save the report (default: var/reports/predictive)")
def generate_predictive_analysis_command(
    entity: str,
    future_years: int,
    report: bool,
    report_path: Optional[str] = None
):
    """
    Generate human-readable analysis of predicted clusters.

    This command uses LLMs to convert predicted DEMISE vectors into
    meaningful insights about future entity behavior.
    """
    logger.info(f"Generating predictive analysis for {entity} (+{future_years} years)")

    # Get entity
    virtual_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not virtual_entity:
        # Try with short ID
        virtual_entity = get_virtual_entity_for_analytics(virtual_entity_short_id=entity)
        if not virtual_entity:
            logger.error(f"Virtual entity not found: {entity}")
            return

    # Note: We're not using a tracker here since we're just querying the database

    # Get predictions from database
    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT id, virtual_entity_id, source_cluster_id, predicted_year,
                       predicted_centroid, predicted_demise, confidence, model_type, created_at
                FROM ana_cluster_predictions
                WHERE virtual_entity_id = %s
                ORDER BY predicted_year, confidence DESC
            """, (virtual_entity.id,))

            # TODO: Convert to ClusterPredictionModel objects
            # For now, just count the rows
            predictions = list(cursor.fetchall())

    if not predictions:
        logger.error(f"No predictions found for {entity}")
        return

    # Generate analysis
    # TODO: Convert predictions to ClusterPredictionModel objects
    # analyses = generate_predictive_analysis(
    #     virtual_entity,
    #     predictions,
    #     tracker
    # )

    logger.info(f"Found {len(predictions)} predictions to analyze")
    logger.info("This command is not fully implemented yet. Please use analyze-predictive-trends instead.")

    # Generate report if requested
    if report:
        logger.info("Report generation is not fully implemented yet.")
        # When implemented, it should look like this:
        # # Create a new run for this analysis
        # with get_bo_conn() as conn:
        #     from eko.db.data.run import RunData
        #     run = RunData.create_predictive_run(
        #         conn=conn,
        #         start_year=start_year,
        #         end_year=end_year,
        #         models=None,  # Will be populated from kg_model_sections
        #         target=virtual_entity.short_id
        #     )
        #     run_id = run.id
        #     logger.info(f"Created new run with ID {run_id} for predictive analysis")
        #
        # if not report_path:
        #     report_path = os.path.join(eko_var_path, "reports/predictive")
        #     os.makedirs(report_path, exist_ok=True)
        #
        # # Retrieve analyses from the database for the current run only
        # with get_bo_conn() as conn:
        #     with conn.cursor() as cursor:
        #         from eko.predictive.dao import PredictiveAnalysisDAO
        #
        #         # Get analyses for this entity and run
        #         all_analyses = []
        #         for year in future_year_list:
        #             year_analyses = PredictiveAnalysisDAO.list_by_entity_and_year(
        #                 cursor,
        #                 virtual_entity.id,
        #                 year,
        #                 run_id=run_id
        #             )
        #             all_analyses.extend(year_analyses)
        #
        # report_file_path = os.path.join(report_path, f"{virtual_entity.short_id}_future_analysis.html")
        # report_file = generate_predictive_report(
        #     virtual_entity,
        #     all_analyses,
        #     predictions,
        #     report_file_path
        # )
        # logger.info(f"Report generated: {report_file}")
        #
        # # Open the report in the browser
        # if report_file:
        #     logger.info(f"Opening report in browser: {report_file}")
        #     webbrowser.open(f"file://{report_file}")


def register_commands(cli):
    """Register CLI commands."""
    cli.add_command(analyze_predictive_trends)
    cli.add_command(create_temporal_clusters_command)
    cli.add_command(predict_future_clusters_command)
    cli.add_command(generate_predictive_analysis_command)
