"""
Data Access Objects for the predictive analytics system.
"""

import json
from typing import List, Optional, Dict, Any, <PERSON><PERSON>

from loguru import logger
from psycopg import Connection, Cursor

from eko.db import get_bo_conn
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.predictive.models import (
    TemporalClusterModel,
    ClusterPredictionModel,
    PredictiveAnalysisModel,
    XferPredictiveAnalysisModel,
    RegressionModelType
)


class TemporalClusterDAO:
    """DAO for temporal clusters."""

    @staticmethod
    def create(cursor: Cursor, model: TemporalClusterModel) -> TemporalClusterModel:
        """
        Create a new temporal cluster in the database.

        Args:
            cursor: Database cursor
            model: Temporal cluster model

        Returns:
            Updated model with ID
        """
        try:

            # Create new cluster
            cursor.execute("""
                INSERT INTO ana_predict_temporal_clusters
                (run_id, virtual_entity_id, year, centroid, statement_ids, size, coherence, created_at)
                VALUES (%s, %s, %s, %s::vector, %s, %s, %s, NOW())
                RETURNING id, created_at
            """, (
                model.run_id,
                model.virtual_entity_id,
                model.year,
                model.centroid,
                model.statement_ids,
                model.size,
                model.coherence
            ))

            result = cursor.fetchone()
            if result:
                model.id = result[0]
                model.created_at = result[1]
                return model
            else:
                raise ValueError("Failed to create temporal cluster")
        except Exception as e:
            logger.exception(f"Error creating/updating temporal cluster: {str(e)}")
            raise

    @staticmethod
    def get_by_id(cursor: Cursor, cluster_id: int) -> Optional[TemporalClusterModel]:
        """
        Get a temporal cluster by ID.

        Args:
            cursor: Database cursor
            cluster_id: Temporal cluster ID

        Returns:
            Temporal cluster model or None if not found
        """
        try:
            cursor.execute("""
                SELECT id, run_id, virtual_entity_id, year, centroid,
                       statement_ids, size, coherence, created_at
                FROM ana_predict_temporal_clusters
                WHERE id = %s
            """, (cluster_id,))

            row = cursor.fetchone()
            if not row:
                return None

            # Parse the centroid vector
            centroid_vector = row[4]
            # If it's a string representation of a vector, convert it to a list
            if isinstance(centroid_vector, str):
                # Remove brackets and split by commas
                try:
                    # Try to parse as a string representation of a list
                    centroid_vector = centroid_vector.strip('[]').split(',')
                    centroid_vector = [float(x.strip()) for x in centroid_vector]
                except Exception as e:
                    logger.warning(f"Error parsing centroid vector: {str(e)}")
                    # Fallback to empty vector
                    centroid_vector = []
            elif hasattr(centroid_vector, '__iter__'):
                # It's already an iterable, convert to list
                centroid_vector = list(centroid_vector)
            else:
                # Fallback to empty vector
                logger.warning(f"Unexpected centroid type: {type(centroid_vector)}")
                centroid_vector = []

            return TemporalClusterModel(
                id=row[0],
                run_id=row[1],
                virtual_entity_id=row[2],
                year=row[3],
                centroid=centroid_vector,
                demise_vector=[],  # Empty vector since we don't have it in the database yet
                statement_ids=row[5],
                size=row[6],
                coherence=row[7],
                created_at=row[8]
            )
        except Exception as e:
            logger.exception(f"Error getting temporal cluster by ID: {str(e)}")
            raise

    @staticmethod
    def list_by_entity_and_year_range(
        cursor: Cursor,
        virtual_entity_id: int,
        start_year: int,
        end_year: int
    ) -> List[TemporalClusterModel]:
        """
        List temporal clusters for an entity within a year range.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID
            start_year: Start year (inclusive)
            end_year: End year (inclusive)

        Returns:
            List of temporal cluster models
        """
        try:
            cursor.execute("""
                SELECT id, run_id, virtual_entity_id, year, centroid,
                       statement_ids, size, coherence, created_at
                FROM ana_predict_temporal_clusters
                WHERE virtual_entity_id = %s AND year BETWEEN %s AND %s
                ORDER BY year
            """, (virtual_entity_id, start_year, end_year))

            results = []
            for row in cursor.fetchall():
                # Parse the centroid vector
                centroid_vector = row[4]

                # Initialize empty demise_vector
                demise_vector = []
                # If it's a string representation of a vector, convert it to a list
                if isinstance(centroid_vector, str):
                    # Remove brackets and split by commas
                    try:
                        # Try to parse as a string representation of a list
                        centroid_vector = centroid_vector.strip('[]').split(',')
                        centroid_vector = [float(x.strip()) for x in centroid_vector]
                    except Exception as e:
                        logger.warning(f"Error parsing centroid vector: {str(e)}")
                        raise
                elif hasattr(centroid_vector, '__iter__'):
                    # It's already an iterable, convert to list
                    centroid_vector = list(centroid_vector)
                else:
                    # Fallback to empty vector
                    raise ValueError(f"Unexpected centroid type: {type(centroid_vector)}")

                results.append(TemporalClusterModel(
                    id=row[0],
                    run_id=row[1],
                    virtual_entity_id=row[2],
                    year=row[3],
                    centroid=centroid_vector,
                    demise_vector=[],  # Empty vector since we don't have it in the database yet
                    statement_ids=row[5],
                    size=row[6],
                    coherence=row[7],
                    created_at=row[8]
                ))

            return results
        except Exception as e:
            logger.exception(f"Error listing temporal clusters: {str(e)}")
            raise

    @staticmethod
    def delete_by_entity(cursor: Cursor, virtual_entity_id: int) -> int:
        """
        Delete all temporal clusters for an entity.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID

        Returns:
            Number of clusters deleted
        """
        try:
            cursor.execute("""
                DELETE FROM ana_predict_temporal_clusters
                WHERE virtual_entity_id = %s
                RETURNING id
            """, (virtual_entity_id,))

            deleted_ids = cursor.fetchall()
            return len(deleted_ids)
        except Exception as e:
            logger.exception(f"Error deleting temporal clusters: {str(e)}")
            raise


class ClusterPredictionDAO:
    """DAO for cluster predictions."""

    @staticmethod
    def create(cursor: Cursor, model: ClusterPredictionModel) -> ClusterPredictionModel:
        """
        Create a new cluster prediction in the database.
        If a prediction with the same source_cluster_id and predicted_year already exists, update it.

        Args:
            cursor: Database cursor
            model: Cluster prediction model

        Returns:
            Updated model with ID
        """
        try:
            # First check if a prediction with the same source cluster and year already exists
            cursor.execute("""
                SELECT id, created_at FROM ana_predict_cluster_predictions
                WHERE source_cluster_id = %s AND predicted_year = %s
            """, (model.source_cluster_id, model.predicted_year))

            existing = cursor.fetchone()

            # Convert vectors to JSON
            predicted_vector_json = json.dumps(model.predicted_vector)
            historical_vector_json = json.dumps(model.historical_vector)

            if existing:
                # Update existing prediction
                cursor.execute("""
                    UPDATE ana_predict_cluster_predictions
                    SET predicted_centroid = %s::vector,
                        predicted_vector = %s::jsonb,
                        final_historical_year = %s,
                        historical_vector = %s::jsonb,
                        confidence = %s,
                        model_type = %s
                    WHERE id = %s
                    RETURNING id, created_at
                """, (
                    model.predicted_centroid,
                    predicted_vector_json,
                    model.final_historical_year,
                    historical_vector_json,
                    model.confidence,
                    model.model_type.value,
                    existing[0]
                ))

                result = cursor.fetchone()
                if result:
                    model.id = result[0]
                    model.created_at = result[1]
                    logger.info(f"Updated existing cluster prediction for source cluster {model.source_cluster_id}, year {model.predicted_year}")
                    return model
                else:
                    raise ValueError("Failed to update cluster prediction")
            else:
                # Create new prediction
                cursor.execute("""
                    INSERT INTO ana_predict_cluster_predictions
                    (run_id, virtual_entity_id, source_cluster_id, predicted_year, predicted_centroid,
                     predicted_vector, final_historical_year, historical_vector, confidence, model_type, created_at)
                    VALUES (%s, %s, %s, %s, %s::vector, %s::jsonb, %s, %s::jsonb, %s, %s, NOW())
                    RETURNING id, created_at
                """, (
                    model.run_id,
                    model.virtual_entity_id,
                    model.source_cluster_id,
                    model.predicted_year,
                    model.predicted_centroid,  # This now calls the property method
                    predicted_vector_json,
                    model.final_historical_year,
                    historical_vector_json,
                    model.confidence,
                    model.model_type.value
                ))

                result = cursor.fetchone()
                if result:
                    model.id = result[0]
                    model.created_at = result[1]
                    return model
                else:
                    raise ValueError("Failed to create cluster prediction")
        except Exception as e:
            logger.exception(f"Error creating/updating cluster prediction: {str(e)}")
            raise

    @staticmethod
    def get_by_id(cursor: Cursor, prediction_id: int) -> Optional[ClusterPredictionModel]:
        """
        Get a cluster prediction by ID.

        Args:
            cursor: Database cursor
            prediction_id: Prediction ID

        Returns:
            Cluster prediction model or None if not found
        """
        try:
            cursor.execute("""
                SELECT id, run_id, virtual_entity_id, source_cluster_id, predicted_year,
                       predicted_centroid, predicted_vector, final_historical_year, historical_vector,
                       confidence, model_type, created_at
                FROM ana_predict_cluster_predictions
                WHERE id = %s
            """, (prediction_id,))

            row = cursor.fetchone()
            if not row:
                return None

            # Parse predicted vector from JSON
            try:
                if isinstance(row[6], list):
                    predicted_vector = row[6]
                elif isinstance(row[6], dict):
                    # If it's a dict, it might be a sparse representation
                    # Convert to a list for compatibility
                    predicted_vector = list(row[6].values())
                else:
                    # Try to parse as JSON
                    parsed = json.loads(row[6])
                    if isinstance(parsed, list):
                        predicted_vector = parsed
                    elif isinstance(parsed, dict):
                        predicted_vector = list(parsed.values())
                    else:
                        logger.warning(f"Unexpected JSON format for predicted vector: {type(parsed)}")
                        predicted_vector = []
            except Exception as e:
                logger.warning(f"Error parsing predicted vector JSON: {str(e)}")
                predicted_vector = []

            # Ensure all values are floats
            predicted_vector = [float(v) for v in predicted_vector]

            # Parse historical vector from JSON
            try:
                if isinstance(row[8], list):
                    historical_vector = row[8]
                elif isinstance(row[8], dict):
                    # If it's a dict, it might be a sparse representation
                    # Convert to a list for compatibility
                    historical_vector = list(row[8].values())
                else:
                    # Try to parse as JSON
                    parsed = json.loads(row[8])
                    if isinstance(parsed, list):
                        historical_vector = parsed
                    elif isinstance(parsed, dict):
                        historical_vector = list(parsed.values())
                    else:
                        logger.warning(f"Unexpected JSON format for historical vector: {type(parsed)}")
                        historical_vector = []
            except Exception as e:
                logger.warning(f"Error parsing historical vector JSON: {str(e)}")
                historical_vector = []

            # Ensure all values are floats
            historical_vector = [float(v) for v in historical_vector]

            # Get the final historical year
            final_historical_year = row[7]

            # Parse the centroid vector
            centroid_vector = row[5]
            # If it's a string representation of a vector, convert it to a list
            if isinstance(centroid_vector, str):
                # Remove brackets and split by commas
                try:
                    # Try to parse as a string representation of a list
                    centroid_vector = centroid_vector.strip('[]').split(',')
                    centroid_vector = [float(x.strip()) for x in centroid_vector]
                except Exception as e:
                    logger.warning(f"Error parsing centroid vector: {str(e)}")
                    # Fallback to empty vector
                    centroid_vector = []
            elif hasattr(centroid_vector, '__iter__'):
                # It's already an iterable, convert to list
                centroid_vector = list(centroid_vector)
            else:
                # Fallback to empty vector
                logger.warning(f"Unexpected centroid type: {type(centroid_vector)}")
                centroid_vector = []

            return ClusterPredictionModel(
                id=row[0],
                run_id=row[1],
                virtual_entity_id=row[2],
                source_cluster_id=row[3],
                predicted_year=row[4],
                final_historical_year=final_historical_year,
                predicted_vector=predicted_vector,
                historical_vector=historical_vector,
                confidence=row[9],
                model_type=RegressionModelType(row[10]),
                created_at=row[11]
            )
        except Exception as e:
            logger.exception(f"Error getting cluster prediction by ID: {str(e)}")
            raise

    @staticmethod
    def list_by_entity_and_year(
        cursor: Cursor,
        virtual_entity_id: int,
        year: int
    ) -> List[ClusterPredictionModel]:
        """
        List cluster predictions for an entity and year.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID
            year: Predicted year

        Returns:
            List of cluster prediction models
        """
        try:
            cursor.execute("""
                SELECT id, run_id, virtual_entity_id, source_cluster_id, predicted_year,
                       predicted_centroid, predicted_vector, final_historical_year, historical_vector,
                       confidence, model_type, created_at
                FROM ana_predict_cluster_predictions
                WHERE virtual_entity_id = %s AND predicted_year = %s
                ORDER BY confidence DESC
            """, (virtual_entity_id, year))

            results = []
            for row in cursor.fetchall():
                # Parse predicted vector from JSON
                try:
                    if isinstance(row[6], list):
                        predicted_vector = row[6]
                    elif isinstance(row[6], dict):
                        # If it's a dict, it might be a sparse representation
                        # Convert to a list for compatibility
                        predicted_vector = list(row[6].values())
                    else:
                        # Try to parse as JSON
                        parsed = json.loads(row[6])
                        if isinstance(parsed, list):
                            predicted_vector = parsed
                        elif isinstance(parsed, dict):
                            predicted_vector = list(parsed.values())
                        else:
                            logger.warning(f"Unexpected JSON format for predicted vector: {type(parsed)}")
                            predicted_vector = []
                except Exception as e:
                    logger.warning(f"Error parsing predicted vector JSON: {str(e)}")
                    predicted_vector = []

                # Ensure all values are floats
                predicted_vector = [float(v) for v in predicted_vector]

                # Parse historical vector from JSON
                try:
                    if isinstance(row[8], list):
                        historical_vector = row[8]
                    elif isinstance(row[8], dict):
                        # If it's a dict, it might be a sparse representation
                        # Convert to a list for compatibility
                        historical_vector = list(row[8].values())
                    else:
                        # Try to parse as JSON
                        parsed = json.loads(row[8])
                        if isinstance(parsed, list):
                            historical_vector = parsed
                        elif isinstance(parsed, dict):
                            historical_vector = list(parsed.values())
                        else:
                            logger.warning(f"Unexpected JSON format for historical vector: {type(parsed)}")
                            historical_vector = []
                except Exception as e:
                    logger.warning(f"Error parsing historical vector JSON: {str(e)}")
                    historical_vector = []

                # Ensure all values are floats
                historical_vector = [float(v) for v in historical_vector]

                # Get the final historical year
                final_historical_year = row[7]

                # Parse the centroid vector
                centroid_vector = row[5]
                # If it's a string representation of a vector, convert it to a list
                if isinstance(centroid_vector, str):
                    # Remove brackets and split by commas
                    try:
                        # Try to parse as a string representation of a list
                        centroid_vector = centroid_vector.strip('[]').split(',')
                        centroid_vector = [float(x.strip()) for x in centroid_vector]
                    except Exception as e:
                        logger.warning(f"Error parsing centroid vector: {str(e)}")
                        # Fallback to empty vector
                        centroid_vector = []
                elif hasattr(centroid_vector, '__iter__'):
                    # It's already an iterable, convert to list
                    centroid_vector = list(centroid_vector)
                else:
                    # Fallback to empty vector
                    logger.warning(f"Unexpected centroid type: {type(centroid_vector)}")
                    centroid_vector = []

                results.append(ClusterPredictionModel(
                    id=row[0],
                    run_id=row[1],
                    virtual_entity_id=row[2],
                    source_cluster_id=row[3],
                    predicted_year=row[4],
                    final_historical_year=final_historical_year,
                    predicted_vector=predicted_vector,
                    historical_vector=historical_vector,
                    confidence=row[9],
                    model_type=RegressionModelType(row[10]),
                    created_at=row[11]
                ))

            return results
        except Exception as e:
            logger.exception(f"Error listing cluster predictions: {str(e)}")
            raise

    @staticmethod
    def list_by_run_id(
        cursor: Cursor,
        run_id: int
    ) -> List[ClusterPredictionModel]:
        """
        List cluster predictions for a specific run.

        Args:
            cursor: Database cursor
            run_id: Run ID

        Returns:
            List of cluster prediction models
        """
        try:
            cursor.execute("""
                SELECT id, run_id, virtual_entity_id, source_cluster_id, predicted_year,
                       predicted_centroid, predicted_vector, final_historical_year, historical_vector,
                       confidence, model_type, created_at
                FROM ana_predict_cluster_predictions
                WHERE run_id = %s
                ORDER BY predicted_year, confidence DESC
            """, (run_id,))

            results = []
            for row in cursor.fetchall():
                # Parse predicted vector from JSON
                try:
                    if isinstance(row[6], list):
                        predicted_vector = row[6]
                    elif isinstance(row[6], dict):
                        # If it's a dict, it might be a sparse representation
                        # Convert to a list for compatibility
                        predicted_vector = list(row[6].values())
                    else:
                        # Try to parse as JSON
                        parsed = json.loads(row[6])
                        if isinstance(parsed, list):
                            predicted_vector = parsed
                        elif isinstance(parsed, dict):
                            predicted_vector = list(parsed.values())
                        else:
                            logger.warning(f"Unexpected JSON format for predicted vector: {type(parsed)}")
                            predicted_vector = []
                except Exception as e:
                    logger.warning(f"Error parsing predicted vector JSON: {str(e)}")
                    predicted_vector = []

                # Ensure all values are floats
                predicted_vector = [float(v) for v in predicted_vector]

                # Parse historical vector from JSON
                try:
                    if isinstance(row[8], list):
                        historical_vector = row[8]
                    elif isinstance(row[8], dict):
                        # If it's a dict, it might be a sparse representation
                        # Convert to a list for compatibility
                        historical_vector = list(row[8].values())
                    else:
                        # Try to parse as JSON
                        parsed = json.loads(row[8])
                        if isinstance(parsed, list):
                            historical_vector = parsed
                        elif isinstance(parsed, dict):
                            historical_vector = list(parsed.values())
                        else:
                            logger.warning(f"Unexpected JSON format for historical vector: {type(parsed)}")
                            historical_vector = []
                except Exception as e:
                    logger.warning(f"Error parsing historical vector JSON: {str(e)}")
                    historical_vector = []

                # Ensure all values are floats
                historical_vector = [float(v) for v in historical_vector]

                # Get the final historical year
                final_historical_year = row[7]

                # Parse the centroid vector
                centroid_vector = row[5]
                # If it's a string representation of a vector, convert it to a list
                if isinstance(centroid_vector, str):
                    # Remove brackets and split by commas
                    try:
                        # Try to parse as a string representation of a list
                        centroid_vector = centroid_vector.strip('[]').split(',')
                        centroid_vector = [float(x.strip()) for x in centroid_vector]
                    except Exception as e:
                        logger.warning(f"Error parsing centroid vector: {str(e)}")
                        # Fallback to empty vector
                        centroid_vector = []
                elif hasattr(centroid_vector, '__iter__'):
                    # It's already an iterable, convert to list
                    centroid_vector = list(centroid_vector)
                else:
                    # Fallback to empty vector
                    logger.warning(f"Unexpected centroid type: {type(centroid_vector)}")
                    centroid_vector = []

                results.append(ClusterPredictionModel(
                    id=row[0],
                    run_id=row[1],
                    virtual_entity_id=row[2],
                    source_cluster_id=row[3],
                    predicted_year=row[4],
                    final_historical_year=final_historical_year,
                    predicted_vector=predicted_vector,
                    historical_vector=historical_vector,
                    confidence=row[9],
                    model_type=RegressionModelType(row[10]),
                    created_at=row[11]
                ))

            return results
        except Exception as e:
            logger.exception(f"Error listing cluster predictions by run ID: {str(e)}")
            raise

    @staticmethod
    def delete_by_entity(cursor: Cursor, virtual_entity_id: int) -> int:
        """
        Delete all cluster predictions for an entity.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID

        Returns:
            Number of predictions deleted
        """
        try:
            cursor.execute("""
                DELETE FROM ana_predict_cluster_predictions
                WHERE virtual_entity_id = %s
                RETURNING id
            """, (virtual_entity_id,))

            deleted_ids = cursor.fetchall()
            return len(deleted_ids)
        except Exception as e:
            logger.exception(f"Error deleting cluster predictions: {str(e)}")
            raise


class PredictiveAnalysisDAO:
    """DAO for predictive analysis."""

    @staticmethod
    def create(cursor: Cursor, model: PredictiveAnalysisModel) -> PredictiveAnalysisModel:
        """
        Create a new predictive analysis in the database.
        If an analysis with the same prediction_id already exists, update it.

        Args:
            cursor: Database cursor
            model: Predictive analysis model

        Returns:
            Updated model with ID
        """
        try:
            # First check if an analysis with the same prediction ID already exists
            cursor.execute("""
                SELECT id, created_at FROM ana_predict_analysis
                WHERE prediction_id = %s
            """, (model.prediction_id,))

            existing = cursor.fetchone()

            if existing:
                # Update existing analysis
                cursor.execute("""
                    UPDATE ana_predict_analysis
                    SET summary = %s,
                        detailed_analysis = %s,
                        potential_risks = %s,
                        potential_opportunities = %s,
                        confidence = %s
                    WHERE id = %s
                    RETURNING id, created_at
                """, (
                    model.summary,
                    model.detailed_analysis,
                    model.potential_risks,
                    model.potential_opportunities,
                    model.confidence,
                    existing[0]
                ))

                result = cursor.fetchone()
                if result:
                    model.id = result[0]
                    model.created_at = result[1]
                    logger.info(f"Updated existing predictive analysis for prediction {model.prediction_id}")
                    return model
                else:
                    raise ValueError("Failed to update predictive analysis")
            else:
                # Create new analysis
                cursor.execute("""
                    INSERT INTO ana_predict_analysis
                    (run_id, prediction_id, virtual_entity_id, year, summary, detailed_analysis,
                     potential_risks, potential_opportunities, confidence, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    RETURNING id, created_at
                """, (
                    model.run_id,
                    model.prediction_id,
                    model.virtual_entity_id,
                    model.year,
                    model.summary,
                    model.detailed_analysis,
                    model.potential_risks,
                    model.potential_opportunities,
                    model.confidence
                ))

                result = cursor.fetchone()
                if result:
                    model.id = result[0]
                    model.created_at = result[1]
                    return model
                else:
                    raise ValueError("Failed to create predictive analysis")
        except Exception as e:
            logger.exception(f"Error creating/updating predictive analysis: {str(e)}")
            raise

    @staticmethod
    def get_by_prediction_id(cursor: Cursor, prediction_id: int) -> Optional[PredictiveAnalysisModel]:
        """
        Get a predictive analysis by prediction ID.

        Args:
            cursor: Database cursor
            prediction_id: Prediction ID

        Returns:
            Predictive analysis model or None if not found
        """
        try:
            cursor.execute("""
                SELECT id, run_id, prediction_id, virtual_entity_id, year, summary,
                       detailed_analysis, potential_risks, potential_opportunities,
                       confidence, created_at
                FROM ana_predict_analysis
                WHERE prediction_id = %s
            """, (prediction_id,))

            row = cursor.fetchone()
            if not row:
                return None

            return PredictiveAnalysisModel(
                id=row[0],
                run_id=row[1],
                prediction_id=row[2],
                virtual_entity_id=row[3],
                year=row[4],
                summary=row[5],
                detailed_analysis=row[6],
                potential_risks=row[7],
                potential_opportunities=row[8],
                confidence=row[9],
                created_at=row[10]
            )
        except Exception as e:
            logger.exception(f"Error getting predictive analysis by prediction ID: {str(e)}")
            raise

    @staticmethod
    def list_by_entity_and_year(
        cursor: Cursor,
        virtual_entity_id: int,
        year: int,
        run_id: Optional[int] = None
    ) -> List[PredictiveAnalysisModel]:
        """
        List predictive analyses for an entity and year.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID
            year: Predicted year
            run_id: Optional run ID to filter by

        Returns:
            List of predictive analysis models
        """
        try:
            query = """
                SELECT id, run_id, prediction_id, virtual_entity_id, year, summary,
                       detailed_analysis, potential_risks, potential_opportunities,
                       confidence, created_at
                FROM ana_predict_analysis
                WHERE virtual_entity_id = %s AND year = %s
            """
            params = [virtual_entity_id, year]

            # Add run_id filter if provided
            if run_id is not None:
                query += " AND run_id = %s"
                params.append(run_id)

            query += " ORDER BY confidence DESC"

            cursor.execute(query, params)

            results = []
            for row in cursor.fetchall():
                results.append(PredictiveAnalysisModel(
                    id=row[0],
                    run_id=row[1],
                    prediction_id=row[2],
                    virtual_entity_id=row[3],
                    year=row[4],
                    summary=row[5],
                    detailed_analysis=row[6],
                    potential_risks=row[7],
                    potential_opportunities=row[8],
                    confidence=row[9],
                    created_at=row[10]
                ))

            return results
        except Exception as e:
            logger.exception(f"Error listing predictive analyses: {str(e)}")
            raise

    @staticmethod
    def list_by_run_id(
        cursor: Cursor,
        run_id: int
    ) -> List[PredictiveAnalysisModel]:
        """
        List predictive analyses for a specific run.

        Args:
            cursor: Database cursor
            run_id: Run ID

        Returns:
            List of predictive analysis models
        """
        try:
            cursor.execute("""
                SELECT id, run_id, prediction_id, virtual_entity_id, year, summary,
                       detailed_analysis, potential_risks, potential_opportunities,
                       confidence, created_at
                FROM ana_predict_analysis
                WHERE run_id = %s
                ORDER BY year, confidence DESC
            """, (run_id,))

            results = []
            for row in cursor.fetchall():
                results.append(PredictiveAnalysisModel(
                    id=row[0],
                    run_id=row[1],
                    prediction_id=row[2],
                    virtual_entity_id=row[3],
                    year=row[4],
                    summary=row[5],
                    detailed_analysis=row[6],
                    potential_risks=row[7],
                    potential_opportunities=row[8],
                    confidence=row[9],
                    created_at=row[10]
                ))

            return results
        except Exception as e:
            logger.exception(f"Error listing predictive analyses by run ID: {str(e)}")
            raise

    @staticmethod
    def sync_to_xfer(cursor: Cursor, virtual_entity_id: int) -> int:
        """
        Sync predictive analyses to the transfer table.
        Only syncs analyses from the latest run.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID

        Returns:
            Number of records synced
        """
        try:
            # First, delete existing records for this entity
            cursor.execute("""
                DELETE FROM xfer_predictive_analysis
                WHERE virtual_entity_id = %s
            """, (virtual_entity_id,))

            # Get the latest run_id for this entity
            cursor.execute("""
                SELECT MAX(run_id) FROM ana_predict_analysis
                WHERE virtual_entity_id = %s
            """, (virtual_entity_id,))

            result = cursor.fetchone()
            if not result or result[0] is None:
                logger.warning(f"No runs found for entity {virtual_entity_id}, nothing to sync")
                return 0

            latest_run_id = result[0]

            logger.info(f"Syncing analyses from latest run {latest_run_id} for entity {virtual_entity_id}")

            # Then, insert new records from the latest run only
            cursor.execute("""
                INSERT INTO xfer_predictive_analysis
                (virtual_entity_id, year, summary, detailed_analysis,
                 potential_risks, potential_opportunities, confidence, created_at,
                 prediction_id, source_cluster_id)
                SELECT
                    pa.virtual_entity_id,
                    pa.year,
                    pa.summary,
                    pa.detailed_analysis,
                    to_jsonb(pa.potential_risks),
                    to_jsonb(pa.potential_opportunities),
                    pa.confidence,
                    NOW(),
                    pa.prediction_id,
                    cp.source_cluster_id
                FROM ana_predict_analysis pa
                JOIN ana_predict_cluster_predictions cp ON pa.prediction_id = cp.id
                WHERE pa.virtual_entity_id = %s AND pa.run_id = %s
                ORDER BY pa.year, pa.confidence DESC
                RETURNING id
            """, (virtual_entity_id, latest_run_id))

            synced_ids = cursor.fetchall()
            logger.info(f"Synced {len(synced_ids)} analyses from run {latest_run_id} to xfer_predictive_analysis")
            return len(synced_ids)
        except Exception as e:
            logger.exception(f"Error syncing predictive analyses to xfer: {str(e)}")
            raise
