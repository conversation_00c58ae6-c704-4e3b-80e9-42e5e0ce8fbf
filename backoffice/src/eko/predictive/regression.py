"""
Regression models for predicting future DEMISE clusters.

This module provides various regression models for predicting the future
positions of DEMISE clusters based on historical data.
"""

import os
import datetime
from abc import ABC, abstractmethod
from typing import List, Dict, Tuple, Optional, Any, Union

import numpy as np
import pandas as pd
from loguru import logger
from psycopg import Connection, Cursor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, ConstantKernel as C
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import StandardScaler

from eko import eko_var_path
from eko.analysis_v2.pipeline_tracker_extended import TraceabilityTracker
from eko.db import get_bo_conn
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.derived.enums import PipelineStage
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.predictive.dao import TemporalClusterDAO, ClusterPredictionDAO
from eko.predictive.models import (
    TemporalClusterModel,
    ClusterPredictionModel,
    RegressionModelType
)
from eko.predictive.temporal_clustering import calculate_demise_centroid
from eko.settings import settings


class BaseRegressionModel(ABC):
    """Base class for regression models."""

    def __init__(self, name: str):
        """
        Initialize the regression model.

        Args:
            name: Model name
        """
        self.name = name
        self.model_type = None

    @abstractmethod
    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the model to the data.

        Args:
            X: Input features (years)
            y: Target values (centroids)
        """
        pass

    @abstractmethod
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions with the model.

        Args:
            X: Input features (years)

        Returns:
            Predicted values (centroids)
        """
        pass

    @abstractmethod
    def get_confidence(self, X: np.ndarray) -> float:
        """
        Get confidence score for predictions.

        Args:
            X: Input features (years)

        Returns:
            Confidence score (0-1)
        """
        pass


class VARModel(BaseRegressionModel):
    """Vector Autoregression model for multivariate time series prediction."""

    def __init__(self):
        """Initialize the VAR model."""
        super().__init__("Vector Autoregression")
        self.model_type = RegressionModelType.VAR
        self.model = None
        self.scaler = StandardScaler()

    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the VAR model to the data.

        Args:
            X: Input features (years)
            y: Target values (centroids)
        """
        try:
            from statsmodels.tsa.vector_ar.var_model import VAR

            # Prepare data for VAR
            # VAR requires a 2D array with shape (n_samples, n_features)
            # where each row is a time point and columns are variables

            # Scale the data
            y_scaled = self.scaler.fit_transform(y)

            # Create a dataset with years and centroids
            data = np.column_stack([X.reshape(-1, 1), y_scaled])

            # Fit the VAR model
            self.model = VAR(data)
            self.results = self.model.fit(maxlags=min(2, len(X) - 1))

            logger.info(f"Fitted VAR model with {len(X)} observations")
        except Exception as e:
            logger.exception(f"Error fitting VAR model: {str(e)}")
            raise

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions with the VAR model.

        Args:
            X: Input features (years)

        Returns:
            Predicted values (centroids)
        """
        if self.model is None:
            raise ValueError("Model not fitted")

        try:
            # Get the last observation from the training data
            last_obs = self.model.endog[-1:]

            # Number of steps to forecast
            steps = int(X[-1] - X[0]) + 1

            # Make forecast
            forecast = self.results.forecast(last_obs, steps)

            # Extract the centroid predictions (all columns except the first one)
            predictions = forecast[:, 1:]

            # Inverse transform to get original scale
            predictions = self.scaler.inverse_transform(predictions)

            return predictions
        except Exception as e:
            logger.exception(f"Error predicting with VAR model: {str(e)}")
            raise

    def get_confidence(self, X: np.ndarray) -> float:
        """
        Get confidence score for VAR predictions.

        Args:
            X: Input features (years)

        Returns:
            Confidence score (0-1)
        """
        if self.model is None:
            return 0.0

        try:
            # Use the model's information criteria as a proxy for confidence
            # Lower AIC/BIC is better, so we invert and normalize
            aic = self.results.aic
            bic = self.results.bic

            # Normalize to 0-1 range (heuristic)
            # Higher values indicate better fit
            confidence = 1.0 / (1.0 + np.exp((aic + bic) / 1000.0))

            return min(max(confidence, 0.0), 1.0)
        except Exception as e:
            logger.exception(f"Error calculating confidence for VAR model: {str(e)}")
            return 0.5  # Default to medium confidence


class GaussianProcessModel(BaseRegressionModel):
    """Gaussian Process Regression model for uncertainty quantification."""

    def __init__(self):
        """Initialize the Gaussian Process model."""
        super().__init__("Gaussian Process Regression")
        self.model_type = RegressionModelType.GAUSSIAN_PROCESS
        self.models = []
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()

    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the Gaussian Process model to the data.

        Args:
            X: Input features (years)
            y: Target values (centroids)
        """
        try:
            # Scale the inputs
            X_scaled = self.scaler_X.fit_transform(X.reshape(-1, 1))
            y_scaled = self.scaler_y.fit_transform(y)

            # Create a separate GP model for each dimension of the centroid
            self.models = []
            for i in range(y.shape[1]):
                # Define the kernel
                kernel = C(1.0, (1e-3, 1e3)) * RBF(1.0, (1e-2, 1e2))

                # Create and fit the model
                gp = GaussianProcessRegressor(
                    kernel=kernel,
                    n_restarts_optimizer=10,
                    alpha=1e-6,
                    normalize_y=True,
                    random_state=42
                )
                gp.fit(X_scaled, y_scaled[:, i])
                self.models.append(gp)

            logger.info(f"Fitted Gaussian Process model with {len(X)} observations and {len(self.models)} dimensions")
        except Exception as e:
            logger.exception(f"Error fitting Gaussian Process model: {str(e)}")
            raise

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions with the Gaussian Process model.

        Args:
            X: Input features (years)

        Returns:
            Predicted values (centroids)
        """
        if not self.models:
            raise ValueError("Model not fitted")

        try:
            # Scale the inputs
            X_scaled = self.scaler_X.transform(X.reshape(-1, 1))

            # Make predictions for each dimension
            predictions = np.zeros((len(X), len(self.models)))
            self.uncertainties = np.zeros((len(X), len(self.models)))

            for i, gp in enumerate(self.models):
                y_pred, sigma = gp.predict(X_scaled, return_std=True)
                predictions[:, i] = y_pred
                self.uncertainties[:, i] = sigma

            # Inverse transform to get original scale
            predictions = self.scaler_y.inverse_transform(predictions)

            return predictions
        except Exception as e:
            logger.exception(f"Error predicting with Gaussian Process model: {str(e)}")
            raise

    def get_confidence(self, X: np.ndarray) -> float:
        """
        Get confidence score for Gaussian Process predictions.

        Args:
            X: Input features (years)

        Returns:
            Confidence score (0-1)
        """
        if not hasattr(self, 'uncertainties') or self.uncertainties is None:
            return 0.5

        try:
            # Use the inverse of the average uncertainty as confidence
            avg_uncertainty = np.mean(self.uncertainties)

            # Convert to confidence score (0-1)
            # Lower uncertainty means higher confidence
            confidence = 1.0 / (1.0 + avg_uncertainty)

            return min(max(confidence, 0.0), 1.0)
        except Exception as e:
            logger.exception(f"Error calculating confidence for Gaussian Process model: {str(e)}")
            return 0.5


class ProphetModel(BaseRegressionModel):
    """Prophet model for trend and seasonality decomposition."""

    def __init__(self):
        """Initialize the Prophet model."""
        super().__init__("Prophet")
        self.model_type = RegressionModelType.PROPHET
        self.models = []
        self.scaler = StandardScaler()

    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the Prophet model to the data.

        Args:
            X: Input features (years)
            y: Target values (centroids)
        """
        try:
            # Check if Prophet is available
            try:
                from prophet import Prophet
            except ImportError:
                logger.warning("Prophet not installed. Using fallback model.")
                # Use Gaussian Process as fallback
                self.fallback = GaussianProcessModel()
                self.fallback.fit(X, y)
                return

            # Scale the data
            y_scaled = self.scaler.fit_transform(y)

            # Create a separate Prophet model for each dimension
            self.models = []
            for i in range(y.shape[1]):
                # Prepare data for Prophet
                df = pd.DataFrame({
                    'ds': pd.to_datetime([f"{int(year)}-01-01" for year in X]),
                    'y': y_scaled[:, i]
                })

                # Create and fit the model
                model = Prophet(
                    yearly_seasonality=False,
                    weekly_seasonality=False,
                    daily_seasonality=False,
                    interval_width=0.95
                )
                model.fit(df)
                self.models.append(model)

            logger.info(f"Fitted Prophet model with {len(X)} observations and {len(self.models)} dimensions")
        except Exception as e:
            logger.exception(f"Error fitting Prophet model: {str(e)}")
            # Use Gaussian Process as fallback
            self.fallback = GaussianProcessModel()
            self.fallback.fit(X, y)

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions with the Prophet model.

        Args:
            X: Input features (years)

        Returns:
            Predicted values (centroids)
        """
        if hasattr(self, 'fallback'):
            return self.fallback.predict(X)

        if not self.models:
            raise ValueError("Model not fitted")

        try:
            from prophet import Prophet
            import pandas as pd

            # Prepare future dataframe for Prophet
            future = pd.DataFrame({
                'ds': pd.to_datetime([f"{int(year)}-01-01" for year in X])
            })

            # Make predictions for each dimension
            predictions = np.zeros((len(X), len(self.models)))
            self.uncertainties = np.zeros((len(X), len(self.models)))

            for i, model in enumerate(self.models):
                forecast = model.predict(future)
                predictions[:, i] = forecast['yhat'].values
                self.uncertainties[:, i] = (
                    forecast['yhat_upper'].values - forecast['yhat_lower'].values
                ) / 3.92  # Convert 95% interval to standard deviation

            # Inverse transform to get original scale
            predictions = self.scaler.inverse_transform(predictions)

            return predictions
        except Exception as e:
            logger.exception(f"Error predicting with Prophet model: {str(e)}")
            if hasattr(self, 'fallback'):
                return self.fallback.predict(X)
            raise

    def get_confidence(self, X: np.ndarray) -> float:
        """
        Get confidence score for Prophet predictions.

        Args:
            X: Input features (years)

        Returns:
            Confidence score (0-1)
        """
        if hasattr(self, 'fallback'):
            return self.fallback.get_confidence(X)

        if not hasattr(self, 'uncertainties') or self.uncertainties is None:
            return 0.5

        try:
            # Use the inverse of the average uncertainty as confidence
            avg_uncertainty = np.mean(self.uncertainties)

            # Convert to confidence score (0-1)
            # Lower uncertainty means higher confidence
            confidence = 1.0 / (1.0 + avg_uncertainty)

            return min(max(confidence, 0.0), 1.0)
        except Exception as e:
            logger.exception(f"Error calculating confidence for Prophet model: {str(e)}")
            return 0.5


class EnsembleModel(BaseRegressionModel):
    """Ensemble model combining multiple regression models."""

    def __init__(self):
        """Initialize the Ensemble model."""
        super().__init__("Ensemble")
        self.model_type = RegressionModelType.ENSEMBLE
        self.models = []
        self.weights = []

    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the Ensemble model to the data.

        Args:
            X: Input features (years)
            y: Target values (centroids)
        """
        try:
            # Create and fit individual models
            self.models = [
                VARModel(),
                GaussianProcessModel(),
                ProphetModel()
            ]

            # Split data for training and validation
            n = len(X)
            train_size = max(3, int(0.7 * n))

            X_train, X_val = X[:train_size], X[train_size:]
            y_train, y_val = y[:train_size], y[train_size:]

            # Fit each model on the training data
            for model in self.models:
                model.fit(X_train, y_train)

            # Evaluate models on validation data if available
            if len(X_val) > 0:
                self.weights = []
                for model in self.models:
                    y_pred = model.predict(X_val)
                    mse = mean_squared_error(y_val, y_pred)
                    # Convert MSE to weight (lower MSE = higher weight)
                    weight = 1.0 / (1.0 + mse)
                    self.weights.append(weight)

                # Normalize weights
                total = sum(self.weights)
                if total > 0:
                    self.weights = [w / total for w in self.weights]
                else:
                    # Equal weights if all models perform poorly
                    self.weights = [1.0 / len(self.models)] * len(self.models)
            else:
                # Equal weights if no validation data
                self.weights = [1.0 / len(self.models)] * len(self.models)

            # Refit models on all data
            for model in self.models:
                model.fit(X, y)

            logger.info(f"Fitted Ensemble model with {len(self.models)} base models")
        except Exception as e:
            logger.exception(f"Error fitting Ensemble model: {str(e)}")
            raise

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions with the Ensemble model.

        Args:
            X: Input features (years)

        Returns:
            Predicted values (centroids)
        """
        if not self.models:
            raise ValueError("Model not fitted")

        try:
            # Get predictions from each model
            all_predictions = []
            for model in self.models:
                try:
                    pred = model.predict(X)
                    all_predictions.append(pred)
                except Exception as e:
                    logger.warning(f"Error in model {model.name}: {str(e)}")

            if not all_predictions:
                raise ValueError("All models failed to predict")

            # Weighted average of predictions
            weighted_pred = np.zeros_like(all_predictions[0])
            for i, pred in enumerate(all_predictions):
                weighted_pred += self.weights[i] * pred

            return weighted_pred
        except Exception as e:
            logger.exception(f"Error predicting with Ensemble model: {str(e)}")
            raise

    def get_confidence(self, X: np.ndarray) -> float:
        """
        Get confidence score for Ensemble predictions.

        Args:
            X: Input features (years)

        Returns:
            Confidence score (0-1)
        """
        if not self.models:
            return 0.0

        try:
            # Weighted average of individual model confidences
            confidences = []
            for model in self.models:
                try:
                    conf = model.get_confidence(X)
                    confidences.append(conf)
                except Exception as e:
                    logger.warning(f"Error getting confidence from {model.name}: {str(e)}")
                    confidences.append(0.5)  # Default confidence

            if not confidences:
                return 0.5

            # Weighted average
            weighted_conf = sum(w * c for w, c in zip(self.weights, confidences))

            return min(max(weighted_conf, 0.0), 1.0)
        except Exception as e:
            logger.exception(f"Error calculating confidence for Ensemble model: {str(e)}")
            return 0.5


def get_regression_model(model_type: RegressionModelType) -> BaseRegressionModel:
    """
    Get a regression model instance by type.

    Args:
        model_type: Type of regression model

    Returns:
        Regression model instance
    """
    models = {
        RegressionModelType.VAR: VARModel,
        RegressionModelType.GAUSSIAN_PROCESS: GaussianProcessModel,
        RegressionModelType.PROPHET: ProphetModel,
        RegressionModelType.ENSEMBLE: EnsembleModel,
        # LSTM model would be added here
    }

    if model_type not in models:
        raise ValueError(f"Unknown model type: {model_type}")

    return models[model_type]()


def predict_future_clusters(
    virtual_entity: VirtualEntityExpandedModel,
    historical_years: List[int],
    future_years: List[int],
    run_id: int,
    model_type: RegressionModelType = RegressionModelType.ENSEMBLE,
    tracker: Optional[TraceabilityTracker] = None
) -> List[ClusterPredictionModel]:
    """
    Predict future clusters for a virtual entity.

    Args:
        virtual_entity: Virtual entity model
        historical_years: List of historical years
        future_years: List of future years to predict
        run_id: Run ID to associate with predictions
        model_type: Type of regression model to use
        tracker: Optional traceability tracker

    Returns:
        List of predicted cluster models
    """
    if tracker:
        # Check if the tracker has the start_stage method
        if hasattr(tracker, 'start_stage'):
            tracker.start_stage(PipelineStage.PREDICTIVE_REGRESSION)

    logger.info(f"Predicting future clusters for {virtual_entity.name} "
                f"(historical: {historical_years}, future: {future_years})")

    predictions = []

    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            # Get historical clusters
            historical_clusters = []
            for year in historical_years:
                clusters = TemporalClusterDAO.list_by_entity_and_year_range(
                    cursor, virtual_entity.id, year, year
                )
                historical_clusters.extend(clusters)

            if not historical_clusters:
                logger.warning(f"No historical clusters found for {virtual_entity.name}")
                if tracker:
                    # Check if the tracker has the end_stage method
                    if hasattr(tracker, 'end_stage'):
                        tracker.end_stage(PipelineStage.PREDICTIVE_REGRESSION, {
                            "predictions_created": 0,
                            "error": "No historical clusters found"
                        })
                return []

            # Group clusters by similarity to track their evolution
            cluster_groups = group_clusters_by_similarity(historical_clusters)

            # Calculate overall DEMISE prediction for all statements
            # This will be used in the LLM analysis to provide a holistic view
            for future_year in future_years:
                try:
                    # Group historical clusters by year for overall prediction
                    historical_clusters_by_year = {}
                    for cluster in historical_clusters:
                        if cluster.year not in historical_clusters_by_year:
                            historical_clusters_by_year[cluster.year] = []
                        historical_clusters_by_year[cluster.year].append(cluster)

                    # Convert to list of lists for calculate_overall_demise_prediction
                    historical_clusters_list = [clusters for year, clusters in sorted(historical_clusters_by_year.items())]

                    # Get the overall prediction vector
                    overall_demise = calculate_overall_demise_prediction(
                        historical_clusters_list,
                        future_year
                    )
                    if overall_demise is None:
                        logger.warning(f"Failed to calculate overall prediction for year {future_year}")
                        continue

                    # Create a special prediction model for the overall prediction
                    # We need to use a valid source_cluster_id that exists in ana_predict_temporal_clusters
                    # Find the most recent cluster to use as the source

                    # Get the final historical year and vector
                    final_historical_year = max(historical_years) if historical_years else datetime.datetime.now().year

                    # Get the historical vector for the final year and find a valid source cluster
                    final_historical_vectors = []
                    valid_source_cluster_id = None

                    for clusters in historical_clusters_list:
                        for cluster in clusters:
                            if cluster.year == final_historical_year:
                                final_historical_vectors.append(np.array(cluster.centroid))
                                # Use the first valid cluster ID we find
                                if valid_source_cluster_id is None and cluster.id is not None:
                                    valid_source_cluster_id = cluster.id

                    # If we couldn't find a valid source cluster, use the first cluster with a valid ID
                    if valid_source_cluster_id is None:
                        for cluster in historical_clusters:
                            if cluster.id is not None:
                                valid_source_cluster_id = cluster.id
                                break

                    # If we still don't have a valid ID, log an error and skip this prediction
                    if valid_source_cluster_id is None:
                        logger.error(f"Could not find a valid source cluster ID for overall prediction for year {future_year}")
                        continue

                    # Calculate the average historical vector
                    if final_historical_vectors:
                        historical_vector = np.mean(final_historical_vectors, axis=0).tolist()
                    else:
                        historical_vector = []

                    overall_prediction = ClusterPredictionModel(
                        run_id=run_id,
                        virtual_entity_id=virtual_entity.id,
                        source_cluster_id=valid_source_cluster_id,  # Use a valid cluster ID instead of 0
                        predicted_year=future_year,
                        final_historical_year=final_historical_year,
                        predicted_vector=overall_demise.tolist(),  # Convert numpy array to list
                        historical_vector=historical_vector,
                        confidence=0.8,  # Default confidence for overall prediction
                        model_type=model_type
                    )

                    # Save to database
                    saved_overall = ClusterPredictionDAO.create(cursor, overall_prediction)
                    predictions.append(saved_overall)

                    logger.info(f"Created overall prediction for year {future_year}")
                except Exception as e:
                    logger.exception(f"Error creating overall prediction for year {future_year}: {str(e)}")
                    raise

            # For each cluster group, predict future clusters
            for group_id, group_clusters in cluster_groups.items():
                # Sort clusters by year
                group_clusters.sort(key=lambda c: c.year)

                # Extract years and centroids
                years = np.array([c.year for c in group_clusters])
                centroids = np.array([c.centroid for c in group_clusters])

                # Skip groups with too few clusters
                if len(years) < settings.predictive_minimum_regression_datapoints:
                    logger.info(f"Skipping group {group_id} with only {len(years)} clusters")
                    continue

                # Create and fit regression model
                model = get_regression_model(model_type)
                model.fit(years, centroids)

                # Predict future centroids
                future_years_array = np.array(future_years)
                predicted_centroids = model.predict(future_years_array)

                # Get confidence score
                confidence = model.get_confidence(future_years_array)

                # Create prediction models
                for i, year in enumerate(future_years):
                    # Get the source cluster (most recent in the group)
                    source_cluster = group_clusters[-1]

                    # Create a predicted vector
                    predicted_demise = predict_demise_model(
                        [np.array(c.centroid) for c in group_clusters],
                        [c.year for c in group_clusters],
                        year
                    )
                    if predicted_demise is None:
                        logger.warning(f"Failed to predict DEMISE model for year {year}")
                        continue

                    # Create prediction model
                    # Ensure source_cluster_id is not None and is a valid ID
                    if source_cluster.id is None:
                        logger.error(f"Source cluster has no ID, skipping prediction for year {year}")
                        continue
                    source_id = source_cluster.id

                    # Get the final historical year and vector
                    final_historical_year = group_clusters[-1].year
                    historical_vector = group_clusters[-1].centroid

                    prediction = ClusterPredictionModel(
                        run_id=run_id,
                        virtual_entity_id=virtual_entity.id,
                        source_cluster_id=source_id,
                        predicted_year=year,
                        final_historical_year=final_historical_year,
                        predicted_vector=predicted_demise.tolist(),  # Convert numpy array to list
                        historical_vector=historical_vector,
                        confidence=confidence,
                        model_type=model_type  # Use the model_type passed to the function
                    )

                    # Save to database
                    saved_prediction = ClusterPredictionDAO.create(cursor, prediction)
                    predictions.append(saved_prediction)

                    logger.info(f"Created prediction for year {year} with confidence {confidence:.2f}")

            conn.commit()

    if tracker:
        # Check if the tracker has the end_stage method
        if hasattr(tracker, 'end_stage'):
            tracker.end_stage(PipelineStage.PREDICTIVE_REGRESSION, {
                "predictions_created": len(predictions),
                "model_type": model_type.value
            })

    return predictions


def group_clusters_by_similarity(
    clusters: List[TemporalClusterModel]
) -> Dict[int, List[TemporalClusterModel]]:
    """
    Group clusters by similarity to track their evolution over time.

    Args:
        clusters: List of temporal cluster models

    Returns:
        Dictionary mapping group IDs to lists of clusters
    """
    # Group clusters by year first
    clusters_by_year = {}
    for cluster in clusters:
        if cluster.year not in clusters_by_year:
            clusters_by_year[cluster.year] = []
        clusters_by_year[cluster.year].append(cluster)

    # Sort years
    years = sorted(clusters_by_year.keys())
    if not years:
        return {}

    # Initialize groups with clusters from the first year
    groups = {i: [cluster] for i, cluster in enumerate(clusters_by_year[years[0]])}

    # For each subsequent year, assign clusters to the most similar group
    for year in years[1:]:
        year_clusters = clusters_by_year[year]

        for cluster in year_clusters:
            best_group = None
            best_similarity = float('inf')

            # Find the most similar group
            for group_id, group_clusters in groups.items():
                # Get the most recent cluster in the group
                latest_cluster = max(group_clusters, key=lambda c: c.year)

                # Skip if the latest cluster is from the current year
                if latest_cluster.year >= year:
                    continue

                # Calculate similarity (cosine distance)
                similarity = cosine_distance(
                    np.array(latest_cluster.centroid),
                    np.array(cluster.centroid)
                )

                if similarity < best_similarity:
                    best_similarity = similarity
                    best_group = group_id

            # If a similar group was found, add the cluster to it
            if best_group is not None and best_similarity < 0.5:  # Increased threshold for similarity
                groups[best_group].append(cluster)
            else:
                # Create a new group
                new_group_id = max(groups.keys()) + 1 if groups else 0
                groups[new_group_id] = [cluster]

    return groups


def cosine_distance(a: np.ndarray, b: np.ndarray) -> float:
    """
    Calculate cosine distance between two vectors.

    Args:
        a: First vector
        b: Second vector

    Returns:
        Cosine distance (0-2, where 0 is identical)
    """
    return 1.0 - np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))


def predict_demise_model(
    historical_vectors: List[np.ndarray],
    historical_years: List[int],
    target_year: int
) -> Optional[np.ndarray]:
    """
    Predict a DEMISE model for a future year based on historical vectors.
    Works purely with numpy arrays throughout the entire process.

    Args:
        historical_vectors: List of historical vectors (numpy arrays)
        historical_years: List of years corresponding to the vectors
        target_year: Year to predict

    Returns:
        Tuple of (Predicted DEMISE model, Dictionary of deltas and final values)
    """
    if not historical_vectors:
        error_msg = "ERROR: No historical vectors provided to predict_demise_model"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # Convert list of vectors to a 2D numpy array for consistent processing
    historical_vectors_array = np.array(historical_vectors)

    # Verify that we have valid vectors with non-zero elements
    if historical_vectors_array.size == 0 or np.all(historical_vectors_array == 0):
        error_msg = f"ERROR: Historical vectors are empty or all zeros"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # Verify that all vectors have the same size
    if len(historical_vectors_array.shape) < 2:
        error_msg = f"ERROR: Historical vectors array has invalid shape: {historical_vectors_array.shape}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # Convert years to numpy array
    years_array = np.array(historical_years)

    # Initialize the predicted vector with zeros
    vector_size = historical_vectors_array.shape[1]
    predicted_vector = np.zeros(vector_size)

    # For each dimension in the vector, perform linear regression
    for i in range(vector_size):
        # Extract historical values for this dimension
        values = historical_vectors_array[:, i]
        predicted_value = 0.0
        # Simple linear regression to predict future value
        if len(values) >= 2:
            try:
                # Fit linear regression
                # Use real values only to avoid complex number issues
                real_values = np.real(values)
                coeffs = np.polyfit(years_array, real_values, 1)
                slope, intercept = coeffs

                # Predict value for target year
                predicted_value = float(slope * target_year + intercept)

                # Ensure value is within bounds (0-1 for most fields)
                predicted_value = min(max(predicted_value, 0.0), 1.0)
            except Exception as e:
                logger.warning(f"Error in linear regression: {str(e)}")
                # Fallback to most recent value
                predicted_value = float(values[-1])
        else:
            # Not enough data for regression, use the most recent value
            predicted_value = float(values[-1])

        # Store the predicted value in the vector
        predicted_vector[i] = predicted_value

    # Check if the predicted vector has non-zero values
    if np.count_nonzero(predicted_vector) < 5:  # Require at least 2 non-zero values
        error_msg = f"ERROR: Predicted vector has too few non-zero values: {np.count_nonzero(predicted_vector)}"
        logger.warning(error_msg)

        # Log the historical data to help diagnose the issue
        logger.warning(f"Historical years: {historical_years}")
        logger.warning(f"Historical vectors non-zero counts: {[np.count_nonzero(v) for v in historical_vectors]}")
        logger.warning(f"Target year: {target_year}")
        return None



    # No need to convert to DEMISE model, just use the vector directly

    # We no longer need to calculate deltas for LLM analysis
    # Just return the predicted vector
    return predicted_vector





def calculate_overall_demise_prediction(
    historical_clusters: List[List[TemporalClusterModel]],
    target_year: int
) -> Optional[np.ndarray]:
    """
    Calculate an overall DEMISE prediction based on all statements.

    Args:
        historical_clusters: List of lists of historical clusters by year
        target_year: Year to predict

    Returns:
        Tuple of (Predicted DEMISE model, Dictionary of deltas and final values)
    """
    # Flatten and average DEMISE models by year
    averaged_models = []
    historical_years = []

    for year_clusters in historical_clusters:
        if not year_clusters:
            continue

        # Get the year
        year = year_clusters[0].year
        historical_years.append(year)

        # Extract DEMISE models as vectors
        demise_vectors = np.array([cluster.centroid for cluster in year_clusters])

        # Calculate average vector for this year
        averaged_vector = np.mean(demise_vectors, axis=0)

        # Store the vector directly - no need to convert back to DEMISE model yet
        averaged_models.append(averaged_vector)

    # Now predict using the averaged models
    return predict_demise_model(averaged_models, historical_years, target_year)
