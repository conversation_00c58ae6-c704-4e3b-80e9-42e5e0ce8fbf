# Predictive Analytics for DEMISE Model

This package implements predictive analytics capabilities for the DEMISE model, enabling forecasting of future entity behavior based on historical statement patterns.

## Overview

The predictive analytics system consists of three main components:

1. **Temporal Clustering**: Clusters statements by year to identify patterns in entity behavior over time
2. **Multivariate Regression**: Predicts future cluster positions based on historical data
3. **Human-Readable Analysis**: Converts predicted clusters into meaningful insights

## Package Structure

```
eko/predictive/
├── __init__.py        # Package exports
├── PLAN.md            # Detailed implementation plan
├── README.md          # This file
├── models.py          # Pydantic models
├── dao.py             # Data Access Objects
├── temporal_clustering.py  # Temporal clustering functionality
├── regression.py      # Regression models for prediction
├── analysis.py        # Human-readable analysis generation
├── utils.py           # Utility functions and report generation
└── cli.py             # CLI commands
```

## Features

### Temporal Clustering

- Groups statements by year for each virtual entity
- Applies clustering to identify patterns in each year (minimum cluster size: 20)
- Calculates cluster centroids and DEMISE model centroids
- Stores results in the `ana_predict_temporal_clusters` table

### Multivariate Regression

- Implements multiple regression models:
  - Vector Autoregression (VAR)
  - Gaussian Process Regression
  - Prophet (time series forecasting)
  - Ensemble (combining multiple models)
- Groups clusters by similarity (similarity threshold: 0.5)
- Requires at least 3 clusters in a group across different years to establish a trend
- Predicts future cluster positions and DEMISE values
- Evaluates model performance and selects the best model
- Stores predictions in the `ana_predict_cluster_predictions` table

### Human-Readable Analysis

- Uses LLMs to convert predicted DEMISE vectors into insights
- Generates summaries, detailed analysis, risks, and opportunities
- Creates HTML reports with visualizations
- Stores analysis in the `ana_predict_analysis` table
- Syncs to `xfer_predictive_analysis` for customer app access

## CLI Commands

The package adds the following CLI commands:

```bash
# Run the full predictive analytics pipeline
python cli.py predictive analyze-predictive-trends --entity "EntityShortId" --start-year 2015 --end-year 2025 --future-years 3 --report

# Create temporal clusters only
python cli.py predictive create-temporal-clusters --entity "EntityShortId" --start-year 2015 --end-year 2025

# Predict future clusters
python cli.py predictive predict-future-clusters --entity "EntityShortId" --start-year 2015 --end-year 2025 --future-years 3 --model-type ensemble

# Generate human-readable analysis
python cli.py predictive generate-predictive-analysis --entity "EntityShortId" --future-years 3 --report
```

Note: For best results, use a wider date range (e.g., 2015-2025) to ensure enough historical data for prediction.

## Database Schema

The package uses the following database tables:

1. `ana_predict_temporal_clusters`: Stores temporal clusters by year
2. `ana_predict_cluster_predictions`: Stores predicted future clusters
3. `ana_predict_analysis`: Stores human-readable analysis
4. `xfer_predictive_analysis`: Transfer table for customer app

All tables include a `run_id` field to distinguish between different analysis runs.

## Run Management

The package creates a new run type 'predictive' in the `ana_runs` table to distinguish predictive analytics runs from other types of runs. Each run includes:

- Start and end years for historical data
- Target entity
- Run ID used to link all related data

## Pipeline Tracking

The package integrates with the existing pipeline tracking system, adding new pipeline stages:

- `PREDICTIVE_ANALYTICS`: Overall predictive analytics process
- `TEMPORAL_CLUSTERING`: Temporal clustering stage
- `PREDICTIVE_REGRESSION`: Regression modeling stage
- `PREDICTIVE_ANALYSIS`: Human-readable analysis stage

## Usage Example

```python
from eko.predictive import (
    create_temporal_clusters,
    predict_future_clusters,
    generate_predictive_analysis,
    generate_predictive_report,
    RegressionModelType
)
from eko.entities.queries import get_entity_by_id
from eko.db import get_bo_conn
from eko.db.data.run import RunData

# Get entity
entity = get_entity_by_id("EntityShortId")

# Create a run for this analysis
with get_bo_conn() as conn:
    run = RunData.create_predictive_run(
        conn=conn,
        start_year=2015,
        end_year=2025,
        target=entity.short_id
    )
    run_id = run.id

# Create temporal clusters
clusters = create_temporal_clusters(
    entity,
    2015,
    2025,
    run_id=run_id
)

# Predict future clusters
predictions = predict_future_clusters(
    entity,
    list(range(2015, 2026)),
    list(range(2026, 2029)),
    RegressionModelType.ENSEMBLE
)

# Generate analysis
analyses = generate_predictive_analysis(entity, predictions)

# Generate report
report_file = generate_predictive_report(
    entity,
    analyses,
    predictions,
    "path/to/report.html"
)
```

## Implementation Status

- [x] Package structure and models
- [x] Database schema design
- [x] Temporal clustering implementation (min cluster size: 20)
- [x] Regression models implementation (similarity threshold: 0.5)
- [x] Human-readable analysis implementation
- [x] CLI commands
- [x] Pipeline tracking integration
- [x] Report generation
- [x] Run management with run_id tracking
- [ ] Integration with customer app
- [ ] Comprehensive testing with real data
- [ ] Performance optimization

## Future Enhancements

- Add more sophisticated regression models (e.g., LSTM neural networks)
- Implement confidence intervals for predictions
- Add interactive visualizations in reports
- Integrate with the dashboard for real-time monitoring
- Add comparative analysis between entities in the same industry
- Optimize similarity threshold and minimum cluster size based on entity size and data availability
- Implement adaptive regression model selection based on data characteristics
- Add support for filtering out disclosure-only statements from analysis
