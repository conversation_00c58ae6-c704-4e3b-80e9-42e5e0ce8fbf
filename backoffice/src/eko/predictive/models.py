"""
Pydantic models for the predictive analytics system.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any, cast

from pydantic import Field, BaseModel

from eko.models.vector.demise.demise_model import DEMISEModel


class  RegressionModelType(str, Enum):
    """Types of regression models supported for prediction."""
    VAR = "vector_autoregression"
    LSTM = "lstm_neural_network"
    GAUSSIAN_PROCESS = "gaussian_process"
    PROPHET = "prophet"
    ENSEMBLE = "ensemble"


class TemporalClusterModel(BaseModel):
    """Model representing a temporal cluster of statements for a specific year."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    year: int
    centroid: List[float] = Field(default_factory=list)
    statement_ids: List[int] = Field(default_factory=list)
    size: int
    coherence: float
    created_at: Optional[datetime] = None

    @property
    def demise_centroid(self) -> DEMISEModel:
        """
        Get the DEMISE centroid as a DEMISEModel.
        This is a computed property to maintain backward compatibility.

        Returns:
            The DEMISE model created from the vector
        """
        if not self.centroid:
            return cast(DEMISEModel, DEMISEModel.model_construct())
        return cast(DEMISEModel, DEMISEModel.from_vector(self.centroid))

    def model_post_init(self, __context: Any) -> None:
        """
        Validate the model after initialization.
        Ensures that the predicted vector has non-zero values.
        """
        super().model_post_init(__context)

        # Check if predicted_vector is properly initialized with non-zero values
        if not self.centroid or all(v == 0 for v in self.centroid):
            raise ValueError(f"ClusterPredictionModel created with empty vector (no non-zero values)")

    class Config:
        arbitrary_types_allowed = True


class ClusterPredictionModel(BaseModel):
    """Model representing a predicted future cluster."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    source_cluster_id: int
    predicted_year: int
    final_historical_year: int  # The final year of historical data used for prediction
    predicted_vector: List[float] = Field(default_factory=list)
    historical_vector: List[float] = Field(default_factory=list)  # Vector from the final historical year
    confidence: float
    model_type: RegressionModelType
    created_at: Optional[datetime] = None

    @property
    def predicted_centroid(self) -> List[float]:
        """
        Get the predicted centroid.
        This is a computed property to maintain backward compatibility.

        Returns:
            The vector representation
        """
        return self.predicted_vector

    @property
    def predicted_demise(self) -> DEMISEModel:
        """
        Get the predicted DEMISE model.
        This is a computed property to maintain backward compatibility.

        Returns:
            The DEMISE model created from the vector
        """
        if not self.predicted_vector:
            return cast(DEMISEModel, DEMISEModel.model_construct())
        return cast(DEMISEModel, DEMISEModel.from_vector(self.predicted_vector))

    def model_post_init(self, __context: Any) -> None:
        """
        Validate the model after initialization.
        Ensures that the predicted vector has non-zero values.
        """
        super().model_post_init(__context)

        # Check if predicted_vector is properly initialized with non-zero values
        if not self.predicted_vector or all(v == 0 for v in self.predicted_vector):
            raise ValueError(f"ClusterPredictionModel created with empty vector (no non-zero values)")

    class Config:
        arbitrary_types_allowed = True


class PredictiveAnalysisModel(BaseModel):
    """Model representing human-readable analysis of predicted behavior."""
    id: Optional[int] = None
    run_id: int
    prediction_id: int
    virtual_entity_id: int
    year: int
    summary: str
    detailed_analysis: str
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    created_at: Optional[datetime] = None


class XferPredictiveAnalysisModel(BaseModel):
    """Transfer model for exposing predictive analysis to the customer app."""
    id: Optional[int] = None
    virtual_entity_id: int
    year: int
    summary: str
    detailed_analysis: str
    potential_risks: Dict[str, Any] = Field(default_factory=dict)
    potential_opportunities: Dict[str, Any] = Field(default_factory=dict)
    confidence: float
    created_at: Optional[datetime] = None
    prediction_id: Optional[int] = None
    source_cluster_id: Optional[int] = None
