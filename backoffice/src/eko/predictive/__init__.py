"""
Package for predictive analytics based on DEMISE embeddings.

This package provides functionality for temporal clustering, regression modeling,
and human-readable analysis of predicted entity behavior.
"""

from .models import (
    TemporalClusterModel,
    ClusterPredictionModel,
    PredictiveAnalysisModel,
    XferPredictiveAnalysisModel,
    RegressionModelType
)
from .temporal_clustering import (
    create_temporal_clusters,
    get_temporal_clusters,
    calculate_demise_centroid
)
from .regression import (
    predict_future_clusters,
    get_regression_model,
    BaseRegressionModel,
    VARModel,
    GaussianProcessModel,
    ProphetModel,
    EnsembleModel
)
from .analysis import (
    generate_predictive_analysis,
    PredictiveAnalysisResponse
)
from .utils import (
    generate_predictive_report,
    visualize_prediction_trends
)

__all__ = [
    # Models
    "TemporalClusterModel",
    "ClusterPredictionModel",
    "PredictiveAnalysisModel",
    "XferPredictiveAnalysisModel",
    "RegressionModelType",
    "PredictiveAnalysisResponse",

    # Temporal clustering
    "create_temporal_clusters",
    "get_temporal_clusters",
    "calculate_demise_centroid",

    # Regression
    "predict_future_clusters",
    "get_regression_model",
    "BaseRegressionModel",
    "VARModel",
    "GaussianProcessModel",
    "ProphetModel",
    "EnsembleModel",

    # Analysis
    "generate_predictive_analysis",

    # Utilities
    "generate_predictive_report",
    "visualize_prediction_trends"
]
