"""
Utility functions for predictive analytics.
"""

import os
import base64
from datetime import datetime
from io import BytesIO
from typing import List, Optional, Tuple, Dict

import matplotlib.pyplot as plt
import numpy as np
from loguru import logger
from matplotlib.figure import Figure
from sklearn.manifold import TSNE
from matplotlib.colors import hsv_to_rgb

from eko import eko_var_path
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.predictive.models import (
    ClusterPredictionModel,
    PredictiveAnalysisModel,
    TemporalClusterModel
)


def generate_predictive_report(
    virtual_entity: VirtualEntityExpandedModel,
    analyses: List[PredictiveAnalysisModel],
    predictions: List[ClusterPredictionModel],
    output_file: str
) -> str:
    """
    Generate a human-readable HTML report of predictive analysis.

    Args:
        virtual_entity: Virtual entity model
        analyses: List of predictive analysis models
        predictions: List of cluster prediction models
        output_file: Path to save the report

    Returns:
        Path to the generated report
    """
    try:
        # Group analyses by year
        analyses_by_year = {}
        for analysis in analyses:
            if analysis.year not in analyses_by_year:
                analyses_by_year[analysis.year] = []
            analyses_by_year[analysis.year].append(analysis)

        # Sort years
        years = sorted(analyses_by_year.keys())

        # Generate visualizations
        _, demise_chart_base64 = create_demise_dimensions_chart(predictions)
        _, confidence_chart_base64 = create_confidence_chart(predictions)
        _, risks_opps_chart_base64 = create_risks_opportunities_chart(analyses)
        _, tsne_chart_base64 = create_tsne_visualization(predictions)

        # Create HTML content - using string concatenation instead of f-strings with backslashes
        html = []

        # HTML header
        html.append("<!DOCTYPE html>")
        html.append("<html lang=\"en\">")
        html.append("<head>")
        html.append("    <meta charset=\"UTF-8\">")
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">")
        # Get run_id from the first analysis if available
        run_id = analyses[0].run_id if analyses else "Unknown"
        html.append(f"    <title>Predictive Analysis Report - {virtual_entity.name} (Run {run_id})</title>")
        html.append("    <style>")
        html.append("        body {")
        html.append("            font-family: Arial, sans-serif;")
        html.append("            line-height: 1.6;")
        html.append("            color: #333;")
        html.append("            max-width: 1200px;")
        html.append("            margin: 0 auto;")
        html.append("            padding: 20px;")
        html.append("        }")
        html.append("        h1, h2, h3, h4 {")
        html.append("            color: #2c3e50;")
        html.append("        }")
        html.append("        .report-header {")
        html.append("            background-color: #f8f9fa;")
        html.append("            padding: 20px;")
        html.append("            border-radius: 5px;")
        html.append("            margin-bottom: 30px;")
        html.append("            border-left: 5px solid #3498db;")
        html.append("        }")
        html.append("        .year-section {")
        html.append("            margin-bottom: 40px;")
        html.append("            border: 1px solid #ddd;")
        html.append("            border-radius: 5px;")
        html.append("            padding: 20px;")
        html.append("        }")
        html.append("        .analysis-card {")
        html.append("            background-color: #fff;")
        html.append("            border: 1px solid #e0e0e0;")
        html.append("            border-radius: 5px;")
        html.append("            padding: 15px;")
        html.append("            margin-bottom: 20px;")
        html.append("            box-shadow: 0 2px 4px rgba(0,0,0,0.05);")
        html.append("        }")
        html.append("        .analysis-header {")
        html.append("            display: flex;")
        html.append("            justify-content: space-between;")
        html.append("            align-items: center;")
        html.append("            margin-bottom: 15px;")
        html.append("            border-bottom: 1px solid #eee;")
        html.append("            padding-bottom: 10px;")
        html.append("        }")
        html.append("        .confidence {")
        html.append("            display: inline-block;")
        html.append("            padding: 5px 10px;")
        html.append("            border-radius: 15px;")
        html.append("            font-size: 14px;")
        html.append("            font-weight: bold;")
        html.append("        }")
        html.append("        .high-confidence {")
        html.append("            background-color: #d4edda;")
        html.append("            color: #155724;")
        html.append("        }")
        html.append("        .medium-confidence {")
        html.append("            background-color: #fff3cd;")
        html.append("            color: #856404;")
        html.append("        }")
        html.append("        .low-confidence {")
        html.append("            background-color: #f8d7da;")
        html.append("            color: #721c24;")
        html.append("        }")
        html.append("        .risks-opportunities {")
        html.append("            display: flex;")
        html.append("            gap: 20px;")
        html.append("            margin-top: 20px;")
        html.append("        }")
        html.append("        .risks, .opportunities {")
        html.append("            flex: 1;")
        html.append("            padding: 15px;")
        html.append("            border-radius: 5px;")
        html.append("        }")
        html.append("        .risks {")
        html.append("            background-color: #fff5f5;")
        html.append("            border-left: 3px solid #e74c3c;")
        html.append("        }")
        html.append("        .opportunities {")
        html.append("            background-color: #f0fff4;")
        html.append("            border-left: 3px solid #2ecc71;")
        html.append("        }")
        html.append("        ul {")
        html.append("            padding-left: 20px;")
        html.append("        }")
        html.append("        .visualization-section {")
        html.append("            margin-bottom: 40px;")
        html.append("            background-color: #fff;")
        html.append("            border: 1px solid #ddd;")
        html.append("            border-radius: 5px;")
        html.append("            padding: 20px;")
        html.append("        }")
        html.append("        .chart-container {")
        html.append("            margin: 20px 0;")
        html.append("            text-align: center;")
        html.append("        }")
        html.append("        .chart-description {")
        html.append("            font-style: italic;")
        html.append("            color: #666;")
        html.append("            margin-top: 10px;")
        html.append("            text-align: center;")
        html.append("        }")
        html.append("        .footer {")
        html.append("            margin-top: 50px;")
        html.append("            text-align: center;")
        html.append("            font-size: 14px;")
        html.append("            color: #7f8c8d;")
        html.append("            padding-top: 20px;")
        html.append("            border-top: 1px solid #eee;")
        html.append("        }")
        html.append("    </style>")
        html.append("</head>")
        html.append("<body>")
        html.append("    <div class=\"report-header\">")
        html.append("        <h1>Predictive Analysis Report</h1>")
        html.append(f"        <p><strong>Entity:</strong> {virtual_entity.name} ({virtual_entity.short_id})</p>")
        html.append(f"        <p><strong>Entity Type:</strong> {virtual_entity.type}</p>")
        html.append(f"        <p><strong>Run ID:</strong> {run_id}</p>")
        html.append(f"        <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>")
        if years:
            html.append(f"        <p><strong>Years Covered:</strong> {min(years)} to {max(years)}</p>")
        else:
            html.append(f"        <p><strong>Years Covered:</strong> None</p>")
        html.append("    </div>")

        # Add visualizations section
        html.append("    <div class=\"visualization-section\">")
        html.append("        <h2>Predictive Trends Visualization</h2>")
        html.append("        <p>These visualizations show the predicted trends for key dimensions and metrics over time.</p>")

        # Add confidence chart
        html.append("        <div class=\"chart-container\">")
        html.append("            <h3>Prediction Confidence by Year</h3>")
        html.append(f"            <img src=\"data:image/png;base64,{confidence_chart_base64}\" alt=\"Prediction Confidence Chart\" style=\"max-width:100%;\">")
        html.append("            <p class=\"chart-description\">This chart shows the confidence level of predictions for each year. Higher values indicate greater confidence in the prediction.</p>")
        html.append("        </div>")

        # Add risks vs opportunities chart
        html.append("        <div class=\"chart-container\">")
        html.append("            <h3>Risks vs. Opportunities by Year</h3>")
        html.append(f"            <img src=\"data:image/png;base64,{risks_opps_chart_base64}\" alt=\"Risks vs. Opportunities Chart\" style=\"max-width:100%;\">")
        html.append("            <p class=\"chart-description\">This chart compares the number of identified risks (red) and opportunities (green) for each predicted year.</p>")
        html.append("        </div>")

        # Add DEMISE dimensions chart
        html.append("        <div class=\"chart-container\">")
        html.append("            <h3>DEMISE Dimensions Over Time</h3>")
        html.append(f"            <img src=\"data:image/png;base64,{demise_chart_base64}\" alt=\"DEMISE Dimensions Chart\" style=\"max-width:100%;\">")
        html.append("            <p class=\"chart-description\">These charts show how key DEMISE dimensions are predicted to evolve over time. The top chart shows Domain dimensions (Climate, Social, Governance), the middle chart shows Impact dimensions (Positive vs. Negative), and the bottom chart shows Motivation and Engagement dimensions.</p>")
        html.append("        </div>")

        # Add t-SNE visualization
        html.append("        <div class=\"chart-container\">")
        html.append("            <h3>Cluster Movement in t-SNE Space</h3>")
        html.append(f"            <img src=\"data:image/png;base64,{tsne_chart_base64}\" alt=\"t-SNE Visualization\" style=\"max-width:100%;\">")
        html.append("            <p class=\"chart-description\">This visualization shows how cluster centroids move over time in a 2D projection of the high-dimensional vector space. Each color represents a different source cluster, with circles (○) showing historical centroids and squares (□) showing predicted centroids. The year is labeled for each point. Similar clusters appear closer together in this space.</p>")
        html.append("        </div>")
        html.append("    </div>")

        # Add year sections
        for year in years:
            year_analyses = analyses_by_year[year]
            # Sort by confidence
            year_analyses.sort(key=lambda a: a.confidence, reverse=True)

            html.append("    <div class=\"year-section\">")
            html.append(f"        <h2>Predictions for {year}</h2>")

            # Add analyses for this year
            for analysis in year_analyses:
                # Determine confidence class
                confidence_class = "high-confidence"
                if analysis.confidence < 0.7:
                    confidence_class = "medium-confidence"
                if analysis.confidence < 0.4:
                    confidence_class = "low-confidence"

                html.append("        <div class=\"analysis-card\">")
                html.append("            <div class=\"analysis-header\">")
                html.append(f"                <h3>Analysis #{analysis.id}</h3>")
                html.append(f"                <span class=\"confidence {confidence_class}\">Confidence: {analysis.confidence:.2f}</span>")
                html.append("            </div>")
                html.append("            ")
                html.append("            <h4>Summary</h4>")
                html.append(f"            <p>{analysis.summary}</p>")
                html.append("            ")
                html.append("            <h4>Detailed Analysis</h4>")

                # Replace newlines with <br> tags
                detailed_analysis = analysis.detailed_analysis.replace("\n", "<br>")
                html.append(f"            <div>{detailed_analysis}</div>")

                html.append("            ")
                html.append("            <div class=\"risks-opportunities\">")
                html.append("                <div class=\"risks\">")
                html.append("                    <h4>Potential Risks</h4>")
                html.append("                    <ul>")

                # Add risks
                for risk in analysis.potential_risks:
                    html.append(f"                        <li>{risk}</li>")

                html.append("                    </ul>")
                html.append("                </div>")
                html.append("                ")
                html.append("                <div class=\"opportunities\">")
                html.append("                    <h4>Potential Opportunities</h4>")
                html.append("                    <ul>")

                # Add opportunities
                for opportunity in analysis.potential_opportunities:
                    html.append(f"                        <li>{opportunity}</li>")

                html.append("                    </ul>")
                html.append("                </div>")
                html.append("            </div>")
                html.append("        </div>")

            html.append("    </div>")

        # Add footer
        html.append("    <div class=\"footer\">")
        html.append("        <p>Generated by Eko Predictive Analytics System</p>")
        html.append(f"        <p>© {datetime.now().year} Eko Intelligence</p>")
        html.append("    </div>")
        html.append("</body>")
        html.append("</html>")

        # Write to file
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w') as f:
            f.write("\n".join(html))

        return output_file
    except Exception as e:
        logger.exception(f"Error generating predictive report: {str(e)}")
        return ""


def figure_to_base64(fig: Figure) -> str:
    """
    Convert a matplotlib figure to a base64 encoded string.

    Args:
        fig: Matplotlib figure

    Returns:
        Base64 encoded string of the figure
    """
    buf = BytesIO()
    fig.savefig(buf, format='png', bbox_inches='tight')
    buf.seek(0)
    img_str = base64.b64encode(buf.read()).decode('utf-8')
    buf.close()
    return img_str


def create_demise_dimensions_chart(predictions: List[ClusterPredictionModel]) -> Tuple[Figure, str]:
    """
    Create a chart showing DEMISE dimensions over time.

    Args:
        predictions: List of cluster prediction models

    Returns:
        Tuple of (Figure, base64 encoded string of the figure)
    """
    try:
        # Group predictions by year
        predictions_by_year = {}
        for prediction in predictions:
            if prediction.predicted_year not in predictions_by_year:
                predictions_by_year[prediction.predicted_year] = []
            predictions_by_year[prediction.predicted_year].append(prediction)

        # Sort years
        years = sorted(predictions_by_year.keys())

        # Extract key DEMISE dimensions for visualization
        dimensions = {
            'domain.environment': [],
            'domain.society': [],
            'domain.governance': [],
            'impact.benefit_to_human_life': [],
            'impact.harm_to_human_life': [],
            'motivation.genuine': [],
            'motivation.superficial': [],
            'engagement.proactive': [],
            'engagement.reactive': []
        }

        # For each year, get the highest confidence prediction
        for year in years:
            year_predictions = predictions_by_year[year]
            best_prediction = max(year_predictions, key=lambda p: p.confidence)

            # Extract dimensions
            demise = best_prediction.predicted_demise

            # Domain dimensions - use climate_change from environment, social_equity from society, and general from governance
            dimensions['domain.environment'].append(demise.domain.environment.climate_change)
            dimensions['domain.society'].append(demise.domain.society.social_equity)
            dimensions['domain.governance'].append(demise.domain.governance.general)

            # Impact dimensions
            dimensions['impact.benefit_to_human_life'].append(demise.impact.benefit_to_human_life)
            dimensions['impact.harm_to_human_life'].append(demise.impact.harm_to_human_life)

            # Motivation dimensions
            dimensions['motivation.genuine'].append(demise.motivation.genuine)
            dimensions['motivation.superficial'].append(demise.motivation.superficial)

            # Engagement dimensions
            dimensions['engagement.proactive'].append(demise.engagement.proactive)
            dimensions['engagement.reactive'].append(demise.engagement.reactive)

        # Create visualization
        fig = plt.figure(figsize=(12, 8))

        # Plot domain dimensions
        plt.subplot(3, 1, 1)
        plt.plot(years, dimensions['domain.environment'], 'g-', label='Environment')
        plt.plot(years, dimensions['domain.society'], 'b-', label='Society')
        plt.plot(years, dimensions['domain.governance'], 'r-', label='Governance')
        plt.title('Domain Dimensions Over Time')
        plt.xlabel('Year')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)

        # Plot impact dimensions
        plt.subplot(3, 1, 2)
        plt.plot(years, dimensions['impact.benefit_to_human_life'], 'g-', label='Benefit to Human Life')
        plt.plot(years, dimensions['impact.harm_to_human_life'], 'r-', label='Harm to Human Life')
        plt.title('Impact Dimensions Over Time')
        plt.xlabel('Year')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)

        # Plot motivation and engagement dimensions
        plt.subplot(3, 1, 3)
        plt.plot(years, dimensions['motivation.genuine'], 'g-', label='Genuine Motivation')
        plt.plot(years, dimensions['motivation.superficial'], 'r-', label='Superficial Motivation')
        plt.plot(years, dimensions['engagement.proactive'], 'b-', label='Proactive Engagement')
        plt.plot(years, dimensions['engagement.reactive'], 'y-', label='Reactive Engagement')
        plt.title('Motivation and Engagement Dimensions Over Time')
        plt.xlabel('Year')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()

        # Convert to base64
        img_str = figure_to_base64(fig)

        return fig, img_str
    except Exception as e:
        logger.exception(f"Error creating DEMISE dimensions chart: {str(e)}")
        return plt.figure(), ""


def create_confidence_chart(predictions: List[ClusterPredictionModel]) -> Tuple[Figure, str]:
    """
    Create a chart showing prediction confidence over time.

    Args:
        predictions: List of cluster prediction models

    Returns:
        Tuple of (Figure, base64 encoded string of the figure)
    """
    try:
        # Group predictions by year
        predictions_by_year = {}
        for prediction in predictions:
            if prediction.predicted_year not in predictions_by_year:
                predictions_by_year[prediction.predicted_year] = []
            predictions_by_year[prediction.predicted_year].append(prediction)

        # Sort years
        years = sorted(predictions_by_year.keys())

        # Get highest confidence prediction for each year
        confidences = []
        for year in years:
            year_predictions = predictions_by_year[year]
            best_prediction = max(year_predictions, key=lambda p: p.confidence)
            confidences.append(best_prediction.confidence)

        # Create visualization
        fig = plt.figure(figsize=(10, 5))

        # Plot confidence with a color gradient based on confidence values
        colors = ['#d4edda', '#fff3cd', '#f8d7da']  # Green, Yellow, Red
        plt.bar(years, confidences, color=colors[0])

        # Add confidence values as text
        for i, conf in enumerate(confidences):
            plt.text(years[i], conf + 0.02, f"{conf:.2f}", ha='center')

        plt.title('Prediction Confidence by Year')
        plt.xlabel('Year')
        plt.ylabel('Confidence')
        plt.ylim(0, 1.1)  # Set y-axis limit to 0-1.1 to accommodate text
        plt.grid(True, linestyle='--', alpha=0.7, axis='y')

        # Convert to base64
        img_str = figure_to_base64(fig)

        return fig, img_str
    except Exception as e:
        logger.exception(f"Error creating confidence chart: {str(e)}")
        return plt.figure(), ""


def create_risks_opportunities_chart(analyses: List[PredictiveAnalysisModel]) -> Tuple[Figure, str]:
    """
    Create a chart showing the number of risks and opportunities by year.

    Args:
        analyses: List of predictive analysis models

    Returns:
        Tuple of (Figure, base64 encoded string of the figure)
    """
    try:
        # Group analyses by year
        analyses_by_year = {}
        for analysis in analyses:
            if analysis.year not in analyses_by_year:
                analyses_by_year[analysis.year] = []
            analyses_by_year[analysis.year].append(analysis)

        # Sort years
        years = sorted(analyses_by_year.keys())

        # Count risks and opportunities for each year
        risks_counts = []
        opportunities_counts = []

        for year in years:
            year_analyses = analyses_by_year[year]
            # Combine all risks and opportunities for this year
            all_risks = []
            all_opportunities = []

            for analysis in year_analyses:
                all_risks.extend(analysis.potential_risks)
                all_opportunities.extend(analysis.potential_opportunities)

            # Count unique risks and opportunities
            unique_risks = set(all_risks)
            unique_opportunities = set(all_opportunities)

            risks_counts.append(len(unique_risks))
            opportunities_counts.append(len(unique_opportunities))

        # Create visualization
        fig = plt.figure(figsize=(10, 6))

        # Set width of bars
        bar_width = 0.35

        # Set position of bars on x axis
        r1 = np.arange(len(years))
        r2 = [x + bar_width for x in r1]

        # Create bars
        plt.bar(r1, risks_counts, width=bar_width, color='#e74c3c', label='Risks')
        plt.bar(r2, opportunities_counts, width=bar_width, color='#2ecc71', label='Opportunities')

        # Add counts as text
        for i, count in enumerate(risks_counts):
            plt.text(r1[i], count + 0.1, str(count), ha='center')

        for i, count in enumerate(opportunities_counts):
            plt.text(r2[i], count + 0.1, str(count), ha='center')

        # Add labels and title
        plt.xlabel('Year')
        plt.ylabel('Count')
        plt.title('Risks vs. Opportunities by Year')
        plt.xticks([r + bar_width/2 for r in range(len(years))], years)
        plt.legend()

        plt.grid(True, linestyle='--', alpha=0.7, axis='y')

        # Convert to base64
        img_str = figure_to_base64(fig)

        return fig, img_str
    except Exception as e:
        logger.exception(f"Error creating risks/opportunities chart: {str(e)}")
        return plt.figure(), ""


def create_tsne_visualization(predictions: List[ClusterPredictionModel]) -> Tuple[Figure, str]:
    """
    Create a t-SNE visualization showing the movement of cluster centroids over time.

    Args:
        predictions: List of cluster prediction models

    Returns:
        Tuple of (Figure, base64 encoded string of the figure)
    """
    try:
        # Group predictions by source cluster ID
        clusters_by_source = {}
        for prediction in predictions:
            if prediction.source_cluster_id not in clusters_by_source:
                clusters_by_source[prediction.source_cluster_id] = []
            clusters_by_source[prediction.source_cluster_id].append(prediction)

        # Sort each cluster's predictions by year
        for source_id in clusters_by_source:
            clusters_by_source[source_id].sort(key=lambda p: p.predicted_year)

        # Collect all vectors for t-SNE
        all_vectors = []
        vector_metadata = []  # Store (source_id, year, is_historical) for each vector

        for source_id, cluster_predictions in clusters_by_source.items():
            # Add historical vector from the first prediction
            if cluster_predictions and hasattr(cluster_predictions[0], 'historical_vector') and cluster_predictions[0].historical_vector:
                all_vectors.append(cluster_predictions[0].historical_vector)
                vector_metadata.append((source_id, cluster_predictions[0].final_historical_year, True))

            # Add predicted vectors
            for prediction in cluster_predictions:
                if prediction.predicted_vector:
                    all_vectors.append(prediction.predicted_vector)
                    vector_metadata.append((source_id, prediction.predicted_year, False))

        # Check if we have enough vectors for t-SNE
        if len(all_vectors) < 2:
            logger.warning("Not enough vectors for t-SNE visualization")
            return plt.figure(), ""

        # Convert to numpy array
        vectors_array = np.array(all_vectors)

        # Apply t-SNE
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, max(3, len(all_vectors) // 5)))
        tsne_results = tsne.fit_transform(vectors_array)

        # Create visualization
        fig, ax = plt.subplots(figsize=(12, 10))

        # Generate colors for each source cluster
        unique_sources = list(set(meta[0] for meta in vector_metadata))
        colors = {}
        for i, source_id in enumerate(unique_sources):
            # Generate evenly spaced colors in HSV space
            hue = i / len(unique_sources)
            colors[source_id] = hsv_to_rgb((hue, 0.8, 0.9))

        # Plot each cluster's path
        for source_id in unique_sources:
            # Get indices for this source
            indices = [i for i, meta in enumerate(vector_metadata) if meta[0] == source_id]

            if not indices:
                continue

            # Get t-SNE coordinates for this source
            x = tsne_results[indices, 0]
            y = tsne_results[indices, 1]
            years = [vector_metadata[i][1] for i in indices]
            is_historical = [vector_metadata[i][2] for i in indices]

            # Plot the path
            ax.plot(x, y, '-', color=colors[source_id], alpha=0.7, linewidth=2,
                    label=f"Cluster {source_id}")

            # Plot points with different markers for historical vs predicted
            for i in range(len(x)):
                marker = 'o' if is_historical[i] else 's'
                ax.scatter(x[i], y[i], color=colors[source_id], marker=marker, s=100,
                          edgecolor='black', linewidth=1)
                ax.text(x[i], y[i], str(years[i]), fontsize=9, ha='center', va='bottom')

        # Add legend and labels
        ax.legend(loc='best', title="Source Clusters")
        ax.set_title('t-SNE Visualization of Cluster Centroids Over Time', fontsize=14)
        ax.set_xlabel('t-SNE Dimension 1', fontsize=12)
        ax.set_ylabel('t-SNE Dimension 2', fontsize=12)

        # Add a note about the markers
        ax.text(0.02, 0.02, "○ Historical centroids\n□ Predicted centroids",
                transform=ax.transAxes, fontsize=10, va='bottom',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))

        plt.tight_layout()

        # Convert to base64
        img_str = figure_to_base64(fig)

        return fig, img_str
    except Exception as e:
        logger.exception(f"Error creating t-SNE visualization: {str(e)}")
        return plt.figure(), ""


def visualize_prediction_trends(
    predictions: List[ClusterPredictionModel],
    output_file: Optional[str] = None
) -> str:
    """
    Visualize prediction trends over time.

    Args:
        predictions: List of cluster prediction models
        output_file: Path to save the visualization

    Returns:
        Path to the generated visualization
    """
    try:
        # Create DEMISE dimensions chart
        fig, _ = create_demise_dimensions_chart(predictions)

        # Save or show the visualization
        if output_file:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            fig.savefig(output_file)
            return output_file
        else:
            # Save to a default location
            default_file = os.path.join(eko_var_path, "reports/predictive/trends.png")
            os.makedirs(os.path.dirname(default_file), exist_ok=True)
            fig.savefig(default_file)
            return default_file
    except Exception as e:
        logger.exception(f"Error visualizing prediction trends: {str(e)}")
        return ""

