"""
Human-readable analysis of predicted DEMISE clusters.

This module provides functionality for converting predicted DEMISE vectors
into meaningful insights about future entity behavior.
"""

import json
from typing import List, Dict, Any, Optional, cast

from loguru import logger
from psycopg import Cursor

from eko.analysis_v2.pipeline_tracker_extended import TraceabilityTracker
from eko.db import get_bo_conn
from eko.llm import LLMModel
from eko.llm.main import call_llms_typed
from eko.models.vector.derived.enums import PipelineStage
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.predictive.dao import PredictiveAnalysisDAO
from eko.predictive.models import (
    ClusterPredictionModel,
    PredictiveAnalysisModel
)
# No longer needed: from eko.predictive.regression import predict_demise_model


from pydantic import BaseModel

class PredictiveAnalysisResponse(BaseModel):
    """Response model for LLM-generated predictive analysis."""

    summary: str
    detailed_analysis: str
    potential_risks: List[str]
    potential_opportunities: List[str]
    confidence: float


def generate_predictive_analysis(
    virtual_entity: VirtualEntityExpandedModel,
    predictions: List[ClusterPredictionModel],
    tracker: Optional[TraceabilityTracker] = None
) -> List[PredictiveAnalysisModel]:
    """
    Generate human-readable analysis of predicted clusters.

    Args:
        virtual_entity: Virtual entity model
        predictions: List of cluster prediction models
        tracker: Optional traceability tracker

    Returns:
        List of predictive analysis models
    """
    if tracker:
        # Check if the tracker has the start_stage method
        if hasattr(tracker, 'start_stage'):
            tracker.start_stage(PipelineStage.PREDICTIVE_ANALYSIS)

    logger.info(f"Generating predictive analysis for {virtual_entity.name} "
                f"with {len(predictions)} predictions")

    analyses = []

    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            # Group predictions by year
            predictions_by_year = {}
            for prediction in predictions:
                if prediction.predicted_year not in predictions_by_year:
                    predictions_by_year[prediction.predicted_year] = []
                predictions_by_year[prediction.predicted_year].append(prediction)

            # Process each year
            for year, year_predictions in predictions_by_year.items():
                # Sort by confidence
                year_predictions.sort(key=lambda p: p.confidence, reverse=True)

                # Get historical context
                historical_context = get_historical_context(cursor, virtual_entity.id, year)

                # Generate analysis for each prediction
                for prediction in year_predictions:
                    # Get the source cluster
                    source_cluster = get_source_cluster(cursor, prediction.source_cluster_id)
                    if not source_cluster:
                        logger.warning(f"Source cluster {prediction.source_cluster_id} not found")
                        continue

                    # Generate analysis using LLM
                    analysis_response = generate_analysis_with_llm(
                        virtual_entity,
                        prediction,
                        source_cluster,
                        historical_context
                    )

                    # Create analysis model
                    analysis_model = PredictiveAnalysisModel(
                        run_id=prediction.run_id,  # Add the run_id from the prediction
                        prediction_id=prediction.id,
                        virtual_entity_id=virtual_entity.id,
                        year=prediction.predicted_year,
                        summary=analysis_response.summary,
                        detailed_analysis=analysis_response.detailed_analysis,
                        potential_risks=analysis_response.potential_risks,
                        potential_opportunities=analysis_response.potential_opportunities,
                        confidence=prediction.confidence
                    )

                    # Save to database
                    saved_analysis = PredictiveAnalysisDAO.create(cursor, analysis_model)
                    analyses.append(saved_analysis)

                    logger.info(f"Created analysis for year {year} with confidence {prediction.confidence:.2f}")

            # Sync to transfer table
            sync_count = PredictiveAnalysisDAO.sync_to_xfer(cursor, virtual_entity.id)
            logger.info(f"Synced {sync_count} analyses to transfer table")

            conn.commit()

    if tracker:
        # Check if the tracker has the end_stage method
        if hasattr(tracker, 'end_stage'):
            tracker.end_stage(PipelineStage.PREDICTIVE_ANALYSIS, {
                "analyses_created": len(analyses)
            })

    return analyses


def get_historical_context(
    cursor: Cursor,
    virtual_entity_id: int,
    target_year: int
) -> Dict[str, Any]:
    """
    Get historical context for analysis.

    Args:
        cursor: Database cursor
        virtual_entity_id: Virtual entity ID
        target_year: Target year for prediction

    Returns:
        Dictionary with historical context
    """
    try:
        # Get historical statements
        cursor.execute("""
            SELECT s.id, s.statement_text, s.model_json, s.start_year
            FROM kg_statements_v2 s
            JOIN kg_virt_entity_map vem ON s.company_id = vem.base_entity_id
            WHERE vem.virt_entity_id = %s
              AND s.start_year < %s
            ORDER BY s.start_year DESC
            LIMIT 50
        """, (virtual_entity_id, target_year))

        statements = []
        for row in cursor.fetchall():
            statements.append({
                "id": row[0],
                "text": row[1],
                "year": row[3]
            })

        # Get historical effect flags
        cursor.execute("""
            SELECT ef.id, ef.title, ef.summary, ef.start_year, ef.end_year, ef.effect_type
            FROM ana_effect_flags ef
            WHERE ef.virtual_entity_id = %s
              AND ef.end_year < %s
            ORDER BY ef.end_year, ef.impact DESC
            LIMIT 200
        """, (virtual_entity_id, target_year))

        effect_flags = []
        for row in cursor.fetchall():
            effect_flags.append({
                "id": row[0],
                "title": row[1],
                "summary": row[2],
                "start_year": row[3],
                "end_year": row[4],
                "effect_type": row[5]
            })

        # For predictive analytics, we don't need to use claims
        # This is part of the greenwashing analysis system, not predictive analytics
        claims = []

        # For predictive analytics, we don't need to use promises
        # This is part of the greenwashing analysis system, not predictive analytics
        promises = []

        return {
            "statements": statements,
            "effect_flags": effect_flags,
            "claims": claims,
            "promises": promises
        }
    except Exception as e:
        logger.exception(f"Error getting historical context: {str(e)}")
        return {
            "statements": [],
            "effect_flags": [],
            "claims": [],
            "promises": []
        }


def get_source_cluster(
    cursor: Cursor,
    cluster_id: int
) -> Optional[Dict[str, Any]]:
    """
    Get source cluster information.

    Args:
        cursor: Database cursor
        cluster_id: Cluster ID

    Returns:
        Dictionary with cluster information or None if not found
    """
    # Special case for overall prediction (source_cluster_id = 0)
    if cluster_id == 0:
        return {
            "id": 0,
            "virtual_entity_id": 0,
            "year": "overall",
            "centroid": [],
            "statements": [{"id": 0, "text": "This is an overall prediction based on all historical statements."}]
        }

    try:
        cursor.execute("""
            SELECT id, virtual_entity_id, year, centroid, statement_ids
            FROM ana_predict_temporal_clusters
            WHERE id = %s
        """, (cluster_id,))

        row = cursor.fetchone()
        if not row:
            return None

        # Get statement texts
        statement_ids = row[4]
        statement_texts = []

        if statement_ids:
            # Use a safer approach to build the query
            from psycopg.sql import SQL, Placeholder

            # Build a list of placeholders for the IN clause
            placeholders = SQL(', ').join([Placeholder()] * len(statement_ids))

            # Build the complete query
            query = SQL("""
                SELECT id, statement_text
                FROM kg_statements_v2
                WHERE id IN ({})
            """).format(placeholders)

            cursor.execute(query, statement_ids)

            for stmt_row in cursor.fetchall():
                statement_texts.append({
                    "id": stmt_row[0],
                    "text": stmt_row[1]
                })

        # The centroid field is a vector, not a JSON string
        centroid_vector = row[3]
        # Convert to list if it's not already
        if hasattr(centroid_vector, '__iter__'):
            centroid_list = list(centroid_vector)
        else:
            centroid_list = []

        return {
            "id": row[0],
            "virtual_entity_id": row[1],
            "year": row[2],
            "centroid": centroid_list,
            "statements": statement_texts
        }
    except Exception as e:
        logger.exception(f"Error getting source cluster: {str(e)}")
        return None


def generate_analysis_with_llm(
    virtual_entity: VirtualEntityExpandedModel,
    prediction: ClusterPredictionModel,
    source_cluster: Dict[str, Any],
    historical_context: Dict[str, Any]
) -> PredictiveAnalysisResponse:
    """
    Generate analysis of predicted behavior using LLM.

    Args:
        virtual_entity: Virtual entity model
        prediction: Cluster prediction model
        source_cluster: Source cluster information
        historical_context: Historical context

    Returns:
        Predictive analysis response
    """
    try:
        # Get the predicted and historical vectors
        predicted_vector = prediction.predicted_vector
        historical_vector = prediction.historical_vector

        # Convert predicted vector to DEMISE model for LLM
        import numpy as np
        from eko.models.vector.demise.demise_model import DEMISEModel

        # Create DEMISE model from the predicted vector
        predicted_demise = cast(DEMISEModel, DEMISEModel.from_np(np.array(predicted_vector)))

        # Get sparse representation for the prompt
        predicted_dict = predicted_demise.to_kv()

        # Log the DEMISE model to help with debugging
        logger.info(f"DEMISE model for prediction {prediction.id}: {len(predicted_dict)} fields")

        # Convert to numpy arrays for easier calculation
        predicted_np = np.array(predicted_vector)
        historical_np = np.array(historical_vector)

        # Calculate delta vector
        delta_vector = (predicted_np - historical_np).tolist()

        historical_demise = cast(DEMISEModel, DEMISEModel.from_np(np.array(historical_vector)))
        historical_dict = historical_demise.to_kv()

        field_descriptions = predicted_demise.get_key_descriptions()

        # Format significant changes for the LLM
        significant_changes = []
        i = 0
        for field_name, _ in predicted_dict.items():
            delta = delta_vector[i]
            i = i + 1
            final = predicted_dict.get(field_name, 0)
            initial = historical_dict.get(field_name, 0)
            description = field_descriptions[field_name]

            # Only include significant changes
            if abs(delta) > 0.05:
                direction = "increased" if delta > 0 else "decreased"
                significant_changes.append(
                    f"- {field_name}: {direction} from {initial:.2f} to {final:.2f} (delta: {delta:.2f})"
                    + (f" - {field_name} = {description}" if description else "")
                )
            # Also include high values even if they didn't change much
            elif final > 0.5 and abs(delta) <= 0.05:
                significant_changes.append(
                    f"- {field_name}: remained significant at {final:.2f}"
                    + (f" - {field_name} = {description}" if description else "")
                )

        # Get statements with similar DEMISE embeddings to the predicted vector
        with get_bo_conn() as conn:
            with conn.cursor() as cursor:
                similar_statements = get_similar_statements(
                    cursor,
                    virtual_entity.id,
                    predicted_vector
                )

        # Format the similar statements for the prompt
        similar_statements_text = format_similar_statements(similar_statements)

        # Historical Context
        flags_text = format_effect_flags(historical_context.get("effect_flags", [])[:50])

        # Create prompt for LLM
        # Build the prompt in parts to avoid f-string backslash issues
        prompt_parts = []

        # Introduction
        prompt_parts.append(f"You are an expert in ethical analysis and corporate behavior prediction. Your task is to analyze a predicted DEMISE model for {virtual_entity.name} for the year {prediction.predicted_year} and provide insights about their future behavior.")

        # Entity Information
        prompt_parts.append(f"""
# Entity Information
- Name: {virtual_entity.name}
- Type: {virtual_entity.type}
- Prediction Year: {prediction.predicted_year}
- Prediction Confidence: {prediction.confidence:.2f}
""")
        prompt_parts.append(f"""
# Historical Context
## Recent Effect Flags (Sample)
{flags_text}
""")

        # Add similar statements section
        prompt_parts.append(f"""
## Statements Similar to Predicted Vector
The following are statements from {virtual_entity.name} that have DEMISE embeddings most similar to the predicted vector, regardless of when they were made. These may provide insight into how the predicted DEMISE vector might manifest in concrete language and actions:

{similar_statements_text}
""")

        # DEMISE Model
        prompt_parts.append(f"""
# Predicted DEMISE Model
The DEMISE model represents Domain, Entity, Motivation, Impact, Statement Type, and Engagement dimensions:

```json
{json.dumps(predicted_demise.to_kv_sparse(0.1), indent=2)}
```
""")

        # Significant Changes
        if not significant_changes:
            prompt_parts.append("# Significant Changes in DEMISE Values\nNo significant changes detected.")
        else:
            changes_text = "\n".join(significant_changes)
            prompt_parts.append(f"# Significant Changes in DEMISE Values\n{changes_text}")

        # Analysis Task
        prompt_parts.append("""
# Analysis Task
Based on the predicted DEMISE model, significant changes, historical context, and similar statements, provide:

1. A concise summary (1-2 sentences) of the predicted behavior
2. A detailed analysis (3-5 paragraphs) explaining the predicted ethical stance and behavior, focusing on the significant changes in the DEMISE model and how they might manifest based on the similar statements
3. A list of potential risks (3-5 items)
4. A list of potential opportunities (3-5 items)

Focus on how the DEMISE dimensions are likely to manifest in concrete actions and policies. Consider both continuity with past behavior and potential changes or new directions. Pay special attention to the similar statements section, as these provide real examples of how the entity expresses itself when exhibiting this ethical stance.

The DEMISE model describes a single conceptual shift, so each of the values do not have meaning on their own, it is the combined delta that we are looking at.

i.e. a +ve change in domain.industry.chemical combined with a -ve change in engagement.preventative means that the company will be less cautious about their chemical practices. So as you can see it's the combination that we're looking for.

Your response should be structured as follows:
```json
{
  "summary": "Concise summary here",
  "detailed_analysis": "Detailed analysis here...",
  "potential_risks": ["Risk 1", "Risk 2", "Risk 3"],
  "potential_opportunities": ["Opportunity 1", "Opportunity 2", "Opportunity 3"],
  "confidence": 0.85
}
```
""")

        # Join all parts
        prompt = "\n".join(prompt_parts)
        print(prompt)

        # Call LLM
        # Convert prompt to messages format
        messages = [{"role": "user", "content": prompt}]

        from eko.llm.main import LLMOptions
        options = LLMOptions(
            cache_key=f"predictive_analysis:{virtual_entity.id}:{prediction.predicted_year}:{prediction.id}"
        )
        response = call_llms_typed(
            [ LLMModel.NORMAL_HQ, LLMModel.NORMAL_HQ_ALT],
            messages,
            max_tokens=4000,
            response_model=PredictiveAnalysisResponse,
            options=options
        )

        return response
    except Exception as e:
        logger.exception(f"Error generating analysis with LLM: {str(e)}")

        # Return a fallback response
        return PredictiveAnalysisResponse(
            summary=f"Predicted behavior for {virtual_entity.name} in {prediction.predicted_year}",
            detailed_analysis="Unable to generate detailed analysis due to an error.",
            potential_risks=["Error in analysis generation"],
            potential_opportunities=["Error in analysis generation"],
            confidence=0.5
        )


def format_statements(statements: List[Dict[str, Any]]) -> str:
    """Format statements for LLM prompt."""
    if not statements:
        return "No historical statements available."

    result = ""
    for i, stmt in enumerate(statements):
        result += f"{i+1}. ({stmt.get('year', 'Unknown year')}) {stmt.get('text', '')}\n"

    return result


def format_effect_flags(flags: List[Dict[str, Any]]) -> str:
    """Format effect flags for LLM prompt."""
    if not flags:
        return "No historical effect flags available."

    result = ""
    for i, flag in enumerate(flags):
        result += f"{i+1}. {flag.get('title', '')} ({flag.get('start_year', '')}-{flag.get('end_year', '')})\n"
        result += f"   {flag.get('summary', '')}\n"

    return result


def format_claims_and_promises(claims: List[Dict[str, Any]], promises: List[Dict[str, Any]]) -> str:
    """Format claims and promises for LLM prompt."""
    result = "## Claims\n"
    if claims:
        for i, claim in enumerate(claims):
            result += f"{i+1}. ({claim.get('year', 'Unknown')}) {claim.get('text', '')}\n"
            result += f"   Verdict: {claim.get('verdict', 'Unknown')}\n"
    else:
        result += "No historical claims available.\n"

    result += "\n## Promises\n"
    if promises:
        for i, promise in enumerate(promises):
            result += f"{i+1}. ({promise.get('year', 'Unknown')}) {promise.get('text', '')}\n"
            result += f"   Target Year: {promise.get('target_year', 'Unknown')}, Verdict: {promise.get('verdict', 'Unknown')}\n"
    else:
        result += "No historical promises available.\n"

    return result


def get_similar_statements(
    cursor: Cursor,
    virtual_entity_id: int,
    predicted_vector: List[float],
    limit: int = 10
) -> List[Dict[str, Any]]:
    """
    Find statements with similar DEMISE embeddings to the predicted vector.

    Args:
        cursor: Database cursor
        virtual_entity_id: Virtual entity ID
        predicted_vector: The predicted DEMISE vector to compare against
        limit: Maximum number of statements to return

    Returns:
        List of statements with similarity scores
    """
    try:
        # Convert the vector to a string for SQL
        vector_str = '[' + ','.join(str(v) for v in predicted_vector) + ']'

        # Get base entity IDs for the virtual entity
        cursor.execute("""
            SELECT base_entity_id
            FROM kg_virt_entity_map
            WHERE virt_entity_id = %s
        """, (virtual_entity_id,))

        base_entity_ids = [row[0] for row in cursor.fetchall()]
        if not base_entity_ids:
            logger.warning(f"No base entities found for virtual entity {virtual_entity_id}")
            return []

        # Convert to array literal for SQL
        entity_ids_str = '{' + ','.join(str(id) for id in base_entity_ids) + '}'

        # Use pg_vector to find similar statements
        # Use SQL() to properly handle the query
        from psycopg.sql import SQL, Literal

        query = SQL("""
            SELECT
                s.id,
                s.statement_text,
                COALESCE(s.start_year, doc.year) AS year,
                1 - (s.demise_embedding <=> {}::vector) AS similarity_score
            FROM kg_statements_v2 s
            JOIN kg_documents doc ON s.doc_id = doc.id
            WHERE (s.company_id = ANY(%s::bigint[])
                  OR s.subject_entities && %s::bigint[]
                  OR s.object_entities && %s::bigint[])
              AND s.demise_embedding IS NOT NULL
             AND (s.is_environmental = TRUE OR s.is_social = TRUE OR
                  s.is_governance = TRUE OR s.is_animal_welfare = TRUE)
            ORDER BY similarity_score DESC
            LIMIT %s
        """).format(Literal(vector_str))

        cursor.execute(query, (entity_ids_str, entity_ids_str, entity_ids_str, limit))

        similar_statements = []
        for row in cursor.fetchall():
            similar_statements.append({
                "id": row[0],
                "text": row[1],
                "year": row[2],
                "similarity_score": row[3]
            })

        logger.info(f"Found {len(similar_statements)} statements with similar DEMISE embeddings")
        return similar_statements

    except Exception as e:
        logger.exception(f"Error finding similar statements: {str(e)}")
        return []


def format_similar_statements(statements: List[Dict[str, Any]]) -> str:
    """Format similar statements for LLM prompt."""
    if not statements:
        return "No similar statements found."

    result = ""
    for i, stmt in enumerate(statements):
        similarity = stmt.get('similarity_score', 0) * 100
        result += f"{i+1}. ({stmt.get('year', 'Unknown year')}) {stmt.get('text', '')} [Similarity: {similarity:.1f}%]\n"

    return result
