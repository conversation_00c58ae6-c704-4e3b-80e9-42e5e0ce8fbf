# Predictive Analytics Plan for DEMISE Model

This document outlines the plan for implementing predictive analytics in the `eko.predictive` package. The goal is to predict future behavior of entities based on historical DEMISE embeddings of their statements.

## Overview

The predictive analytics system will consist of three main components:

1. **Temporal Clustering**: Cluster statements by year to identify patterns in entity behavior
2. **Multivariate Regression**: Predict future cluster positions based on historical data
3. **Human-Readable Analysis**: Convert predicted clusters into meaningful insights

## 1. Temporal Clustering

### Approach

We will use clustering like that found in `eko.analysis_v2.effects.clustering` but to create year-specific clusters for each virtual entity.

### Implementation Plan

1. Create a new module `eko.predictive.temporal_clustering` that will:
   - Query statements from `kg_statements_v2` for a given virtual entity
   - Group statements by year
   - Apply clustering to each year's statements using DEMISE embeddings
   - Store cluster centroids and metadata for each year

2. For each year, we will:
   - Extract DEMISE embeddings from statements
   - Apply clustering 
   - Calculate cluster centroids and track their evolution over time

3. Store temporal clusters in a new table `ana_temporal_clusters` with:
   - `id`: Primary key
   - `virtual_entity_id`: Entity ID
   - `year`: Year of the cluster
   - `centroid`: Vector representing the cluster center
   - `demise_centroid`: Full DEMISE model centroid
   - `statement_ids`: Array of statement IDs in the cluster
   - `size`: Number of statements in the cluster
   - `coherence`: Measure of cluster coherence (avg distance to centroid)

## 2. Multivariate Regression for Prediction

### Approach

We will implement several regression models to predict future cluster positions based on historical data. The system will evaluate which model performs best for each entity and use it for predictions.

### Implementation Plan

1. Create a new module `eko.predictive.regression` that will:
   - Load temporal clusters for a given entity
   - Train regression models on historical data
   - Predict future cluster positions
   - Evaluate model performance

2. Implement multiple regression models:
   - **Vector Autoregression (VAR)**: For capturing relationships between multiple time series
   - **LSTM Neural Networks**: For capturing complex temporal patterns
   - **Gaussian Process Regression**: For uncertainty quantification
   - **Prophet**: For trend and seasonality decomposition

3. For each entity and cluster type:
   - Train models on data up to year N-3
   - Validate on years N-2 to N
   - Select the best performing model
   - Generate predictions for future years

4. Store predictions in a new table `ana_cluster_predictions` with:
   - `id`: Primary key
   - `virtual_entity_id`: Entity ID
   - `source_cluster_id`: ID of the source cluster
   - `predicted_year`: Year being predicted
   - `predicted_centroid`: Vector representing the predicted cluster center
   - `predicted_demise`: Predicted DEMISE model
   - `confidence`: Confidence score for the prediction
   - `model_type`: Type of model used for prediction
   - `created_at`: Timestamp

## 3. Human-Readable Analysis

### Approach

We will use LLMs to convert predicted DEMISE vectors into meaningful human-readable insights about future entity behavior.

### Implementation Plan

1. Create a new module `eko.predictive.analysis` that will:
   - Load predicted clusters
   - Generate human-readable descriptions
   - Identify potential future risks and opportunities

2. For each predicted cluster:
   - Convert the predicted DEMISE vector back to a structured DEMISE model
   - Use LLMs to generate insights about predicted behavior
   - Compare with historical patterns to identify changes

3. The LLM prompt will include:
   - Historical DEMISE patterns for context
   - Predicted DEMISE values
   - Entity metadata
   - Industry context

4. Store analysis in a new table `ana_predictive_analysis` with:
   - `id`: Primary key
   - `prediction_id`: ID of the related prediction
   - `virtual_entity_id`: Entity ID
   - `year`: Year being analyzed
   - `summary`: Short summary of predicted behavior
   - `detailed_analysis`: Detailed analysis text
   - `potential_risks`: Identified potential risks
   - `potential_opportunities`: Identified potential opportunities
   - `confidence`: Confidence score for the analysis
   - `created_at`: Timestamp

5. Create a transfer table `xfer_predictive_analysis` for exposing the analysis to the customer app.

## Database Schema

### New Tables

1. `ana_temporal_clusters`
   ```sql
   CREATE TABLE ana_temporal_clusters (
       id SERIAL PRIMARY KEY,
       virtual_entity_id BIGINT NOT NULL REFERENCES kg_virtual_entities(id) ON DELETE CASCADE,
       year INT NOT NULL,
       centroid VECTOR(1024) NOT NULL,
       demise_centroid JSONB NOT NULL,
       statement_ids BIGINT[] NOT NULL,
       size INT NOT NULL,
       coherence FLOAT NOT NULL,
       created_at TIMESTAMP NOT NULL DEFAULT NOW(),
       UNIQUE (virtual_entity_id, year)
   );
   ```

2. `ana_cluster_predictions`
   ```sql
   CREATE TABLE ana_cluster_predictions (
       id SERIAL PRIMARY KEY,
       virtual_entity_id BIGINT NOT NULL REFERENCES kg_virtual_entities(id) ON DELETE CASCADE,
       source_cluster_id BIGINT NOT NULL REFERENCES ana_temporal_clusters(id) ON DELETE CASCADE,
       predicted_year INT NOT NULL,
       predicted_centroid VECTOR(1024) NOT NULL,
       predicted_demise JSONB NOT NULL,
       confidence FLOAT NOT NULL,
       model_type VARCHAR(50) NOT NULL,
       created_at TIMESTAMP NOT NULL DEFAULT NOW(),
       UNIQUE (virtual_entity_id, predicted_year, source_cluster_id)
   );
   ```

3. `ana_predictive_analysis`
   ```sql
   CREATE TABLE ana_predictive_analysis (
       id SERIAL PRIMARY KEY,
       prediction_id BIGINT NOT NULL REFERENCES ana_cluster_predictions(id) ON DELETE CASCADE,
       virtual_entity_id BIGINT NOT NULL REFERENCES kg_virtual_entities(id) ON DELETE CASCADE,
       year INT NOT NULL,
       summary TEXT NOT NULL,
       detailed_analysis TEXT NOT NULL,
       potential_risks TEXT[] NOT NULL,
       potential_opportunities TEXT[] NOT NULL,
       confidence FLOAT NOT NULL,
       created_at TIMESTAMP NOT NULL DEFAULT NOW(),
       UNIQUE (prediction_id)
   );
   ```

4. `xfer_predictive_analysis`
   ```sql
   CREATE TABLE xfer_predictive_analysis (
       id SERIAL PRIMARY KEY,
       virtual_entity_id BIGINT NOT NULL,
       year INT NOT NULL,
       summary TEXT NOT NULL,
       detailed_analysis TEXT NOT NULL,
       potential_risks JSONB NOT NULL,
       potential_opportunities JSONB NOT NULL,
       confidence FLOAT NOT NULL,
       created_at TIMESTAMP NOT NULL DEFAULT NOW(),
       UNIQUE (virtual_entity_id, year)
   );
   ```

## Package Structure

```
eko/
└── predictive/
    ├── __init__.py
    ├── PLAN.md
    ├── temporal_clustering.py
    ├── regression.py
    ├── analysis.py
    ├── models.py
    ├── dao.py
    ├── cli.py
    └── utils.py
```

## CLI Commands

We will add the following CLI commands:

1. `analyze-predictive-trends`: Run the full predictive analytics pipeline
   ```bash
   python cli.py analyze-predictive-trends --entity "EntityShortId" --start-year 2019 --end-year 2025 --future-years 3
   ```

2. `create-temporal-clusters`: Create temporal clusters only
   ```bash
   python cli.py create-temporal-clusters --entity "EntityShortId" --start-year 2019 --end-year 2025
   ```

3. `predict-future-clusters`: Generate predictions for future clusters
   ```bash
   python cli.py predict-future-clusters --entity "EntityShortId" --future-years 3
   ```

4. `generate-predictive-analysis`: Generate human-readable analysis from predictions
   ```bash
   python cli.py generate-predictive-analysis --entity "EntityShortId" --future-years 3
   ```

## Implementation Timeline

1. **Phase 1: Temporal Clustering**
   - Implement `temporal_clustering.py`
   - Create database tables
   - Add CLI command for creating temporal clusters
   - Test with sample entities

2. **Phase 2: Regression Models**
   - Implement `regression.py` with multiple model types
   - Add model evaluation and selection
   - Add CLI command for prediction
   - Test prediction accuracy

3. **Phase 3: Human-Readable Analysis**
   - Implement `analysis.py` with LLM integration
   - Create transfer table for customer app
   - Add CLI command for analysis generation
   - Test end-to-end pipeline

## Technical Considerations

1. **Performance**: 
   - Use pg_vector for efficient vector operations
   - Implement batch processing for large entities
   - Cache intermediate results

2. **Accuracy**:
   - Evaluate multiple regression models
   - Use ensemble methods for improved predictions
   - Incorporate confidence scores

3. **Interpretability**:
   - Generate human-readable explanations
   - Visualize prediction trends
   - Provide confidence intervals

4. **Integration**:
   - Integrate with existing effect analysis pipeline
   - Expose results to customer app
   - Add visualization components

## Conclusion

This predictive analytics system will enhance the existing DEMISE model by adding a temporal dimension and forecasting capabilities. By predicting future ethical behavior, it will provide valuable insights for stakeholders and help identify potential risks and opportunities before they materialize.

The implementation will leverage existing clustering and analysis capabilities while adding new regression models and LLM-based interpretation to create a comprehensive predictive analytics solution.
