import html2text
import traceback

from psycopg.errors import UniqueViolation
from typing import Optional

import datetime

from urllib.parse import urlparse

import langdetect
import os
from bs4 import BeautifulSoup
from goose3 import Goose
from loguru import logger

from eko import eko_var_path
from eko.config import MIN_ARTICLE_LENGTH, MAX_CHARS_FOR_LLMS
from eko.db import get_bo_conn
from eko.domains.domain_categorizer import get_domain_info
from eko.llm import LLMModel, PROMPT_NOT_CONVERSATIONAL
from eko.llm.main import call_llm_boolean
from eko.llm.prompts import prompt
from eko.nlp.chunk import chunk_page_on_paragraph
from eko.nlp.util import to_kebab_case
from eko.scrape.doc_processor import DocumentProcessor
from eko.scrape.eko_reports import upload_file, store_chunks
from eko.settings import settings
from eko.util.hash import md5_hash_url, sha256_hash_file
from eko.web.get import download

def html_to_markdown(html_content):
    h = html2text.HTML2Text()
    h.wrap_links = False  # Do not wrap links
    h.wrap_tables = False  # Do not wrap tables
    h.protect_links = True
    h.ignore_links = False  # Keep the links in the output
    return h.handle(html_content)


class WebpageProcessor(DocumentProcessor):
    """
    Processor for HTML webpages.
    """

    def _prepare_file_path(self) -> None:
        """
        Prepare the file path for the webpage.
        """
        self.filename = f"{to_kebab_case(self.url)[:60]}-{md5_hash_url(self.url)}.html"
        dir_path = os.path.join(eko_var_path, f"files/source_reports/url_hashed/{md5_hash_url(self.url)[:2]}")
        os.makedirs(dir_path, exist_ok=True)
        self.file_path = os.path.join(dir_path, self.filename)

    def _download_document(self) -> bool:
        """
        Download the webpage.

        Returns:
            bool: True if download was successful, False otherwise
        """
        if os.path.exists(self.file_path):
            logger.info(f"HTML {self.file_path} already exists locally. Skipping download.")
            try:
                with open(self.file_path, 'r', encoding='utf-8') as file:
                    self.content = file.read()
                self.file_hash = sha256_hash_file(self.file_path)
                return True
            except Exception as e:
                logger.warning(f"Error reading existing file {self.file_path}: {str(e)}. Will try to download again.")
                # Continue to download the file again

        try:
            response = download(self.url)
            if response is None:
                logger.info(f"Failed to download {self.url}")
                with get_bo_conn() as conn:
                    self._mark_processed(conn, True, '1 day')
                return False

            self.content = response
            with open(self.file_path, 'w', encoding='utf-8') as file:
                file.write(self.content)

            # Calculate file hash
            self.file_hash = sha256_hash_file(self.file_path)
            return True
        except Exception as e:
            msg = str(f"Error downloading {self.url}: {str(e)}")
            logger.exception(msg)
            logger.info(f"Timeout downloading {self.url}")
            with get_bo_conn() as conn:
                self._mark_processed(conn, True, '1 day')
            return False

    def _process_content(self) -> bool:
        """
        Process the webpage content.

        Returns:
            bool: True if processing was successful, False otherwise
        """
        # Parse HTML with BeautifulSoup
        soup = BeautifulSoup(self.content, 'html.parser')
        self.raw_text = str(soup)

        # Check language
        try:
            if langdetect.detect(self.content) != "en":
                logger.info(f"Skipping {self.url} because it's not in English")
                return False
        except Exception as e:
            logger.info(f"Error detecting language for {self.url}: {str(e)}")
        try:
            # Extract article content with Goose
            g = Goose()
            article = g.extract(raw_html=self.content)
        except Exception as e:
            logger.info(f"Error extracting article with Goose for {self.url}: {str(e)}")
            return False

        # Check if article has enough text
        if not self.force_accept and (article.cleaned_text is None or len(article.cleaned_text) < MIN_ARTICLE_LENGTH):
            logger.info(f"Skipping {self.url} because it has not enough text {len(article.cleaned_text)}")
            return False

        # Get domain info
        try:
            domain_info = get_domain_info(urlparse(self.url).netloc)
            if domain_info:
                domain = domain_info.domain
                owning_entity = domain_info.owning_entity
            else:
                domain = urlparse(self.url).netloc
                owning_entity = None
        except Exception as e:
            logger.warning(f"Error getting domain info for {self.url}: {str(e)}")
            domain = urlparse(self.url).netloc
            owning_entity = None

        # Set origin domain if not provided
        if self.origin_domain is None:
            self.origin_domain = domain

        # Format article text
        article_text = f"""
---
Title: {article.title}
Published Date: {article.publish_date}
Downloaded Date: {datetime.datetime.now()}
Publish Year: {article.publish_datetime_utc.year if article.publish_datetime_utc else None}
Authors: <AUTHORS>
Summary: {article.meta_description}
Keywords:{", ".join(article.meta_keywords)}
Article:
---
{html_to_markdown(article.raw_html)}
"""

        # Check if entity is mentioned in the article
        if not self.force_accept and (self.name is not None or self.expanded_entity is not None):
            if not self.is_entity_mentioned(article_text):
                logger.info(f"Skipping {self.url} because it's not about {self.name}")
                return False

        # Truncate if too large
        self.clean_text = article_text
        if len(article_text) > 1000000:
            logger.info("HTML is too large to process converting to plain text")
            self.clean_text = article_text[:MAX_CHARS_FOR_LLMS]

        # Check if it's an article
        if not call_llm_boolean([LLMModel.NORMAL_CHEAP], prompt(PROMPT_NOT_CONVERSATIONAL,
                                f"Please identify if this is an article, i.e. prose about a subject not a web page. Web pages typically list information, prose tells a story. Is this a journalistic article?\n\n <content>{article_text[:5000]}</content>.\n\nPlease answer with a simple yes or no.")):
            logger.warning(f"Skipping {self.url} because it's not an article")
            return False

        # Check minimum length
        if len(article_text) < MIN_ARTICLE_LENGTH:
            logger.info("Article is too small to process")
            return False

        # Store article metadata
        self.article = article
        return True

    def _store_document(self) -> Optional[int]:
        """
        Store the webpage in the database.

        Returns:
            Optional[int]: Document ID if storage was successful, None otherwise
        """
        assert self.file_hash is not None

        with get_bo_conn() as conn:
            with conn.cursor() as cursor:
                try:
                    # Get publish date, ensuring it's a datetime or None
                    publish_date = getattr(self.article, 'publish_date', None)
                    if publish_date is not None and not isinstance(publish_date, datetime.datetime):
                        publish_date = None

                    # Upload file to database
                    self.doc_id = upload_file(
                        cursor,
                        url=self.url,
                        path=self.file_path,
                        filename=self.filename,
                        text_hash=self.file_hash,
                        clean_text=self.clean_text,
                        rough_text=self.raw_text,
                        doc_id=self.doc_id,
                        file_type="html",
                        research_types=self.research_types,
                        # Handle None case for publish_date
                        publish_date=publish_date,
                        origin_domain=self.origin_domain,
                    )

                    # Store chunks
                    store_chunks(conn, self.doc_id, chunk_page_on_paragraph(self.clean_text, settings.max_page_size_chars))

                    # Mark as processed
                    cursor.execute("UPDATE kg_documents SET last_processed=now() WHERE hash = %s", (self.file_hash,))
                    self._mark_processed(conn, False, '30 day')

                    return self.doc_id
                except UniqueViolation:
                    logger.info(f"Skipping {self.url} it's already in the database.")
                    conn.rollback()
                    self._mark_processed(conn, False, '30 day')
                    return None
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Error processing webpage {self.url}: {str(e)}")
                    self._mark_processed(conn, True, '1 hour')
                    logger.exception(e)
                    traceback.print_exc()
                    if os.path.exists(self.file_path):
                        os.remove(self.file_path)
                    return None
