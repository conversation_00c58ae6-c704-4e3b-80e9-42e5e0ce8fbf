from urllib.parse import urlparse

from loguru import logger
from typing import Optional, List

from abc import ABC, abstractmethod

from eko.db import get_bo_conn
from eko.llm import LLMModel
from eko.llm.main import call_llm_boolean
from eko.llm.prompts import prompt
from eko.log.log import set_log_context, log_info
from eko.models import ResearchType
from eko.models.virtual_entity import VirtualEntityExpandedModel

def get_never_download_domains():
    """Get a list of domains that should never be downloaded from the database"""
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT domain FROM kg_domains WHERE blacklisted = TRUE")
            return [row[0] for row in cur.fetchall()]


def is_company_mentioned(name, text):
    """
    Check if a company is mentioned in the text.

    Args:
        name: Name of the company
        text: Text to check

    Returns:
        bool: True if the company is mentioned, False otherwise
    """
    answer = call_llm_boolean(
        [LLMModel.VERY_SMALL_ULTRA_FAST, LLMModel.NORMAL_CHEAP],
        prompt("You are a terse company analyst.",
               f"Please identify if this article is about the company known as {name}, it must be about that company not any of it's parent companies or it's equivalents in other countries. Article extract: {text[:20000]}. Please answer with a simple yes or no.")
    )
    return answer



def is_virt_entity_mentioned(expanded_entity: VirtualEntityExpandedModel, text):
    """
    Check if a virtual entity is mentioned in the text.

    Args:
        expanded_entity: Expanded entity model
        text: Text to check

    Returns:
        bool: True if the entity is mentioned, False otherwise
    """
    answer = call_llm_boolean(
        [LLMModel.VERY_SMALL_ULTRA_FAST, LLMModel.NORMAL_CHEAP],
        prompt("You are a terse company analyst.",
               f"Please identify if this article is about the {expanded_entity.for_referencing_in_prompts()}. Article extract: {text[:20000]}. Please answer with a simple yes or no.")
    )
    return answer



class DocumentProcessor(ABC):
    """
    Abstract base class for document processors.

    This class defines the interface for processing different types of documents
    (webpages, PDFs, etc.) and provides common functionality.
    """

    def __init__(self,
                 url: str,
                 research_types: Optional[List[ResearchType]] = None,
                 name: Optional[str] = None,
                 force_accept: bool = False,
                 expanded_entity: Optional[VirtualEntityExpandedModel] = None,
                 requester: Optional[str] = None,
                 origin_domain: Optional[str] = None):
        """
        Initialize the document processor.

        Args:
            url: URL of the document to process
            research_types: List of research types for the document
            name: Name of the entity being researched
            force_accept: Whether to force processing even if the document would normally be skipped
            expanded_entity: Expanded entity model for entity detection
            requester: ID of the requester
            origin_domain: Domain of the document origin
        """
        self.url = url
        self.research_types = research_types or []
        self.name = name
        self.force_accept = force_accept
        self.expanded_entity = expanded_entity
        self.requester = requester
        self.origin_domain = origin_domain
        self.doc_id = None
        self.file_hash = None
        self.file_path = None
        self.filename = None
        self.content = None
        self.clean_text = None
        self.raw_text = None

        # Set logging context
        set_log_context(self.__class__.__name__, name, requester)

    def process(self) -> Optional[int]:
        """
        Process the document.

        Returns:
            Optional[int]: Document ID if processing was successful, None otherwise
        """
        log_info(self.__class__.__name__, f"Processing {self.url}")

        # Validate URL
        if not self.url.startswith('http'):
            logger.info(f"Skipping {self.url} because it's not a valid URL")
            return None

        # Check if domain is blacklisted
        if not self.force_accept and urlparse(self.url).netloc in get_never_download_domains():
            logger.info(f"Skipping {self.url} because it's in the never download list")
            return None

        # Check if URL has been processed recently
        if self._check_recently_processed():
            return None

        # Prepare file path and create directories
        self._prepare_file_path()

        # Download the document
        if not self._download_document():
            return None

        # Check if document is already in database
        self._check_existing_document()

        # Process the document content
        if not self._process_content():
            return None

        # Store the document in the database
        return self._store_document()

    def _check_recently_processed(self) -> bool:
        """
        Check if the URL has been processed recently.

        Returns:
            bool: True if the URL has been processed recently, False otherwise
        """
        with get_bo_conn() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT url FROM ana_processed_urls WHERE url = %s AND retry IS NOT NULL AND retry > now()",
                    (self.url,)
                )
                fetchone = cursor.fetchone()

                if not self.force_accept and fetchone is not None:
                    logger.info(f"Skipping {fetchone} because it's been processed recently")
                    return True
        return False

    @abstractmethod
    def _prepare_file_path(self) -> None:
        """
        Prepare the file path for the document.
        """
        pass

    @abstractmethod
    def _download_document(self) -> bool:
        """
        Download the document.

        Returns:
            bool: True if download was successful, False otherwise
        """
        pass

    def _check_existing_document(self) -> None:
        """
        Check if the document already exists in the database.
        """
        with get_bo_conn() as conn:
            with conn.cursor() as cursor:
                # Check if document exists by URL or filename
                cursor.execute("SELECT id FROM kg_documents WHERE url = %s OR name = %s",
                              (self.url, self.filename))
                reports = cursor.fetchall()
                if reports:
                    self.doc_id = reports[0][0]

                # Check if document exists by hash and is already processed
                if self.file_hash:
                    cursor.execute(
                        "SELECT id, hash FROM kg_documents WHERE hash = %s AND last_processed IS NOT NULL",
                        (self.file_hash,)
                    )
                    reports = cursor.fetchall()
                    if reports:
                        if not self.force_accept:
                            logger.info(f"Skipping {self.url} it's already in the database and marked as processed.")
                            self._mark_processed(conn, True)
                            self.doc_id = reports[0][0]
                        else:
                            logger.info(f"Reprocessing {self.url} because force_accept is set")
                            cursor.execute("UPDATE kg_documents SET last_processed = NULL WHERE hash = %s",
                                          (self.file_hash,))
                            conn.commit()

    @abstractmethod
    def _process_content(self) -> bool:
        """
        Process the document content.

        Returns:
            bool: True if processing was successful, False otherwise
        """
        pass

    @abstractmethod
    def _store_document(self) -> Optional[int]:
        """
        Store the document in the database.

        Returns:
            Optional[int]: Document ID if storage was successful, None otherwise
        """
        pass

    def _mark_processed(self, conn, errored: bool = False, retry_interval: str = '30 day') -> None:
        """
        Mark the URL as processed in the database.

        Args:
            conn: Database connection
            errored: Whether an error occurred during processing
            retry_interval: Interval before the URL can be processed again
        """
        with conn.cursor() as cursor:
            cursor.execute(
                f"INSERT INTO ana_processed_urls (url, errored, retry) VALUES (%s, %s, now() + interval '{retry_interval}') "
                "ON CONFLICT(url) DO UPDATE SET created_at = now(), retry = excluded.retry",
                (self.url, errored)
            )
            conn.commit()

    def is_entity_mentioned(self, text: str) -> bool:
        """
        Check if the entity is mentioned in the text.

        Args:
            text: Text to check

        Returns:
            bool: True if the entity is mentioned, False otherwise
        """
        if self.expanded_entity is not None:
            return is_virt_entity_mentioned(self.expanded_entity, text)
        elif self.name is not None:
            return is_company_mentioned(self.name, text)
        return True  # If no entity is specified, assume it's mentioned
