import traceback
import datetime

from psycopg.errors import UniqueViolation
from typing import Optional

from urllib.parse import urlparse

import os
from loguru import logger

from eko import eko_var_path
from eko.config import MIN_ARTICLE_LENGTH
from eko.db import get_bo_conn
from eko.domains.domain_categorizer import get_domain_info
from eko.nlp.util import to_kebab_case
from eko.pdf.main import get_filename_from_url, download_pdf, extract_text_from_file
from eko.scrape.doc_processor import DocumentProcessor
from eko.scrape.eko_reports import upload_file, store_chunks
from eko.util.hash import md5_hash_url, sha256_hash_file


class PDFProcessor(DocumentProcessor):
    """
    Processor for PDF documents.
    """

    def _prepare_file_path(self) -> None:
        """
        Prepare the file path for the PDF.
        """
        filename_from_url = get_filename_from_url(self.url)
        self.filename = f"{to_kebab_case(filename_from_url)[:60] if filename_from_url else 'file'}-{md5_hash_url(self.url)}.pdf"
        dir_path = os.path.join(eko_var_path, f"files/source_reports/url_hashed/{md5_hash_url(self.url)[:2]}")
        os.makedirs(dir_path, exist_ok=True)
        self.file_path = os.path.join(dir_path, self.filename)

    def _download_document(self) -> bool:
        """
        Download the PDF.

        Returns:
            bool: True if download was successful, False otherwise
        """
        if os.path.exists(self.file_path):
            logger.info(f"PDF {self.file_path} already exists locally. Skipping download.")
            self.file_hash = sha256_hash_file(self.file_path)
            return True

        downloaded = download_pdf(self.url, self.file_path)
        if downloaded is None:
            logger.info(f"Failed to download {self.url}")
            with get_bo_conn() as conn:
                self._mark_processed(conn, True, '1 day')
            return False

        # Calculate file hash
        self.file_hash = sha256_hash_file(self.file_path)
        return True

    def _process_content(self) -> bool:
        """
        Process the PDF content.

        Returns:
            bool: True if processing was successful, False otherwise
        """
        # Extract text without cleaning for initial check
        try:
            rough_text_array = extract_text_from_file(self.file_path, clean_text=False)
        except Exception as e:
            logger.error(f"Error extracting text from PDF {self.url}: {str(e)}")
            if os.path.exists(self.file_path):
                os.remove(self.file_path)
            return False

        rough_combined_text = " ".join(rough_text_array)
        self.raw_text = rough_combined_text

        # Check if entity is mentioned in the PDF
        if not self.force_accept and 'disclosure' not in self.research_types and (self.name is not None or self.expanded_entity is not None):
            if not self.is_entity_mentioned(rough_combined_text[:5000]):
                logger.warning(f"Skipping {self.url} because it's probably not about {self.expanded_entity.title}")
                return False

        # Extract text with cleaning
        text_array = extract_text_from_file(self.file_path)
        text_array = ["" if x is None else x for x in text_array]
        combined_text = " ".join(text_array)
        self.clean_text = combined_text
        self.text_array = text_array

        # Check minimum length
        if len(combined_text) < MIN_ARTICLE_LENGTH:
            logger.info("PDF is too small to process")
            return False

        # Get domain info if not provided
        if self.origin_domain is None:
            try:
                domain_info = get_domain_info(urlparse(self.url).netloc)
                if domain_info:
                    self.origin_domain = domain_info.domain
                else:
                    self.origin_domain = urlparse(self.url).netloc
            except Exception as e:
                logger.warning(f"Error getting domain info for {self.url}: {str(e)}")
                self.origin_domain = urlparse(self.url).netloc

        return True

    def _store_document(self) -> Optional[int]:
        """
        Store the PDF in the database.

        Returns:
            Optional[int]: Document ID if storage was successful, None otherwise
        """
        with get_bo_conn() as conn:
            with conn.cursor() as cursor:
                try:
                    # Upload file to database
                    self.doc_id = upload_file(
                        cursor,
                        url=self.url,
                        path=self.file_path,
                        filename=self.filename,
                        text_hash=self.file_hash,
                        clean_text=self.clean_text,
                        rough_text=self.raw_text,
                        doc_id=self.doc_id,
                        file_type="pdf",
                        research_types=self.research_types,
                        # Pass current date for publish_date since it's required
                        publish_date=datetime.datetime.now(),
                        origin_domain=self.origin_domain,
                    )

                    # Store chunks
                    store_chunks(conn, self.doc_id, self.text_array, self.requester)

                    # Mark as processed
                    cursor.execute("UPDATE kg_documents SET last_processed=now() WHERE hash = %s", (self.file_hash,))
                    self._mark_processed(conn, False, '30 day')

                    return self.doc_id
                except UniqueViolation:
                    logger.info(f"Skipping {self.url} it's already in the database.")
                    conn.rollback()
                    self._mark_processed(conn, False, '30 day')
                    return self.doc_id
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Error processing PDF {self.url}: {str(e)}")
                    self._mark_processed(conn, True, '1 hour')
                    logger.exception(e)
                    traceback.print_exc()
                    if os.path.exists(self.file_path):
                        os.remove(self.file_path)
                    return None
