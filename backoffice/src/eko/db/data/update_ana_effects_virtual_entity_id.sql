-- Add virtual_entity_id column to ana_effects table if it doesn't exist
ALTER TABLE ana_effects ADD COLUMN IF NOT EXISTS virtual_entity_id INTEGER;

-- Create a temporary mapping table to store entity_name to virtual_entity_id mappings
CREATE TEMP TABLE entity_name_to_virtual_entity_id AS
SELECT name, id FROM kg_virt_entities;

-- Update ana_effects table with virtual_entity_id based on entity_name
UPDATE ana_effects ae
SET virtual_entity_id = m.id
FROM entity_name_to_virtual_entity_id m
WHERE ae.entity_name = m.name
AND ae.virtual_entity_id IS NULL;

-- Drop the temporary table
DROP TABLE entity_name_to_virtual_entity_id;

-- Create an index on virtual_entity_id for better query performance
CREATE INDEX IF NOT EXISTS idx_ana_effects_virtual_entity_id ON ana_effects(virtual_entity_id);
