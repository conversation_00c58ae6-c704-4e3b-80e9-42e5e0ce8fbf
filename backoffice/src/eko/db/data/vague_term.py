"""
Data Access Object (DAO) for vague term analysis.
"""
from typing import List, Optional, Dict, Any

from loguru import logger
from psycopg import Connection

from eko.db import get_bo_conn
from eko.models.vague import VagueTermModel, VagueTermSummaryModel


class VagueTermData:
    """
    Data Access Object for vague term analysis.
    """

    @staticmethod
    def create(conn: Connection, model: VagueTermModel) -> VagueTermModel:
        """
        Create a new vague term analysis.

        Args:
            conn: Database connection
            model: Vague term model to create

        Returns:
            Created vague term model with ID set
        """
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO ana_vague_v2 (
                    virt_entity_id, run_id, phrase, score, explanation,
                    analysis, summary, doc_chunk_ids, citations, rank
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (run_id, virt_entity_id, phrase)
                DO UPDATE SET
                    score = EXCLUDED.score,
                    explanation = EXCLUDED.explanation,
                    analysis = EXCLUDED.analysis,
                    summary = EXCLUDED.summary,
                    doc_chunk_ids = EXCLUDED.doc_chunk_ids,
                    citations = EXCLUDED.citations,
                    rank = EXCLUDED.rank
                RETURNING id, created_at
            """, (
                model.virt_entity_id,
                model.run_id,
                model.phrase,
                model.score,
                model.explanation,
                model.analysis,
                model.summary,
                model.doc_chunk_ids,
                [c.model_dump() for c in model.citations] if model.citations else [],
                model.rank
            ))

            result = cur.fetchone()
            if result:
                model.id = result[0]
                model.created_at = result[1]

            return model

    @staticmethod
    def get_by_id(conn: Connection, vague_term_id: int) -> Optional[VagueTermModel]:
        """
        Get a vague term analysis by ID.

        Args:
            conn: Database connection
            vague_term_id: ID of the vague term analysis

        Returns:
            Vague term model if found, None otherwise
        """
        with conn.cursor() as cur:
            cur.execute("""
                SELECT
                    id, virt_entity_id, run_id, phrase, score, explanation,
                    analysis, summary, doc_chunk_ids, citations, rank, created_at
                FROM ana_vague_v2
                WHERE id = %s
            """, (vague_term_id,))

            row = cur.fetchone()
            if not row:
                return None

            return VagueTermModel(
                id=row[0],
                virt_entity_id=row[1],
                run_id=row[2],
                phrase=row[3],
                score=row[4],
                explanation=row[5],
                analysis=row[6],
                summary=row[7],
                doc_chunk_ids=row[8],
                citations=row[9],
                rank=row[10],
                created_at=row[11]
            )

    @staticmethod
    def get_by_entity_and_run(conn: Connection, virt_entity_id: int, run_id: int) -> List[VagueTermModel]:
        """
        Get all vague term analyses for an entity and run.

        Args:
            conn: Database connection
            virt_entity_id: ID of the virtual entity
            run_id: ID of the analysis run

        Returns:
            List of vague term models
        """
        with conn.cursor() as cur:
            cur.execute("""
                SELECT
                    id, virt_entity_id, run_id, phrase, score, explanation,
                    analysis, summary, doc_chunk_ids, citations, rank, created_at
                FROM ana_vague_v2
                WHERE virt_entity_id = %s AND run_id = %s AND phrase != '__summary__'
                ORDER BY score DESC
            """, (virt_entity_id, run_id))

            rows = cur.fetchall()
            return [
                VagueTermModel(
                    id=row[0],
                    virt_entity_id=row[1],
                    run_id=row[2],
                    phrase=row[3],
                    score=row[4],
                    explanation=row[5],
                    analysis=row[6],
                    summary=row[7],
                    doc_chunk_ids=row[8],
                    citations=row[9],
                    rank=row[10],
                    created_at=row[11]
                )
                for row in rows
            ]

    @staticmethod
    def get_summary(conn: Connection, virt_entity_id: int, run_id: int) -> Optional[VagueTermSummaryModel]:
        """
        Get the summary of vague term analyses for an entity and run.

        Args:
            conn: Database connection
            virt_entity_id: ID of the virtual entity
            run_id: ID of the analysis run

        Returns:
            Summary model if found, None otherwise
        """
        with conn.cursor() as cur:
            cur.execute("""
                SELECT
                    id, virt_entity_id, run_id, phrase, score, explanation,
                    analysis, summary, doc_chunk_ids, citations, rank, created_at
                FROM ana_vague_v2
                WHERE virt_entity_id = %s AND run_id = %s AND phrase = '__summary__'
            """, (virt_entity_id, run_id))

            row = cur.fetchone()
            if not row:
                return None

            return VagueTermSummaryModel(
                id=row[0],
                virt_entity_id=row[1],
                run_id=row[2],
                phrase=row[3],
                score=row[4],
                explanation=row[5],
                analysis=row[6],
                summary=row[7],
                doc_chunk_ids=row[8],
                citations=row[9],
                rank=row[10],
                created_at=row[11]
            )

    @staticmethod
    def delete_by_run(conn: Connection, run_id: int) -> int:
        """
        Delete all vague term analyses for a run.

        Args:
            conn: Database connection
            run_id: ID of the analysis run

        Returns:
            Number of deleted analyses
        """
        with conn.cursor() as cur:
            cur.execute("DELETE FROM ana_vague_v2 WHERE run_id = %s RETURNING id", (run_id,))
            deleted = cur.fetchall()
            return len(deleted)
