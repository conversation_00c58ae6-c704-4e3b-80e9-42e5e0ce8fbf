"""
Sync functions for prediction-v2 data to xfer tables.
"""

from typing import Dict
from loguru import logger
from psycopg import Connection

from eko.db import get_cus_conn
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.prediction_v2.models import (
    PredictiveComponentAnalysisModel,
    ClusterAnalysisModel,
    EntityYearAnalysisModel
)


def sync_prediction_v2_to_xfer(conn: Connection, run_id: int,virtual_entity:VirtualEntityExpandedModel) -> Dict[str, int]:
    """
    Sync prediction-v2 data to the xfer tables for a specific run.

    Args:
        conn: Database connection
        run_id: ID of the analysis run

    Returns:
        Dictionary with counts of synced records by type
    """
    synced_counts = {
        "component": 0,
        "cluster": 0,
        "entity_year": 0
    }

    try:
        logger.info(f"Syncing prediction-v2 data to xfer tables for run {run_id}")
        virtual_entity_id = virtual_entity.id
        entity_xid = virtual_entity.short_id

        # Sync component analyses
        synced_counts["component"] = _sync_component_analyses(conn, run_id, virtual_entity_id, entity_xid)

        # Sync cluster analyses
        synced_counts["cluster"] = _sync_cluster_analyses(conn, run_id, virtual_entity_id, entity_xid)

        # Sync entity-year analyses
        synced_counts["entity_year"] = _sync_entity_year_analyses(conn, run_id, virtual_entity_id, entity_xid)

        logger.info(f"Successfully synced prediction-v2 data to xfer tables: {synced_counts}")

    except Exception as e:
        logger.error(f"Error syncing prediction-v2 data to xfer tables for run {run_id}: {e}")
        logger.exception(e)

    return synced_counts


def _sync_component_analyses(conn: Connection, run_id: int, virtual_entity_id: int, entity_xid: str) -> int:
    """
    Sync component analyses to the xfer_predict_component table.

    Args:
        conn: Database connection
        run_id: Run ID
        virtual_entity_id: Virtual entity ID
        entity_xid: Entity short ID

    Returns:
        Number of records synced
    """
    synced_count = 0

    try:
        # Get all component analyses for this run
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM ana_predict_component_analysis
                WHERE run_id = %s AND virtual_entity_id = %s
            """, (run_id, virtual_entity_id))

            rows = cursor.fetchall()

            if not rows:
                logger.info(f"No component analyses found for run {run_id}, virtual entity {virtual_entity_id}")
                return 0

            # Process each component analysis
            for row in rows:
                # Create a model from the row
                analysis = PredictiveComponentAnalysisModel(
                    id=row[0],
                    run_id=row[1],
                    prediction_id=row[2],
                    virtual_entity_id=row[3],
                    cluster_id=row[4],
                    component_type=row[5],
                    year=row[6],
                    summary=row[7],
                    detailed_analysis=row[8],
                    potential_risks=row[9],
                    potential_opportunities=row[10],
                    confidence=row[11],
                    created_at=row[12]
                )

                # Convert to JSON for storage
                model_json = analysis.model_dump_json()

                # Sync to customer database
                with get_cus_conn() as cus_conn:
                    with cus_conn.cursor() as cus_cursor:
                        # First delete any existing record
                        cus_cursor.execute("""
                            DELETE FROM xfer_predict_component
                            WHERE entity_xid = %s AND run_id = %s AND cluster_id = %s
                            AND component_type = %s AND year = %s
                        """, (
                            entity_xid,
                            run_id,
                            analysis.cluster_id,
                            analysis.component_type,
                            analysis.year
                        ))

                        # Insert the new record
                        cus_cursor.execute("""
                            INSERT INTO xfer_predict_component (
                                entity_xid, run_id, cluster_id, component_type, year, model
                            ) VALUES (%s, %s, %s, %s, %s, %s)
                            RETURNING id
                        """, (
                            entity_xid,
                            run_id,
                            analysis.cluster_id,
                            analysis.component_type,
                            analysis.year,
                            model_json
                        ))

                        result = cus_cursor.fetchone()
                        if result:
                            synced_count += 1
                            logger.debug(f"Synced component analysis {result[0]} to xfer_predict_component table")

                    cus_conn.commit()

            logger.info(f"Successfully synced {synced_count} component analyses to xfer_predict_component table")

    except Exception as e:
        logger.error(f"Error syncing component analyses to xfer_predict_component: {e}")
        logger.exception(e)

    return synced_count


def _sync_cluster_analyses(conn: Connection, run_id: int, virtual_entity_id: int, entity_xid: str) -> int:
    """
    Sync cluster analyses to the xfer_predict_cluster table.

    Args:
        conn: Database connection
        run_id: Run ID
        virtual_entity_id: Virtual entity ID
        entity_xid: Entity short ID

    Returns:
        Number of records synced
    """
    synced_count = 0

    try:
        # Get all cluster analyses for this run
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM ana_predict_cluster_analysis
                WHERE run_id = %s AND virtual_entity_id = %s
            """, (run_id, virtual_entity_id))

            rows = cursor.fetchall()

            if not rows:
                logger.info(f"No cluster analyses found for run {run_id}, virtual entity {virtual_entity_id}")
                return 0

            # Process each cluster analysis
            for row in rows:
                # Create a model from the row
                analysis = ClusterAnalysisModel(
                    id=row[0],
                    run_id=row[1],
                    virtual_entity_id=row[2],
                    cluster_id=row[3],
                    year=row[4],
                    summary=row[5],
                    detailed_analysis=row[6],
                    motivation_summary=row[7],
                    statement_type_summary=row[8],
                    engagement_summary=row[9],
                    impact_summary=row[10],
                    potential_risks=row[11],
                    potential_opportunities=row[12],
                    confidence=row[13],
                    created_at=row[14]
                )

                # Convert to JSON for storage
                model_json = analysis.model_dump_json()

                # Sync to customer database
                with get_cus_conn() as cus_conn:
                    with cus_conn.cursor() as cus_cursor:
                        # First delete any existing record
                        cus_cursor.execute("""
                            DELETE FROM xfer_predict_cluster
                            WHERE entity_xid = %s AND run_id = %s AND cluster_id = %s AND year = %s
                        """, (
                            entity_xid,
                            run_id,
                            analysis.cluster_id,
                            analysis.year
                        ))

                        # Insert the new record
                        cus_cursor.execute("""
                            INSERT INTO xfer_predict_cluster (
                                entity_xid, run_id, cluster_id, year, model
                            ) VALUES (%s, %s, %s, %s, %s)
                            RETURNING id
                        """, (
                            entity_xid,
                            run_id,
                            analysis.cluster_id,
                            analysis.year,
                            model_json
                        ))

                        result = cus_cursor.fetchone()
                        if result:
                            synced_count += 1
                            logger.debug(f"Synced cluster analysis {result[0]} to xfer_predict_cluster table")

                    cus_conn.commit()

            logger.info(f"Successfully synced {synced_count} cluster analyses to xfer_predict_cluster table")

    except Exception as e:
        logger.error(f"Error syncing cluster analyses to xfer_predict_cluster: {e}")
        logger.exception(e)

    return synced_count


def _sync_entity_year_analyses(conn: Connection, run_id: int, virtual_entity_id: int, entity_xid: str) -> int:
    """
    Sync entity-year analyses to the xfer_predict_entity_year table.

    Args:
        conn: Database connection
        run_id: Run ID
        virtual_entity_id: Virtual entity ID
        entity_xid: Entity short ID

    Returns:
        Number of records synced
    """
    synced_count = 0

    try:
        # Get all entity-year analyses for this run
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM ana_predict_entity_year_analysis
                WHERE run_id = %s AND virtual_entity_id = %s
            """, (run_id, virtual_entity_id))

            rows = cursor.fetchall()

            if not rows:
                logger.info(f"No entity-year analyses found for run {run_id}, virtual entity {virtual_entity_id}")
                return 0

            # Process each entity-year analysis
            for row in rows:
                # Create a model from the row
                analysis = EntityYearAnalysisModel(
                    id=row[0],
                    run_id=row[1],
                    virtual_entity_id=row[2],
                    year=row[3],
                    summary=row[4],
                    detailed_analysis=row[5],
                    cluster_summaries=row[6],
                    potential_risks=row[7],
                    potential_opportunities=row[8],
                    confidence=row[9],
                    created_at=row[10]
                )

                # Convert to JSON for storage
                model_json = analysis.model_dump_json()

                # Sync to customer database
                with get_cus_conn() as cus_conn:
                    with cus_conn.cursor() as cus_cursor:
                        # First delete any existing record
                        cus_cursor.execute("""
                            DELETE FROM xfer_predict_entity_year
                            WHERE entity_xid = %s AND run_id = %s AND year = %s
                        """, (
                            entity_xid,
                            run_id,
                            analysis.year
                        ))

                        # Insert the new record
                        cus_cursor.execute("""
                            INSERT INTO xfer_predict_entity_year (
                                entity_xid, run_id, year, model
                            ) VALUES (%s, %s, %s, %s)
                            RETURNING id
                        """, (
                            entity_xid,
                            run_id,
                            analysis.year,
                            model_json
                        ))

                        result = cus_cursor.fetchone()
                        if result:
                            synced_count += 1
                            logger.debug(f"Synced entity-year analysis {result[0]} to xfer_predict_entity_year table")

                    cus_conn.commit()

            logger.info(f"Successfully synced {synced_count} entity-year analyses to xfer_predict_entity_year table")

    except Exception as e:
        logger.error(f"Error syncing entity-year analyses to xfer_predict_entity_year: {e}")
        logger.exception(e)

    return synced_count