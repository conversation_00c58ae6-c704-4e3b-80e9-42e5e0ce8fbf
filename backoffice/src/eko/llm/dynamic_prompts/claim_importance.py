"""
Prompt functions for scoring claim importance.
"""


def create_claim_importance_prompt(claim_text: str, company_name: str = "", context: str = "") -> list[dict]:
    """
    Create a prompt for scoring the importance of a claim.
    
    Args:
        claim_text: The text of the claim to score
        company_name: Name of the company making the claim
        context: Additional context about the claim
        
    Returns:
        List of message dictionaries for LLM
    """
    return [
        {
            "role": "system",
            "content": "You are an API. You are not conversational. You can only provide an importance rating as a single integer number from 0 to 100 for a given claim."
        },
        {
            "role": "user", 
            "content": f"""
Please rate from 0 to 100 the importance of the supplied claim. 

IMPORTANCE SCORING GUIDELINES:

The importance score reflects how newsworthy AND impactful the claim is:

0-10: Administrative/procedural claims with no public interest
- Examples: "This report was dated '25/01/2001'", "<PERSON> is our MD", "We filed our annual report"

11-30: Minor operational claims with limited impact
- Examples: "We updated our website", "We hired 5 new employees", "We moved offices"

31-50: Moderate business claims with some stakeholder interest  
- Examples: "We increased revenue by 10%", "We launched a new product line", "We expanded to 3 new markets"

51-70: Significant ESG/sustainability claims with notable impact
- Examples: "We reduced carbon emissions by 25%", "We achieved gender pay equity", "We eliminated single-use plastics"

71-90: Major ESG achievements or workforce issues with high public interest
- Examples: "We achieved carbon neutrality", "We ended all child labor in our supply chain", "We became a B-Corp"

91-100: Extraordinary global impact claims that would be front-page news
- Examples: "We have ended poverty in the world", "We solved climate change", "We cured cancer"

IMPORTANT FACTORS:
- Green credentials and environmental claims score higher
- Workforce and social impact issues score higher  
- Third-party verified claims score higher than self-reported claims
- Claims with measurable, specific outcomes score higher than vague statements
- Claims affecting large numbers of people score higher

Company: {company_name}
Context: {context}

Please provide a single number from 0-100 for the importance of this claim:

{claim_text}
"""
        }
    ]
