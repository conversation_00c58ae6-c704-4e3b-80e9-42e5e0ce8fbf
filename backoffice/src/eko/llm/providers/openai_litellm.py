import os
from typing import Dict, List, Type, TypeVar, Union, Any

import instructor
from loguru import logger
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.litellm_provider import LiteLLMProvider
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

class OpenAILiteLLMProvider(LiteLLMProvider):
    """
    OpenAI provider implementation using litellm.
    
    This provider is configured specifically for OpenAI models and handles
    authentication and model mapping for OpenAI's models.
    """
    
    def __init__(self, 
                 api_key: str = None, 
                 base_url: str = None,
                 use_instructor: bool = True,
                 instructor_mode=instructor.Mode.TOOLS):
        """
        Initialize the OpenAI provider.
        
        Args:
            api_key: API key for OpenAI (defaults to environment variable)
            base_url: Base URL for OpenAI API (defaults to standard OpenAI API)
            use_instructor: Whether to use instructor for structured outputs
            instructor_mode: Mode to use with instructor
        """
        # Get API key from environment if not provided
        if api_key is None:
            api_key = os.environ.get("OPENAI_API_KEY")
            
        super().__init__(
            model_prefix=None,  # OpenAI models don't need a prefix
            api_key=api_key,
            base_url=base_url,
            use_instructor=use_instructor,
            instructor_mode=instructor_mode
        )
        
        logger.info(f"Initialized OpenAI provider with litellm")
        
    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the model name for OpenAI.
        
        Args:
            llm: The LLM model to use
            
        Returns:
            The model name for OpenAI
        """
        return llm.value.name


class OSeriesLiteLLMProvider(OpenAILiteLLMProvider):
    """
    OpenAI O-Series provider implementation using litellm.
    
    This provider is configured specifically for OpenAI's O-Series models.
    """
    
    def __init__(self):
        """
        Initialize the O-Series provider.
        """
        super().__init__(
            use_instructor=True,
            instructor_mode=instructor.Mode.TOOLS
        )
        
        logger.info("Initialized OpenAI O-Series provider with litellm")
