import os
from typing import Dict, List, Type, TypeVar, Union, Any

import instructor
from loguru import logger
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.litellm_provider import Li<PERSON><PERSON><PERSON>rovider
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

class OpenAICompatibleLiteLLMProvider(LiteLLMProvider):
    """
    OpenAI-compatible provider implementation using litellm.
    
    This provider is configured for services that implement the OpenAI API interface,
    such as DeepInfra, DeepSeek, Hyperbolic, etc.
    """
    
    def __init__(self, 
                 api_key: str,
                 base_url: str,
                 provider_name: str = "custom",
                 model_prefix: str = None,
                 use_instructor: bool = True,
                 instructor_mode=instructor.Mode.JSON):
        """
        Initialize the OpenAI-compatible provider.
        
        Args:
            api_key: API key for the provider
            base_url: Base URL for the provider's API
            provider_name: Name of the provider (for logging)
            model_prefix: Optional prefix to add to model names
            use_instructor: Whether to use instructor for structured outputs
            instructor_mode: Mode to use with instructor
        """
        super().__init__(
            model_prefix=model_prefix,
            api_key=api_key,
            base_url=base_url,
            use_instructor=use_instructor,
            instructor_mode=instructor_mode
        )
        
        self.provider_name = provider_name
        
        logger.info(f"Initialized {provider_name} provider with litellm using base URL {base_url}")
        
    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the model name for the provider.
        
        Args:
            llm: The LLM model to use
            
        Returns:
            The model name with the appropriate prefix if needed
        """
        model_name = llm.value.name
        
        # Add prefix if specified and not already present
        if self.model_prefix and not model_name.startswith(self.model_prefix):
            return f"{self.model_prefix}{model_name}"
        
        return model_name
