import os
from typing import Dict, List, Type, TypeVar, Union, Any

import instructor
from loguru import logger
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.litellm_provider import LiteLLMProvider
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

class VertexProvider(LiteLLMProvider):
    """
    Specialized provider for Google Vertex AI models using litellm.
    
    This provider is configured specifically for Vertex AI and handles the
    authentication and model mapping for Google's models.
    """
    
    def __init__(self, project_id="92299100527", location="us-central1", base_model="vertex/gemini-2.0-flash-001"):
        """
        Initialize the Vertex AI provider.
        
        Args:
            project_id: Google Cloud project ID
            location: Google Cloud location
        """
        super().__init__(
            model_prefix="vertex_ai/",
            vertex_project_id=project_id,
            vertex_location=location,
            use_instructor=True,
            instructor_mode=instructor.Mode.JSON
        )
        
        self.project_id = project_id
        self.location = location
        
        # Ensure authentication is set up
        # This will use the application default credentials from gcloud auth
        # Make sure the user has run `gcloud auth application-default login` first
        logger.info(f"Initializing Vertex AI provider with project {project_id} in {location}")
        
        # Set environment variables for litellm
        os.environ["VERTEX_PROJECT"] = project_id
        os.environ["VERTEX_LOCATION"] = location
        
    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the full model name with Vertex AI prefix.
        
        For custom fine-tuned models, we use the endpoint ID directly.
        For standard models, we use the model name with the vertex_ai/ prefix.
        
        Args:
            llm: The LLM model to use
            
        Returns:
            The full model name for Vertex AI
        """
        model_name = llm.value.name
        
        # For custom fine-tuned models, use the endpoint ID directly
        if model_name.isdigit():
            return f"vertex_ai/gemini/{model_name}"

        # For standard models, use the model name with prefix
        return f"vertex_ai/{model_name}"
        
    def call_chat_with_tools(self, llm: LLMModel, messages: List[Dict], tools: List[Dict],
                           max_tokens: int = 4000, temperature=0.0, metadata: dict = {}) -> Any:
        """
        Call the Vertex AI model with tools (function calling).
        
        Note: As of now, Vertex AI models have limited support for function calling
        compared to OpenAI. This implementation will be updated as Vertex AI
        adds more support for function calling.
        
        Args:
            llm: The LLM model to use
            messages: List of chat messages
            tools: List of tool definitions
            max_tokens: Maximum tokens to generate
            temperature: Temperature for sampling
            metadata: Additional metadata
            
        Returns:
            The final response from the model after tool interactions
        """
        # For now, Vertex AI custom models don't fully support function calling
        # in the same way as OpenAI. Future implementation could include a
        # similar tool loop as other providers.
        logger.warning("Function calling with Vertex AI is limited. Using standard chat completion instead.")
        
        # Fall back to regular chat completion
        return self.call_chat(llm, messages, max_tokens, response_model=None, temperature=temperature, metadata=metadata)
