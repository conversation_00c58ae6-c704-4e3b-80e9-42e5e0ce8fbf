import os
from typing import Dict, List, Type, TypeVar, Union, Any

import instructor
from loguru import logger
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.litellm_provider import Li<PERSON><PERSON><PERSON>rovider
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

class RunPodLiteLLMProvider(LiteLLMProvider):
    """
    RunPod provider implementation using litellm.
    
    This provider is configured specifically for RunPod endpoints and handles
    authentication and model mapping for RunPod's models.
    """
    
    def __init__(self, 
                 api_key: str = None,
                 endpoint_id: str = None,
                 use_instructor: bool = True,
                 instructor_mode=instructor.Mode.JSON):
        """
        Initialize the RunPod provider.
        
        Args:
            api_key: API key for RunPod (defaults to environment variable)
            endpoint_id: RunPod endpoint ID
            use_instructor: Whether to use instructor for structured outputs
            instructor_mode: Mode to use with instructor
        """
        # Get API key and endpoint ID from environment if not provided
        if api_key is None:
            api_key = os.environ.get("RUNPOD_API_KEY")
        
        if endpoint_id is None:
            endpoint_id = os.environ.get("RUNPOD_ENDPOINT_ID")
            
        if endpoint_id is None:
            raise ValueError("RunPod endpoint ID must be provided")
            
        # Format the model name with the endpoint ID
        model_prefix = f"runpod/{endpoint_id}/"
            
        super().__init__(
            model_prefix=model_prefix,
            api_key=api_key,
            use_instructor=use_instructor,
            instructor_mode=instructor_mode
        )
        
        self.endpoint_id = endpoint_id
        
        logger.info(f"Initialized RunPod provider with litellm for endpoint {endpoint_id}")
        
    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the model name for RunPod.
        
        For RunPod, we use the endpoint ID as the model identifier.
        
        Args:
            llm: The LLM model to use
            
        Returns:
            The model name for RunPod with the appropriate prefix
        """
        # For RunPod, we use the endpoint ID as the model identifier
        return f"runpod/{self.endpoint_id}/{llm.value.name}"
