import os
from typing import Dict, List, Type, TypeVar, Union, Any

import instructor
from loguru import logger
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.litellm_provider import Li<PERSON><PERSON><PERSON>rovider
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

class DeepSeekLiteLLMProvider(LiteLLMProvider):
    """
    DeepSeek provider implementation using litellm.
    
    This provider is configured specifically for DeepSeek models and handles
    authentication and model mapping for DeepSeek's models.
    """
    
    def __init__(self, 
                 api_key: str = "sk-089aea78c132484fa2e02823e51eded8",
                 base_url: str = "https://api.deepseek.com",
                 use_instructor: bool = True,
                 instructor_mode=instructor.Mode.JSON):
        """
        Initialize the DeepSeek provider.
        
        Args:
            api_key: API key for DeepSeek (defaults to hardcoded key)
            base_url: Base URL for DeepSeek API (defaults to standard DeepSeek API)
            use_instructor: Whether to use instructor for structured outputs
            instructor_mode: Mode to use with instructor
        """
        super().__init__(
            model_prefix="deepseek/",  # DeepSeek models use the deepseek/ prefix in litellm
            api_key=api_key,
            base_url=base_url,
            use_instructor=use_instructor,
            instructor_mode=instructor_mode
        )
        
        logger.info(f"Initialized DeepSeek provider with litellm using base URL {base_url}")
        
    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the model name for DeepSeek.
        
        Args:
            llm: The LLM model to use
            
        Returns:
            The model name with the appropriate prefix if needed
        """
        model_name = llm.value.name
        
        # Add prefix if not already present
        if not model_name.startswith("deepseek/"):
            return f"deepseek/{model_name}"
        
        return model_name
