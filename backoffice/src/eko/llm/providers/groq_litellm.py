import os
from typing import Dict, List, Type, TypeVar, Union, Any

import instructor
from loguru import logger
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.litellm_provider import LiteLLMProvider
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

class GroqLiteLLMProvider(LiteLLMProvider):
    """
    Groq provider implementation using litellm.
    
    This provider is configured specifically for Groq models and handles
    authentication and model mapping for Groq's models.
    """
    
    def __init__(self, 
                 api_key: str = None,
                 use_instructor: bool = True,
                 instructor_mode=instructor.Mode.JSON):
        """
        Initialize the Groq provider.
        
        Args:
            api_key: API key for Groq (defaults to environment variable)
            use_instructor: Whether to use instructor for structured outputs
            instructor_mode: Mode to use with instructor
        """
        # Get API key from environment if not provided
        if api_key is None:
            api_key = os.environ.get("GROQ_API_KEY")
            
        super().__init__(
            model_prefix="groq/",  # Groq models use the groq/ prefix in litellm
            api_key=api_key,
            use_instructor=use_instructor,
            instructor_mode=instructor_mode
        )
        
        logger.info(f"Initialized Groq provider with litellm")
        
    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the model name for Groq.
        
        Args:
            llm: The LLM model to use
            
        Returns:
            The model name for Groq with the appropriate prefix
        """
        model_name = llm.value.name
        
        # Add prefix if not already present
        if not model_name.startswith("groq/"):
            return f"groq/{model_name}"
        
        return model_name
