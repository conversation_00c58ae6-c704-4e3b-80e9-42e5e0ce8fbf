import os
from instructor import Instructor
from litellm import completion
from typing import Dict, List, Type, TypeVar, Union, Any, cast

import instructor
import litellm
from loguru import logger
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.litellm_provider import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from eko.settings import settings
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

class GeminiLiteLLMProvider(LiteLLMProvider):
    """
    Gemini provider implementation using litellm.

    This provider is configured specifically for Google's Gemini models and handles
    authentication and model mapping for Gemini models through LiteLLM.
    """

    def __init__(self, api_key: str, use_instructor: bool = True, instructor_mode=instructor.Mode.GEMINI_JSON):
        """
        Initialize the Gemini provider.

        Args:
            api_key: API key for Gemini (defaults to environment variable)
            use_instructor: Whether to use instructor for structured outputs
            instructor_mode: Mode to use with instructor
        """
        # Get API key from environment if not provided
        if api_key is None:
            api_key = os.environ.get("GOOGLE_GENERATIVE_AI_API_KEY", "AIzaSyCffot1AZ2_Jgbrjbo60kLQvFnpKbK5_vE")

        super().__init__(
            model_prefix="gemini/",  # Gemini models use the gemini/ prefix in litellm
            api_key=api_key,
            use_instructor=use_instructor,
            instructor_mode=instructor_mode
        )

        logger.info("Initialized Gemini provider with litellm")

    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the model name for Gemini.

        Args:
            llm: The LLM model to use

        Returns:
            The model name for Gemini with the appropriate prefix
        """
        model_name = llm.value.name

        # Add prefix if not already present
        if not model_name.startswith("gemini/"):
            return f"gemini/{model_name}"

        return model_name

    def call_chat(self, llm: LLMModel, messages: List[Dict], max_tokens: int = 4000,
                  response_model: Union[None, Type[T]] = None, temperature=0.0,
                  metadata: dict = {}) -> Union[str, T]:
        """
        Call the Gemini model with chat messages and return response.

        This method overrides the base LiteLLMProvider implementation to handle
        Gemini-specific response structures.

        Args:
            llm: The LLM model to use
            messages: List of chat messages
            max_tokens: Maximum tokens in the response
            response_model: Optional Pydantic model for response parsing
            temperature: Temperature for sampling
            metadata: Additional metadata

        Returns:
            A string response or Pydantic model instance
        """
        try:
            # Clean messages by removing cache control
            messages = self.remove_cache_control(messages)
            model_name = self.get_model_name(llm)

            # Prepare additional parameters
            params = {
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
            }

            # Add API key and base URL if provided
            if self.api_key:
                params["api_key"] = self.api_key
            if self.base_url:
                params["api_base"] = self.base_url

            logger.info(f"Calling {model_name} with {len(messages)} messages")

            if response_model is None:
                # Get raw text response
                response = litellm.completion(**params, model=model_name)

                # Safely extract content from the response
                try:
                    # First try the standard OpenAI format
                    if hasattr(response, 'choices') and len(response.choices) > 0:
                        answer = response.choices[0].message.content
                    # If that fails, try Gemini-specific format
                    elif hasattr(response, 'content'):
                        answer = response.content
                    elif hasattr(response, 'text'):
                        answer = response.text
                    else:
                        # Last resort: convert to string and return
                        logger.warning(f"Unusual response structure from Gemini: {response}")
                        answer = str(response)
                except Exception as content_error:
                    logger.error(f"Error extracting content from Gemini response: {content_error}")
                    logger.error(f"Response structure: {response}")
                    # Return a safe fallback
                    answer = "Error: Unable to extract response content from Gemini"

                # Try to display cost, but don't fail if it doesn't work
                try:
                    self.display_cost(model_name, response, messages)
                except Exception as cost_error:
                    logger.warning(f"Unable to calculate cost for Gemini response: {cost_error}")

                logger.debug(f"Text based answer from Gemini: {answer}")
                return cast(str, answer)
            else:
                # Get structured response with instructor
                if self.use_instructor:
                    client = instructor.from_litellm(completion, mode=self.instructor_mode)
                    response, raw_completion = client.chat.completions.create_with_completion(
                        response_model=response_model,
                        model=model_name,
                        **params
                    )
                else:
                    raise NotImplementedError("Structured output with instructor not supported for this provider")


                # Try to display cost, but don't fail if it doesn't work
                try:
                    self.display_cost(model_name, response, messages, raw_completion)
                except Exception as cost_error:
                    logger.exception(cost_error)
                    logger.warning(f"Unable to calculate cost for Gemini response: {cost_error}")

                return cast(T, response)

        except Exception as e:
            logger.error(f"Error calling Gemini with LiteLLM: {e}")
            logger.exception(e)
            raise e

    def call_chat_with_tools(self, llm: LLMModel, messages: List[Dict], tools: List[Dict],
                           max_tokens: int = 4000, temperature=0.0, metadata: dict = {}) -> Any:
        """
        Call the Gemini model with tools (function calling).

        Note: Gemini models have their own implementation of function calling
        which is handled by litellm.

        Args:
            llm: The LLM model to use
            messages: List of chat messages
            tools: List of tool definitions
            max_tokens: Maximum tokens to generate
            temperature: Temperature for sampling
            metadata: Additional metadata

        Returns:
            The final response from the model after tool interactions
        """
        # Use the standard implementation from LiteLLMProvider
        return super().call_chat_with_tools(
            llm=llm,
            messages=messages,
            tools=tools,
            max_tokens=max_tokens,
            temperature=temperature,
            metadata=metadata
        )
