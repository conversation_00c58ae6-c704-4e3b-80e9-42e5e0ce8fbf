import os
from typing import Dict, List, Type, TypeVar, Union, Any

import instructor
import litellm
from loguru import logger
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.litellm_provider import <PERSON><PERSON><PERSON><PERSON>rovider
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

class AnthropicLiteLLMProvider(LiteLLMProvider):
    """
    Anthropic provider implementation using litellm.

    This provider is configured specifically for Anthropic Claude models and handles
    authentication and model mapping for Anthropic's models.
    """

    def __init__(self,
                 api_key: str = None,
                 use_instructor: bool = True,
                 instructor_mode=instructor.Mode.JSON):
        """
        Initialize the Anthropic provider.

        Args:
            api_key: API key for Anthropic (defaults to environment variable)
            use_instructor: Whether to use instructor for structured outputs
            instructor_mode: Mode to use with instructor
        """
        # Get API key from environment if not provided
        if api_key is None:
            api_key = os.environ.get("ANTHROPIC_API_KEY")

        # Set litellm.modify_params to True to enable tool calling with Anthropic
        # This adds a dummy tool to the request when needed
        litellm.modify_params = True

        super().__init__(
            model_prefix="anthropic/",  # Anthropic models use the anthropic/ prefix in litellm
            api_key=api_key,
            use_instructor=use_instructor,
            instructor_mode=instructor_mode
        )

        logger.info("Initialized Anthropic provider with litellm (modify_params=True)")

    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the model name for Anthropic.

        Args:
            llm: The LLM model to use

        Returns:
            The model name for Anthropic with the appropriate prefix
        """
        model_name = llm.value.name

        # Add prefix if not already present
        if not model_name.startswith("anthropic/"):
            return f"anthropic/{model_name}"

        return model_name
