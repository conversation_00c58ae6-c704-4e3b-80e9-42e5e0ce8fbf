import os
from typing import Dict, List, Type, TypeVar, Union, Any

import instructor
from loguru import logger
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.litellm_provider import LiteLLMProvider
from eko.typing import not_none

T = TypeVar('T', bound=BaseModel)

class OllamaLiteLLMProvider(LiteLLMProvider):
    """
    Ollama provider implementation using litellm.
    
    This provider is configured specifically for Ollama models and handles
    authentication and model mapping for Ollama's models.
    """
    
    def __init__(self, 
                 base_url: str = "http://136.243.58.43:11434/v1",
                 use_instructor: bool = True,
                 instructor_mode=instructor.Mode.JSON):
        """
        Initialize the Ollama provider.
        
        Args:
            base_url: Base URL for Ollama API
            use_instructor: Whether to use instructor for structured outputs
            instructor_mode: Mode to use with instructor
        """
        super().__init__(
            model_prefix="ollama/",  # Ollama models use the ollama/ prefix in litellm
            api_key="ollama",  # Ollama doesn't use an API key, but litellm expects one
            base_url=base_url,
            use_instructor=use_instructor,
            instructor_mode=instructor_mode
        )
        
        logger.info(f"Initialized Ollama provider with litellm using base URL {base_url}")
        
    def get_model_name(self, llm: LLMModel) -> str:
        """
        Get the model name for Ollama.
        
        Args:
            llm: The LLM model to use
            
        Returns:
            The model name for Ollama with the appropriate prefix
        """
        model_name = llm.value.name
        
        # Add prefix if not already present
        if not model_name.startswith("ollama/"):
            return f"ollama/{model_name}"
        
        return model_name
