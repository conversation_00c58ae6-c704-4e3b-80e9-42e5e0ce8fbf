from pydantic import BaseModel

from eko.domains.domain_categorizer import DomainInfo
from eko.llm import PROMPT_NOT_CONVERSATIONAL, LLMModel, call_llms
from eko.llm.main import call_llms_typed, LLMOptions
from eko.llm.prompts import prompt
from eko.models import Report
import datetime

class TestModel(BaseModel):
    test: str

options = LLMOptions()
response= call_llms([LLMModel.GEMINI_FLASH_LITE_FINETUNED], prompt(PROMPT_NOT_CONVERSATIONAL, "Please return a test value, btw the date is "+str(datetime.datetime.now())), 8000, response_model=DomainInfo, options=options)
response= call_llms([LLMModel.GEMINI_FLASH], prompt(PROMPT_NOT_CONVERSATIONAL, "Please return a test value, btw the date today is "+str(datetime.datetime.now())), 8000, response_model=DomainInfo, options=options)
print(response)
