"""
Data models for the web agent.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Set

from pydantic import BaseModel, Field


class ContentType(str, Enum):
    """Types of content the agent can process."""
    WEBPAGE = "webpage"
    PDF = "pdf"
    ARTICLE = "article"
    REPORT = "report"
    SOCIAL_MEDIA = "social_media"
    OTHER = "other"


class RelevanceScore(int, Enum):
    """Relevance score for content."""
    NOT_RELEVANT = 0
    LOW_RELEVANCE = 1
    MEDIUM_RELEVANCE = 2
    HIGH_RELEVANCE = 3
    VERY_HIGH_RELEVANCE = 4


class ESGCategory(str, Enum):
    """ESG categories for classifying information."""
    ENVIRONMENTAL = "environmental"
    SOCIAL = "social"
    GOVERNANCE = "governance"
    GENERAL = "general"
    OTHER = "other"


class BehaviorType(str, Enum):
    """Types of company behavior."""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    MIXED = "mixed"
    UNKNOWN = "unknown"


class PageContent(BaseModel):
    """Content extracted from a webpage or document."""
    url: str
    title: Optional[str] = None
    content_type: ContentType
    text: str
    summary: Optional[str] = None
    original_html: Optional[str] = None
    extracted_at: datetime = Field(default_factory=datetime.now)
    metadata: Dict = Field(default_factory=dict)


class Link(BaseModel):
    """A link found on a webpage."""
    url: str
    text: Optional[str] = None
    source_url: str
    relevance_score: RelevanceScore = RelevanceScore.MEDIUM_RELEVANCE
    relevance_reason: Optional[str] = None
    visited: bool = False
    visit_priority: int = 0


class ESGInsight(BaseModel):
    """An insight about a company's ESG behavior."""
    company_name: str
    category: ESGCategory
    behavior_type: BehaviorType
    description: str
    source_url: str
    source_title: Optional[str] = None
    extracted_at: datetime = Field(default_factory=datetime.now)
    confidence: float = 0.5  # 0.0 to 1.0
    metadata: Dict = Field(default_factory=dict)


class AgentMemory(BaseModel):
    """Memory of the agent's findings and state."""
    company_name: str
    visited_urls: Set[str] = Field(default_factory=set)
    queued_links: List[Link] = Field(default_factory=list)
    insights: List[ESGInsight] = Field(default_factory=list)
    downloaded_files: List[str] = Field(default_factory=list)
    search_queries_used: List[str] = Field(default_factory=list)
    notes: List[str] = Field(default_factory=list)
    start_time: datetime = Field(default_factory=datetime.now)
    last_updated: datetime = Field(default_factory=datetime.now)


class AgentAction(BaseModel):
    """An action taken by the agent."""
    action_type: str
    params: Dict = Field(default_factory=dict)
    reasoning: str
    timestamp: datetime = Field(default_factory=datetime.now)


class AgentThought(BaseModel):
    """A thought process of the agent."""
    thought: str
    timestamp: datetime = Field(default_factory=datetime.now)


class AgentState(BaseModel):
    """Current state of the agent."""
    memory: AgentMemory
    current_url: Optional[str] = None
    current_content: Optional[PageContent] = None
    last_action: Optional[AgentAction] = None
    thoughts: List[AgentThought] = Field(default_factory=list)
    status: str = "initialized"
