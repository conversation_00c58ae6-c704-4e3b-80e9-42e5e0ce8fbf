"""
Package for predictive analytics v2 based on DEMISE embeddings.

This package provides functionality for clustering by Domain+Subject+Object and
performing separate regression analysis on Motivation, Statement Types, Engagement, and Impact.
"""

from .models import (
    DSO_ClusterModel,
    ComponentPredictionModel,
    PredictiveComponentAnalysisModel,
    XferPredictiveComponentAnalysisModel,
    ClusterAnalysisModel,
    XferClusterAnalysisModel,
    EntityYearAnalysisModel,
    XferEntityYearAnalysisModel,
    RegressionModelType,
    ComponentType
)
from .clustering import (
    create_dso_clusters,
    get_dso_clusters
)
from .regression import (
    predict_component_trends,
    get_regression_model,
    BaseRegressionModel,
    VARModel,
    GaussianProcessModel,
    ProphetModel,
    EnsembleModel
)
from .analysis import (
    generate_component_analysis,
    generate_cluster_analysis,
    generate_entity_year_analysis,
    ComponentAnalysisResponse,
    ClusterAnalysisResponse,
    EntityYearAnalysisResponse
)
from .utils import (
    generate_predictive_report,
    visualize_cluster_trends
)

__all__ = [
    # Models
    "DSO_ClusterModel",
    "ComponentPredictionModel",
    "PredictiveComponentAnalysisModel",
    "XferPredictiveComponentAnalysisModel",
    "ClusterAnalysisModel",
    "XferClusterAnalysisModel",
    "EntityYearAnalysisModel",
    "XferEntityYearAnalysisModel",
    "RegressionModelType",
    "ComponentType",
    "ComponentAnalysisResponse",
    "ClusterAnalysisResponse",
    "EntityYearAnalysisResponse",

    # Clustering
    "create_dso_clusters",
    "get_dso_clusters",

    # Regression
    "predict_component_trends",
    "get_regression_model",
    "BaseRegressionModel",
    "VARModel",
    "GaussianProcessModel",
    "ProphetModel",
    "EnsembleModel",

    # Analysis
    "generate_component_analysis",
    "generate_cluster_analysis",
    "generate_entity_year_analysis",

    # Utilities
    "generate_predictive_report",
    "visualize_cluster_trends"
]
