"""
Data models for predictive analytics v2.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field


class RegressionModelType(str, Enum):
    """Enum for regression model types."""
    VAR = "var"
    GAUSSIAN_PROCESS = "gaussian_process"
    PROPHET = "prophet"
    ENSEMBLE = "ensemble"


class ComponentType(str, Enum):
    """Enum for DEMISE component types."""
    MOTIVATION = "motivation"
    STATEMENT_TYPE = "statement_type"
    ENGAGEMENT = "engagement"
    IMPACT = "impact"


class DSO_ClusterModel(BaseModel):
    """Model representing a cluster based on Domain+Subject+Object."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    year: int
    domain_centroid: List[float] = Field(default_factory=list)
    subject_centroid: List[float] = Field(default_factory=list)
    object_centroid: List[float] = Field(default_factory=list)
    statement_ids: List[int] = Field(default_factory=list)
    size: int
    coherence: float
    created_at: Optional[datetime] = None

    @property
    def dso_centroid(self) -> List[float]:
        """
        Get the combined Domain+Subject+Object centroid.

        Returns:
            The combined vector representation
        """
        return self.domain_centroid + self.subject_centroid + self.object_centroid


class ComponentPredictionModel(BaseModel):
    """Model representing a predicted component trend."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    cluster_id: int
    component_type: ComponentType
    predicted_year: int
    final_historical_year: int
    predicted_vector: List[float] = Field(default_factory=list)
    historical_vector: List[float] = Field(default_factory=list)
    historical_vectors_by_year: Dict[int, List[float]] = Field(default_factory=dict, description="Dictionary mapping historical years to their corresponding vectors")
    confidence: float
    model_type: RegressionModelType
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None


class PredictiveComponentAnalysisModel(BaseModel):
    """Model representing a human-readable analysis of a predicted component trend."""
    id: Optional[int] = None
    run_id: int
    prediction_id: int
    virtual_entity_id: int
    cluster_id: int
    component_type: ComponentType
    year: int
    summary: str
    detailed_analysis: str
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None


class XferPredictiveComponentAnalysisModel(BaseModel):
    """Transfer model for predictive component analysis."""
    id: Optional[int] = None
    run_id: int
    prediction_id: int
    virtual_entity_id: int
    cluster_id: int
    component_type: str
    year: int
    summary: str
    detailed_analysis: str
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None


class ComponentAnalysisResponse(BaseModel):
    """Response model for LLM-generated component analysis."""
    summary: str
    detailed_analysis: str
    potential_risks: List[str]
    potential_opportunities: List[str]
    confidence: float


class ClusterAnalysisModel(BaseModel):
    """Model representing a combined analysis of all components for a cluster."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    cluster_id: int
    year: int
    summary: str
    detailed_analysis: str
    motivation_summary: str
    statement_type_summary: Optional[str] = None
    engagement_summary: str
    impact_summary: str
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    historical_vectors_by_year: Dict[int, Dict[str, List[float]]] = Field(default_factory=dict, description="Dictionary mapping historical years to component vectors by type")
    created_at: Optional[datetime] = None


class EntityYearAnalysisModel(BaseModel):
    """Model representing a combined analysis of all clusters for an entity in a specific year."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    year: int
    summary: str
    detailed_analysis: str
    cluster_summaries: Dict[int, str] = Field(default_factory=dict)
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None


class XferClusterAnalysisModel(BaseModel):
    """Transfer model for cluster analysis."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    cluster_id: int
    year: int
    summary: str
    detailed_analysis: str
    motivation_summary: str
    statement_type_summary: Optional[str] = None
    engagement_summary: str
    impact_summary: str
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    historical_vectors_by_year: Dict[str, Dict[str, List[float]]] = Field(default_factory=dict, description="Dictionary mapping historical years to component vectors by type")
    created_at: Optional[datetime] = None


class XferEntityYearAnalysisModel(BaseModel):
    """Transfer model for entity-year analysis."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    year: int
    summary: str
    detailed_analysis: str
    cluster_summaries: Dict[str, str] = Field(default_factory=dict)
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None


class ClusterAnalysisResponse(BaseModel):
    """Response model for LLM-generated cluster analysis."""
    summary: str
    detailed_analysis: str
    potential_risks: List[str]
    potential_opportunities: List[str]
    confidence: float


class EntityYearAnalysisResponse(BaseModel):
    """Response model for LLM-generated entity-year analysis."""
    summary: str
    detailed_analysis: str
    potential_risks: List[str]
    potential_opportunities: List[str]
    confidence: float
