"""
Human-readable analysis of predicted DEMISE component trends.

This module provides functionality for converting predicted component vectors
into meaningful insights about future entity behavior.
"""

import json
from typing import List, Dict, Any, Optional, cast, Tuple

from loguru import logger
from psycopg import Cursor
from psycopg.rows import dict_row

from eko.analysis_v2.effects import BENEFIT_IMPACT_SCALE, HARM_IMPACT_SCALE
from eko.analysis_v2.pipeline_tracker_extended import TraceabilityTracker
from eko.db import get_bo_conn
from eko.llm import LLMModel
from eko.llm.main import call_llms_typed, LLMOptions
from eko.models.vector.base_vector_model import BaseVectorModel
from eko.models.vector.demise.domain import DomainModel
from eko.models.vector.demise.engagement import EngagementModel
from eko.models.vector.demise.impact import ImpactModel, IMPACT_SCALE
from eko.models.vector.demise.motivation import MotivationModel
from eko.models.vector.demise.statement import StatementTypeModel
# No need for PipelineStage import anymore
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.prediction_v2.dao import (
    PredictiveComponentAnalysisDAO,
    ClusterAnalysisDAO,
    EntityYearAnalysisDAO,
    ComponentPredictionDAO
)
from eko.prediction_v2.models import (
    ComponentPredictionModel,
    PredictiveComponentAnalysisModel,
    ComponentAnalysisResponse,
    ClusterAnalysisModel,
    EntityYearAnalysisModel,
    ClusterAnalysisResponse,
    EntityYearAnalysisResponse,
    ComponentType
)

UNIVERSAL_PROMPT= """Clusters are an internal concept refer instead to the domains of the clusters (e.g. greenwashing, oil industry etc.)
not the name, the word 'cluster' or id of the cluster. Likewise domain is an internal term, and refers to topics/subjects that an analysis relates to.
You should describe things as a journalist working for a large corporate would do, that is formal and objective but not dry.
"""

def get_component_description(component_type: ComponentType) -> str:
    """
    Get a human-readable description of a DEMISE component.

    Args:
        component_type: Type of component

    Returns:
        Human-readable description
    """
    descriptions = {
        ComponentType.MOTIVATION: (
            "The Motivation component represents the underlying motivations for actions or statements. "
            "It includes dimensions like Genuine (entity genuinely cares about addressing issues), "
            "Compliant (entity acts to comply with regulations), Pressured (entity acts due to external pressure), "
            "Superficial (entity acts superficially for appearance), and Opportunistic (entity acts to gain advantage)."
        ),
        ComponentType.STATEMENT_TYPE: (
            "The Statement Type component categorizes statements based on their temporal nature, purpose, and intent. "
            "It includes dimensions like describes_action, describes_event, describes_fact, past_statement, "
            "present_statement, future_statement, beneficial_statement, neutral_statement, harmful_statement, "
            "descriptive, directive, assertive, predictive, and promissory."
        ),
        ComponentType.ENGAGEMENT: (
            "The Engagement component represents how entities engage with issues. "
            "It includes dimensions like Preventative (anticipating and preventing issues), "
            "Altruistic (acting out of intrinsic moral/ethical motivations), "
            "Proactive (addressing issues before they become public), "
            "Responsive (quickly addressing issues once identified), "
            "Reactive (responding only after external pressure), "
            "Dismissive (downplaying the importance of issues), and "
            "Passive (acknowledging issues without taking action)."
        ),
        ComponentType.IMPACT: (
            "The Impact component describes how actions or events affect entities. "
            "It includes dimensions related to harm and benefit to various entities (humans, animals, environment), "
            "as well as the proportion and duration of the impact."
        )
    }

    return descriptions.get(component_type, "Unknown component type")


def get_similar_statements(
    cursor: Cursor[Dict[str, Any]],
    predicted_vector: List[float],
    component_type: ComponentType,
    limit: int = 5
) -> List[Dict[str, Any]]:
    """
    Get statements with similar component vectors to the predicted vector.

    Args:
        cursor: Database cursor
        predicted_vector: Predicted component vector
        component_type: Type of component
        limit: Maximum number of statements to return

    Returns:
        List of similar statements
    """
    # Convert the predicted vector to a PostgreSQL array
    vector_str = "{" + ",".join(str(v) for v in predicted_vector) + "}"

    # Determine which part of the DEMISE embedding to use for similarity
    # This is a simplified approach - in a real implementation, you would need to
    # extract the specific component from the full DEMISE embedding
    query = """
    WITH component_vectors AS (
        SELECT
            id,
            text,
            year,
            base_entity_id,
            demise_embedding,
            CASE
                WHEN %s = 'motivation' THEN demise_embedding[100:150]  -- Example indices for motivation
                WHEN %s = 'statement_type' THEN demise_embedding[150:200]  -- Example indices for statement type
                WHEN %s = 'engagement' THEN demise_embedding[200:250]  -- Example indices for engagement
                WHEN %s = 'impact' THEN demise_embedding[250:300]  -- Example indices for impact
                ELSE demise_embedding
            END AS component_vector
        FROM
            kg_statements_v2
        WHERE
            demise_embedding IS NOT NULL
    )
    SELECT
        s.id,
        s.text,
        s.year,
        e.name AS entity_name,
        1 - (s.component_vector <=> %s::float[]) AS similarity
    FROM
        component_vectors s
    JOIN
        kg_base_entities e ON s.base_entity_id = e.id
    ORDER BY
        similarity DESC
    LIMIT %s
    """

    cursor.execute(
        query,
        (
            component_type.value,
            component_type.value,
            component_type.value,
            component_type.value,
            vector_str,
            limit
        )
    )

    return cursor.fetchall()


def generate_component_analysis(
    virtual_entity: VirtualEntityExpandedModel,
    predictions: List[ComponentPredictionModel],
    tracker: Optional[TraceabilityTracker] = None
) -> List[PredictiveComponentAnalysisModel]:
    """
    Generate human-readable analysis of predicted component trends.
    Only analyzes the final year in the prediction period.

    Args:
        virtual_entity: Virtual entity model
        predictions: List of component prediction models
        tracker: Optional traceability tracker

    Returns:
        List of predictive component analysis models
    """
    if tracker:
        # Log the start of analysis
        logger.info("Starting predictive component analysis stage")

    logger.info(f"Generating component analysis for {virtual_entity.name} "
                f"with {len(predictions)} predictions")

    analyses = []

    # Find the final prediction year
    if not predictions:
        logger.warning(f"No predictions found for {virtual_entity.name}")
        return []

    final_year = max(p.predicted_year for p in predictions)
    logger.info(f"Only analyzing the final prediction year: {final_year}")

    # Filter predictions to only include the final year
    final_year_predictions = [p for p in predictions if p.predicted_year == final_year]
    logger.info(f"Found {len(final_year_predictions)} predictions for the final year {final_year}")

    # Group predictions by cluster and component type
    predictions_by_cluster_component = {}
    for prediction in final_year_predictions:
        key = (prediction.cluster_id, prediction.component_type)
        if key not in predictions_by_cluster_component:
            predictions_by_cluster_component[key] = []
        predictions_by_cluster_component[key].append(prediction)

    with get_bo_conn() as conn:
        # Process each cluster and component type
        for (cluster_id, component_type), cluster_predictions in predictions_by_cluster_component.items():
            # Process each prediction (skip StatementType)
            for prediction in cluster_predictions:
                # Skip StatementType analysis
                if prediction.component_type == ComponentType.STATEMENT_TYPE:
                    logger.info(f"Skipping StatementType analysis for cluster {cluster_id} in year {prediction.predicted_year}")
                    continue

                year = prediction.predicted_year

                logger.info(f"Analyzing {component_type} prediction for cluster {cluster_id} in year {year}")

                # No similar statements for now
                similar_statements = []

                # Generate analysis using LLM
                analysis_response = analyze_component_prediction(
                    virtual_entity,
                    prediction,
                    similar_statements
                )

                # Create analysis model
                analysis_model = PredictiveComponentAnalysisModel(
                    run_id=prediction.run_id,
                    prediction_id=prediction.id,
                    virtual_entity_id=virtual_entity.id,
                    cluster_id=cluster_id,
                    component_type=component_type,
                    year=year,
                    summary=analysis_response.summary,
                    detailed_analysis=analysis_response.detailed_analysis,
                    potential_risks=analysis_response.potential_risks,
                    potential_opportunities=analysis_response.potential_opportunities,
                    confidence=prediction.confidence,
                    core_domain_keys=prediction.core_domain_keys,  # Copy the core_domain_keys from the prediction
                    overall_impact=prediction.overall_impact  # Copy the overall_impact from the prediction
                )

                # Save to database using a cursor with tuple rows
                with conn.cursor() as tuple_cursor:
                    saved_analysis = PredictiveComponentAnalysisDAO.create(tuple_cursor, analysis_model)
                    analyses.append(saved_analysis)

                logger.info(f"Created analysis for {component_type} in cluster {cluster_id} "
                            f"for year {year} with confidence {prediction.confidence:.2f}")

        conn.commit()

    if tracker:
        # Log the end of analysis
        logger.info(f"Completed predictive component analysis stage: {len(analyses)} analyses created, {len(final_year_predictions)} predictions processed")

    return analyses


def generate_cluster_analysis(
    virtual_entity: VirtualEntityExpandedModel,
    component_analyses: List[PredictiveComponentAnalysisModel],
    tracker: Optional[TraceabilityTracker] = None
) -> List[ClusterAnalysisModel]:
    """
    Generate combined cluster-level analysis from component analyses.

    Args:
        virtual_entity: Virtual entity model
        component_analyses: List of component analysis models
        tracker: Optional traceability tracker

    Returns:
        List of cluster analysis models
    """
    if tracker:
        # Log the start of analysis
        logger.info("Starting cluster analysis stage")

    logger.info(f"Generating cluster analysis for {virtual_entity.name} "
                f"with {len(component_analyses)} component analyses")

    cluster_analyses = []

    # Group analyses by cluster and year
    analyses_by_cluster_year = {}
    for analysis in component_analyses:
        key = (analysis.cluster_id, analysis.year)
        if key not in analyses_by_cluster_year:
            analyses_by_cluster_year[key] = []
        analyses_by_cluster_year[key].append(analysis)

    # Get all component predictions to access historical data
    with get_bo_conn() as conn:
        with conn.cursor(row_factory=dict_row) as cursor:
            from eko.prediction_v2.dao import ComponentPredictionDAO

            # Get all predictions for this virtual entity
            all_predictions = ComponentPredictionDAO.find_by_virtual_entity(
                cursor, virtual_entity.id
            )

            # Group predictions by cluster and component type
            predictions_by_cluster_component = {}
            for prediction in all_predictions:
                key = (prediction.cluster_id, prediction.component_type)
                if key not in predictions_by_cluster_component:
                    predictions_by_cluster_component[key] = []
                predictions_by_cluster_component[key].append(prediction)

        # Process each cluster and year
        for (cluster_id, year), cluster_year_analyses in analyses_by_cluster_year.items():
            # Check if we have the required component types (we're skipping StatementType)
            component_types = {analysis.component_type for analysis in cluster_year_analyses}
            required_types = {ComponentType.MOTIVATION, ComponentType.ENGAGEMENT, ComponentType.IMPACT}
            if not required_types.issubset(component_types):
                logger.warning(f"Skipping cluster {cluster_id} in year {year} - missing required component types")
                continue

            logger.info(f"Analyzing cluster {cluster_id} in year {year}")

            # Organize analyses by component type
            analyses_by_component = {}
            for analysis in cluster_year_analyses:
                analyses_by_component[analysis.component_type] = analysis

            # Generate combined analysis using LLM
            analysis_response = analyze_cluster_prediction(
                virtual_entity,
                cluster_id,
                year,
                analyses_by_component
            )

            # Calculate average confidence
            avg_confidence = sum(analysis.confidence for analysis in cluster_year_analyses) / len(cluster_year_analyses)

            # Get core_domain_keys and core_domain_descriptions from the motivation analysis
            motivation_analysis = analyses_by_component[ComponentType.MOTIVATION]
            impact_analysis = analyses_by_component[ComponentType.IMPACT]

            # Get the values directly from the component analysis models
            core_domain_keys = motivation_analysis.core_domain_keys
            overall_impact = impact_analysis.overall_impact

            # Collect historical vectors by year for all component types
            historical_vectors_by_year = {}

            # Process each component type
            for component_type in [ComponentType.MOTIVATION, ComponentType.ENGAGEMENT, ComponentType.IMPACT]:
                # Get predictions for this cluster and component type
                key = (cluster_id, component_type)
                if key in predictions_by_cluster_component:
                    component_predictions = predictions_by_cluster_component[key]

                    # Process each prediction
                    for prediction in component_predictions:
                        # Add historical vectors for each year
                        for year, vector in prediction.historical_vectors_by_year.items():
                            if year not in historical_vectors_by_year:
                                historical_vectors_by_year[year] = {}

                            # Store the vector by component type
                            historical_vectors_by_year[year][component_type.value] = vector

            # Create analysis model
            analysis_model = ClusterAnalysisModel(
                run_id=cluster_year_analyses[0].run_id,
                virtual_entity_id=virtual_entity.id,
                cluster_id=cluster_id,
                year=year,
                summary=analysis_response.summary,
                detailed_analysis=analysis_response.detailed_analysis,
                motivation_summary=analyses_by_component[ComponentType.MOTIVATION].summary,
                statement_type_summary="StatementType analysis has been disabled in the prediction system.",  # We're skipping StatementType analysis
                engagement_summary=analyses_by_component[ComponentType.ENGAGEMENT].summary,
                impact_summary=analyses_by_component[ComponentType.IMPACT].summary,
                potential_risks=analysis_response.potential_risks,
                potential_opportunities=analysis_response.potential_opportunities,
                confidence=avg_confidence,
                core_domain_keys=core_domain_keys,
                overall_impact=overall_impact,
                historical_vectors_by_year=historical_vectors_by_year
            )

            # Save to database
            with conn.cursor() as tuple_cursor:
                saved_analysis = ClusterAnalysisDAO.create(tuple_cursor, analysis_model)
                cluster_analyses.append(saved_analysis)

            logger.info(f"Created cluster analysis for cluster {cluster_id} "
                        f"in year {year} with confidence {avg_confidence:.2f}")

        conn.commit()

    if tracker:
        # Log the end of analysis
        logger.info(f"Completed cluster analysis stage: {len(cluster_analyses)} analyses created")

    return cluster_analyses


def generate_entity_year_analysis(
    virtual_entity: VirtualEntityExpandedModel,
    cluster_analyses: List[ClusterAnalysisModel],
    tracker: Optional[TraceabilityTracker] = None
) -> List[EntityYearAnalysisModel]:
    """
    Generate combined entity-year analysis from cluster analyses.

    Args:
        virtual_entity: Virtual entity model
        cluster_analyses: List of cluster analysis models
        tracker: Optional traceability tracker

    Returns:
        List of entity-year analysis models
    """
    if tracker:
        # Log the start of analysis
        logger.info("Starting entity-year analysis stage")

    logger.info(f"Generating entity-year analysis for {virtual_entity.name} "
                f"with {len(cluster_analyses)} cluster analyses")

    entity_year_analyses = []

    # Group analyses by year
    analyses_by_year = {}
    for analysis in cluster_analyses:
        if analysis.year not in analyses_by_year:
            analyses_by_year[analysis.year] = []
        analyses_by_year[analysis.year].append(analysis)

    with get_bo_conn() as conn:
        # Process each year
        for year, year_analyses in analyses_by_year.items():
            logger.info(f"Analyzing entity {virtual_entity.name} in year {year}")

            # Generate combined analysis using LLM
            analysis_response = analyze_entity_year_prediction(
                virtual_entity,
                year,
                year_analyses
            )

            # Calculate average confidence
            avg_confidence = sum(analysis.confidence for analysis in year_analyses) / len(year_analyses)

            # Create cluster summaries dictionary
            cluster_summaries = {analysis.cluster_id: analysis.summary for analysis in year_analyses}

            # Collect all core_domain_keys and core_domain_descriptions from cluster analyses
            all_core_domain_keys = set()
            core_domain_descriptions_map = {}
            avg_overall_impact = 0.0

            for analysis in year_analyses:
                # Add core_domain_keys
                if analysis.core_domain_keys:
                    all_core_domain_keys.update(analysis.core_domain_keys)


                # Add overall_impact
                if analysis.overall_impact != 0.0:
                    avg_overall_impact += analysis.overall_impact

            # Calculate average overall_impact
            if year_analyses:
                avg_overall_impact /= len(year_analyses)

            # Convert to lists
            core_domain_keys = list(all_core_domain_keys)

            # Create analysis model
            analysis_model = EntityYearAnalysisModel(
                run_id=year_analyses[0].run_id,
                virtual_entity_id=virtual_entity.id,
                year=year,
                summary=analysis_response.summary,
                detailed_analysis=analysis_response.detailed_analysis,
                cluster_summaries=cluster_summaries,
                potential_risks=analysis_response.potential_risks,
                potential_opportunities=analysis_response.potential_opportunities,
                confidence=avg_confidence,
                core_domain_keys=core_domain_keys,
                overall_impact=avg_overall_impact
            )

            # Save to database
            with conn.cursor() as tuple_cursor:
                saved_analysis = EntityYearAnalysisDAO.create(tuple_cursor, analysis_model)
                entity_year_analyses.append(saved_analysis)

            logger.info(f"Created entity-year analysis for {virtual_entity.name} "
                        f"in year {year} with confidence {avg_confidence:.2f}")

        conn.commit()

    if tracker:
        # Log the end of analysis
        logger.info(f"Completed entity-year analysis stage: {len(entity_year_analyses)} analyses created")

    return entity_year_analyses


def analyze_cluster_prediction(
    virtual_entity: VirtualEntityExpandedModel,
    cluster_id: int,
    year: int,
    analyses_by_component: Dict[ComponentType, PredictiveComponentAnalysisModel]
) -> ClusterAnalysisResponse:
    """
    Analyze a cluster prediction by combining component analyses.

    Args:
        virtual_entity: Virtual entity model
        cluster_id: Cluster ID
        year: Year
        analyses_by_component: Dictionary of component analyses by component type

    Returns:
        Cluster analysis response
    """
    # Build the prompt
    prompt_parts = []

    # Entity information
    prompt_parts.append(f"""
# Entity Information
- **Name**: {virtual_entity.name}
- **Year**: {year}
""")

    # Domain information and overall impact
    motivation_analysis = analyses_by_component[ComponentType.MOTIVATION]
    impact_analysis = analyses_by_component[ComponentType.IMPACT]

    # Get domain keys and overall impact
    domain_info = ""

    # Get domain information directly from the motivation analysis model
    if motivation_analysis.core_domain_keys :
        domain_info = "# Domain Information\n"
        domain_info += "This analysis relates to the following domains/topics:\n\n"
        domain_info += domain_keys_to_text(motivation_analysis.core_domain_keys)+"\n"

    # Add overall impact from the impact analysis model directly
    if impact_analysis.overall_impact != 0.0:
        domain_info+= impact_text(domain_info, impact_analysis.overall_impact)

    if domain_info:
        prompt_parts.append(domain_info)

    # Component summaries
    prompt_parts.append(f"""
# Component Analyses

## Motivation
{analyses_by_component[ComponentType.MOTIVATION].summary}

## Engagement
{analyses_by_component[ComponentType.ENGAGEMENT].summary}

## Impact
{analyses_by_component[ComponentType.IMPACT].summary}

{IMPACT_SCALE}
""")

#     # Component detailed analyses
#     prompt_parts.append(f"""
# # Detailed Component Analyses
#
# ## Motivation
# {analyses_by_component[ComponentType.MOTIVATION].detailed_analysis}
#
# ## Engagement
# {analyses_by_component[ComponentType.ENGAGEMENT].detailed_analysis}
#
# ## Impact
# {analyses_by_component[ComponentType.IMPACT].detailed_analysis}
# """)

    # Potential risks and opportunities
    all_risks = []
    all_opportunities = []

    for component_type, analysis in analyses_by_component.items():
        for risk in analysis.potential_risks:
            all_risks.append(f"**{component_type.value}**: {risk}")

        for opportunity in analysis.potential_opportunities:
            all_opportunities.append(f"**{component_type.value}**: {opportunity}")

    risks_text = "\n".join([f"- {risk}" for risk in all_risks])
    opportunities_text = "\n".join([f"- {opportunity}" for opportunity in all_opportunities])

    prompt_parts.append(f"""
# Potential Risks
{risks_text}

# Potential Opportunities
{opportunities_text}
""")

    # Analysis task
    prompt_parts.append(f"""
# Analysis Task
Based on the component analyses provided, generate a comprehensive analysis of this cluster's predicted behavior:

1. A concise summary (3-4 sentences) that integrates the key insights from all components
2. A detailed analysis (2-3 paragraphs) explaining:
- How the different components interact with each other
- What overall pattern or strategy emerges from these interactions
- What this reveals about the entity's approach to this domain/topic
- Pay special attention to the overall impact score and how it relates to the entity's motivations and engagement
3. A consolidated list of the most significant potential risks (select 3-5 most important)
4. A consolidated list of the most significant potential opportunities (select 3-5 most important)
5. A confidence score (0.0-1.0) for this analysis

Your analysis should focus on the interplay between motivations, engagement approaches, and impacts, providing a
holistic view of the entity's behavior in this specific domain.

Pay particular attention to whether the overall impact is positive or negative and what that means for stakeholders.

{UNIVERSAL_PROMPT}
""")

    # Join the prompt parts
    prompt = "\n".join(prompt_parts)

    # Call the LLM
    messages = [{"role": "user", "content": prompt}]

    response = call_llms_typed(
        [LLMModel.NORMAL_HQ, LLMModel.NORMAL_ALL_ROUNDER],
        messages,
        6000,
        response_model=ClusterAnalysisResponse,
        options=LLMOptions(temperature=0.2),
    )

    return response



def analyze_entity_year_prediction(
    virtual_entity: VirtualEntityExpandedModel,
    year: int,
    cluster_analyses: List[ClusterAnalysisModel]
) -> EntityYearAnalysisResponse:
    """
    Analyze an entity-year prediction by combining cluster analyses.

    Args:
        virtual_entity: Virtual entity model
        year: Year
        cluster_analyses: List of cluster analysis models

    Returns:
        Entity-year analysis response
    """
    # Build the prompt
    prompt_parts = []

    # Entity information
    prompt_parts.append(f"""
# Entity Information
- **Name**: {virtual_entity.name}
- **Year**: {year}
- **Number of Clusters**: {len(cluster_analyses)}
""")

    # Cluster summaries
    prompt_parts.append("# Cluster Summaries\n")

    # Now iterate through cluster analyses and use the core_domain_keys directly from the models
    for analysis in cluster_analyses:
        # For each cluster analysis, use the core_domain_keys directly
        domain_info = ""

        # Use the core_domain_keys directly from the cluster analysis model
        if analysis.core_domain_keys:
            domain_info = "# Domain Information\n"
            domain_info += "This analysis relates to the following domains/topics only, please make sure you refer to them in your analysis:\n\n"
            domain_info += domain_keys_to_text(analysis.core_domain_keys)

            # Add overall impact if available
            if analysis.overall_impact != 0.0:
                domain_info += impact_text(domain_info, analysis.overall_impact)

        prompt_parts.append(f"""
## Cluster {analysis.cluster_id}
{domain_info}
**Summary**: {analysis.summary}

**Key Component Insights**:
- **Motivation**: {analysis.motivation_summary}
- **Statement Type**: {analysis.statement_type_summary}
- **Engagement**: {analysis.engagement_summary}
- **Impact**: {analysis.impact_summary}
""")

    # Detailed analyses
    prompt_parts.append("# Detailed Cluster Analyses\n")

    for analysis in cluster_analyses:
        prompt_parts.append(f"""
## Cluster {analysis.cluster_id}
{analysis.detailed_analysis}
""")

    # Potential risks and opportunities
    all_risks = []
    all_opportunities = []

    for analysis in cluster_analyses:
        for risk in analysis.potential_risks:
            all_risks.append(f"**Cluster {analysis.cluster_id}**: {risk}")

        for opportunity in analysis.potential_opportunities:
            all_opportunities.append(f"**Cluster {analysis.cluster_id}**: {opportunity}")

    risks_text = "\n".join([f"- {risk}" for risk in all_risks])
    opportunities_text = "\n".join([f"- {opportunity}" for opportunity in all_opportunities])

    prompt_parts.append(f"""
# Potential Risks
{risks_text}

# Potential Opportunities
{opportunities_text}
""")

    # Analysis task
    prompt_parts.append(f"""
# Analysis Task
Based on the cluster analyses provided, generate a comprehensive entity-level analysis for this year:

1. A concise summary (4-5 sentences) that integrates the key insights from all clusters
2. A detailed analysis (3-4 paragraphs) explaining:
- How the different domain clusters relate to each other
- What overall corporate strategy emerges from these clusters
- How the entity balances different priorities across domains
- What this reveals about the entity's overall approach to sustainability and ethics
3. A consolidated list of the most significant potential risks (select 5-7 most important)
4. A consolidated list of the most significant potential opportunities (select 5-7 most important)
5. A confidence score (0.0-1.0) for this analysis

Your analysis should focus on providing a holistic view of the entity's behavior across all domains, identifying patterns, contradictions, and strategic priorities.
{UNIVERSAL_PROMPT}
""")

    # Join the prompt parts
    prompt = "\n".join(prompt_parts)

    # Call the LLM
    messages = [{"role": "user", "content": prompt}]

    response = call_llms_typed(
        [LLMModel.NORMAL_HQ, LLMModel.NORMAL_ALL_ROUNDER],
        messages,
        8000,
        response_model=EntityYearAnalysisResponse,
        options=LLMOptions(temperature=0.2)
    )

    return response



def analyze_component_prediction(
    virtual_entity: VirtualEntityExpandedModel,
    prediction: ComponentPredictionModel,
    similar_statements: List[Dict[str, Any]]
) -> ComponentAnalysisResponse:
    """
    Analyze a component prediction using an LLM.
    This function routes to the appropriate component-specific analysis function.

    Args:
        virtual_entity: Virtual entity model
        prediction: Component prediction model
        similar_statements: List of similar statements

    Returns:
        Component analysis response
    """

    # Route to the appropriate component-specific analysis function
    component_type = prediction.component_type

    if component_type == ComponentType.MOTIVATION:
        return analyze_motivation_prediction(virtual_entity, prediction, similar_statements)
    elif component_type == ComponentType.STATEMENT_TYPE:
        raise NotImplementedError("StatementType analysis is not implemented")
    elif component_type == ComponentType.ENGAGEMENT:
        return analyze_engagement_prediction(virtual_entity, prediction, similar_statements)
    elif component_type == ComponentType.IMPACT:
        return analyze_impact_prediction(virtual_entity, prediction, similar_statements)
    else:
        raise ValueError(f"Unknown component type: {component_type}")


def domain_keys_to_text(keys: List[str]) -> str:
    descriptions= DomainModel.get_key_descriptions()
    return "\n".join([f"- {key}: {descriptions[key]}" for key in sorted(keys)])

def _prepare_common_prompt_parts(
    virtual_entity: VirtualEntityExpandedModel,
    prediction: ComponentPredictionModel,
    similar_statements: List[Dict[str, Any]],
    model: BaseVectorModel,
    historical_model: BaseVectorModel
) -> Tuple[List[str], List[str]]:
    """
    Prepare common prompt parts used by all component analysis functions.

    Args:
        virtual_entity: Virtual entity model
        prediction: Component prediction model
        similar_statements: List of similar statements
        model: Current component model
        historical_model: Historical component model

    Returns:
        Tuple of (prompt_parts, significant_changes)
    """
    # Get sparse representations for the prompt
    component_dict = model.to_kv()
    historical_component_dict = historical_model.to_kv()

    # Calculate significant changes
    significant_changes = []
    for key, value in component_dict.items():
        historical_value = historical_component_dict.get(key, 0.0)
        change = value - historical_value
        if abs(change) > 0.1:  # Only include significant changes
            direction = "increased" if change > 0 else "decreased"
            significant_changes.append(f"{key} has {direction} from {historical_value:.2f} to {value:.2f}")
    significant_changes= sorted(significant_changes)

    # Prepare similar statements text
    similar_statements_text = ""
    if similar_statements:
        similar_statements= sorted(similar_statements, key=lambda x: x['similarity'], reverse=True)
        similar_statements_text = "## Similar Statements\n\n"
        for i, statement in enumerate(similar_statements):
            similar_statements_text += (
                f" **{statement['entity_name']} ({statement['year']})**: "
                f"{statement['text']} (Similarity: {statement['similarity']:.2f})\n\n"
            )
    # Build the prompt
    prompt_parts = []

    # Entity information
    prompt_parts.append(f"""
# Entity Information
- **Name**: {virtual_entity.name}
- **Year**: {prediction.predicted_year}
- **Component Type**: {prediction.component_type.value}
- **Confidence**: {prediction.confidence:.2f}
""")

    # Domain information and overall impact
    domain_info = ""

    # Add domain information
    if prediction.core_domain_keys:
        domain_info = "# Domain Information\n"
        domain_info += "This analysis relates to the following domains/topics only, please make sure you refer to them in your analysis:\n\n"
        domain_info += domain_keys_to_text(prediction.core_domain_keys)

    # Add overall impact for all component types (not just Impact)
    if prediction.overall_impact != 0.0 and prediction.component_type != ComponentType.IMPACT:
        # For Impact component, we add this separately in analyze_impact_prediction
        if domain_info:
            domain_info += "\n"
        domain_info += impact_text(domain_info, prediction.overall_impact)

    if domain_info:
        prompt_parts.append(domain_info)

    # Component description
    prompt_parts.append(f"""
# Component Description
{get_component_description(prediction.component_type)}
""")

    # Historical context
    prompt_parts.append(f"""
# Historical Context
The final historical year is {prediction.final_historical_year}.

## Historical Component Values
```json
{json.dumps(historical_component_dict, indent=2)}
```
""")

    # Similar statements
    prompt_parts.append(f"""
{similar_statements_text}
""")

    # Predicted component
    prompt_parts.append(f"""
# Predicted Component Values
```json
{json.dumps(component_dict, indent=2)}
```
""")

    if domain_info:
        prompt_parts.append(domain_info)

    # Significant changes
    if not significant_changes:
        prompt_parts.append("# Significant Changes\nNo significant changes detected.")
    else:
        changes_text = "\n".join(significant_changes)
        prompt_parts.append(f"# Significant Changes\n{changes_text}")

    print(prompt_parts)
    return prompt_parts, significant_changes


def impact_text(domain_info, score):
    domain_info += "# Overall Impact\n"
    domain_info += f"The overall impact score is: {score * 100:.0f}%\n"
    if score > 0:
        domain_info += BENEFIT_IMPACT_SCALE
    else:
        domain_info += "This indicates a harmful impact.\n"
    domain_info += HARM_IMPACT_SCALE
    return domain_info


def analyze_motivation_prediction(
    virtual_entity: VirtualEntityExpandedModel,
    prediction: ComponentPredictionModel,
    similar_statements: List[Dict[str, Any]]
) -> ComponentAnalysisResponse:
    """
    Analyze a Motivation component prediction using an LLM.

    This analysis focuses on why a company takes actions (genuine, compliant, pressured, etc.)
    and how their motivations are changing over time.

    Args:
        virtual_entity: Virtual entity model
        prediction: Component prediction model
        similar_statements: List of similar statements

    Returns:
        Component analysis response
    """

    # Get the predicted and historical vectors
    predicted_vector = prediction.predicted_vector
    historical_vector = prediction.historical_vector

    # Create temporary models for the vectors
    import numpy as np

    model = cast(MotivationModel, MotivationModel.from_np(np.array(predicted_vector)))
    historical_model = cast(MotivationModel, MotivationModel.from_np(np.array(historical_vector)))

    # Get common prompt parts
    prompt_parts, _ = _prepare_common_prompt_parts(
        virtual_entity, prediction, similar_statements, model, historical_model
    )

    # Add motivation-specific analysis task
    prompt_parts.append(f"""
# Motivation Analysis Task
Based on the predicted Motivation component values, significant changes, and historical context, provide:

1. A concise summary (2-3 sentences) of how the entity's motivations are changing over time
2. A detailed analysis (1-2 paragraphs) explaining:
- What is driving the entity's actions (genuine concern, compliance, external pressure, etc.)
- How these motivations are evolving and why
- What this reveals about the entity's core values and priorities
3. A list of potential risks associated with these motivation changes
4. A list of potential opportunities associated with these motivation changes
5. A confidence score (0.0-1.0) for this analysis

Focus specifically on the underlying reasons WHY the company is taking actions within the specified domain/topic.

Bear in mind the impact score, this indicates the seriousness of the actions taken within the domain.

Are they genuinely concerned about doing good, merely complying with regulations, responding to external pressure, acting superficially, or opportunistically seeking advantage?
{UNIVERSAL_PROMPT}
""")

    # Join the prompt parts
    prompt = "\n".join(prompt_parts)

    # Call the LLM
    messages = [{"role": "user", "content": prompt}]

    response = call_llms_typed([LLMModel.NORMAL_ALL_ROUNDER], messages, 4000, response_model=ComponentAnalysisResponse)

    return response


def analyze_statement_type_prediction(
    virtual_entity: VirtualEntityExpandedModel,
    prediction: ComponentPredictionModel,
    similar_statements: List[Dict[str, Any]]
) -> ComponentAnalysisResponse:
    """
    Analyze a Statement Type component prediction using an LLM.

    This analysis focuses on the types of statements made (claims, promises, etc.)
    and how the communication style is changing over time.

    Args:
        virtual_entity: Virtual entity model
        prediction: Component prediction model
        similar_statements: List of similar statements

    Returns:
        Component analysis response
    """

    # Get the predicted and historical vectors
    predicted_vector = prediction.predicted_vector
    historical_vector = prediction.historical_vector

    # Create temporary models for the vectors
    import numpy as np

    model = cast(StatementTypeModel, StatementTypeModel.from_np(np.array(predicted_vector)))
    historical_model = cast(StatementTypeModel, StatementTypeModel.from_np(np.array(historical_vector)))

    # Get common prompt parts
    prompt_parts, _ = _prepare_common_prompt_parts(
        virtual_entity, prediction, similar_statements, model, historical_model
    )

    # Add statement type-specific analysis task
    prompt_parts.append(f"""
# Statement Type Analysis Task
Based on the predicted Statement Type component values, significant changes, and historical context, provide:

1. A concise summary (2-3 sentences) of how the entity's communication style is changing over time
2. A detailed analysis (1-2 paragraphs) explaining:
- Whether the entity is making more claims about past/present or promises about the future
- How the balance between factual statements, action descriptions, and event descriptions is shifting
- What this reveals about the entity's communication strategy and transparency
3. A list of potential risks associated with these statement type changes
4. A list of potential opportunities associated with these statement type changes
5. A confidence score (0.0-1.0) for this analysis

Focus specifically on the TYPES of statements the company is making. Are they making more claims about past accomplishments or promises about future actions? Are they becoming more assertive, directive, or promissory in their communications? How might this affect stakeholder trust and expectations?
{UNIVERSAL_PROMPT}
""")

    # Join the prompt parts
    prompt = "\n".join(prompt_parts)

    # Call the LLM
    messages = [{"role": "user", "content": prompt}]

    response = call_llms_typed(
        [LLMModel.NORMAL_ALL_ROUNDER],
        messages,
        4000,
        response_model=ComponentAnalysisResponse,
        options=LLMOptions(temperature=0.2)
    )

    return response



def analyze_engagement_prediction(
    virtual_entity: VirtualEntityExpandedModel,
    prediction: ComponentPredictionModel,
    similar_statements: List[Dict[str, Any]]
) -> ComponentAnalysisResponse:
    """
    Analyze an Engagement component prediction using an LLM.

    This analysis focuses on how a company engages with issues (preventative, reactive, etc.)
    and how their engagement approach is changing over time.

    Args:
        virtual_entity: Virtual entity model
        prediction: Component prediction model
        similar_statements: List of similar statements

    Returns:
        Component analysis response
    """
    # Get the predicted and historical vectors
    predicted_vector = prediction.predicted_vector
    historical_vector = prediction.historical_vector

    # Create temporary models for the vectors
    import numpy as np

    model = cast(EngagementModel, EngagementModel.from_np(np.array(predicted_vector)))
    historical_model = cast(EngagementModel, EngagementModel.from_np(np.array(historical_vector)))

    # Get common prompt parts
    prompt_parts, _ = _prepare_common_prompt_parts(
        virtual_entity, prediction, similar_statements, model, historical_model
    )

    # Add engagement-specific analysis task
    prompt_parts.append(f"""
# Engagement Analysis Task
Based on the predicted Engagement component values, significant changes, and historical context, provide:

1. A concise summary (2-3 sentences) of how the entity's engagement approach is changing over time
2. A detailed analysis (1-2 paragraphs) explaining:
- Whether the entity is becoming more proactive/preventative or reactive/dismissive
- How the entity's level of engagement with issues is evolving
- What this reveals about the entity's operational priorities and stakeholder relationships
3. A list of potential risks associated with these engagement changes
4. A list of potential opportunities associated with these engagement changes
5. A confidence score (0.0-1.0) for this analysis

Focus specifically on HOW the company engages with issues in this domain, consider how large or small the impact is to that domain
and how appropriate the type of engagement is. So if the impact is negative and large and the company is dismissive then this is a risk. If` the
impact is negative and large and the company is preventative then this is an opportunity.

How adaptable is the company to changing circumstances?

Bear in mind the impact score, this indicates the seriousness of the actions taken within the domain.

Are they becoming more preventative (anticipating issues before they arise), proactive (addressing issues early), responsive (addressing issues quickly once identified), reactive (responding only after pressure), dismissive (downplaying issues), or passive (acknowledging but not acting)? How might this affect their ability to address challenges effectively?
{UNIVERSAL_PROMPT}
""")

    # Join the prompt parts
    prompt = "\n".join(prompt_parts)

    # Call the LLM
    messages = [{"role": "user", "content": prompt}]

    response = call_llms_typed(
        [LLMModel.NORMAL_ALL_ROUNDER],
        messages,
        4000,
        response_model=ComponentAnalysisResponse,
        options=LLMOptions(temperature=0.2)
    )

    return response



def analyze_impact_prediction(
    virtual_entity: VirtualEntityExpandedModel,
    prediction: ComponentPredictionModel,
    similar_statements: List[Dict[str, Any]]
) -> ComponentAnalysisResponse:
    """
    Analyze an Impact component prediction using an LLM.

    This analysis focuses on the effects of actions (harm/benefit to humans, animals, environment)
    and how the impact is changing over time.

    Args:
        virtual_entity: Virtual entity model
        prediction: Component prediction model
        similar_statements: List of similar statements

    Returns:
        Component analysis response
    """

    # Get the predicted and historical vectors
    predicted_vector = prediction.predicted_vector
    historical_vector = prediction.historical_vector

    # Create temporary models for the vectors
    import numpy as np

    model = cast(ImpactModel, ImpactModel.from_np(np.array(predicted_vector)))
    historical_model = cast(ImpactModel, ImpactModel.from_np(np.array(historical_vector)))

    # Get common prompt parts
    prompt_parts, _ = _prepare_common_prompt_parts(
        virtual_entity, prediction, similar_statements, model, historical_model
    )

    # Add impact-specific analysis task
    prompt_parts.append(f"""
{IMPACT_SCALE}

# Impact Analysis Task
Based on the predicted Impact component values, significant changes, and historical context, provide:

1. A concise summary (2-3 sentences) of how the entity's impact is changing over time
2. A detailed analysis (1-2 paragraphs) explaining:
- Whether the entity is causing more benefit or harm to humans, animals, and the environment
- How the scale (proportion) and duration of impacts are changing
- What this reveals about the entity's actual effects on stakeholders and ecosystems
3. A list of potential risks associated with these impact changes
4. A list of potential opportunities associated with these impact changes
5. A confidence score (0.0-1.0) for this analysis

Focus specifically on the EFFECTS of the company's actions within the domain specified, the domain describes what sphere
the impact is changing in.

Are they causing more benefit or harm to humans, animals, and the environment?
Is the scale (proportion) of their impact increasing or decreasing?
Is the duration of their impact becoming longer or shorter?


How might these changes affect their sustainability and social responsibility profile?

{UNIVERSAL_PROMPT}

""")

    # Join the prompt parts
    prompt = "\n".join(prompt_parts)

    # Call the LLM
    messages = [{"role": "user", "content": prompt}]

    response = call_llms_typed(
        [LLMModel.NORMAL_ALL_ROUNDER], messages, 4000, response_model=ComponentAnalysisResponse, options=LLMOptions(temperature=0.2)
    )

    return response
