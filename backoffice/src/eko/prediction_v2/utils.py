"""
Utility functions for predictive analytics v2.

This module provides functions for visualizing component trends and generating reports.
"""

import os
import base64
from datetime import datetime
from io import BytesIO
from typing import List, Optional, Tuple, cast, Dict, Any

import matplotlib.pyplot as plt
import numpy as np
from loguru import logger
from matplotlib.figure import Figure
from sklearn.manifold import TSNE
from matplotlib.colors import hsv_to_rgb

from eko import eko_var_path
from eko.models.vector.demise.engagement import EngagementModel
from eko.models.vector.demise.impact import ImpactModel
from eko.models.vector.demise.motivation import MotivationModel
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.prediction_v2.models import (
    DSO_ClusterModel,
    ComponentPredictionModel,
    PredictiveComponentAnalysisModel,
    ClusterAnalysisModel,
    EntityYearAnalysisModel,
    ComponentType
)


def figure_to_base64(fig: Figure) -> str:
    """
    Convert a matplotlib figure to a base64 encoded string.

    Args:
        fig: Matplotlib figure

    Returns:
        Base64 encoded string of the figure
    """
    buf = BytesIO()
    fig.savefig(buf, format='png', bbox_inches='tight')
    buf.seek(0)
    img_str = base64.b64encode(buf.read()).decode('utf-8')
    buf.close()
    return img_str





def create_tsne_visualization(
    clusters: List[DSO_ClusterModel],
    predictions: List[ComponentPredictionModel]
) -> Tuple[Figure, str]:
    """
    Create a t-SNE visualization showing the movement of DSO clusters over time,
    including both historical and predicted data points.

    Args:
        clusters: List of DSO cluster models
        predictions: List of component prediction models

    Returns:
        Tuple of (Figure, base64 encoded string of the figure)
    """

    # Group clusters by ID
    clusters_by_id = {}
    for cluster in clusters:
        if cluster.id not in clusters_by_id:
            clusters_by_id[cluster.id] = []
        clusters_by_id[cluster.id].append(cluster)

    # Group predictions by cluster ID
    predictions_by_cluster = {}
    for prediction in predictions:
        if prediction.cluster_id not in predictions_by_cluster:
            predictions_by_cluster[prediction.cluster_id] = []
        predictions_by_cluster[prediction.cluster_id].append(prediction)

    # Prepare vectors and metadata for t-SNE
    all_vectors = []
    vector_metadata = []  # (cluster_id, year, is_historical)

    # Add historical cluster vectors
    for cluster_id, cluster_list in clusters_by_id.items():
        for cluster in cluster_list:
            all_vectors.append(cluster.dso_centroid)
            vector_metadata.append((cluster_id, cluster.year, True))

    # Add predicted vectors
    for cluster_id, prediction_list in predictions_by_cluster.items():
        # Only use one component type (e.g., MOTIVATION) for visualization
        # since we're just visualizing the DSO clusters
        motivation_predictions = [p for p in prediction_list if p.component_type == ComponentType.MOTIVATION]

        for prediction in motivation_predictions:
            # Find the corresponding cluster
            if cluster_id in clusters_by_id:
                # Use the DSO centroid from the cluster
                cluster = next((c for c in clusters_by_id[cluster_id] if c.year == prediction.final_historical_year), None)
                if cluster:
                    all_vectors.append(cluster.dso_centroid)
                    vector_metadata.append((cluster_id, prediction.predicted_year, False))

    # Check if we have enough vectors for t-SNE
    if len(all_vectors) < 2:
        logger.warning("Not enough vectors for t-SNE visualization")
        return plt.figure(), ""

    # Convert to numpy array
    vectors_array = np.array(all_vectors)

    # Apply t-SNE
    tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, max(3, len(all_vectors) // 5)))
    tsne_results = tsne.fit_transform(vectors_array)

    # Create visualization
    fig, ax = plt.subplots(figsize=(12, 10))

    # Generate colors for each cluster
    unique_clusters = list(set(meta[0] for meta in vector_metadata))
    colors = {}
    for i, cluster_id in enumerate(unique_clusters):
        # Generate evenly spaced colors in HSV space
        hue = i / len(unique_clusters)
        colors[cluster_id] = hsv_to_rgb((hue, 0.8, 0.9))

    # Plot each cluster's path
    for cluster_id in unique_clusters:
        # Get indices for this cluster
        indices = [i for i, meta in enumerate(vector_metadata) if meta[0] == cluster_id]

        if not indices:
            continue

        # Get t-SNE coordinates for this cluster
        x = tsne_results[indices, 0]
        y = tsne_results[indices, 1]
        years = [vector_metadata[i][1] for i in indices]
        is_historical = [vector_metadata[i][2] for i in indices]

        # Sort by year
        sorted_indices = np.argsort(years)
        x = x[sorted_indices]
        y = y[sorted_indices]
        years = [years[i] for i in sorted_indices]
        is_historical = [is_historical[i] for i in sorted_indices]

        # Find where historical data ends and predictions begin
        if True in is_historical and False in is_historical:
            # Find the first predicted point
            pred_start_idx = is_historical.index(False)

            # Plot historical path with solid line
            if pred_start_idx > 0:
                ax.plot(x[:pred_start_idx], y[:pred_start_idx], '-',
                        color=colors[cluster_id], alpha=0.8, linewidth=2.5,
                        label=f"Cluster {cluster_id} (Historical)")

            # Plot predicted path with dashed line
            if pred_start_idx < len(x):
                # Connect the last historical point to the first predicted point
                if pred_start_idx > 0:
                    ax.plot([x[pred_start_idx-1], x[pred_start_idx]],
                            [y[pred_start_idx-1], y[pred_start_idx]],
                            '--', color=colors[cluster_id], alpha=0.6, linewidth=1.5)

                # Plot the rest of the predicted path
                ax.plot(x[pred_start_idx:], y[pred_start_idx:], '--',
                        color=colors[cluster_id], alpha=0.6, linewidth=2,
                        label=f"Cluster {cluster_id} (Predicted)")
        else:
            # If all points are historical or all are predicted
            linestyle = '-' if all(is_historical) else '--'
            alpha = 0.8 if all(is_historical) else 0.6
            label_suffix = "(Historical)" if all(is_historical) else "(Predicted)"

            ax.plot(x, y, linestyle, color=colors[cluster_id], alpha=alpha, linewidth=2,
                    label=f"Cluster {cluster_id} {label_suffix}")

        # Plot points for each year along the path
        for i in range(len(x)):
            # Use different markers for historical vs predicted
            marker = 'o' if is_historical[i] else 's'

            # Different sizes and edge widths for historical vs predicted
            size = 140 if is_historical[i] else 120
            edge_width = 1.8 if is_historical[i] else 1.5

            # Make the points more visible
            ax.scatter(x[i], y[i], color=colors[cluster_id], marker=marker, s=size,
                      edgecolor='black', linewidth=edge_width, zorder=10)

            # Add year labels with improved visibility
            ax.text(x[i], y[i], str(years[i]), fontsize=10, fontweight='bold',
                   ha='center', va='bottom', bbox=dict(facecolor='white', alpha=0.7,
                                                      pad=2, edgecolor='none'))

    # Add legend and labels
    handles, labels = ax.get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    ax.legend(by_label.values(), by_label.keys(), loc='best', title="Clusters")

    ax.set_title('t-SNE Visualization of DSO Clusters Over Time', fontsize=14)
    ax.set_xlabel('t-SNE Dimension 1', fontsize=12)
    ax.set_ylabel('t-SNE Dimension 2', fontsize=12)

    # Add a note about the markers
    ax.text(0.02, 0.02, "○ Historical clusters\n□ Predicted clusters",
            transform=ax.transAxes, fontsize=10, va='bottom',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))

    plt.tight_layout()

    # Convert to base64
    img_str = figure_to_base64(fig)

    return fig, img_str



def generate_predictive_report(
    virtual_entity: VirtualEntityExpandedModel,
    clusters: List[DSO_ClusterModel],
    predictions: List[ComponentPredictionModel],
    component_analyses: List[PredictiveComponentAnalysisModel],
    cluster_analyses: Optional[List[ClusterAnalysisModel]] = None,
    entity_year_analyses: Optional[List[EntityYearAnalysisModel]] = None,
    output_file: Optional[str] = None
) -> str:
    """
    Generate a human-readable HTML report of predictive component analysis.

    Args:
        virtual_entity: Virtual entity model
        clusters: List of DSO cluster models
        predictions: List of component prediction models
        component_analyses: List of predictive component analysis models
        cluster_analyses: Optional list of cluster analysis models
        entity_year_analyses: Optional list of entity-year analysis models
        output_file: Path to save the report

    Returns:
        Path to the generated report
    """

    # Group component analyses by cluster, component type, and year
    analyses_by_cluster_component_year = {}
    for analysis in component_analyses:
        key = (analysis.cluster_id, analysis.component_type, analysis.year)
        analyses_by_cluster_component_year[key] = analysis

    # Group clusters by ID
    clusters_by_id = {}
    for cluster in clusters:
        if cluster.id not in clusters_by_id:
            clusters_by_id[cluster.id] = []
        clusters_by_id[cluster.id].append(cluster)

    # Group cluster analyses by cluster ID
    cluster_analyses_by_id = {}
    if cluster_analyses:
        for analysis in cluster_analyses:
            if analysis.cluster_id not in cluster_analyses_by_id:
                cluster_analyses_by_id[analysis.cluster_id] = []
            cluster_analyses_by_id[analysis.cluster_id].append(analysis)

    # Generate cluster trend charts
    cluster_charts = {}
    for cluster_id in clusters_by_id.keys():
        # Find the most recent cluster analysis for this cluster
        cluster_analysis = None
        if cluster_id in cluster_analyses_by_id:
            # Sort by year and get the most recent one
            sorted_analyses = sorted(cluster_analyses_by_id[cluster_id], key=lambda a: a.year)
            if sorted_analyses:
                cluster_analysis = sorted_analyses[-1]

        _, chart_base64 = create_cluster_trend_chart(cluster_id, clusters, predictions, cluster_analysis)
        cluster_charts[cluster_id] = chart_base64

    # Generate t-SNE visualization
    _, tsne_chart_base64 = create_tsne_visualization(clusters, predictions)

    # Create HTML content
    html = []
    html.append("<!DOCTYPE html>")
    html.append("<html lang=\"en\">")
    html.append("<head>")
    html.append("    <meta charset=\"UTF-8\">")
    html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">")
    html.append(f"    <title>Predictive Analysis for {virtual_entity.name}</title>")
    html.append("    <style>")
    html.append("        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 1200px; margin: 0 auto; padding: 20px; }")
    html.append("        h1, h2, h3 { color: #2c3e50; }")
    html.append("        h1 { border-bottom: 2px solid #3498db; padding-bottom: 10px; }")
    html.append("        h2 { border-bottom: 1px solid #bdc3c7; padding-bottom: 5px; margin-top: 30px; }")
    html.append("        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }")
    html.append("        .chart-container { margin: 30px 0; padding: 20px; background-color: #fff; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }")
    html.append("        .chart-description { color: #7f8c8d; font-style: italic; margin-top: 10px; }")
    html.append("        .analysis-container { margin: 30px 0; padding: 20px; background-color: #fff; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }")
    html.append("        .cluster-section { margin-top: 40px; padding-top: 20px; border-top: 2px dashed #bdc3c7; }")
    html.append("        .component-section { margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; }")
    html.append("        .year-section { margin-top: 20px; padding: 15px; background-color: #fff; border-left: 4px solid #3498db; }")
    html.append("        .summary { font-weight: bold; }")
    html.append("        .risks { color: #e74c3c; }")
    html.append("        .opportunities { color: #2ecc71; }")
    html.append("        .confidence { font-style: italic; color: #7f8c8d; }")
    html.append("        ul { padding-left: 20px; }")
    html.append("        li { margin-bottom: 5px; }")
    html.append("    </style>")
    html.append("</head>")
    html.append("<body>")

    # Header
    html.append("    <div class=\"header\">")
    html.append(f"        <h1>Predictive Analysis for {virtual_entity.name}</h1>")
    html.append(f"        <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>")
    html.append("    </div>")

    # Overview
    html.append("    <h2>Overview</h2>")
    html.append("    <p>This report presents a predictive analysis of future trends in DEMISE components for the entity. The analysis is based on historical data and uses regression modeling to predict future trends.</p>")

    # Add t-SNE visualization
    html.append("    <div class=\"chart-container\">")
    html.append("        <h3>Cluster Movement in t-SNE Space</h3>")
    html.append(f"        <img src=\"data:image/png;base64,{tsne_chart_base64}\" alt=\"t-SNE Visualization\" style=\"max-width:100%;\">")
    html.append("        <p class=\"chart-description\">This visualization shows how DSO clusters move over time in a 2D projection of the high-dimensional vector space. Each color represents a different cluster, with circles (○) showing historical clusters and squares (□) showing predicted clusters. The year is labeled for each point. Similar clusters appear closer together in this space.</p>")
    html.append("    </div>")

    # Cluster trend charts
    html.append("    <h2>Cluster Trends</h2>")
    html.append("    <p>These charts show how key dimensions from Motivation, Engagement, and Impact components evolve over time for each cluster. Solid lines represent historical data, while dashed lines represent predicted future trends.</p>")

    # Add a chart for each cluster
    for cluster_id, chart_base64 in cluster_charts.items():
        html.append("    <div class=\"chart-container\">")
        html.append(f"        <h3>Cluster {cluster_id} Trends</h3>")
        html.append(f"        <img src=\"data:image/png;base64,{chart_base64}\" alt=\"Cluster {cluster_id} Chart\" style=\"max-width:100%;\">")
        html.append("        <p class=\"chart-description\">This chart shows how key dimensions evolve over time for this cluster. Genuine Motivation and Proactive Engagement indicate positive trends, while Opportunistic Motivation and Reactive Engagement indicate negative trends. Human Benefit and Harm show the impact on human life.</p>")
        html.append("    </div>")

    # Detailed Analysis
    html.append("    <h2>Detailed Analysis by Cluster</h2>")

    # Group clusters by ID
    clusters_by_id = {}
    for cluster in clusters:
        if cluster.id not in clusters_by_id:
            clusters_by_id[cluster.id] = []
        clusters_by_id[cluster.id].append(cluster)

    # Process each cluster
    for cluster_id, cluster_list in clusters_by_id.items():
        # Sort clusters by year
        cluster_list.sort(key=lambda c: c.year)

        # Get the most recent cluster for display
        latest_cluster = cluster_list[-1]

        html.append(f"    <div class=\"cluster-section\" id=\"cluster-{cluster_id}\">")
        html.append(f"        <h3>Cluster {cluster_id}</h3>")
        html.append(f"        <p>This cluster contains {latest_cluster.size} statements from {latest_cluster.year}.</p>")

        # Process each component type
        for component_type in ComponentType:
            # Check if we have analyses for this cluster and component type
            cluster_component_analyses = [a for a in component_analyses if a.cluster_id == cluster_id and a.component_type == component_type]

            if not cluster_component_analyses:
                continue

            # Sort analyses by year
            cluster_component_analyses.sort(key=lambda a: a.year)

            html.append(f"        <div class=\"component-section\" id=\"cluster-{cluster_id}-{component_type.value}\">")
            html.append(f"            <h4>{component_type.value.capitalize()} Analysis</h4>")

            # Process each year
            for analysis in cluster_component_analyses:
                html.append(f"            <div class=\"year-section\" id=\"cluster-{cluster_id}-{component_type.value}-{analysis.year}\">")
                html.append(f"                <h5>{analysis.year}</h5>")
                html.append(f"                <p class=\"summary\">{analysis.summary}</p>")
                html.append(f"                <p>{analysis.detailed_analysis}</p>")

                # Risks
                if analysis.potential_risks:
                    html.append("                <h6 class=\"risks\">Potential Risks:</h6>")
                    html.append("                <ul class=\"risks\">")
                    for risk in analysis.potential_risks:
                        html.append(f"                    <li>{risk}</li>")
                    html.append("                </ul>")

                # Opportunities
                if analysis.potential_opportunities:
                    html.append("                <h6 class=\"opportunities\">Potential Opportunities:</h6>")
                    html.append("                <ul class=\"opportunities\">")
                    for opportunity in analysis.potential_opportunities:
                        html.append(f"                    <li>{opportunity}</li>")
                    html.append("                </ul>")

                # Confidence
                html.append(f"                <p class=\"confidence\">Confidence: {analysis.confidence:.2f}</p>")
                html.append("            </div>")

            html.append("        </div>")

        html.append("    </div>")

    # Add cluster-level analysis section if available
    if cluster_analyses:
        html.append("    <h2>Combined Cluster Analysis</h2>")
        html.append("    <p>This section presents a combined analysis of all components for each cluster, providing a holistic view of the cluster's behavior.</p>")

        # Group cluster analyses by cluster ID
        cluster_analyses_by_id = {}
        for analysis in cluster_analyses:
            if analysis.cluster_id not in cluster_analyses_by_id:
                cluster_analyses_by_id[analysis.cluster_id] = []
            cluster_analyses_by_id[analysis.cluster_id].append(analysis)

        # Process each cluster
        for cluster_id, analyses_list in cluster_analyses_by_id.items():
            # Sort analyses by year
            analyses_list.sort(key=lambda a: a.year)

            html.append(f"    <div class=\"cluster-section\" id=\"combined-cluster-{cluster_id}\">")
            html.append(f"        <h3>Combined Analysis for Cluster {cluster_id}</h3>")

            # Process each year
            for analysis in analyses_list:
                html.append(f"        <div class=\"year-section\" id=\"combined-cluster-{cluster_id}-{analysis.year}\">")
                html.append(f"            <h4>Year {analysis.year}</h4>")
                html.append(f"            <p class=\"summary\">{analysis.summary}</p>")
                html.append(f"            <p>{analysis.detailed_analysis}</p>")

                # Component summaries
                html.append("            <h5>Component Summaries</h5>")
                html.append("            <ul>")
                html.append(f"                <li><strong>Motivation:</strong> {analysis.motivation_summary}</li>")
                html.append(f"                <li><strong>Statement Type:</strong> {analysis.statement_type_summary}</li>")
                html.append(f"                <li><strong>Engagement:</strong> {analysis.engagement_summary}</li>")
                html.append(f"                <li><strong>Impact:</strong> {analysis.impact_summary}</li>")
                html.append("            </ul>")

                # Risks
                if analysis.potential_risks:
                    html.append("            <h5 class=\"risks\">Potential Risks:</h5>")
                    html.append("            <ul class=\"risks\">")
                    for risk in analysis.potential_risks:
                        html.append(f"                <li>{risk}</li>")
                    html.append("            </ul>")

                # Opportunities
                if analysis.potential_opportunities:
                    html.append("            <h5 class=\"opportunities\">Potential Opportunities:</h5>")
                    html.append("            <ul class=\"opportunities\">")
                    for opportunity in analysis.potential_opportunities:
                        html.append(f"                <li>{opportunity}</li>")
                    html.append("            </ul>")

                # Confidence
                html.append(f"            <p class=\"confidence\">Confidence: {analysis.confidence:.2f}</p>")
                html.append("        </div>")

            html.append("    </div>")

    # Add entity-year analysis section if available
    if entity_year_analyses:
        html.append("    <h2>Entity-Level Yearly Analysis</h2>")
        html.append("    <p>This section presents a combined analysis of all clusters for each year, providing a holistic view of the entity's behavior.</p>")

        # Sort entity-year analyses by year
        entity_year_analyses.sort(key=lambda a: a.year)

        # Process each year
        for analysis in entity_year_analyses:
            html.append(f"    <div class=\"year-section\" id=\"entity-year-{analysis.year}\">")
            html.append(f"        <h3>Entity Analysis for Year {analysis.year}</h3>")
            html.append(f"        <p class=\"summary\">{analysis.summary}</p>")
            html.append(f"        <p>{analysis.detailed_analysis}</p>")

            # Cluster summaries
            if analysis.cluster_summaries:
                html.append("        <h4>Cluster Summaries</h4>")
                html.append("        <ul>")
                for cluster_id, summary in analysis.cluster_summaries.items():
                    html.append(f"            <li><strong>Cluster {cluster_id}:</strong> {summary}</li>")
                html.append("        </ul>")

            # Risks
            if analysis.potential_risks:
                html.append("        <h4 class=\"risks\">Potential Risks:</h4>")
                html.append("        <ul class=\"risks\">")
                for risk in analysis.potential_risks:
                    html.append(f"            <li>{risk}</li>")
                html.append("        </ul>")

            # Opportunities
            if analysis.potential_opportunities:
                html.append("        <h4 class=\"opportunities\">Potential Opportunities:</h4>")
                html.append("        <ul class=\"opportunities\">")
                for opportunity in analysis.potential_opportunities:
                    html.append(f"            <li>{opportunity}</li>")
                html.append("        </ul>")

            # Confidence
            html.append(f"        <p class=\"confidence\">Confidence: {analysis.confidence:.2f}</p>")
            html.append("    </div>")

    html.append("</body>")
    html.append("</html>")

    # Join HTML content
    html_content = "\n".join(html)

    # Save to file
    if output_file:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, "w") as f:
            f.write(html_content)
        return output_file
    else:
        # Save to a default location
        run_id = predictions[0].run_id if predictions else "unknown"
        entity_id = virtual_entity.id
        default_file = os.path.join(
            eko_var_path,
            f"reports/predictive/{entity_id}_{run_id}.html"
        )
        os.makedirs(os.path.dirname(default_file), exist_ok=True)
        with open(default_file, "w") as f:
            f.write(html_content)
        return default_file




def create_cluster_trend_chart(
    cluster_id: int,
    clusters: List[DSO_ClusterModel],
    predictions: List[ComponentPredictionModel],
    cluster_analysis: Optional[ClusterAnalysisModel] = None
) -> Tuple[Figure, str]:
    """
    Create a single chart showing trends for a specific cluster over time,
    including both historical and predicted data for motivation, impact, and engagement.

    Args:
        cluster_id: ID of the cluster to visualize
        clusters: List of historical DSO cluster models
        predictions: List of component prediction models for all clusters
        cluster_analysis: Optional cluster analysis model with historical data

    Returns:
        Tuple of (Figure, base64 encoded string of the figure)
    """
    # Filter predictions for this cluster
    cluster_predictions = [p for p in predictions if p.cluster_id == cluster_id]

    if not cluster_predictions:
        logger.warning(f"No predictions found for cluster {cluster_id}")
        return plt.figure(), ""

    # Filter historical clusters for this cluster
    historical_clusters = [c for c in clusters if c.id == cluster_id]

    # Group predictions by component type
    predictions_by_component = {}
    for prediction in cluster_predictions:
        if prediction.component_type not in predictions_by_component:
            predictions_by_component[prediction.component_type] = []
        predictions_by_component[prediction.component_type].append(prediction)

    # Get all years (both historical and predicted)
    all_years = set()

    # First try to get historical years from cluster analysis if available
    historical_vectors_by_year = {}
    if cluster_analysis and cluster_analysis.historical_vectors_by_year:
        # Convert string keys to integers if needed
        for year_str, vectors in cluster_analysis.historical_vectors_by_year.items():
            try:
                year = int(year_str) if isinstance(year_str, str) else year_str
                historical_vectors_by_year[year] = vectors
                all_years.add(year)
            except (ValueError, TypeError):
                continue

    # If no historical data in cluster analysis, get from predictions
    if not historical_vectors_by_year:
        for prediction in cluster_predictions:
            # Add all historical years
            all_years.update(prediction.historical_vectors_by_year.keys())

    # Add predicted years
    for prediction in cluster_predictions:
        all_years.add(prediction.predicted_year)

    all_years = sorted(all_years)

    # Find the index where historical data ends and predictions begin
    historical_years = sorted(set(c.year for c in historical_clusters))
    predicted_years = sorted(set(p.predicted_year for p in cluster_predictions))

    # If we don't have historical years from clusters, get them from cluster analysis or predictions
    if not historical_years:
        if historical_vectors_by_year:
            historical_years = sorted(historical_vectors_by_year.keys())
        elif cluster_predictions:
            # Get all historical years from the predictions
            for prediction in cluster_predictions:
                historical_years.extend(prediction.historical_vectors_by_year.keys())
            historical_years = sorted(set(historical_years))

    # Create visualization
    fig, ax = plt.subplots(figsize=(12, 8))

    # Define key dimensions to visualize for each component
    key_dimensions = {
        ComponentType.MOTIVATION: [
            ('genuine', 'g', 'Genuine Motivation'),
            ('opportunistic', 'r', 'Opportunistic Motivation')
        ],
        ComponentType.ENGAGEMENT: [
            ('proactive', 'b', 'Proactive Engagement'),
            ('reactive', 'orange', 'Reactive Engagement')
        ],
        ComponentType.IMPACT: [
            ('benefit_to_human_life', 'purple', 'Human Benefit'),
            ('harm_to_human_life', 'brown', 'Human Harm')
        ]
    }

    # Plot historical data with solid lines
    for component_type, dimensions in key_dimensions.items():
        if component_type not in predictions_by_component:
            continue

        component_predictions = predictions_by_component[component_type]
        component_type_str = component_type.value

        # Collect all historical years - first from cluster analysis, then from predictions
        all_historical_years = set()

        # First try to get from cluster analysis
        if historical_vectors_by_year and any(component_type_str in vectors for vectors in historical_vectors_by_year.values()):
            for year, vectors in historical_vectors_by_year.items():
                if component_type_str in vectors:
                    all_historical_years.add(year)

        # If no historical data in cluster analysis for this component, get from predictions
        if not all_historical_years:
            for prediction in component_predictions:
                all_historical_years.update(prediction.historical_vectors_by_year.keys())

        # Group predicted data by year
        predicted_data_by_year = {}
        for prediction in component_predictions:
            if prediction.predicted_year not in predicted_data_by_year:
                predicted_data_by_year[prediction.predicted_year] = []
            predicted_data_by_year[prediction.predicted_year].append(prediction)

        # Plot each dimension
        for dim_name, color, label in dimensions:
            # Historical data points - use all available historical years
            hist_years = sorted(all_historical_years)
            hist_values = []

            for year in hist_years:
                # Calculate average value for this dimension across all predictions
                dim_sum = 0.0
                count = 0

                # First try to get from cluster analysis
                if year in historical_vectors_by_year and component_type_str in historical_vectors_by_year[year]:
                    vector = historical_vectors_by_year[year][component_type_str]

                    # Create appropriate model based on component type
                    if component_type == ComponentType.MOTIVATION:
                        model = cast(MotivationModel, MotivationModel.from_np(np.array(vector)))
                    elif component_type == ComponentType.ENGAGEMENT:
                        model = cast(EngagementModel, EngagementModel.from_np(np.array(vector)))
                    elif component_type == ComponentType.IMPACT:
                        model = cast(ImpactModel, ImpactModel.from_np(np.array(vector)))
                    else:
                        continue

                    # Extract dimension value if it exists
                    if hasattr(model, dim_name):
                        dim_sum += getattr(model, dim_name)
                        count += 1

                # If no data from cluster analysis, get from predictions
                if count == 0:
                    for data in component_predictions:
                        # Check if this prediction has data for this year
                        if year in data.historical_vectors_by_year:
                            # Get the vector for this year
                            vector = data.historical_vectors_by_year[year]

                            # Create appropriate model based on component type
                            if component_type == ComponentType.MOTIVATION:
                                model = cast(MotivationModel, MotivationModel.from_np(np.array(vector)))
                            elif component_type == ComponentType.ENGAGEMENT:
                                model = cast(EngagementModel, EngagementModel.from_np(np.array(vector)))
                            elif component_type == ComponentType.IMPACT:
                                model = cast(ImpactModel, ImpactModel.from_np(np.array(vector)))
                            else:
                                continue

                            # Extract dimension value if it exists
                            if hasattr(model, dim_name):
                                dim_sum += getattr(model, dim_name)
                                count += 1

                # Calculate average
                if count > 0:
                    hist_values.append(dim_sum / count)
                else:
                    hist_values.append(0.0)

            # Predicted data points
            pred_years = sorted(predicted_data_by_year.keys())
            pred_values = []

            for year in pred_years:
                year_data = predicted_data_by_year[year]
                # Calculate average value for this dimension across all predictions
                dim_sum = 0.0
                count = 0

                for data in year_data:
                    # Create appropriate model based on component type
                    if component_type == ComponentType.MOTIVATION:
                        model = cast(MotivationModel, MotivationModel.from_np(np.array(data.predicted_vector)))
                    elif component_type == ComponentType.ENGAGEMENT:
                        model = cast(EngagementModel, EngagementModel.from_np(np.array(data.predicted_vector)))
                    elif component_type == ComponentType.IMPACT:
                        model = cast(ImpactModel, ImpactModel.from_np(np.array(data.predicted_vector)))
                    else:
                        continue

                    # Extract dimension value if it exists
                    if hasattr(model, dim_name):
                        dim_sum += getattr(model, dim_name)
                        count += 1

                # Calculate average
                if count > 0:
                    pred_values.append(dim_sum / count)
                else:
                    pred_values.append(0.0)

            # Plot historical data with solid line
            if hist_years and hist_values:
                ax.plot(hist_years, hist_values, color=color, linestyle='-', linewidth=2,
                        label=f"{label} (Historical)")

                # Add markers for historical points
                ax.scatter(hist_years, hist_values, color=color, s=80, zorder=10,
                          edgecolor='black', linewidth=1.5)

            # Plot predicted data with dashed line
            if pred_years and pred_values:
                ax.plot(pred_years, pred_values, color=color, linestyle='--', linewidth=2,
                        label=f"{label} (Predicted)")

                # Add markers for predicted points
                ax.scatter(pred_years, pred_values, color=color, s=80, zorder=10,
                          edgecolor='black', linewidth=1.5, marker='s')

            # If we have both historical and predicted data, connect them
            if hist_years and hist_values and pred_years and pred_values:
                # Connect the last historical point to the first predicted point
                ax.plot([hist_years[-1], pred_years[0]],
                        [hist_values[-1], pred_values[0]],
                        color=color, linestyle=':', linewidth=1.5)

    # Add a vertical line to separate historical and predicted data if we have both
    if historical_years and predicted_years:
        # Add the vertical separator line
        ax.axvline(x=max(historical_years) + 0.5, color='k', linestyle='--', alpha=0.5)
        ax.text(max(historical_years) + 0.5, ax.get_ylim()[1] * 0.95,
               'Historical | Predicted', ha='center', va='top',
               bbox=dict(facecolor='white', alpha=0.8, edgecolor='none'))

    # Set title and labels
    ax.set_title(f'Cluster {cluster_id} Trends Over Time')
    ax.set_xlabel('Year')
    ax.set_ylabel('Value')
    ax.legend(loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=3)
    ax.grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout()

    # Convert to base64
    img_str = figure_to_base64(fig)

    return fig, img_str



def visualize_cluster_trends(
    clusters: List[DSO_ClusterModel],
    predictions: List[ComponentPredictionModel],
    cluster_id: int,
    output_file: Optional[str] = None,
    cluster_analysis: Optional[ClusterAnalysisModel] = None
) -> str:
    """
    Visualize trends for a specific cluster over time.

    Args:
        clusters: List of DSO cluster models
        predictions: List of component prediction models
        cluster_id: ID of the cluster to visualize
        output_file: Path to save the visualization
        cluster_analysis: Optional cluster analysis model with historical data

    Returns:
        Path to the generated visualization
    """

    # Create cluster trend chart
    fig, _ = create_cluster_trend_chart(cluster_id, clusters, predictions, cluster_analysis)

    # Save or show the visualization
    if output_file:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        fig.savefig(output_file)
        return output_file
    else:
        # Save to a default location
        cluster_predictions = [p for p in predictions if p.cluster_id == cluster_id]
        run_id = cluster_predictions[0].run_id if cluster_predictions else "unknown"
        default_file = os.path.join(
            eko_var_path,
            f"reports/predictive/cluster_{cluster_id}_trends_{run_id}.png"
        )
        os.makedirs(os.path.dirname(default_file), exist_ok=True)
        fig.savefig(default_file)
        return default_file


