"""
CLI commands for predictive analytics v2.
"""

import webbrowser
from psycopg.rows import dict_row
from typing import Optional

import click
from loguru import logger

from eko.analysis_v2.pipeline_tracker_extended import get_traceability_tracker
from eko.db import get_bo_conn
from eko.db.data.xfer import XferData
from eko.entities.virtual_queries import get_virtual_entity_for_analytics
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.prediction_v2.analysis import (
    generate_component_analysis,
    generate_cluster_analysis,
    generate_entity_year_analysis
)
from eko.prediction_v2.models import RegressionModelType
from eko.prediction_v2.regression import predict_component_trends
from eko.prediction_v2.clustering import create_dso_clusters
from eko.prediction_v2.utils import generate_predictive_report, visualize_cluster_trends

from eko.db.data.run import RunData
from eko.typing import not_none


@click.group(name="predictive-v2")
def predictive_v2_cli():
    """Commands for predictive analytics v2."""
    pass


@predictive_v2_cli.command(name="analyze-component-trends")
@click.option("--entity", required=True, help="Entity short ID")
@click.option("--start-year", required=True, type=int, help="Start year (inclusive)")
@click.option("--end-year", required=True, type=int, help="End year (inclusive)")
@click.option("--future-years", required=True, type=int, help="Number of future years to predict")
@click.option("--model-type", type=click.Choice([m.value for m in RegressionModelType]),
              default=RegressionModelType.ENSEMBLE.value, help="Regression model type")
@click.option("--report/--no-report", default=False, help="Generate HTML report")
@click.option("--open-report/--no-open-report", default=False, help="Open report in browser")
def analyze_component_trends(
    entity: str,
    start_year: int,
    end_year: int,
    future_years: int,
    model_type: str,
    report: bool,
    open_report: bool
):
    """
    Run the full predictive analytics v2 pipeline.

    This command performs the following steps:
    1. Create DSO clusters for historical years
    2. Predict component trends for future years
    3. Generate human-readable analysis
    4. Optionally generate and open an HTML report
    """
    logger.info(f"Running predictive analytics v2 for {entity} from {start_year} to {end_year} + {future_years} years")

    # Get virtual entity
    virtual_entity = get_virtual_entity_for_analytics(entity)
    if not virtual_entity:
        logger.error(f"Virtual entity not found: {entity}")
        return

    # Initialize tracker as None
    tracker = None
    run_id = None

    # Create a run for this analysis
    with get_bo_conn() as conn:
        run = RunData.create_predictive_run(
            conn=conn,
            start_year=start_year,
            end_year=end_year,
            target=entity
        )
        run_id = run.id

        # Now initialize the tracker with the connection and run_id
        tracker = get_traceability_tracker(conn, run_id)

    run_prediction(end_year, future_years, model_type, open_report, report, run_id, start_year, tracker, virtual_entity)


def run_prediction(end_year, future_years, model_type, open_report, report, run_id, start_year, tracker,
                   virtual_entity:VirtualEntityExpandedModel):
    
    # Step 1: Create DSO clusters
    logger.info("Step 1: Creating DSO clusters")
    clusters = create_dso_clusters(
        virtual_entity,
        start_year,
        end_year,
        tracker,
        run_id
    )
    logger.info(f"Created {len(clusters)} DSO clusters")
    # Step 2: Predict component trends
    logger.info("Step 2: Predicting component trends")
    historical_years = list(range(start_year, end_year + 1))
    future_year_list = list(range(end_year + 1, end_year + future_years + 1))
    predictions = predict_component_trends(
        not_none(run_id),
        clusters, virtual_entity, tracker, RegressionModelType(model_type),
        historical_years,
        future_year_list,
    )
    logger.info(f"Created {len(predictions)} component predictions")
    # Step 3: Generate component-level analysis
    logger.info("Step 3: Generating component-level analysis")
    component_analyses = generate_component_analysis(
        virtual_entity,
        predictions,
        tracker
    )
    logger.info(f"Created {len(component_analyses)} component analyses")
    # Step 4: Generate cluster-level analysis
    logger.info("Step 4: Generating cluster-level analysis")
    cluster_analyses = generate_cluster_analysis(
        virtual_entity,
        component_analyses,
        tracker
    )
    logger.info(f"Created {len(cluster_analyses)} cluster analyses")
    # Step 5: Generate entity-year analysis
    logger.info("Step 5: Generating entity-year analysis")
    entity_year_analyses = generate_entity_year_analysis(
        virtual_entity,
        cluster_analyses,
        tracker
    )
    logger.info(f"Created {len(entity_year_analyses)} entity-year analyses")
    # Step 6: Generate report
    if report:
        logger.info("Step 6: Generating HTML report")
        report_file = generate_predictive_report(
            virtual_entity,
            clusters,
            predictions,
            component_analyses,
            cluster_analyses,
            entity_year_analyses
        )
        logger.info(f"Report generated: {report_file}")

        if open_report and report_file:
            logger.info(f"Opening report in browser: {report_file}")
            webbrowser.open(f"file://{report_file}")
    # Step 7: Sync data to xfer tables
    logger.info("Step 7: Syncing data to xfer tables")
    with get_bo_conn() as conn:
        synced_counts = XferData.sync_prediction_v2_to_xfer(conn, not_none(run_id), virtual_entity)
        logger.info(f"Synced data to xfer tables: {synced_counts}")
    logger.info("Predictive analytics v2 completed successfully")


@predictive_v2_cli.command(name="create-dso-clusters")
@click.option("--entity", required=True, help="Entity short ID")
@click.option("--start-year", required=True, type=int, help="Start year (inclusive)")
@click.option("--end-year", required=True, type=int, help="End year (inclusive)")
@click.option("--run-id", type=int, help="Run ID (optional, will create a new run if not provided)")
def create_dso_clusters_cmd(
    entity: str,
    start_year: int,
    end_year: int,
    run_id: Optional[int] = None
):
    """
    Create DSO clusters for historical years.
    """
    logger.info(f"Creating DSO clusters for {entity} from {start_year} to {end_year}")

    # Get virtual entity
    virtual_entity = get_virtual_entity_for_analytics(entity)
    if not virtual_entity:
        logger.error(f"Virtual entity not found: {entity}")
        return

    # Initialize tracker as None
    tracker = None

    # Create a run if not provided
    with get_bo_conn() as conn:
        if run_id is None:
            run = RunData.create_predictive_run(
                conn=conn,
                start_year=start_year,
                end_year=end_year,
                target=entity
            )
            run_id = run.id

        # Now initialize the tracker with the connection and run_id
        tracker = get_traceability_tracker(conn, run_id)

    # Create DSO clusters
    clusters = create_dso_clusters(
        virtual_entity,
        start_year,
        end_year,
        tracker,
        run_id
    )

    logger.info(f"Created {len(clusters)} DSO clusters")



@predictive_v2_cli.command(name="generate-component-analysis")
@click.option("--entity", required=True, help="Entity short ID")
@click.option("--run-id", required=True, type=int, help="Run ID")
@click.option("--report/--no-report", default=False, help="Generate HTML report")
@click.option("--open-report/--no-open-report", default=False, help="Open report in browser")
def generate_component_analysis_cmd(
    entity: str,
    run_id: int,
    report: bool,
    open_report: bool
):
    """
    Generate human-readable analysis of predicted component trends.
    """
    logger.info(f"Generating component analysis for {entity} with run ID {run_id}")

    # Get virtual entity
    virtual_entity = get_virtual_entity_for_analytics(entity)
    if not virtual_entity:
        logger.error(f"Virtual entity not found: {entity}")
        return

    # Initialize tracker as None
    tracker = None
    clusters = None
    predictions = None

    # Get predictions from database
    with get_bo_conn() as conn:
        # Initialize the tracker with the connection and run_id
        tracker = get_traceability_tracker(conn, run_id)

        with conn.cursor(row_factory=dict_row) as cursor:
            from eko.prediction_v2.dao import ComponentPredictionDAO, DSO_ClusterDAO

            # Get predictions
            predictions = ComponentPredictionDAO.find_by_virtual_entity_and_run(
                cursor, virtual_entity.id, run_id
            )

            if not predictions:
                logger.error(f"No predictions found for entity {entity} with run ID {run_id}")
                return

            # Get clusters
            clusters = DSO_ClusterDAO.find_by_virtual_entity_and_run(
                cursor, virtual_entity.id, run_id
            )

            if not clusters:
                logger.error(f"No clusters found for entity {entity} with run ID {run_id}")
                return

    # Generate analysis
    analyses = generate_component_analysis(
        virtual_entity,
        predictions,
        tracker
    )

    logger.info(f"Created {len(analyses)} component analyses")

    # Generate report
    if report:
        logger.info("Generating HTML report")
        report_file = generate_predictive_report(
            virtual_entity,
            clusters,
            predictions,
            analyses
        )
        logger.info(f"Report generated: {report_file}")

        if open_report and report_file:
            logger.info(f"Opening report in browser: {report_file}")
            webbrowser.open(f"file://{report_file}")


@predictive_v2_cli.command(name="visualize-cluster-trends")
@click.option("--entity", required=True, help="Entity short ID")
@click.option("--run-id", required=True, type=int, help="Run ID")
@click.option("--cluster-id", required=True, type=int, help="Cluster ID to visualize")
@click.option("--output-file", help="Output file path (optional)")
@click.option("--open/--no-open", default=False, help="Open visualization in browser")
def visualize_cluster_trends_cmd(
    entity: str,
    run_id: int,
    cluster_id: int,
    output_file: Optional[str] = None,
    open: bool = False
):
    """
    Visualize trends for a specific cluster over time.
    """
    logger.info(f"Visualizing cluster {cluster_id} trends for {entity} with run ID {run_id}")

    # Get virtual entity
    virtual_entity = get_virtual_entity_for_analytics(entity)
    if not virtual_entity:
        logger.error(f"Virtual entity not found: {entity}")
        return

    # Get predictions and clusters from database
    with get_bo_conn() as conn:
        with conn.cursor(row_factory=dict_row) as cursor:
            from eko.prediction_v2.dao import ComponentPredictionDAO, DSO_ClusterDAO

            # Get predictions
            predictions = ComponentPredictionDAO.find_by_virtual_entity_and_run(
                cursor, virtual_entity.id, run_id
            )

            if not predictions:
                logger.error(f"No predictions found for entity {entity} with run ID {run_id}")
                return

            # Get clusters
            clusters = DSO_ClusterDAO.find_by_virtual_entity_and_run(
                cursor, virtual_entity.id, run_id
            )

            if not clusters:
                logger.error(f"No clusters found for entity {entity} with run ID {run_id}")
                return

    # Visualize cluster trends
    visualization_file = visualize_cluster_trends(
        clusters,
        predictions,
        cluster_id,
        output_file
    )

    logger.info(f"Visualization generated: {visualization_file}")

    if open and visualization_file:
        logger.info(f"Opening visualization in browser: {visualization_file}")
        webbrowser.open(f"file://{visualization_file}")


@predictive_v2_cli.command(name="generate-report")
@click.option("--entity", required=True, help="Entity short ID")
@click.option("--run-id", required=True, type=int, help="Run ID")
@click.option("--output-file", help="Output file path (optional)")
@click.option("--open/--no-open", default=False, help="Open report in browser")
def generate_report_cmd(
    entity: str,
    run_id: int,
    output_file: Optional[str] = None,
    open: bool = False
):
    """
    Generate an HTML report of predictive component analysis.
    """
    logger.info(f"Generating report for {entity} with run ID {run_id}")

    # Get virtual entity
    virtual_entity = get_virtual_entity_for_analytics(entity)
    if not virtual_entity:
        logger.error(f"Virtual entity not found: {entity}")
        return

    # Get data from database
    with get_bo_conn() as conn:
        with conn.cursor(row_factory=dict_row) as cursor:
            from eko.prediction_v2.dao import (
                ComponentPredictionDAO,
                DSO_ClusterDAO,
                PredictiveComponentAnalysisDAO
            )

            # Get predictions
            predictions = ComponentPredictionDAO.find_by_virtual_entity_and_run(
                cursor, virtual_entity.id, run_id
            )

            if not predictions:
                logger.error(f"No predictions found for entity {entity} with run ID {run_id}")
                return

            # Get clusters
            clusters = DSO_ClusterDAO.find_by_virtual_entity_and_run(
                cursor, virtual_entity.id, run_id
            )

            if not clusters:
                logger.error(f"No clusters found for entity {entity} with run ID {run_id}")
                return

            # Get analyses
            analyses = PredictiveComponentAnalysisDAO.find_by_virtual_entity_and_run(
                cursor, virtual_entity.id, run_id
            )

            if not analyses:
                logger.error(f"No analyses found for entity {entity} with run ID {run_id}")
                return

    # Generate report
    report_file = generate_predictive_report(
        virtual_entity,
        clusters,
        predictions,
        analyses,
        None,  # cluster_analyses
        None,  # entity_year_analyses
        output_file
    )

    logger.info(f"Report generated: {report_file}")

    if open and report_file:
        logger.info(f"Opening report in browser: {report_file}")
        webbrowser.open(f"file://{report_file}")


def register_cli(cli):
    """Register CLI commands."""
    cli.add_command(predictive_v2_cli)
