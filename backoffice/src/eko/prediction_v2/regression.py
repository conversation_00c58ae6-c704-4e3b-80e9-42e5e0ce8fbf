"""
Regression functionality for predictive analytics v2.

This module provides functions for predicting future trends in DEMISE components
(Motivation, Statement Types, Engagement, Impact) for each DSO cluster.
"""

import os
from abc import ABC, abstractmethod
from psycopg.rows import dict_row
from typing import List, Dict, Optional, Any, Tuple, Type, cast

import numpy as np
import pandas as pd
from loguru import logger
from psycopg import Cursor
from sklearn.preprocessing import StandardScaler
from sklearn.gaussian_process.kernels import RBF, ConstantKernel as C
from sklearn.gaussian_process import GaussianProcessRegressor

from eko.analysis_v2.pipeline_tracker_extended import TraceabilityTracker
from eko.cache.pg_cache import MultiLevelCache
from eko.db import get_bo_conn
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.derived.enums import PipelineStage
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.prediction_v2.dao import DSO_ClusterDAO, ComponentPredictionDAO
from eko.prediction_v2.clustering import extract_core_domain_keys
from eko.prediction_v2.models import (
    DSO_ClusterModel,
    ComponentPredictionModel,
    RegressionModelType,
    ComponentType
)
from pgvector.psycopg import register_vector
from eko.settings import settings
from eko.typing import not_none

cache= MultiLevelCache("prediction_v2")

class BaseRegressionModel(ABC):
    """Base class for regression models."""

    def __init__(self, name: str):
        """
        Initialize the regression model.

        Args:
            name: Name of the model
        """
        self.name = name
        self.model_type = None

    @abstractmethod
    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the model to the data.

        Args:
            X: Input features (years)
            y: Target values (vectors)
        """
        pass

    @abstractmethod
    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Predict values for the given input features.

        Args:
            X: Input features (years)

        Returns:
            Tuple of (predictions, confidence)
        """
        pass


class VARModel(BaseRegressionModel):
    """Vector Autoregression model."""

    def __init__(self):
        """Initialize the VAR model."""
        super().__init__("VAR")
        self.model_type = RegressionModelType.VAR
        self.model = None
        self.results = None
        self.scaler = StandardScaler()

    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the VAR model to the data.

        Args:
            X: Input features (years)
            y: Target values (vectors)
        """
        try:
            from statsmodels.tsa.vector_ar.var_model import VAR

            # Prepare data for VAR
            # VAR requires a 2D array with shape (n_samples, n_features)
            # where each row is a time point and columns are variables

            # Scale the data
            y_scaled = self.scaler.fit_transform(y)

            # Create a dataset with years and vectors
            data = np.column_stack([X.reshape(-1, 1), y_scaled])

            # Fit the VAR model
            self.model = VAR(data)
            self.results = self.model.fit(maxlags=min(2, len(X) - 1))

            logger.info(f"Fitted VAR model with {len(X)} observations")
        except Exception as e:
            logger.exception(f"Error fitting VAR model: {str(e)}")
            raise

    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Predict values for the given input features.

        Args:
            X: Input features (years)

        Returns:
            Tuple of (predictions, confidence)
        """
        try:
            if self.results is None:
                raise ValueError("Model not fitted")

            # Get the last observation from the training data
            last_obs = self.results.y[-1:]

            # Calculate the number of steps to forecast
            steps = len(X)

            # Forecast future values
            forecast = self.results.forecast(last_obs, steps)

            # Extract the predicted values (excluding the year column)
            predictions_scaled = forecast[:, 1:]

            # Inverse transform to get the original scale
            predictions = self.scaler.inverse_transform(predictions_scaled)

            # Calculate confidence based on the model's fit
            # For VAR, we use a simple heuristic based on the number of observations
            # and the forecast horizon
            n_obs = len(self.results.y)
            confidence = np.ones(len(X)) * max(0.1, min(0.9, n_obs / (n_obs + steps)))

            return predictions, confidence
        except Exception as e:
            logger.exception(f"Error predicting with VAR model: {str(e)}")
            raise


class GaussianProcessModel(BaseRegressionModel):
    """Gaussian Process Regression model."""

    def __init__(self):
        """Initialize the Gaussian Process model."""
        super().__init__("Gaussian Process")
        self.model_type = RegressionModelType.GAUSSIAN_PROCESS
        self.models = []
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()

    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the Gaussian Process model to the data.

        Args:
            X: Input features (years)
            y: Target values (vectors)
        """
        try:
            # Scale the inputs
            X_scaled = self.scaler_X.fit_transform(X.reshape(-1, 1))
            y_scaled = self.scaler_y.fit_transform(y)

            # Create a separate GP model for each dimension of the vector
            self.models = []
            for i in range(y.shape[1]):
                # Define the kernel
                kernel = C(1.0, (1e-3, 1e3)) * RBF(1.0, (1e-2, 1e2))

                # Create and fit the model
                gp = GaussianProcessRegressor(
                    kernel=kernel,
                    n_restarts_optimizer=10,
                    alpha=1e-6,
                    normalize_y=True,
                    random_state=42
                )
                gp.fit(X_scaled, y_scaled[:, i])
                self.models.append(gp)

            logger.info(f"Fitted Gaussian Process model with {len(X)} observations and {len(self.models)} dimensions")
        except Exception as e:
            logger.exception(f"Error fitting Gaussian Process model: {str(e)}")
            raise

    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Predict values for the given input features.

        Args:
            X: Input features (years)

        Returns:
            Tuple of (predictions, confidence)
        """
        try:
            if not self.models:
                raise ValueError("Model not fitted")

            # Scale the inputs
            X_scaled = self.scaler_X.transform(X.reshape(-1, 1))

            # Predict each dimension separately
            predictions_scaled = np.zeros((len(X), len(self.models)))
            confidence = np.zeros(len(X))

            for i, gp in enumerate(self.models):
                # Predict with the GP model
                y_pred, y_std = gp.predict(X_scaled, return_std=True)
                predictions_scaled[:, i] = y_pred

                # Calculate confidence as 1 - normalized standard deviation
                # Average across all dimensions
                confidence += 1.0 / (1.0 + y_std)

            # Normalize confidence to [0, 1]
            confidence /= len(self.models)
            confidence = np.clip(confidence, 0.1, 0.9)

            # Inverse transform to get the original scale
            predictions = self.scaler_y.inverse_transform(predictions_scaled)

            return predictions, confidence
        except Exception as e:
            logger.exception(f"Error predicting with Gaussian Process model: {str(e)}")
            raise


class ProphetModel(BaseRegressionModel):
    """Prophet model for trend and seasonality decomposition."""

    def __init__(self):
        """Initialize the Prophet model."""
        super().__init__("Prophet")
        self.model_type = RegressionModelType.PROPHET
        self.models = []
        self.scaler = StandardScaler()
        self.fallback = None

    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the Prophet model to the data.

        Args:
            X: Input features (years)
            y: Target values (vectors)
        """
        try:
            # Check if Prophet is available
            try:
                from prophet import Prophet
            except ImportError:
                logger.warning("Prophet not installed. Using fallback model.")
                # Use Gaussian Process as fallback
                self.fallback = GaussianProcessModel()
                self.fallback.fit(X, y)
                return

            # Scale the data
            y_scaled = self.scaler.fit_transform(y)

            # Create a separate Prophet model for each dimension
            self.models = []
            for i in range(y.shape[1]):
                # Prepare data for Prophet
                df = pd.DataFrame({
                    'ds': pd.to_datetime([f"{int(year)}-01-01" for year in X]),
                    'y': y_scaled[:, i]
                })

                # Create and fit the model
                model = Prophet(
                    yearly_seasonality=False,
                    weekly_seasonality=False,
                    daily_seasonality=False,
                    interval_width=0.95
                )
                model.fit(df)
                self.models.append(model)

            logger.info(f"Fitted Prophet model with {len(X)} observations and {len(self.models)} dimensions")
        except Exception as e:
            logger.exception(f"Error fitting Prophet model: {str(e)}")
            # Use Gaussian Process as fallback
            self.fallback = GaussianProcessModel()
            self.fallback.fit(X, y)

    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Predict values for the given input features.

        Args:
            X: Input features (years)

        Returns:
            Tuple of (predictions, confidence)
        """
        try:
            # If using fallback model
            if self.fallback is not None:
                return self.fallback.predict(X)

            if not self.models:
                raise ValueError("Model not fitted")

            # Prepare future dataframe for Prophet
            future_dates = pd.DataFrame({
                'ds': pd.to_datetime([f"{int(year)}-01-01" for year in X])
            })

            # Predict each dimension separately
            predictions_scaled = np.zeros((len(X), len(self.models)))
            confidence = np.zeros(len(X))

            for i, model in enumerate(self.models):
                # Predict with Prophet
                forecast = model.predict(future_dates)
                predictions_scaled[:, i] = forecast['yhat'].values

                # Calculate confidence based on prediction intervals
                upper = forecast['yhat_upper'].values
                lower = forecast['yhat_lower'].values
                interval_width = upper - lower
                # Normalize to [0, 1] range
                dim_confidence = 1.0 / (1.0 + interval_width)
                confidence += dim_confidence

            # Normalize confidence to [0, 1]
            confidence /= len(self.models)
            confidence = np.clip(confidence, 0.1, 0.9)

            # Inverse transform to get the original scale
            predictions = self.scaler.inverse_transform(predictions_scaled)

            return predictions, confidence
        except Exception as e:
            logger.exception(f"Error predicting with Prophet model: {str(e)}")
            # If fallback is available, use it
            if self.fallback is not None:
                return self.fallback.predict(X)
            raise


class EnsembleModel(BaseRegressionModel):
    """Ensemble model combining multiple regression models."""

    def __init__(self):
        """Initialize the Ensemble model."""
        super().__init__("Ensemble")
        self.model_type = RegressionModelType.ENSEMBLE
        self.models = []
        self.weights = []

    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Fit the Ensemble model to the data.

        Args:
            X: Input features (years)
            y: Target values (vectors)
        """
        try:
            # Create and fit individual models
            models = [
                GaussianProcessModel(),
                VARModel()
            ]

            # Try to add Prophet if available
            try:
                models.append(ProphetModel())
            except Exception:
                logger.warning("Prophet model not available, skipping in ensemble")

            # Fit each model
            for model in models:
                try:
                    model.fit(X, y)
                    self.models.append(model)
                except Exception as e:
                    logger.warning(f"Error fitting {model.name} model: {str(e)}")

            # If no models were successfully fitted, raise an error
            if not self.models:
                raise ValueError("No models could be fitted")

            # For now, use equal weights for all models
            self.weights = np.ones(len(self.models)) / len(self.models)

            logger.info(f"Fitted Ensemble model with {len(self.models)} sub-models")
        except Exception as e:
            logger.exception(f"Error fitting Ensemble model: {str(e)}")
            raise

    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Predict values for the given input features.

        Args:
            X: Input features (years)

        Returns:
            Tuple of (predictions, confidence)
        """
        try:
            if not self.models:
                raise ValueError("Model not fitted")

            # Get predictions from each model
            all_predictions = []
            all_confidences = []

            for model in self.models:
                predictions, confidence = model.predict(X)
                all_predictions.append(predictions)
                all_confidences.append(confidence)

            # Combine predictions using weights
            weighted_predictions = np.zeros_like(all_predictions[0])
            weighted_confidence = np.zeros_like(all_confidences[0])

            for i, (pred, conf) in enumerate(zip(all_predictions, all_confidences)):
                weighted_predictions += self.weights[i] * pred
                weighted_confidence += self.weights[i] * conf

            return weighted_predictions, weighted_confidence
        except Exception as e:
            logger.exception(f"Error predicting with Ensemble model: {str(e)}")
            raise


def get_regression_model(model_type: RegressionModelType) -> BaseRegressionModel:
    """
    Get a regression model instance by type.

    Args:
        model_type: Type of regression model

    Returns:
        Regression model instance
    """
    model_map = {
        RegressionModelType.VAR: VARModel,
        RegressionModelType.GAUSSIAN_PROCESS: GaussianProcessModel,
        RegressionModelType.PROPHET: ProphetModel,
        RegressionModelType.ENSEMBLE: EnsembleModel
    }

    model_class = model_map.get(model_type)
    if model_class is None:
        raise ValueError(f"Unknown model type: {model_type}")

    return model_class()

def extract_component_vector(demise_model: DEMISEModel, component_type: ComponentType) -> List[float]:
    """
    Extract a specific component vector from a DEMISE embedding.

    Args:
        demise_embedding: Full DEMISE embedding
        component_type: Type of component to extract

    Returns:
        Component vector
    """

    # Extract the requested component vector
    if component_type == ComponentType.MOTIVATION:
        return demise_model.motivation.to_vector()
    elif component_type == ComponentType.STATEMENT_TYPE:
        raise NotImplementedError("Statement type component extraction not implemented")
    elif component_type == ComponentType.ENGAGEMENT:
        return demise_model.engagement.to_vector()
    elif component_type == ComponentType.IMPACT:
        return demise_model.impact.to_vector()
    else:
        raise ValueError(f"Unknown component type: {component_type}")


def predict_component_trends(
        run_id:int,
    clusters: List[DSO_ClusterModel],
    virtual_entity: VirtualEntityExpandedModel,
    tracker: TraceabilityTracker,
    model_type: RegressionModelType,
    historical_years: List[int],
    future_years: List[int],
) -> List[ComponentPredictionModel]:
    """
    Predict future trends for DEMISE components in each DSO cluster.

    Returns:
        List of component prediction models
    """
    if tracker:
        # Check if the tracker has the start_stage method
        if hasattr(tracker, 'start_stage'):
            tracker.start_stage(PipelineStage.PREDICTIVE_REGRESSION)

    logger.info(f"Predicting component trends for {virtual_entity.name} "
                f"from {historical_years} to {future_years}")

    predictions = []

    with get_bo_conn() as conn:
        register_vector(conn)
        with conn.cursor(row_factory=dict_row) as cursor:

            # Group clusters by year
            clusters_by_year = {}
            for cluster in clusters:
                if cluster.year not in clusters_by_year:
                    clusters_by_year[cluster.year] = []
                clusters_by_year[cluster.year].append(cluster)

            # Get statement embeddings for each cluster
            for cluster in clusters:
                statement_ids = cluster.statement_ids
                if not statement_ids:
                    continue

                # Get DEMISE embeddings for statements in this cluster
                query = """
                SELECT id, model_json
                FROM kg_statements_v2
                WHERE id = ANY(%s)
                """
                cursor.execute(query, (statement_ids,))
                statements = cursor.fetchall()

                # Extract component vectors for each component type
                for component_type in [ComponentType.MOTIVATION, ComponentType.ENGAGEMENT, ComponentType.IMPACT]:
                    # Extract component vectors from statements
                    component_vectors = []
                    for statement in statements:
                        demise = DEMISEModel.model_validate(statement["model_json"])

                        component_vector = extract_component_vector(demise, component_type)
                        component_vectors.append(component_vector)

                    if not component_vectors:
                        continue

                    # Calculate component centroid for this cluster
                    component_centroid = np.mean(component_vectors, axis=0).tolist()

                    # Find similar clusters in other years
                    similar_clusters = []
                    for year, year_clusters in clusters_by_year.items():
                        if year == cluster.year:
                            continue

                        # Find the most similar cluster in this year
                        best_similarity = -1
                        best_cluster = None

                        for year_cluster in year_clusters:
                            # Calculate similarity based on DSO centroids
                            similarity = np.dot(
                                np.array(cluster.dso_centroid),
                                np.array(year_cluster.dso_centroid)
                            ) / (
                                np.linalg.norm(cluster.dso_centroid) *
                                np.linalg.norm(year_cluster.dso_centroid)
                            )

                            if similarity > best_similarity:
                                best_similarity = similarity
                                best_cluster = year_cluster

                        # If similarity is above threshold, add to similar clusters
                        if best_similarity > 0.5 and best_cluster is not None:
                            similar_clusters.append((year, best_cluster, best_similarity))

                    # Sort by year
                    similar_clusters.sort(key=lambda x: x[0])

                    # If we don't have enough data points, skip
                    if len(similar_clusters) < settings.predictive_minimum_regression_datapoints - 1:
                        logger.info(f"Skipping component {component_type} for cluster {cluster.id} - "
                                    f"not enough similar clusters ({len(similar_clusters) + 1})")
                        continue

                    # Prepare data for regression
                    X = np.array([cluster.year] + [year for year, _, _ in similar_clusters])

                    # Get component centroids for similar clusters
                    y = np.array([component_centroid])

                    for _, similar_cluster, _ in similar_clusters:
                        # Get statements for this cluster
                        similar_statement_ids = similar_cluster.statement_ids
                        if not similar_statement_ids:
                            continue

                        # Get DEMISE embeddings for statements in this cluster
                        query = """
                        SELECT id, model_json
                        FROM kg_statements_v2
                        WHERE id = ANY(%s)
                        """
                        cursor.execute(query, (similar_statement_ids,))
                        similar_statements = cursor.fetchall()

                        # Extract component vectors
                        similar_component_vectors = []
                        for statement in similar_statements:
                            demise = DEMISEModel.model_validate(statement["model_json"])

                            component_vector = extract_component_vector(demise, component_type)
                            similar_component_vectors.append(component_vector)

                        if not similar_component_vectors:
                            continue

                        # Calculate component centroid for this cluster
                        similar_component_centroid = np.mean(similar_component_vectors, axis=0)
                        y = np.vstack([y, similar_component_centroid])

                    # If we don't have enough data points after filtering, skip
                    if len(y) < settings.predictive_minimum_regression_datapoints:
                        logger.info(f"Skipping component {component_type} for cluster {cluster.id} - "
                                    f"not enough valid similar clusters ({len(y)})")
                        continue

                    # Create and fit regression model
                    regression_model = get_regression_model(model_type)

                    try:
                        regression_model.fit(X, y)

                        # Predict future values
                        future_X = np.array(future_years)
                        predicted_vectors, confidences = regression_model.predict(future_X)

                        # Create prediction models for each future year
                        for i, year in enumerate(future_years):
                            # Get the final historical year and vector
                            final_historical_year = max(X)
                            final_historical_vector = y[np.argmax(X)]

                            # Create a dictionary mapping historical years to their vectors
                            historical_vectors_by_year = {}
                            for idx, hist_year in enumerate(X):
                                historical_vectors_by_year[int(hist_year)] = y[idx].tolist()

                            # Extract core domain keys from the cluster's domain centroid
                            core_domain_keys = extract_core_domain_keys(
                                cluster.domain_centroid,
                                threshold=0.7
                            )

                            # Calculate overall impact if this is an impact component
                            overall_impact = 0.0
                            cursor.execute("SELECT AVG(impact_value) FROM kg_statements_v2 WHERE id = ANY(%s)", (statement_ids,))
                            if cursor.rowcount > 0:
                                overall_impact = not_none(cursor.fetchone()).get("avg", 0.0)

                            prediction = ComponentPredictionModel(
                                run_id=run_id,
                                virtual_entity_id=virtual_entity.id,
                                cluster_id=not_none(cluster.id),
                                component_type=component_type,
                                predicted_year=year,
                                final_historical_year=int(final_historical_year),
                                predicted_vector=predicted_vectors[i].tolist(),
                                historical_vector=final_historical_vector.tolist(),
                                historical_vectors_by_year=historical_vectors_by_year,
                                confidence=float(confidences[i]),
                                model_type=model_type,
                                core_domain_keys=core_domain_keys,
                                overall_impact=overall_impact
                            )

                            # Save to database
                            saved_prediction = ComponentPredictionDAO.create(cursor, prediction)
                            predictions.append(saved_prediction)

                            logger.info(f"Created prediction for component {component_type} "
                                        f"in cluster {cluster.id} for year {year} "
                                        f"with confidence {confidences[i]:.2f}")

                    except Exception as e:
                        logger.exception(f"Error predicting component {component_type} "
                                        f"for cluster {cluster.id}: {str(e)}")

            conn.commit()

    if tracker:
        # Check if the tracker has the end_stage method
        if hasattr(tracker, 'end_stage'):
            tracker.end_stage(PipelineStage.PREDICTIVE_REGRESSION, {
                "predictions_created": len(predictions),
                "clusters_processed": len(clusters)
            })

    return predictions
