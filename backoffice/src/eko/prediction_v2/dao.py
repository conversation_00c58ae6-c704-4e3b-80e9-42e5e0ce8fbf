"""
Data Access Objects for predictive analytics v2.
"""
import json

from typing import List, Optional, Dict, Any, Tu<PERSON>, Union

from loguru import logger
from psycopg import Cursor
from psycopg.rows import dict_row, DictRow

from eko.prediction_v2.models import (
    DSO_ClusterModel,
    ComponentPredictionModel,
    PredictiveComponentAnalysisModel,
    XferPredictiveComponentAnalysisModel,
    ClusterAnalysisModel,
    XferClusterAnalysisModel,
    EntityYearAnalysisModel,
    XferEntityYearAnalysisModel,
    ComponentType
)


class DSO_ClusterDAO:
    """DAO for DSO_ClusterModel."""

    @staticmethod
    def create(cursor: Cursor, model: DSO_ClusterModel) -> DSO_ClusterModel:
        """
        Create a new DSO cluster.

        Args:
            cursor: Database cursor
            model: DSO cluster model

        Returns:
            Created DSO cluster model with ID
        """
        from psycopg.rows import dict_row

        query = """
        INSERT INTO ana_predict_dso_clusters (
            run_id,
            virtual_entity_id,
            year,
            domain_centroid,
            subject_centroid,
            object_centroid,
            statement_ids,
            size,
            coherence
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id, created_at
        """

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(
                query,
                (
                    model.run_id,
                    model.virtual_entity_id,
                    model.year,
                    model.domain_centroid,
                    model.subject_centroid,
                    model.object_centroid,
                    model.statement_ids,
                    model.size,
                    model.coherence
                )
            )

            result = dict_cursor.fetchone()
            model.id = result["id"]
            model.created_at = result["created_at"]

        return model

    @staticmethod
    def find_by_id(cursor: Cursor, cluster_id: int) -> Optional[DSO_ClusterModel]:
        """
        Find a DSO cluster by ID.

        Args:
            cursor: Database cursor
            cluster_id: DSO cluster ID

        Returns:
            DSO cluster model or None if not found
        """
        query = """
        SELECT *
        FROM ana_predict_dso_clusters
        WHERE id = %s
        """

        # Create a new cursor with dict_row factory
        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(query, (cluster_id,))
            row = dict_cursor.fetchone()

            if not row:
                return None

            return DSO_ClusterModel(
                id=row["id"],
                run_id=row["run_id"],
                virtual_entity_id=row["virtual_entity_id"],
                year=row["year"],
                domain_centroid=row["domain_centroid"],
                subject_centroid=row["subject_centroid"],
                object_centroid=row["object_centroid"],
                statement_ids=row["statement_ids"],
                size=row["size"],
                coherence=row["coherence"],
                created_at=row["created_at"]
            )

    @staticmethod
    def find_by_virtual_entity_and_run(
        cursor: Cursor[DictRow],
        virtual_entity_id: int,
        run_id: int
    ) -> List[DSO_ClusterModel]:
        """
        Find DSO clusters by virtual entity ID and run ID.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID
            run_id: Run ID

        Returns:
            List of DSO cluster models
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_dso_clusters
        WHERE virtual_entity_id = %s AND run_id = %s
        ORDER BY year, id
        """

        cursor.execute(query, (virtual_entity_id, run_id))
        rows = cursor.fetchall()

        return [
            DSO_ClusterModel(
                id=row["id"],
                run_id=row["run_id"],
                virtual_entity_id=row["virtual_entity_id"],
                year=row["year"],
                domain_centroid=row["domain_centroid"],
                subject_centroid=row["subject_centroid"],
                object_centroid=row["object_centroid"],
                statement_ids=row["statement_ids"],
                size=row["size"],
                coherence=row["coherence"],
                created_at=row["created_at"]
            )
            for row in rows
        ]


class ComponentPredictionDAO:
    """DAO for ComponentPredictionModel."""

    @staticmethod
    def create(cursor: Cursor[DictRow], model: ComponentPredictionModel) -> ComponentPredictionModel:
        """
        Create a new component prediction.

        Args:
            cursor: Database cursor
            model: Component prediction model

        Returns:
            Created component prediction model with ID
        """
        from psycopg.rows import dict_row

        query = """
        INSERT INTO ana_predict_component_predictions (
            run_id,
            virtual_entity_id,
            cluster_id,
            component_type,
            predicted_year,
            final_historical_year,
            predicted_vector,
            historical_vector,
            historical_vectors_by_year,
            confidence,
            model_type,
            core_domain_keys,
            overall_impact
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id, created_at
        """

        cursor.execute(
            query,
            (
                model.run_id,
                model.virtual_entity_id,
                model.cluster_id,
                model.component_type.value,
                model.predicted_year,
                model.final_historical_year,
                model.predicted_vector,
                model.historical_vector,
                json.dumps(model.historical_vectors_by_year),
                model.confidence,
                model.model_type.value,
                model.core_domain_keys,
                model.overall_impact
            )
        )

        result = cursor.fetchone()
        if not result:
            raise ValueError("Failed to create component prediction")

        model.id = result["id"]
        model.created_at = result["created_at"]

        return model

    @staticmethod
    def find_by_id(cursor: Cursor[DictRow], prediction_id: int) -> Optional[ComponentPredictionModel]:
        """
        Find a component prediction by ID.

        Args:
            cursor: Database cursor
            prediction_id: Component prediction ID

        Returns:
            Component prediction model or None if not found
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_component_predictions
        WHERE id = %s
        """

        cursor.execute(query, (prediction_id,))
        row = cursor.fetchone()

        if not row:
            return None

        return ComponentPredictionModel(
            id=row["id"],
            run_id=row["run_id"],
            virtual_entity_id=row["virtual_entity_id"],
            cluster_id=row["cluster_id"],
            component_type=ComponentType(row["component_type"]),
            predicted_year=row["predicted_year"],
            final_historical_year=row["final_historical_year"],
            predicted_vector=row["predicted_vector"],
            historical_vector=row["historical_vector"],
            historical_vectors_by_year=row.get("historical_vectors_by_year", {}),
            confidence=row["confidence"],
            model_type=row["model_type"],
            core_domain_keys=row.get("core_domain_keys", []),
            overall_impact=row.get("overall_impact", 0.0),
            created_at=row["created_at"]
        )

    @staticmethod
    def find_by_virtual_entity_and_run(
        cursor: Cursor[DictRow],
        virtual_entity_id: int,
        run_id: int
    ) -> List[ComponentPredictionModel]:
        """
        Find component predictions by virtual entity ID and run ID.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID
            run_id: Run ID

        Returns:
            List of component prediction models
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_component_predictions
        WHERE virtual_entity_id = %s AND run_id = %s
        ORDER BY cluster_id, component_type, predicted_year
        """

        cursor.execute(query, (virtual_entity_id, run_id))
        rows = cursor.fetchall()

        return [
            ComponentPredictionModel(
                id=row["id"],
                run_id=row["run_id"],
                virtual_entity_id=row["virtual_entity_id"],
                cluster_id=row["cluster_id"],
                component_type=ComponentType(row["component_type"]),
                predicted_year=row["predicted_year"],
                final_historical_year=row["final_historical_year"],
                predicted_vector=row["predicted_vector"],
                historical_vector=row["historical_vector"],
                historical_vectors_by_year=row.get("historical_vectors_by_year", {}),
                confidence=row["confidence"],
                model_type=row["model_type"],
                core_domain_keys=row.get("core_domain_keys", []),
                overall_impact=row.get("overall_impact", 0.0),
                created_at=row["created_at"]
            )
            for row in rows
        ]

    @staticmethod
    def find_by_virtual_entity(
        cursor: Cursor[DictRow],
        virtual_entity_id: int
    ) -> List[ComponentPredictionModel]:
        """
        Find component predictions by virtual entity ID.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID

        Returns:
            List of component prediction models
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_component_predictions
        WHERE virtual_entity_id = %s
        ORDER BY run_id, cluster_id, component_type, predicted_year
        """

        cursor.execute(query, (virtual_entity_id,))
        rows = cursor.fetchall()

        return [
            ComponentPredictionModel(
                id=row["id"],
                run_id=row["run_id"],
                virtual_entity_id=row["virtual_entity_id"],
                cluster_id=row["cluster_id"],
                component_type=ComponentType(row["component_type"]),
                predicted_year=row["predicted_year"],
                final_historical_year=row["final_historical_year"],
                predicted_vector=row["predicted_vector"],
                historical_vector=row["historical_vector"],
                historical_vectors_by_year=row.get("historical_vectors_by_year", {}),
                confidence=row["confidence"],
                model_type=row["model_type"],
                core_domain_keys=row.get("core_domain_keys", []),
                overall_impact=row.get("overall_impact", 0.0),
                created_at=row["created_at"]
            )
            for row in rows
        ]


class PredictiveComponentAnalysisDAO:
    """DAO for PredictiveComponentAnalysisModel."""

    @staticmethod
    def create(cursor: Cursor, model: PredictiveComponentAnalysisModel) -> PredictiveComponentAnalysisModel:
        """
        Create a new predictive component analysis.

        Args:
            cursor: Database cursor
            model: Predictive component analysis model

        Returns:
            Created predictive component analysis model with ID
        """
        from psycopg.rows import dict_row

        query = """
        INSERT INTO ana_predict_component_analysis (
            run_id,
            prediction_id,
            virtual_entity_id,
            cluster_id,
            component_type,
            year,
            summary,
            detailed_analysis,
            potential_risks,
            potential_opportunities,
            confidence,
            core_domain_keys,
            overall_impact
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id, created_at
        """

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(
                query,
                (
                    model.run_id,
                    model.prediction_id,
                    model.virtual_entity_id,
                    model.cluster_id,
                    model.component_type.value,
                    model.year,
                    model.summary,
                    model.detailed_analysis,
                    model.potential_risks,
                    model.potential_opportunities,
                    model.confidence,
                    model.core_domain_keys,
                    model.overall_impact
                )
            )

            result = dict_cursor.fetchone()
            if result:
                model.id = result["id"]
                model.created_at = result["created_at"]
            else:
                raise ValueError("Failed to create predictive component analysis")

        return model

    @staticmethod
    def find_by_id(cursor: Cursor, analysis_id: int) -> Optional[PredictiveComponentAnalysisModel]:
        """
        Find a predictive component analysis by ID.

        Args:
            cursor: Database cursor
            analysis_id: Predictive component analysis ID

        Returns:
            Predictive component analysis model or None if not found
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_component_analysis
        WHERE id = %s
        """

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(query, (analysis_id,))
            row = dict_cursor.fetchone()

            if not row:
                return None

            return PredictiveComponentAnalysisModel(
                id=row["id"],
                run_id=row["run_id"],
                prediction_id=row["prediction_id"],
                virtual_entity_id=row["virtual_entity_id"],
                cluster_id=row["cluster_id"],
                component_type=ComponentType(row["component_type"]),
                year=row["year"],
                summary=row["summary"],
                detailed_analysis=row["detailed_analysis"],
                potential_risks=row["potential_risks"],
                potential_opportunities=row["potential_opportunities"],
                confidence=row["confidence"],
                core_domain_keys=row.get("core_domain_keys", []),
                overall_impact=row.get("overall_impact", 0.0),
                created_at=row["created_at"]
            )

    @staticmethod
    def find_by_virtual_entity_and_run(
        cursor: Cursor,
        virtual_entity_id: int,
        run_id: int
    ) -> List[PredictiveComponentAnalysisModel]:
        """
        Find predictive component analyses by virtual entity ID and run ID.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID
            run_id: Run ID

        Returns:
            List of predictive component analysis models
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_component_analysis
        WHERE virtual_entity_id = %s AND run_id = %s
        ORDER BY cluster_id, component_type, year
        """

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(query, (virtual_entity_id, run_id))
            rows = dict_cursor.fetchall()

            return [
                PredictiveComponentAnalysisModel(
                    id=row["id"],
                    run_id=row["run_id"],
                    prediction_id=row["prediction_id"],
                    virtual_entity_id=row["virtual_entity_id"],
                    cluster_id=row["cluster_id"],
                    component_type=ComponentType(row["component_type"]),
                    year=row["year"],
                    summary=row["summary"],
                    detailed_analysis=row["detailed_analysis"],
                    potential_risks=row["potential_risks"],
                    potential_opportunities=row["potential_opportunities"],
                    confidence=row["confidence"],
                    core_domain_keys=row.get("core_domain_keys", []),
                    overall_impact=row.get("overall_impact", 0.0),
                    created_at=row["created_at"]
                )
                for row in rows
            ]


class XferPredictiveComponentAnalysisDAO:
    """DAO for XferPredictiveComponentAnalysisModel."""

    @staticmethod
    def create(cursor: Cursor, model: XferPredictiveComponentAnalysisModel) -> XferPredictiveComponentAnalysisModel:
        """
        Create a new transfer predictive component analysis.

        Args:
            cursor: Database cursor
            model: Transfer predictive component analysis model

        Returns:
            Created transfer predictive component analysis model with ID
        """
        from psycopg.rows import dict_row

        query = """
        INSERT INTO xfer_predictive_component_analysis (
            run_id,
            prediction_id,
            virtual_entity_id,
            cluster_id,
            component_type,
            year,
            summary,
            detailed_analysis,
            potential_risks,
            potential_opportunities,
            confidence,
            core_domain_keys,
            overall_impact,
            created_at
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id
        """

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(
                query,
                (
                    model.run_id,
                    model.prediction_id,
                    model.virtual_entity_id,
                    model.cluster_id,
                    model.component_type,
                    model.year,
                    model.summary,
                    model.detailed_analysis,
                    model.potential_risks,
                    model.potential_opportunities,
                    model.confidence,
                    model.core_domain_keys,
                    model.overall_impact,
                    model.created_at
                )
            )

            result = dict_cursor.fetchone()
            if result:
                model.id = result["id"]
            else:
                raise ValueError("Failed to create transfer predictive component analysis")

        return model

    @staticmethod
    def sync_from_analysis(cursor: Cursor, analysis: PredictiveComponentAnalysisModel) -> XferPredictiveComponentAnalysisModel:
        """
        Sync a predictive component analysis to the transfer table.

        Args:
            cursor: Database cursor
            analysis: Predictive component analysis model

        Returns:
            Created transfer predictive component analysis model
        """
        xfer_model = XferPredictiveComponentAnalysisModel(
            run_id=analysis.run_id,
            prediction_id=analysis.prediction_id,
            virtual_entity_id=analysis.virtual_entity_id,
            cluster_id=analysis.cluster_id,
            component_type=analysis.component_type.value,
            year=analysis.year,
            summary=analysis.summary,
            detailed_analysis=analysis.detailed_analysis,
            potential_risks=analysis.potential_risks,
            potential_opportunities=analysis.potential_opportunities,
            confidence=analysis.confidence,
            core_domain_keys=analysis.core_domain_keys,
            overall_impact=analysis.overall_impact,
            created_at=analysis.created_at
        )

        return XferPredictiveComponentAnalysisDAO.create(cursor, xfer_model)


class ClusterAnalysisDAO:
    """DAO for ClusterAnalysisModel."""

    @staticmethod
    def create(cursor: Cursor, model: ClusterAnalysisModel) -> ClusterAnalysisModel:
        """
        Create a new cluster analysis.

        Args:
            cursor: Database cursor
            model: Cluster analysis model

        Returns:
            Created cluster analysis model with ID
        """
        from psycopg.rows import dict_row
        import json

        query = """
        INSERT INTO ana_predict_cluster_analysis (
            run_id,
            virtual_entity_id,
            cluster_id,
            year,
            summary,
            detailed_analysis,
            motivation_summary,
            statement_type_summary,
            engagement_summary,
            impact_summary,
            potential_risks,
            potential_opportunities,
            confidence,
            core_domain_keys,
            overall_impact,
            historical_vectors_by_year
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id, created_at
        """

        # Convert historical_vectors_by_year to JSON
        historical_vectors_json = json.dumps(model.historical_vectors_by_year)

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(
                query,
                (
                    model.run_id,
                    model.virtual_entity_id,
                    model.cluster_id,
                    model.year,
                    model.summary,
                    model.detailed_analysis,
                    model.motivation_summary,
                    model.statement_type_summary,
                    model.engagement_summary,
                    model.impact_summary,
                    model.potential_risks,
                    model.potential_opportunities,
                    model.confidence,
                    model.core_domain_keys,
                    model.overall_impact,
                    historical_vectors_json
                )
            )

            result = dict_cursor.fetchone()
            if result:
                model.id = result["id"]
                model.created_at = result["created_at"]

        return model

    @staticmethod
    def find_by_id(cursor: Cursor, analysis_id: int) -> Optional[ClusterAnalysisModel]:
        """
        Find a cluster analysis by ID.

        Args:
            cursor: Database cursor
            analysis_id: Cluster analysis ID

        Returns:
            Cluster analysis model or None if not found
        """
        from psycopg.rows import dict_row
        import json

        query = """
        SELECT *
        FROM ana_predict_cluster_analysis
        WHERE id = %s
        """

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(query, (analysis_id,))
            row = dict_cursor.fetchone()

            if not row:
                return None

            # Parse historical_vectors_by_year from JSON
            historical_vectors_by_year = {}
            if row.get("historical_vectors_by_year"):
                try:
                    historical_vectors_by_year = json.loads(row["historical_vectors_by_year"])
                except (json.JSONDecodeError, TypeError):
                    pass

            return ClusterAnalysisModel(
                id=row["id"],
                run_id=row["run_id"],
                virtual_entity_id=row["virtual_entity_id"],
                cluster_id=row["cluster_id"],
                year=row["year"],
                summary=row["summary"],
                detailed_analysis=row["detailed_analysis"],
                motivation_summary=row["motivation_summary"],
                statement_type_summary=row["statement_type_summary"],
                engagement_summary=row["engagement_summary"],
                impact_summary=row["impact_summary"],
                potential_risks=row["potential_risks"],
                potential_opportunities=row["potential_opportunities"],
                confidence=row["confidence"],
                core_domain_keys=row.get("core_domain_keys", []),
                overall_impact=row.get("overall_impact", 0.0),
                historical_vectors_by_year=historical_vectors_by_year,
                created_at=row["created_at"]
            )

    @staticmethod
    def find_by_cluster_and_year(
        cursor: Cursor,
        cluster_id: int,
        year: int,
        run_id: Optional[int] = None
    ) -> Optional[ClusterAnalysisModel]:
        """
        Find a cluster analysis by cluster ID and year.

        Args:
            cursor: Database cursor
            cluster_id: Cluster ID
            year: Year
            run_id: Optional run ID

        Returns:
            Cluster analysis model or None if not found
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_cluster_analysis
        WHERE cluster_id = %s AND year = %s
        """
        params = [cluster_id, year]

        if run_id is not None:
            query += " AND run_id = %s"
            params.append(run_id)

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(query, params)
            row = dict_cursor.fetchone()

            if not row:
                return None

            return ClusterAnalysisModel(
                id=row["id"],
                run_id=row["run_id"],
                virtual_entity_id=row["virtual_entity_id"],
                cluster_id=row["cluster_id"],
                year=row["year"],
                summary=row["summary"],
                detailed_analysis=row["detailed_analysis"],
                motivation_summary=row["motivation_summary"],
                statement_type_summary=row["statement_type_summary"],
                engagement_summary=row["engagement_summary"],
                impact_summary=row["impact_summary"],
                potential_risks=row["potential_risks"],
                potential_opportunities=row["potential_opportunities"],
                confidence=row["confidence"],
                created_at=row["created_at"]
            )

    @staticmethod
    def find_by_virtual_entity_and_year(
        cursor: Cursor,
        virtual_entity_id: int,
        year: int,
        run_id: Optional[int] = None
    ) -> List[ClusterAnalysisModel]:
        """
        Find cluster analyses by virtual entity ID and year.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID
            year: Year
            run_id: Optional run ID

        Returns:
            List of cluster analysis models
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_cluster_analysis
        WHERE virtual_entity_id = %s AND year = %s
        """
        params = [virtual_entity_id, year]

        if run_id is not None:
            query += " AND run_id = %s"
            params.append(run_id)

        query += " ORDER BY confidence DESC"

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(query, params)
            rows = dict_cursor.fetchall()

            return [
                ClusterAnalysisModel(
                    id=row["id"],
                    run_id=row["run_id"],
                    virtual_entity_id=row["virtual_entity_id"],
                    cluster_id=row["cluster_id"],
                    year=row["year"],
                    summary=row["summary"],
                    detailed_analysis=row["detailed_analysis"],
                    motivation_summary=row["motivation_summary"],
                    statement_type_summary=row["statement_type_summary"],
                    engagement_summary=row["engagement_summary"],
                    impact_summary=row["impact_summary"],
                    potential_risks=row["potential_risks"],
                    potential_opportunities=row["potential_opportunities"],
                    confidence=row["confidence"],
                    created_at=row["created_at"]
                )
                for row in rows
            ]


class EntityYearAnalysisDAO:
    """DAO for EntityYearAnalysisModel."""

    @staticmethod
    def create(cursor: Cursor, model: EntityYearAnalysisModel) -> EntityYearAnalysisModel:
        """
        Create a new entity-year analysis.

        Args:
            cursor: Database cursor
            model: Entity-year analysis model

        Returns:
            Created entity-year analysis model with ID
        """
        from psycopg.rows import dict_row
        import json

        query = """
        INSERT INTO ana_predict_entity_year_analysis (
            run_id,
            virtual_entity_id,
            year,
            summary,
            detailed_analysis,
            cluster_summaries,
            potential_risks,
            potential_opportunities,
            confidence,
            core_domain_keys,
            overall_impact
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id, created_at
        """

        # Convert cluster_summaries dict to JSON
        cluster_summaries_json = json.dumps({str(k): v for k, v in model.cluster_summaries.items()})

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(
                query,
                (
                    model.run_id,
                    model.virtual_entity_id,
                    model.year,
                    model.summary,
                    model.detailed_analysis,
                    cluster_summaries_json,
                    model.potential_risks,
                    model.potential_opportunities,
                    model.confidence,
                    model.core_domain_keys,
                    model.overall_impact
                )
            )

            result = dict_cursor.fetchone()
            if result:
                model.id = result["id"]
                model.created_at = result["created_at"]

        return model

    @staticmethod
    def find_by_id(cursor: Cursor, analysis_id: int) -> Optional[EntityYearAnalysisModel]:
        """
        Find an entity-year analysis by ID.

        Args:
            cursor: Database cursor
            analysis_id: Entity-year analysis ID

        Returns:
            Entity-year analysis model or None if not found
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_entity_year_analysis
        WHERE id = %s
        """

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(query, (analysis_id,))
            row = dict_cursor.fetchone()

            if not row:
                return None

            # Convert JSON cluster_summaries to dict
            cluster_summaries = row["cluster_summaries"]
            cluster_summaries_dict = {int(k): v for k, v in cluster_summaries.items()}

            return EntityYearAnalysisModel(
                id=row["id"],
                run_id=row["run_id"],
                virtual_entity_id=row["virtual_entity_id"],
                year=row["year"],
                summary=row["summary"],
                detailed_analysis=row["detailed_analysis"],
                cluster_summaries=cluster_summaries_dict,
                potential_risks=row["potential_risks"],
                potential_opportunities=row["potential_opportunities"],
                confidence=row["confidence"],
                core_domain_keys=row.get("core_domain_keys", []),
                overall_impact=row.get("overall_impact", 0.0),
                created_at=row["created_at"]
            )

    @staticmethod
    def find_by_virtual_entity_and_year(
        cursor: Cursor,
        virtual_entity_id: int,
        year: int,
        run_id: Optional[int] = None
    ) -> Optional[EntityYearAnalysisModel]:
        """
        Find an entity-year analysis by virtual entity ID and year.

        Args:
            cursor: Database cursor
            virtual_entity_id: Virtual entity ID
            year: Year
            run_id: Optional run ID

        Returns:
            Entity-year analysis model or None if not found
        """
        from psycopg.rows import dict_row

        query = """
        SELECT *
        FROM ana_predict_entity_year_analysis
        WHERE virtual_entity_id = %s AND year = %s
        """
        params = [virtual_entity_id, year]

        if run_id is not None:
            query += " AND run_id = %s"
            params.append(run_id)

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(query, params)
            row = dict_cursor.fetchone()

            if not row:
                return None

            # Convert JSON cluster_summaries to dict
            cluster_summaries = row["cluster_summaries"]
            cluster_summaries_dict = {int(k): v for k, v in cluster_summaries.items()}

            return EntityYearAnalysisModel(
                id=row["id"],
                run_id=row["run_id"],
                virtual_entity_id=row["virtual_entity_id"],
                year=row["year"],
                summary=row["summary"],
                detailed_analysis=row["detailed_analysis"],
                cluster_summaries=cluster_summaries_dict,
                potential_risks=row["potential_risks"],
                potential_opportunities=row["potential_opportunities"],
                confidence=row["confidence"],
                core_domain_keys=row.get("core_domain_keys", []),
                overall_impact=row.get("overall_impact", 0.0),
                created_at=row["created_at"]
            )


class XferClusterAnalysisDAO:
    """DAO for XferClusterAnalysisModel."""

    @staticmethod
    def create(cursor: Cursor, model: XferClusterAnalysisModel) -> XferClusterAnalysisModel:
        """
        Create a new transfer cluster analysis.

        Args:
            cursor: Database cursor
            model: Transfer cluster analysis model

        Returns:
            Created transfer cluster analysis model with ID
        """
        from psycopg.rows import dict_row
        import json

        query = """
        INSERT INTO xfer_predict_cluster_analysis (
            run_id,
            virtual_entity_id,
            cluster_id,
            year,
            summary,
            detailed_analysis,
            motivation_summary,
            statement_type_summary,
            engagement_summary,
            impact_summary,
            potential_risks,
            potential_opportunities,
            confidence,
            core_domain_keys,
            overall_impact,
            historical_vectors_by_year
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id
        """

        # Convert historical_vectors_by_year to JSON
        historical_vectors_json = json.dumps(model.historical_vectors_by_year)

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(
                query,
                (
                    model.run_id,
                    model.virtual_entity_id,
                    model.cluster_id,
                    model.year,
                    model.summary,
                    model.detailed_analysis,
                    model.motivation_summary,
                    model.statement_type_summary,
                    model.engagement_summary,
                    model.impact_summary,
                    model.potential_risks,
                    model.potential_opportunities,
                    model.confidence,
                    model.core_domain_keys,
                    model.overall_impact,
                    historical_vectors_json
                )
            )

            result = dict_cursor.fetchone()
            if result:
                model.id = result["id"]

        return model

    @staticmethod
    def sync_from_analysis(cursor: Cursor, analysis: ClusterAnalysisModel) -> XferClusterAnalysisModel:
        """
        Sync a cluster analysis to the transfer table.

        Args:
            cursor: Database cursor
            analysis: Cluster analysis model

        Returns:
            Created transfer cluster analysis model
        """
        # Convert historical_vectors_by_year to string keys for transfer model
        historical_vectors_by_year = {}
        for year, vectors in analysis.historical_vectors_by_year.items():
            historical_vectors_by_year[str(year)] = vectors

        xfer_model = XferClusterAnalysisModel(
            run_id=analysis.run_id,
            virtual_entity_id=analysis.virtual_entity_id,
            cluster_id=analysis.cluster_id,
            year=analysis.year,
            summary=analysis.summary,
            detailed_analysis=analysis.detailed_analysis,
            motivation_summary=analysis.motivation_summary,
            statement_type_summary=analysis.statement_type_summary,
            engagement_summary=analysis.engagement_summary,
            impact_summary=analysis.impact_summary,
            potential_risks=analysis.potential_risks,
            potential_opportunities=analysis.potential_opportunities,
            confidence=analysis.confidence,
            core_domain_keys=analysis.core_domain_keys,
            overall_impact=analysis.overall_impact,
            historical_vectors_by_year=historical_vectors_by_year,
            created_at=analysis.created_at
        )

        return XferClusterAnalysisDAO.create(cursor, xfer_model)


class XferEntityYearAnalysisDAO:
    """DAO for XferEntityYearAnalysisModel."""

    @staticmethod
    def create(cursor: Cursor, model: XferEntityYearAnalysisModel) -> XferEntityYearAnalysisModel:
        """
        Create a new transfer entity-year analysis.

        Args:
            cursor: Database cursor
            model: Transfer entity-year analysis model

        Returns:
            Created transfer entity-year analysis model with ID
        """
        from psycopg.rows import dict_row
        import json

        query = """
        INSERT INTO xfer_predict_entity_year_analysis (
            run_id,
            virtual_entity_id,
            year,
            summary,
            detailed_analysis,
            cluster_summaries,
            potential_risks,
            potential_opportunities,
            confidence,
            core_domain_keys,
            overall_impact
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id
        """

        # Convert cluster_summaries dict to JSON
        cluster_summaries_json = json.dumps(model.cluster_summaries)

        with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
            dict_cursor.execute(
                query,
                (
                    model.run_id,
                    model.virtual_entity_id,
                    model.year,
                    model.summary,
                    model.detailed_analysis,
                    cluster_summaries_json,
                    model.potential_risks,
                    model.potential_opportunities,
                    model.confidence,
                    model.core_domain_keys,
                    model.overall_impact
                )
            )

            result = dict_cursor.fetchone()
            if result:
                model.id = result["id"]

        return model

    @staticmethod
    def sync_from_analysis(cursor: Cursor, analysis: EntityYearAnalysisModel) -> XferEntityYearAnalysisModel:
        """
        Sync an entity-year analysis to the transfer table.

        Args:
            cursor: Database cursor
            analysis: Entity-year analysis model

        Returns:
            Created transfer entity-year analysis model
        """
        # Convert int keys to string keys for transfer model
        cluster_summaries = {str(k): v for k, v in analysis.cluster_summaries.items()}

        xfer_model = XferEntityYearAnalysisModel(
            run_id=analysis.run_id,
            virtual_entity_id=analysis.virtual_entity_id,
            year=analysis.year,
            summary=analysis.summary,
            detailed_analysis=analysis.detailed_analysis,
            cluster_summaries=cluster_summaries,
            potential_risks=analysis.potential_risks,
            potential_opportunities=analysis.potential_opportunities,
            confidence=analysis.confidence,
            core_domain_keys=analysis.core_domain_keys,
            overall_impact=analysis.overall_impact,
            created_at=analysis.created_at
        )

        return XferEntityYearAnalysisDAO.create(cursor, xfer_model)
