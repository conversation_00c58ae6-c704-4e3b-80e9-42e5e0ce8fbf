# Overview of the EKO Prediction V2 System

The `prediction_v2` system is designed for predictive analytics of entity behavior based on the DEMISE (Domain, Engagement, Motivation, Impact, Statement Type) components. It processes historical statement data for an entity, clusters these statements, predicts future trends for DEMISE components within these clusters, and then uses LLMs to generate human-readable analyses of these predictions.

## Core Modules and Their Functionality

1.  **`models.py`**:
    *   Defines all primary data structures using Pydantic models.
    *   Key models include:
        *   `DSO_ClusterModel`: Represents clusters of statements based on their Domain, Subject, and Object vectors. Each cluster is specific to a year.
        *   `ComponentPredictionModel`: Stores the raw predicted vector for a specific DEMISE component (Motivation, Engagement, Impact) for a given cluster in a future year. It also includes the historical vector it was based on and a confidence score.
        *   `PredictiveComponentAnalysisModel`: Contains the LLM-generated textual analysis (summary, details, risks, opportunities) for a single `ComponentPredictionModel`.
        *   `ClusterAnalysisModel`: Holds the LLM-generated analysis that synthesizes insights from all component analyses (Motivation, Engagement, Impact) for a specific DSO cluster in a given future year.
        *   `EntityYearAnalysisModel`: Contains the LLM-generated analysis that provides an overall view of the entity's predicted behavior for a specific future year, based on all cluster analyses for that year.
        *   `Xfer...Model` variants: These are models used for transferring the final analysis data to other systems or tables.
    *   Includes enums like `RegressionModelType` (e.g., VAR, Gaussian Process, Prophet, Ensemble) and `ComponentType` (Motivation, Statement Type, Engagement, Impact).

2.  **`dao.py` (Data Access Objects)**:
    *   Manages all database interactions (CRUD operations) for the models defined in `models.py`.
    *   Each main model (e.g., `DSO_ClusterModel`, `ComponentPredictionModel`) has a corresponding DAO class (e.g., `DSO_ClusterDAO`, `ComponentPredictionDAO`) responsible for its persistence.

3.  **`clustering.py`**:
    *   Responsible for creating `DSO_ClusterModel`s from historical statement data.
    *   `get_statements_by_year()`: Fetches statements for a given virtual entity within a specified year range.
    *   `extract_dso_components()`: Extracts the combined Domain, Subject, and Object vector from a full DEMISE embedding.
    *   `extract_core_domain_keys()`: Identifies the most prominent domain keys from a cluster's domain centroid, which helps in understanding the thematic focus of the cluster.
    *   `create_dso_clusters()`:
        *   Takes a virtual entity and a historical year range as input.
        *   For each year, it retrieves relevant statements.
        *   Extracts DSO vectors from the DEMISE embeddings of these statements.
        *   Performs clustering (e.g., DBSCAN) on these DSO vectors to group semantically similar statements within that year.
        *   Calculates centroids (domain, subject, object) for each resulting cluster.
        *   Saves these clusters (as `DSO_ClusterModel` instances) to the database.

4.  **`regression.py`**:
    *   Handles the prediction of future DEMISE component vectors.
    *   Defines a `BaseRegressionModel` abstract class and concrete implementations:
        *   `VARModel`: Vector Autoregression.
        *   `GaussianProcessModel`: Gaussian Process Regression.
        *   `ProphetModel`: Facebook's Prophet time series forecasting model.
        *   `EnsembleModel`: Combines predictions from multiple models.
    *   `extract_component_vector()`: Isolates a specific component's vector (e.g., Motivation) from a full DEMISE model.
    *   `predict_component_trends()`:
        *   Iterates through each historical `DSO_ClusterModel`.
        *   For each DEMISE component (Motivation, Engagement, Impact):
            *   It identifies "similar" clusters from other historical years by comparing their DSO centroids. This creates a lineage or trajectory for the initial cluster.
            *   It constructs a time series of component vectors using the component centroids from this cluster lineage.
            *   A selected regression model (e.g., Ensemble) is fitted to this time series.
            *   The model then predicts the component vectors for specified future years.
            *   These predictions, along with confidence scores, historical context, `core_domain_keys` (derived from the cluster's domain centroid), and `overall_impact` (average impact score of statements in the cluster), are saved as `ComponentPredictionModel` instances.

5.  **`analysis.py`**:
    *   Uses Large Language Models (LLMs) to interpret the numerical predictions from `regression.py` and generate qualitative, human-readable insights.
    *   `get_component_description()`: Provides static textual descriptions of what each DEMISE component represents.
    *   `analyze_component_prediction()` (and its specialized versions like `analyze_motivation_prediction`):
        *   Takes a `ComponentPredictionModel`.
        *   Formats a detailed prompt for an LLM, including the predicted vector, the last historical vector, significant changes, `core_domain_keys`, `overall_impact`, and general context.
        *   The LLM generates a summary, detailed analysis, potential risks, and potential opportunities.
        *   These are saved as a `PredictiveComponentAnalysisModel`.
    *   `generate_component_analysis()`: Orchestrates the above for all component predictions.
    *   `generate_cluster_analysis()`:
        *   Aggregates `PredictiveComponentAnalysisModel`s for a specific cluster and future year.
        *   Prompts an LLM to synthesize these individual component analyses into a single, holistic analysis for the cluster in that year.
        *   Saved as `ClusterAnalysisModel`.
    *   `generate_entity_year_analysis()`:
        *   Aggregates `ClusterAnalysisModel`s for a specific future year across all clusters.
        *   Prompts an LLM to provide an entity-wide summary for that year.
        *   Saved as `EntityYearAnalysisModel`.

6.  **`utils.py`**:
    *   Contains helper functions, primarily for creating visualizations and reports.
    *   `create_motivation_chart()`, `create_statement_type_chart()`, `create_engagement_chart()`, `create_impact_chart()`: Generate matplotlib line charts showing the predicted trends of various dimensions within each DEMISE component over future years.
    *   `create_tsne_visualization()`: Creates a t-SNE (t-distributed Stochastic Neighbor Embedding) plot. This visualizes the high-dimensional DSO cluster centroids (both historical and predicted) in a 2D space, showing how clusters might evolve or shift their thematic focus over time.
    *   `generate_predictive_report()`: Compiles all the generated charts and textual analyses (component, cluster, and entity-year levels) into a single HTML report.

7.  **`cli.py`**:
    *   Provides a command-line interface (using the `click` library) to execute the prediction pipeline.
    *   `analyze-component-trends`: The main command that orchestrates the entire workflow:
        1.  Calls `create_dso_clusters()` to generate historical clusters.
        2.  Calls `predict_component_trends()` to predict future component vectors.
        3.  Calls `generate_component_analysis()`, `generate_cluster_analysis()`, and `generate_entity_year_analysis()` to produce LLM-based textual insights.
        4.  Optionally calls `generate_predictive_report()` to create the HTML summary.
        5.  Handles data synchronization to transfer tables (for consumption by other systems).
    *   Offers other granular commands to run specific stages of the pipeline independently (e.g., only clustering, only report generation).

## Overall Workflow

1.  **Initialization**: The process starts via a CLI command, specifying an entity and relevant year ranges.
2.  **Historical Clustering**: The system fetches historical statements for the entity. For each year, statements are clustered based on their Domain, Subject, and Object (DSO) characteristics, forming `DSO_ClusterModel`s.
3.  **Trend Prediction**: For each historical DSO cluster and for each key DEMISE component (Motivation, Engagement, Impact):
    *   A temporal sequence of component vectors is built by linking the cluster to similar DSO clusters from other historical years.
    *   A regression model (e.g., Ensemble) is trained on this sequence.
    *   This model forecasts the component's vector for several future years, resulting in `ComponentPredictionModel`s.
4.  **LLM-Powered Analysis**:
    *   **Component Level**: Each `ComponentPredictionModel` (a raw vector prediction) is passed to an LLM. The LLM provides a textual interpretation of what this predicted change means, including potential risks and opportunities. This becomes a `PredictiveComponentAnalysisModel`.
    *   **Cluster Level**: For each DSO cluster in each future year, the LLM synthesizes the individual `PredictiveComponentAnalysisModel`s (for Motivation, Engagement, Impact) into a more holistic `ClusterAnalysisModel`. This explains the predicted behavior of that specific thematic cluster.
    *   **Entity-Year Level**: For each future year, the LLM takes all `ClusterAnalysisModel`s for that year and generates a high-level `EntityYearAnalysisModel`, summarizing the entity's overall predicted trajectory.
5.  **Reporting**: Visualizations (component trend charts, t-SNE plot of cluster evolution) and all textual analyses are compiled into an HTML report.

The system leverages PostgreSQL for data storage (including pgvector for embeddings), `loguru` for logging, and a `TraceabilityTracker` for monitoring pipeline execution stages.
