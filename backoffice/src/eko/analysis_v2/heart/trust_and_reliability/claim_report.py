"""
Report generation for claim analysis.

This module provides functions to generate human-readable reports from claim analysis results.
"""
from typing import Dict, Any


def generate_claim_report(results: Dict[str, Any]) -> str:
    """
    Generate a human-readable report from claim analysis results.
    
    Args:
        results: Dictionary containing claim analysis results
        
    Returns:
        String containing the formatted report
    """
    report = []
    report.append("=" * 80)
    report.append("CLAIM ANALYSIS REPORT")
    report.append("=" * 80)
    report.append("")
    
    # Extract key information
    entity_name = results.get("entity", "Unknown Entity")
    run_id = results.get("run_id", "Unknown")
    total_claims = results.get("total_claims", 0)
    successful_verdicts = results.get("successful_verdicts", 0)
    claims = results.get("claims", [])
    verdicts = results.get("verdicts", [])
    
    # Basic summary
    report.append(f"Entity: {entity_name}")
    report.append(f"Run ID: {run_id}")
    report.append(f"Total Claims Analyzed: {total_claims}")
    report.append(f"Successful Verdicts: {successful_verdicts}")
    report.append("")
    
    # Merging summary
    merged_claims = sum(1 for c in claims if getattr(c, 'is_merged', False))
    if merged_claims > 0:
        report.append("-" * 80)
        report.append("MERGING SUMMARY")
        report.append("-" * 80)
        report.append(f"Original Claims Count: {total_claims}")
        report.append(f"Claims Merged: {merged_claims}")
        report.append(f"Merge Reduction: {merged_claims / total_claims * 100:.1f}%")
        
        # Count by merge type
        domain_merges = sum(1 for c in claims if getattr(c, 'merge_type', '') == 'domain')
        text_merges = sum(1 for c in claims if getattr(c, 'merge_type', '') == 'text')
        combined_merges = sum(1 for c in claims if getattr(c, 'merge_type', '') == 'domain+text')
        
        report.append(f"Domain-based Merges: {domain_merges}")
        report.append(f"Text-based Merges: {text_merges}")
        report.append(f"Combined Domain+Text Merges: {combined_merges}")
        report.append("")
        
        # List merged claims
        report.append("Merged Claims:")
        for claim in claims:
            if getattr(claim, 'is_merged', False):
                merged_ids = getattr(claim, 'merged_from_ids', [])
                report.append(f"  - Claim {claim.statement_id}: Merged from {len(merged_ids)} claims")
                report.append(f"    Merge type: {getattr(claim, 'merge_type', 'unknown')}")
                report.append(f"    Text: \"{claim.text[:100]}...\"")
                report.append("")
        
    # Verdict summary
    if verdicts:
        valid_claims = sum(1 for v in verdicts if v.valid_claim)
        invalid_claims = sum(1 for v in verdicts if not v.valid_claim)
        greenwashing = sum(1 for v in verdicts if v.greenwashing)
        
        report.append("-" * 80)
        report.append("VERDICT SUMMARY")
        report.append("-" * 80)
        report.append(f"Valid Claims: {valid_claims} ({valid_claims/len(verdicts)*100:.1f}%)")
        report.append(f"Invalid Claims: {invalid_claims} ({invalid_claims/len(verdicts)*100:.1f}%)")
        report.append(f"Greenwashing Instances: {greenwashing} ({greenwashing/len(verdicts)*100:.1f}%)")
        report.append("")
        
        # Confidence distribution
        high_confidence = sum(1 for v in verdicts if v.confidence >= 90)
        medium_confidence = sum(1 for v in verdicts if 70 <= v.confidence < 90)
        low_confidence = sum(1 for v in verdicts if v.confidence < 70)
        
        report.append("Confidence Levels:")
        report.append(f"  High (90-100): {high_confidence} ({high_confidence/len(verdicts)*100:.1f}%)")
        report.append(f"  Medium (70-89): {medium_confidence} ({medium_confidence/len(verdicts)*100:.1f}%)")
        report.append(f"  Low (<70): {low_confidence} ({low_confidence/len(verdicts)*100:.1f}%)")
        report.append("")
        
        # Detailed verdicts
        report.append("-" * 80)
        report.append("DETAILED VERDICTS")
        report.append("-" * 80)
        
        for i, verdict in enumerate(verdicts, 1):
            # Get claim text from the verdict
            claim_text = verdict.statement_text if hasattr(verdict, 'statement_text') else "Claim text not available"
            
            report.append(f"Claim #{i}:")
            report.append(f"  Status: {'VALID' if verdict.valid_claim else 'INVALID'}")
            report.append(f"  Greenwashing: {'YES' if verdict.greenwashing else 'NO'}")
            report.append(f"  Confidence: {verdict.confidence}")
            report.append(f"  Document: {verdict.doc_title} ({verdict.doc_year})")
            report.append(f"  Claim: \"{claim_text}\"")
            
            # Add conclusion if available
            if hasattr(verdict, 'conclusion') and verdict.conclusion:
                report.append(f"  Conclusion: {verdict.conclusion}")
            
            # Add summary if available
            if hasattr(verdict, 'summary') and verdict.summary:
                report.append(f"  Summary: {verdict.summary}")
            
            report.append("")
    else:
        report.append("No verdicts available.")
    
    report.append("=" * 80)
    report.append("END OF REPORT")
    report.append("=" * 80)
    
    return "\n".join(report)
