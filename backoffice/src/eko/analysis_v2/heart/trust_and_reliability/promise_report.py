"""
Report generation for promise analysis.

This module provides functions to generate human-readable reports from promise analysis results.
"""
from typing import Dict, Any


def generate_promise_report(results: Dict[str, Any]) -> str:
    """
    Generate a human-readable report from promise analysis results.

    Args:
        results: Dictionary containing promise analysis results

    Returns:
        String containing the formatted report
    """
    report = []
    report.append("=" * 80)
    report.append("PROMISE ANALYSIS REPORT")
    report.append("=" * 80)
    report.append("")

    # Extract key information
    entity_name = results.get("entity", "Unknown Entity")
    run_id = results.get("run_id", "Unknown")
    total_promises = results.get("total_promises", 0)
    successful_verdicts = results.get("successful_verdicts", 0)
    promises = results.get("promises", [])
    verdicts = results.get("verdicts", [])

    # Basic summary
    report.append(f"Entity: {entity_name}")
    report.append(f"Run ID: {run_id}")
    report.append(f"Total Promises Analyzed: {total_promises}")
    report.append(f"Successful Verdicts: {successful_verdicts}")
    report.append("")

    # Merging summary
    merged_promises = sum(1 for p in promises if getattr(p, 'is_merged', False))
    if merged_promises > 0:
        report.append("-" * 80)
        report.append("MERGING SUMMARY")
        report.append("-" * 80)
        report.append(f"Original Promises Count: {total_promises}")
        report.append(f"Promises Merged: {merged_promises}")
        report.append(f"Merge Reduction: {merged_promises / total_promises * 100:.1f}%")

        # Count by merge type
        domain_merges = sum(1 for p in promises if getattr(p, 'merge_type', '') == 'domain')
        text_merges = sum(1 for p in promises if getattr(p, 'merge_type', '') == 'text')
        combined_merges = sum(1 for p in promises if getattr(p, 'merge_type', '') == 'domain+text')

        report.append(f"Domain-based Merges: {domain_merges}")
        report.append(f"Text-based Merges: {text_merges}")
        report.append(f"Combined Domain+Text Merges: {combined_merges}")
        report.append("")

        # List merged promises
        report.append("Merged Promises:")
        for promise in promises:
            if getattr(promise, 'is_merged', False):
                merged_ids = getattr(promise, 'merged_from_ids', [])
                report.append(f"  - Promise {promise.statement_id}: Merged from {len(merged_ids)} promises")
                report.append(f"    Merge type: {getattr(promise, 'merge_type', 'unknown')}")
                report.append(f"    Text: \"{promise.text[:100]}...\"")
                report.append("")

    # Verdict summary
    if verdicts:
        kept_promises = sum(1 for v in verdicts if v.promise_kept)
        broken_promises = sum(1 for v in verdicts if not v.promise_kept)
        greenwashing = sum(1 for v in verdicts if v.greenwashing)

        report.append("-" * 80)
        report.append("VERDICT SUMMARY")
        report.append("-" * 80)
        report.append(f"Promises Kept: {kept_promises} ({kept_promises/len(verdicts)*100:.1f}%)")
        report.append(f"Promises Broken: {broken_promises} ({broken_promises/len(verdicts)*100:.1f}%)")
        report.append(f"Greenwashing Instances: {greenwashing} ({greenwashing/len(verdicts)*100:.1f}%)")
        report.append("")

        # Confidence distribution
        high_confidence = sum(1 for v in verdicts if v.confidence >= 90)
        medium_confidence = sum(1 for v in verdicts if 70 <= v.confidence < 90)
        low_confidence = sum(1 for v in verdicts if v.confidence < 70)

        report.append("Confidence Levels:")
        report.append(f"  High (90-100): {high_confidence} ({high_confidence/len(verdicts)*100:.1f}%)")
        report.append(f"  Medium (70-89): {medium_confidence} ({medium_confidence/len(verdicts)*100:.1f}%)")
        report.append(f"  Low (<70): {low_confidence} ({low_confidence/len(verdicts)*100:.1f}%)")
        report.append("")

        # Detailed verdicts
        report.append("-" * 80)
        report.append("DETAILED VERDICTS")
        report.append("-" * 80)

        for i, verdict in enumerate(verdicts, 1):
            # Get promise text from the verdict
            promise_text = verdict.statement_text if hasattr(verdict, 'statement_text') else "Promise text not available"

            report.append(f"Promise #{i}:")
            report.append(f"  Status: {'KEPT' if verdict.promise_kept else 'BROKEN'}")
            report.append(f"  Greenwashing: {'YES' if verdict.greenwashing else 'NO'}")
            report.append(f"  Confidence: {verdict.confidence}")
            report.append(f"  Document: {verdict.doc_title} ({verdict.doc_year})")
            report.append(f"  Promise: \"{promise_text}\"")

            # Add conclusion if available
            if hasattr(verdict, 'conclusion') and verdict.conclusion:
                report.append(f"  Conclusion: {verdict.conclusion}")

            # Add summary if available
            if hasattr(verdict, 'summary') and verdict.summary:
                report.append(f"  Summary: {verdict.summary}")

            report.append("")
    else:
        report.append("No verdicts available.")

    report.append("=" * 80)
    report.append("END OF REPORT")
    report.append("=" * 80)

    return "\n".join(report)
