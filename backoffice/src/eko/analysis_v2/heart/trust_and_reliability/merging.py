"""
Merging functionality for claims and promises.

This module provides functions to merge similar claims and promises models
to avoid duplicates, similar to how effect models and effect flags are merged.
"""

import numpy as np
from loguru import logger
from sklearn.cluster import DBSCAN
from typing import List, Tuple, Optional, Union

from eko.analysis_v2.heart.trust_and_reliability.claims_and_promises import ClaimModel, PromiseModel
from eko.analysis_v2.pipeline_tracker_extended import TraceabilityTracker
from eko.llm.main import get_embedding
from eko.models.vector.derived.enums import PipelineStage
from eko.settings import settings


def extract_vectors_from_claims(claims: List[ClaimModel]) -> Tuple[np.ndarray, List[ClaimModel]]:
    """
    Extract domain vectors from claim models.

    Args:
        claims: List of claim models

    Returns:
        Tuple of (numpy array of domain vectors, list of valid claim models)
    """
    domain_vectors = []
    valid_claims = []

    for claim in claims:
        try:
            # Extract domain vector from the DEMISE model
            if claim.demise_model and hasattr(claim.demise_model, 'domain'):
                domain_vector = claim.demise_model.domain.to_vector()
                domain_vectors.append(domain_vector)
                valid_claims.append(claim)
            else:
                logger.warning(f"Claim {claim.statement_id} has no domain vector")
        except Exception as e:
            logger.error(f"Error extracting domain vector from claim {claim.statement_id}: {e}")

    return np.array(domain_vectors), valid_claims


def extract_vectors_from_promises(promises: List[PromiseModel]) -> Tuple[np.ndarray, List[PromiseModel]]:
    """
    Extract domain vectors from promise models.

    Args:
        promises: List of promise models

    Returns:
        Tuple of (numpy array of domain vectors, list of valid promise models)
    """
    domain_vectors = []
    valid_promises = []

    for promise in promises:
        try:
            # Extract domain vector from the DEMISE model
            if promise.demise_model and hasattr(promise.demise_model, 'domain'):
                domain_vector = promise.demise_model.domain.to_vector()
                domain_vectors.append(domain_vector)
                valid_promises.append(promise)
            else:
                logger.warning(f"Promise {promise.statement_id} has no domain vector")
        except Exception as e:
            logger.error(f"Error extracting domain vector from promise {promise.statement_id}: {e}")

    return np.array(domain_vectors), valid_promises


def get_text_embeddings(models: Union[List[ClaimModel], List[PromiseModel]]) -> Tuple[List[List[float]], List[int]]:
    """
    Get text embeddings for a list of models.

    Args:
        models: List of claim or promise models

    Returns:
        Tuple of (list of text embeddings, list of valid indices)
    """
    embeddings = []
    valid_indices = []

    for i, model in enumerate(models):
        try:
            # Get embedding for the text
            text = model.text
            embedding = get_embedding(text)
            if embedding:
                embeddings.append(embedding)
                valid_indices.append(i)
        except Exception as e:
            logger.error(f"Error getting embedding for model {i}: {e}")

    return embeddings, valid_indices


def merge_claim_group(claims_to_merge: List[ClaimModel]) -> ClaimModel:
    """
    Merge a group of claim models into a single model.

    Args:
        claims_to_merge: List of claim models to merge

    Returns:
        A new merged claim model
    """
    if not claims_to_merge:
        raise ValueError("Cannot merge empty list of claim models")

    # Use the most recent claim as the primary one
    primary_claim = max(claims_to_merge, key=lambda claim: claim.doc_year)

    max_importance = max(claim.importance for claim in claims_to_merge if claim.importance is not None)

    # Create the merged model
    merged_model = ClaimModel(
        statement_id=primary_claim.statement_id,
        importance=max_importance,
        statement=primary_claim.statement,
        text=primary_claim.text,
        context=primary_claim.context,
        doc_id=primary_claim.doc_id,
        doc_year=primary_claim.doc_year,
        start_year=primary_claim.start_year,
        end_year=primary_claim.end_year,
        doc_title=primary_claim.doc_title,
        doc_authors=primary_claim.doc_authors,
        demise_model=primary_claim.demise_model,
        # Add virtual entity information if available
        virtual_entity_id=getattr(primary_claim, 'virtual_entity_id', None),
        virtual_entity_short_id=getattr(primary_claim, 'virtual_entity_short_id', None),
        virtual_entity=getattr(primary_claim, 'virtual_entity', None),
        # Add merging information
        merged_from_ids=[claim.statement_id for claim in claims_to_merge],
        is_merged=True,
        merge_type="domain"  # Will be updated to "domain+text" if text merging is also applied
    )

    return merged_model


def merge_promise_group(promises_to_merge: List[PromiseModel]) -> PromiseModel:
    """
    Merge a group of promise models into a single model.

    Args:
        promises_to_merge: List of promise models to merge

    Returns:
        A new merged promise model
    """
    if not promises_to_merge:
        raise ValueError("Cannot merge empty list of promise models")

    # Use the earliest promise as the primary one
    primary_promise = min(promises_to_merge, key=lambda promise: promise.doc_year)

    # Create the merged model
    merged_model = PromiseModel(
        statement_id=primary_promise.statement_id,
        statement=primary_promise.statement,
        text=primary_promise.text,
        context=primary_promise.context,
        doc_id=primary_promise.doc_id,
        doc_year=primary_promise.doc_year,
        start_year=primary_promise.start_year,
        end_year=primary_promise.end_year,
        doc_title=primary_promise.doc_title,
        doc_authors=primary_promise.doc_authors,
        demise_model=primary_promise.demise_model,
        virtual_entity_id=primary_promise.virtual_entity_id,
        virtual_entity_short_id=primary_promise.virtual_entity_short_id,
        virtual_entity=primary_promise.virtual_entity,
        # Add merging information
        merged_from_ids=[promise.statement_id for promise in promises_to_merge],
        is_merged=True,
        merge_type="domain",  # Will be updated to "domain+text" if text merging is also applied
    )

    return merged_model


def cluster_claims_by_domain(claims: List[ClaimModel], tracker: Optional[TraceabilityTracker] = None, max_claims=40) -> \
List[
    ClaimModel]:
    """
    Cluster and merge claims by their domain vectors.

    Args:
        claims: List of claims to cluster and merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged claims (domain-based merging)
        :param max_claims: 
    """
    if len(claims) <= 1:
        return claims

    # Extract domain vectors and get valid claims
    domain_vectors, valid_claims = extract_vectors_from_claims(claims)

    if len(valid_claims) <= 1:
        logger.warning("Not enough valid claims with domain vectors for clustering")
        return claims

    # Apply DBSCAN clustering
    dbscan = DBSCAN(eps=settings.claims_merging_domain_eps, min_samples=1, metric='cosine')
    clusters = dbscan.fit_predict(domain_vectors)

    # Group by cluster
    domain_cluster_groups = {}
    for i, cluster_id in enumerate(clusters):
        if cluster_id not in domain_cluster_groups:
            domain_cluster_groups[cluster_id] = []
        domain_cluster_groups[cluster_id].append(i)

    # Collect domain clusters to merge
    domain_merge_groups = []
    for domain_cluster_id, domain_indices in domain_cluster_groups.items():
        # Only process domain clusters with more than one claim
        if len(domain_indices) > 1 and domain_cluster_id != -1:  # Skip noise points (cluster_id = -1)
            # Group by year first
            year_groups = {}
            for idx in domain_indices:
                claim = valid_claims[idx]
                # Group by start year (rounded to nearest 2 years)
                year_key = (
                                   claim.get_year() // settings.claims_merging_year_adjacency_threshold) * settings.claims_merging_year_adjacency_threshold
                if year_key not in year_groups:
                    year_groups[year_key] = []
                year_groups[year_key].append(idx)

            # Add each year group as a merge group
            for year_indices in year_groups.values():
                if len(year_indices) > 1:
                    domain_merge_groups.append(year_indices)

    # Prepare for merging
    merged_claims = []
    merged_indices = set()

    # Perform domain-based merging
    for claim_indices in domain_merge_groups:
        # Collect all claims to be merged
        claims_to_merge = [valid_claims[idx] for idx in claim_indices][:max_claims]

        # Add indices to the merged set
        for idx in claim_indices[:max_claims]:
            merged_indices.add(idx)

        # Create a merged claim
        merged_claim = merge_claim_group(claims_to_merge)

        # Track the merge operation if tracker is provided
        if tracker:
            tracker.record_stat(
                entity=None,
                stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
                count=1,
                metadata={
                    "merge_type": "domain",
                    "models_merged": len(claims_to_merge),
                    "similarity_threshold": settings.claims_merging_domain_eps
                }
            )

        merged_claims.append(merged_claim)

    # Add the claims that weren't merged
    for i, claim in enumerate(valid_claims):
        if i not in merged_indices:
            merged_claims.append(claim)

    # Add any claims that didn't have valid domain vectors
    for claim in claims:
        if claim not in valid_claims:
            merged_claims.append(claim)

    logger.info(f"After domain merging: {len(merged_claims)} claims (from {len(claims)})")

    return merged_claims


def cluster_promises_by_domain(promises: List[PromiseModel], tracker: Optional[TraceabilityTracker] = None) -> List[
    PromiseModel]:
    """
    Cluster and merge promises by their domain vectors.

    Args:
        promises: List of promises to cluster and merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged promises (domain-based merging)
    """
    if len(promises) <= 1:
        return promises

    # Extract domain vectors and get valid promises
    domain_vectors, valid_promises = extract_vectors_from_promises(promises)

    if len(valid_promises) <= 1:
        logger.warning("Not enough valid promises with domain vectors for clustering")
        return promises

    # Apply DBSCAN clustering
    dbscan = DBSCAN(eps=settings.promises_merging_domain_eps, min_samples=1, metric='cosine')
    clusters = dbscan.fit_predict(domain_vectors)

    # Group by cluster
    domain_cluster_groups = {}
    for i, cluster_id in enumerate(clusters):
        if cluster_id not in domain_cluster_groups:
            domain_cluster_groups[cluster_id] = []
        domain_cluster_groups[cluster_id].append(i)

    # Collect domain clusters to merge
    domain_merge_groups = []
    for domain_cluster_id, domain_indices in domain_cluster_groups.items():
        # Only process domain clusters with more than one promise
        if len(domain_indices) > 1 and domain_cluster_id != -1:  # Skip noise points (cluster_id = -1)
            # Group by year first
            year_groups = {}
            for idx in domain_indices:
                promise = valid_promises[idx]
                # Group by start year (rounded to nearest 2 years)
                year_key = (
                                   promise.get_year() // settings.promises_merging_year_adjacency_threshold) * settings.promises_merging_year_adjacency_threshold
                if year_key not in year_groups:
                    year_groups[year_key] = []
                year_groups[year_key].append(idx)

            # Add each year group as a merge group
            for year_indices in year_groups.values():
                if len(year_indices) > 1:
                    domain_merge_groups.append(year_indices)

    # Prepare for merging
    merged_promises = []
    merged_indices = set()

    # Perform domain-based merging
    for promise_indices in domain_merge_groups:
        # Collect all promises to be merged
        promises_to_merge = [valid_promises[idx] for idx in promise_indices]

        # Add indices to the merged set
        for idx in promise_indices:
            merged_indices.add(idx)

        # Create a merged promise
        merged_promise = merge_promise_group(promises_to_merge)

        # Track the merge operation if tracker is provided
        if tracker:
            tracker.record_stat(
                entity=None,
                stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
                count=1,
                metadata={
                    "merge_type": "domain",
                    "models_merged": len(promises_to_merge),
                    "similarity_threshold": settings.promises_merging_domain_eps
                }
            )

        merged_promises.append(merged_promise)

    # Add the promises that weren't merged
    for i, promise in enumerate(valid_promises):
        if i not in merged_indices:
            merged_promises.append(promise)

    # Add any promises that didn't have valid domain vectors
    for promise in promises:
        if promise not in valid_promises:
            merged_promises.append(promise)

    logger.info(f"After domain merging: {len(merged_promises)} promises (from {len(promises)})")

    return merged_promises


def cluster_by_text(models: Union[List[ClaimModel], List[PromiseModel]]) -> List[List[int]]:
    """
    Cluster models by their text embeddings using DBSCAN.

    Args:
        models: List of models to cluster

    Returns:
        List of lists, where each inner list contains indices of models to merge
    """
    # Get text embeddings for summaries
    text_embeddings, valid_indices = get_text_embeddings(models)

    if len(valid_indices) <= 1:
        return []

    # Convert to numpy array
    text_vectors_array = np.array(text_embeddings)

    # Parameters for DBSCAN for text embedding clustering
    # Use the appropriate settings based on model type
    if isinstance(models[0], ClaimModel):
        text_eps = settings.claims_merging_text_eps
    else:
        text_eps = settings.promises_merging_text_eps

    text_min_samples = 1  # Minimum cluster size

    # Apply DBSCAN clustering on text embeddings
    text_dbscan = DBSCAN(eps=text_eps, min_samples=text_min_samples, metric='cosine')
    text_clusters = text_dbscan.fit_predict(text_vectors_array)

    # Group by text cluster
    text_cluster_groups = {}
    for i, cluster_id in enumerate(text_clusters):
        if cluster_id not in text_cluster_groups:
            text_cluster_groups[cluster_id] = []
        text_cluster_groups[cluster_id].append(valid_indices[i])

    to_merge = []
    for text_cluster_id, indices in text_cluster_groups.items():
        # Only include clusters with more than one model and not noise
        if len(indices) > 1 and text_cluster_id != -1:
            to_merge.append(indices)

    return to_merge


def cluster_claims_by_text(claims: List[ClaimModel], tracker: Optional[TraceabilityTracker] = None,
                           max_claims: int = 40) -> List[ClaimModel]:
    """
    Cluster and merge claims by their text embeddings.

    Args:
        claims: List of claims to cluster and merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged claims (text-based merging)
    """
    if len(claims) <= 1:
        return claims

    # Cluster by text
    text_merge_groups = cluster_by_text(claims)

    if not text_merge_groups:
        return claims

    # Prepare for merging
    merged_claims = []
    merged_indices = set()

    # Perform text-based merging
    for claim_indices in text_merge_groups:
        # Collect all claims to be merged
        claims_to_merge = [claims[idx] for idx in claim_indices][:max_claims]

        # Add indices to the merged set
        for idx in claim_indices[:max_claims]:
            merged_indices.add(idx)

        # Create a merged claim
        merged_claim = merge_claim_group(claims_to_merge)

        # Update merge type to indicate both domain and text merging
        if getattr(merged_claim, 'is_merged', False):
            merged_claim.merge_type = "domain+text"

        # Track the merge operation if tracker is provided
        if tracker:
            tracker.record_stat(
                entity=None,
                stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
                count=1,
                metadata={
                    "merge_type": "text",
                    "models_merged": len(claims_to_merge),
                    "similarity_threshold": settings.claims_merging_text_eps
                }
            )

        merged_claims.append(merged_claim)

    # Add the claims that weren't merged
    for i, claim in enumerate(claims):
        if i not in merged_indices:
            merged_claims.append(claim)

    logger.info(f"After text merging: {len(merged_claims)} claims (from {len(claims)})")

    return merged_claims


def cluster_promises_by_text(promises: List[PromiseModel], tracker: Optional[TraceabilityTracker] = None) -> List[
    PromiseModel]:
    """
    Cluster and merge promises by their text embeddings.

    Args:
        promises: List of promises to cluster and merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged promises (text-based merging)
    """
    if len(promises) <= 1:
        return promises

    # Cluster by text
    text_merge_groups = cluster_by_text(promises)

    if not text_merge_groups:
        return promises

    # Prepare for merging
    merged_promises = []
    merged_indices = set()

    # Perform text-based merging
    for promise_indices in text_merge_groups:
        # Collect all promises to be merged
        promises_to_merge = [promises[idx] for idx in promise_indices]

        # Add indices to the merged set
        for idx in promise_indices:
            merged_indices.add(idx)

        # Create a merged promise
        merged_promise = merge_promise_group(promises_to_merge)

        # Update merge type to indicate both domain and text merging
        if getattr(merged_promise, 'is_merged', False):
            merged_promise.merge_type = "domain+text"

        # Track the merge operation if tracker is provided
        if tracker:
            tracker.record_stat(
                entity=None,
                stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
                count=1,
                metadata={
                    "merge_type": "text",
                    "models_merged": len(promises_to_merge),
                    "similarity_threshold": settings.promises_merging_text_eps
                }
            )

        merged_promises.append(merged_promise)

    # Add the promises that weren't merged
    for i, promise in enumerate(promises):
        if i not in merged_indices:
            merged_promises.append(promise)

    logger.info(f"After text merging: {len(merged_promises)} promises (from {len(promises)})")

    return merged_promises


def merge_similar_claims(claims: List[ClaimModel], tracker: Optional[TraceabilityTracker] = None) -> List[ClaimModel]:
    """
    Merge similar claims in a two-step process:
    1. First cluster all claims by domain and merge them
    2. Then cluster all merged claims by text similarity and merge again

    Args:
        claims: List of claims to merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged claims
    """
    if len(claims) <= 1:
        return claims

    logger.info(f"Starting with {len(claims)} claims before merging")

    # Step 1: Cluster by domain and merge
    domain_merged_claims = cluster_claims_by_domain(claims, tracker)

    # If no domain merging occurred, return the original claims
    if len(domain_merged_claims) == len(claims):
        logger.info("No domain-based merging occurred for claims")
    else:
        logger.info(f"Domain-based merging: {len(claims)} -> {len(domain_merged_claims)} claims")

    # Step 2: Cluster by text and merge
    text_merged_claims = cluster_claims_by_text(domain_merged_claims, tracker)

    # If no text merging occurred, return the domain-merged claims
    if len(text_merged_claims) == len(domain_merged_claims):
        logger.info("No text-based merging occurred for claims")
    else:
        logger.info(f"Text-based merging: {len(domain_merged_claims)} -> {len(text_merged_claims)} claims")

    # Log final clustering statistics
    if tracker:
        tracker.record_stat(
            entity=None,
            stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
            count=len(text_merged_claims),
            metadata={
                "clustering_method": "Sequential Domain-Text DBSCAN",
                "clustering_metric": "cosine",
                "domain_eps": settings.claims_merging_domain_eps,
                "text_eps": settings.claims_merging_text_eps,
                "domain_min_samples": 1,
                "text_min_samples": 1,
                "initial_count": len(claims),
                "after_domain_merge_count": len(domain_merged_claims),
                "final_count": len(text_merged_claims),
                "total_merged_count": len(claims) - len(text_merged_claims)
            }
        )

    return text_merged_claims


def merge_similar_promises(promises: List[PromiseModel], tracker: Optional[TraceabilityTracker] = None) -> List[
    PromiseModel]:
    """
    Merge similar promises in a two-step process:
    1. First cluster all promises by domain and merge them
    2. Then cluster all merged promises by text similarity and merge again

    Args:
        promises: List of promises to merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged promises
    """
    if len(promises) <= 1:
        return promises

    logger.info(f"Starting with {len(promises)} promises before merging")

    # Step 1: Cluster by domain and merge
    domain_merged_promises = cluster_promises_by_domain(promises, tracker)

    # If no domain merging occurred, return the original promises
    if len(domain_merged_promises) == len(promises):
        logger.info("No domain-based merging occurred for promises")
    else:
        logger.info(f"Domain-based merging: {len(promises)} -> {len(domain_merged_promises)} promises")

    # Step 2: Cluster by text and merge
    text_merged_promises = cluster_promises_by_text(domain_merged_promises, tracker)

    # If no text merging occurred, return the domain-merged promises
    if len(text_merged_promises) == len(domain_merged_promises):
        logger.info("No text-based merging occurred for promises")
    else:
        logger.info(f"Text-based merging: {len(domain_merged_promises)} -> {len(text_merged_promises)} promises")

    # Log final clustering statistics
    if tracker:
        tracker.record_stat(
            entity=None,
            stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
            count=len(text_merged_promises),
            metadata={
                "clustering_method": "Sequential Domain-Text DBSCAN",
                "clustering_metric": "cosine",
                "domain_eps": settings.promises_merging_domain_eps,
                "text_eps": settings.promises_merging_text_eps,
                "domain_min_samples": 1,
                "text_min_samples": 1,
                "initial_count": len(promises),
                "after_domain_merge_count": len(domain_merged_promises),
                "final_count": len(text_merged_promises),
                "total_merged_count": len(promises) - len(text_merged_promises)
            }
        )

    return text_merged_promises
