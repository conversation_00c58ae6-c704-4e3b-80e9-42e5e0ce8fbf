"""
LLM-based analysis for selective highlighting detection.
"""
from typing import Dict, List
from loguru import logger
from pydantic import BaseModel, Field
from typing_extensions import deprecated

from eko.llm import LLMModel, PROMPT_CREATE_CITATIONS
from eko.llm.main import call_llms_typed, LLMOptions
from eko.analysis_v2.citations import extract_eko_citations, expand_citations
from eko.analysis_v2.selective_highlighting.models import CherryPickingModel, FloodingModel
from eko.db import get_bo_conn
from eko.db.data.statement import StatementData
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.typing import not_none


class SelectiveHighlightingResponse(BaseModel):
    """Response model for LLM analysis of selective highlighting."""
    title: str = Field(..., description="A clear, descriptive title for this analysis (5-10 words)")
    short_title: str = Field(..., description="A very concise title (2-3 words) suitable for use as a label in UI elements")
    summary: str = Field(..., description="A brief summary of the selective highlighting behavior (1-2 sentences)")
    analysis: str = Field(..., description="A detailed analysis (300-500 words) of how this represents selective highlighting, the potential impact, and ethical considerations")
    reason: str = Field(..., description="A concise explanation (1-2 sentences) of why this is selective highlighting")
    severity: int = Field(..., ge=0, le=100, description="A score from 0-100 representing the severity of this behavior")
    confidence: int = Field(..., ge=0, le=100, description="A score from 0-100 representing confidence in this analysis")
    authentic: int = Field(..., ge=0, le=100, description="A score from 0-100 representing how deliberate this behavior appears to be")


@deprecated("Not used")
def get_statements_with_metadata(statement_ids: List[int]) -> Dict[int, Dict]:
    """
    Get statements with their metadata by their IDs.

    Args:
        statement_ids: List of statement IDs

    Returns:
        Dictionary mapping statement IDs to statement data including text, doc_id, and doc_page_id
    """
    result = {}
    if not statement_ids:
        return result

    with get_bo_conn() as conn:
        statements = StatementData.get_statements_by_ids(conn, statement_ids)

        for stmt_id, stmt in statements.items():
            result[stmt_id] = {
                "text": stmt.statement_text,
                "doc_id": stmt.doc_id,
                "doc_page_id": stmt.page_id,
                "impact_value": stmt.metadata.impact_value,
                "year": stmt.doc_year
            }

    return result


def analyze_cherry_picking(model: CherryPickingModel) -> CherryPickingModel:
    """
    Use LLM to analyze cherry picking and update the model with analysis, reason, and label.

    Args:
        model: CherryPickingModel instance

    Returns:
        Updated CherryPickingModel with analysis, reason, and label fields populated
    """
    # Prepare context for LLM with citations
    positive_text = "\n".join([
        f"- {stmt.statement_text} [^{stmt.page_id}]"
        for stmt in model.positive_statements
    ])

    negative_text = "\n".join([
        f"- {stmt.statement_text} [^{stmt.page_id}]"
        for stmt in model.negative_statements
    ])

    entity_name = "The company"  # Default name if we don't have it

    # Create system prompt
    system_prompt = """You are a precise, detailed and analytic researcher for an industry magazine, you report on companies' environmental, social and governance (ESG) issues. You always cite your sources, preserve facts and include direct quotations from the text."""

    # Create user prompt
    user_prompt = f"""Based on the following information about {entity_name}, create a detailed analysis of their selective highlighting (cherry picking) behavior.

Cherry picking is a selective highlighting technique where positive information is emphasized while related negative information is omitted or downplayed.

POSITIVE STATEMENTS (emphasized by the company):
{positive_text}

NEGATIVE STATEMENTS (related information that was not emphasized):
{negative_text}

METRICS:
- Average positive impact: {model.average_positive_impact:.2f}
- Negative impact: {model.negative_impact:.2f}
- Repeat count (number of documents with similar positive statements): {model.repeat_count}

Please provide a detailed analysis of this cherry picking behavior, including a title, summary, and explanation of why this represents cherry picking. Include citations to the source documents using the [^123] format.
"""

    try:
        # Use call_llms_typed with the SelectiveHighlightingResponse model
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        from eko.llm.main import LLMOptions
        llm_response = call_llms_typed(
            [LLMModel.NORMAL_HQ], messages, 8000, response_model=SelectiveHighlightingResponse, options=(LLMOptions(thinking=True))
        )

        # Extract citations from the analysis
        citations = []
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cursor:
                    # Extract citation IDs from text and validate they exist
                    citation_ids = extract_eko_citations(llm_response.analysis)
                    if citation_ids:
                        # Expand citations to get full metadata
                        citations = expand_citations(cursor, citation_ids)
        except Exception as e:
            logger.error(f"Error extracting citations: {e}")
            logger.exception(e)

        # Update the model with the LLM-generated content
        model.analysis = llm_response.analysis
        model.reason = llm_response.reason
        model.label = llm_response.short_title

        # Update the model with the LLM-generated metrics
        model.severity = llm_response.severity
        model.confidence = llm_response.confidence
        model.authenticity = llm_response.authentic

        # Store citations in the model if it has a citations field
        if hasattr(model, 'citations'):
            model.citations = citations

        return model

    except Exception as e:
        logger.exception(f"Error analyzing cherry picking with LLM: {e}")
        # Set default values if LLM analysis fails
        model.analysis = f"Cherry picking detected with {len(model.positive_statements)} positive statements and {len(model.negative_statements)} negative statements."
        model.reason = "Selective highlighting of positive information"
        model.label = f"Cherry Picking {model.id}"
        return model


def analyze_flooding(virtual_entity: VirtualEntityExpandedModel, model: FloodingModel) -> FloodingModel:
    """
    Use LLM to analyze flooding and update the model with analysis, reason, and label.

    Args:
        model: FloodingModel instance

    Returns:
        Updated FloodingModel with analysis, reason, and label fields populated
    """
    # Prepare context for LLM with citations
    mapped_pages=list(set(map(lambda x: (x.page_id, x.context),sorted(model.positive_statements+model.negative_statements, key=lambda x: not_none(x.page_id)))))
    mapped_pages=sorted(mapped_pages, key=lambda x: x[0])
    referenced_pages= "\n".join([
        f"<page id='{mapped_page[0]}'>{mapped_page[1]}</page>"
        for mapped_page in mapped_pages
    ])

    positive_text = "\n".join([
        f"- {stmt.statement_text} [^{stmt.page_id}]"
        for stmt in sorted(model.positive_statements, key=lambda x: not_none(x.page_id))
    ])

    negative_text = "\n".join([
        f"- {stmt.statement_text} [^{stmt.page_id}]"
        for stmt in sorted(model.negative_statements, key=lambda x: not_none(x.page_id))
    ])

    # Create system prompt
    system_prompt = """You are a precise, detailed and analytic researcher for an industry magazine, you report on companies' environmental, social and governance (ESG) issues. You always cite your sources, preserve facts and include direct quotations from the text."""

    # Create user prompt
    user_prompt = f"""
{referenced_pages}    
    
Based on the following information about {virtual_entity.for_referencing_in_prompts()}.

Create a detailed analysis of their information flooding behavior.

Information flooding is a technique where a large number of positive statements are used to overwhelm or distract from negative information on the same topic.

POSITIVE STATEMENTS (numerous statements on the same topic):
{positive_text}

NEGATIVE STATEMENTS (related negative information that might be obscured):
{negative_text}

METRICS:
- Average positive impact: {model.average_positive_impact:.2f}
- Negative impact: {model.negative_impact:.2f}
- Number of positive statements: {model.positive_count}

Please provide a detailed analysis of this information flooding behavior, including a title, summary, and explanation of why this represents information flooding. 
{PROMPT_CREATE_CITATIONS}
"""

    # Use call_llms_typed with the SelectiveHighlightingResponse model
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    llm_response = call_llms_typed(
        [LLMModel.NORMAL_HQ], messages, 16000, response_model=SelectiveHighlightingResponse, options=(LLMOptions(thinking=True))
    )

    # Extract citations from the analysis
    citations = []
    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            # Extract citation IDs from text and validate they exist
            citation_ids = extract_eko_citations(llm_response.analysis)
            if citation_ids:
                # Expand citations to get full metadata
                citations = expand_citations(cursor, citation_ids)

    # Update the model with the LLM-generated content
    model.analysis = llm_response.analysis
    model.reason = llm_response.reason
    model.label = llm_response.short_title
    model.severity = llm_response.severity
    model.confidence = llm_response.confidence
    model.authenticity = llm_response.authentic
    model.citations = citations

    return model
