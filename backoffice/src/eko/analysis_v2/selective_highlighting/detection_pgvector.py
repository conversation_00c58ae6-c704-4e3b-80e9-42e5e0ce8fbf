"""
We use HDBSCAN style clustering to detect cherry-picking and flooding. It walks the KNNs tree to find clusters,
we use pg_vector as it is **very** fast.
"""
from collections import defaultdict

import numpy as np
import re
import time
from loguru import logger
from typing import List, Optional, Any, Union

from eko.analysis_v2.pipeline_tracker import PipelineTracker
from eko.analysis_v2.selective_highlighting.models import CherryPickingModel, FloodingModel
from eko.db import get_bo_conn  # Assuming this provides a DB connection
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.settings import settings


# Assuming psycopg2 or psycopg 3 is used
# import psycopg2
# from psycopg2.extras import execute_values
# from eko.models.statement_metadata import StatementAndMetadata # Unused?

def parse_embedding(embedding: Union[str, list, np.ndarray, None]) -> Optional[np.ndarray]:
    """Parse embedding from various formats into a numpy array."""
    if embedding is None:
        return None

    try:
        # If it's already a numpy array
        if isinstance(embedding, np.ndarray):
            return embedding.flatten()

        # If it's a list
        if isinstance(embedding, list):
            return np.array(embedding, dtype=np.float64).flatten()

        # If it's a string (most likely case based on the error)
        if isinstance(embedding, str):
            # Handle np.str_('[values]') format
            if embedding.startswith('np.str_'):
                # Extract the numbers from inside the brackets
                match = re.search(r'\[(.*)\]', embedding)
                if match:
                    values_str = match.group(1)
                    values = [float(x.strip()) for x in values_str.split(',')]
                    return np.array(values, dtype=np.float64)

            # Handle regular array string like '[0.1, 0.2, 0.3]'
            if embedding.startswith('[') and embedding.endswith(']'):
                values_str = embedding[1:-1]
                values = [float(x.strip()) for x in values_str.split(',')]
                return np.array(values, dtype=np.float64)

        # If we get here, we couldn't parse the embedding
        logger.warning(f"Unable to parse embedding format: {type(embedding)}")
        return None

    except Exception as e:
        logger.warning(f"Error parsing embedding: {str(e)[:100]}")  # Limit error message length
        return None

# --- Helper Function (Updated) ---
def calculate_average_vector(vectors: List[Any]) -> Optional[np.ndarray]:
    """
    Calculates the average vector from a list of vectors.
    Handles various vector formats including string representations.
    """
    valid_vectors = []
    for v in vectors:
        if v is not None:
            try:
                # Use the parse_embedding function to handle different formats
                np_v = parse_embedding(v)
                if np_v is not None:
                    valid_vectors.append(np_v)
                else:
                    logger.warning(f"Could not parse vector: {v}")
            except Exception as e:
                logger.exception(f"Error processing vector: {v}. Error: {e}")
                continue

    if not valid_vectors:
        return None

    # Ensure all vectors have the same length
    vector_lengths = [v.shape[0] for v in valid_vectors]
    if len(set(vector_lengths)) > 1:
        logger.warning(f"Vectors have different lengths: {vector_lengths}. Using the first vector's length.")
        target_length = vector_lengths[0]
        valid_vectors = [v[:target_length] if v.shape[0] > target_length else
                         np.pad(v, (0, target_length - v.shape[0])) for v in valid_vectors]

    vector_stack = np.vstack(valid_vectors)
    average_vector = np.mean(vector_stack, axis=0)
    return average_vector

# --- Cherry Picking Detection (KNN Version) ---
def detect_cherry_picking_pgvector_knn( # Renamed function for clarity
        virtual_entity: VirtualEntityExpandedModel,
        run_id: Optional[int] = None,
        tracker: Optional[PipelineTracker] = None
) -> List[CherryPickingModel]:
    """
    Detect cherry picking using pg_vector KNN search for finding similar statements.

    Assumes a pg_vector index exists on the 'text_embedding' column for efficiency.
    """
    start_time = time.time()
    logger.info(f"Starting cherry picking detection (KNN) for virtual entity {virtual_entity.id}.")

    # Get settings
    min_repeat_count = settings.cherry_picking_min_repeat_count
    # Calculate distance threshold: similarity >= T <=> distance <= 1 - T
    statement_distance_threshold = 1.0 - settings.cherry_picking_statement_similarity_threshold

    cherry_picking_instances = []


    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Step 1: Fetch candidate positive statements with their embeddings
            cur.execute("""
                        SELECT s.id, s.doc_id, s.impact_value, s.domain_embedding, s.text_embedding
                        FROM kg_statements_v2 s
                                 JOIN kg_virt_entity_map m ON s.company_id = m.base_entity_id OR
                                                              m.base_entity_id = ANY (s.subject_entities)
                        WHERE m.virt_entity_id = %s
                          AND s.impact_value >= %s
                          AND s.is_disclosure = TRUE
                          AND s.doc_id IS NOT NULL
                          AND s.text_embedding IS NOT NULL -- Crucial for KNN search
                          AND (s.is_environmental OR s.is_social OR s.is_governance OR s.is_animal_welfare)
                        """, (virtual_entity.id, (settings.cherry_picking_positive_impact_threshold)))

            positive_statements = cur.fetchall()

            if not positive_statements:
                logger.info(f"No positive statements meeting initial criteria found for VE {virtual_entity.id}")
                return []

            logger.info(f"Found {len(positive_statements)} candidate positive statements for VE {virtual_entity.id}.")

            # Prepare data and IDs for KNN query
            stmt_data = {}
            valid_positive_stmt_ids = []
            for stmt in positive_statements:
                stmt_id = stmt[0]
                text_embedding = stmt[4]
                # Ensure embedding is usable (though query already checked for NOT NULL)
                if text_embedding is not None:
                    stmt_data[stmt_id] = {
                        'id': stmt_id,
                        'doc_id': stmt[1],
                        'impact_value': stmt[2],
                        'domain_embedding': stmt[3],
                        # 'text_embedding': text_embedding # No need to store text_embedding again unless needed later
                    }
                    valid_positive_stmt_ids.append(stmt_id)

            if len(valid_positive_stmt_ids) < 2:
                logger.info(f"Not enough positive statements ({len(valid_positive_stmt_ids)}) with text embeddings to compare for VE {virtual_entity.id}")
                return []

            logger.info(f"Performing KNN search for similar pairs among {len(valid_positive_stmt_ids)} statements using distance threshold {statement_distance_threshold:.4f}...")

            # Step 2: Use KNN (via LATERAL join) to find similar pairs based on text_embedding
            # This query leverages the pg_vector index on text_embedding.
            # For each statement s1, it finds neighbors s2 within the distance threshold.
            cur.execute("""
                        SELECT s1.id AS stmt1_id, s2.id AS stmt2_id
                        FROM kg_statements_v2 s1
                                 JOIN kg_virt_entity_map m1 ON s1.company_id = m1.base_entity_id OR m1.base_entity_id = ANY(s1.subject_entities)
                            -- Use LATERAL join to find neighbors for each s1 efficiently
                                 CROSS JOIN LATERAL (
                            SELECT s2.id
                            FROM kg_statements_v2 s2
                                     JOIN kg_virt_entity_map m2 ON s2.company_id = m2.base_entity_id OR m2.base_entity_id = ANY(s2.subject_entities)
                            WHERE
                                m2.virt_entity_id = %s -- Ensure s2 is from the same virt_entity
                              AND s2.id = ANY(%s::int[]) -- Ensure s2 is in our candidate list
                              AND s1.id < s2.id -- Avoid self-pairs and duplicates (s1,s2 vs s2,s1)
                              AND s2.text_embedding IS NOT NULL -- Ensure s2 has embedding
                              AND s1.text_embedding <=> s2.text_embedding <= %s
                            ) AS s2 -- The alias for the subquery result is mandatory
                        WHERE m1.virt_entity_id = %s -- Filter s1 to the same virt_entity
                          AND s1.id = ANY(%s::int[]) -- Ensure s1 is in our candidate list
                          AND s1.text_embedding IS NOT NULL; -- Ensure s1 has embedding
                        """, (
                            virtual_entity.id,             # Param for m2.virt_entity_id
                            valid_positive_stmt_ids,    # Param for s2.id = ANY
                            statement_distance_threshold, # Param for distance check
                            virtual_entity.id,             # Param for m1.virt_entity_id
                            valid_positive_stmt_ids     # Param for s1.id = ANY
                        ))

            similar_pairs = cur.fetchall()
            logger.info(f"Found {len(similar_pairs)} similar pairs using KNN search.")

            # Step 3: Build graph and find groups (Same as before)
            graph = defaultdict(set)
            for stmt1_id, stmt2_id in similar_pairs:
                if stmt1_id in stmt_data and stmt2_id in stmt_data:
                    graph[stmt1_id].add(stmt2_id)
                    graph[stmt2_id].add(stmt1_id)

            similarity_groups = []
            visited = set()
            all_group_ids = set(graph.keys()) | {nid for neighbors in graph.values() for nid in neighbors}

            for stmt_id in valid_positive_stmt_ids: # Iterate through all candidates
                if stmt_id in visited or stmt_id not in all_group_ids: # Skip visited or isolated nodes
                    visited.add(stmt_id)
                    continue

                group = []
                queue = [stmt_id]
                visited.add(stmt_id)
                group_doc_ids = set()

                while queue:
                    current_id = queue.pop(0)
                    if current_id not in stmt_data: continue
                    group.append(current_id)
                    group_doc_ids.add(stmt_data[current_id]['doc_id'])
                    # Check neighbors only if node exists in graph
                    if current_id in graph:
                        for neighbor_id in graph[current_id]:
                            if neighbor_id not in visited:
                                visited.add(neighbor_id)
                                queue.append(neighbor_id)

                # Check repeat count threshold
                if len(group_doc_ids) >= min_repeat_count:
                    similarity_groups.append(group)

            logger.info(f"Found {len(similarity_groups)} groups meeting repeat count ({min_repeat_count}).")

            # Step 4: Find related negative statements (Same as before, uses average domain vector)
            for group in similarity_groups:
                group_domain_vectors = [stmt_data[stmt_id]['domain_embedding'] for stmt_id in group if stmt_id in stmt_data and stmt_data[stmt_id]['domain_embedding'] is not None]
                if not group_domain_vectors: continue

                avg_domain_vector = calculate_average_vector(group_domain_vectors)
                if avg_domain_vector is None: continue
                avg_domain_vector_list = avg_domain_vector.tolist()

                avg_positive_impact = sum(stmt_data[stmt_id]['impact_value'] for stmt_id in group if stmt_id in stmt_data) / len(group)

                # Find negative statements in the same domain (using avg domain vector)
                # Ensure pg_vector index exists on domain_embedding too for this query!
                domain_distance_threshold = 1.0 - settings.cherry_picking_domain_similarity_threshold
                cur.execute("""
                            SELECT s.id, s.impact_value
                            FROM kg_statements_v2 s
                                     JOIN kg_virt_entity_map m
                                          ON s.company_id = m.base_entity_id
                                              OR m.base_entity_id = ANY (s.subject_entities)
                            WHERE m.virt_entity_id = %s
                              AND s.impact_value <= %s
                              AND s.statement_category = 'action'
                              AND s.domain_embedding IS NOT NULL
                              AND s.id <> ALL (%s::int[]) -- Exclude statements in positive group
                              -- Compare with the average domain vector using distance threshold
                              AND s.domain_embedding <=> %s::vector <= %s
                            """, (
                                virtual_entity.id,
                                (settings.cherry_picking_negative_impact_threshold),
                                group,
                                avg_domain_vector_list,
                                domain_distance_threshold  # Use domain distance threshold here
                            ))
                negative_statements = cur.fetchall()

                if not negative_statements: continue
                logger.info(f"Found {len(negative_statements)} negative statements for group {group[:3]}...")

                negative_stmt_ids = [stmt[0] for stmt in negative_statements]
                avg_negative_impact = sum(stmt[1] for stmt in negative_statements) / len(negative_statements)

                # Get full statement objects for positive and negative statements
                from eko.db.data.statement import StatementData
                with get_bo_conn() as conn:
                    positive_statements = list(StatementData.get_statements_by_ids(conn, group).values())
                    negative_statements = list(StatementData.get_statements_by_ids(conn, negative_stmt_ids).values())

                # Create the initial model
                instance = CherryPickingModel(
                    virt_entity_id=virtual_entity.id,
                    positive_statements=positive_statements,
                    negative_statements=negative_statements,
                    domain_vector=avg_domain_vector_list,
                    average_positive_impact=float(avg_positive_impact),
                    negative_impact=float(avg_negative_impact),
                    repeat_count=len(set(stmt_data[stmt_id]['doc_id'] for stmt_id in group if stmt_id in stmt_data)),
                    run_id=run_id,
                    analysis="",
                    reason="",
                    label=""
                )

                from eko.analysis_v2.selective_highlighting.llm_analysis import analyze_cherry_picking
                instance = analyze_cherry_picking(instance)
                logger.info(f"LLM analysis completed for cherry picking instance. Label: {instance.label}")

                cherry_picking_instances.append(instance)


    end_time = time.time()
    elapsed_time = end_time - start_time
    logger.info(f"Cherry picking detection (KNN) for VE {virtual_entity.id} completed in {elapsed_time:.2f}s. Found {len(cherry_picking_instances)} instances.")
    if tracker:
        # Use record_stat instead of log_metric
        from eko.models.vector.derived.enums import PipelineStage
        tracker.record_stat(
            entity=None,
            stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
            processing_time_ms=int(elapsed_time * 1000),
            count=len(cherry_picking_instances),
            metadata={
                "virt_entity_id": virtual_entity.id,
                "detection_method": "cherry_picking_pgvector_knn"
            }
        )

    return cherry_picking_instances


# --- Flooding Detection (KNN Version) ---
def detect_flooding_pgvector_knn( # Renamed function for clarity
        virtual_entity: VirtualEntityExpandedModel,
        run_id: Optional[int] = None,
        tracker: Optional[PipelineTracker] = None
) -> List[FloodingModel]:
    """
    Detect flooding using pg_vector KNN search for finding similar domain statements.

    Assumes a pg_vector index exists on the 'domain_embedding' column for efficiency.
    """
    start_time = time.time()
    logger.info(f"Starting flooding detection (KNN) for virtual entity {virtual_entity.id}.")

    # Calculate distance threshold: similarity >= T <=> distance <= 1 - T
    domain_distance_threshold = 1.0 - settings.flooding_similarity_threshold

    flooding_instances = []

    
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Step 1: Fetch candidate small positive statements with their domain embeddings
            cur.execute("""
                        SELECT s.id, s.impact_value, s.domain_embedding
                        FROM kg_statements_v2 s
                                 JOIN kg_virt_entity_map m ON s.company_id = m.base_entity_id OR
                                                              m.base_entity_id = ANY (s.subject_entities)
                        WHERE m.virt_entity_id = %s
                          AND s.impact_value > 0
                          AND s.impact_value <= %s
                          AND s.statement_category = 'action'
                          AND NOT s.is_disclosure
                          AND (s.is_environmental OR s.is_social OR s.is_governance OR s.is_animal_welfare)
                          AND s.domain_embedding IS NOT NULL -- Crucial for KNN search
                        """, (virtual_entity.id, (settings.flooding_small_positive_threshold)))

            small_positive_statements = cur.fetchall()

            if len(small_positive_statements) < settings.flooding_min_positive_cluster_size:
                logger.info(
                    f"Not enough small positive statements ({len(small_positive_statements)}) for flooding KNN analysis (min: {settings.flooding_min_positive_cluster_size}) for VE {virtual_entity.id}.")
                return []

            logger.info(f"Found {len(small_positive_statements)} candidate small positive statements for VE {virtual_entity.id}.")

            # Prepare data and IDs for KNN query
            stmt_data = {}
            valid_positive_stmt_ids = []
            for stmt in small_positive_statements:
                stmt_id = stmt[0]
                domain_embedding = stmt[2]
                if domain_embedding is not None:
                    stmt_data[stmt_id] = {
                        'id': stmt_id,
                        'impact_value': stmt[1],
                        'domain_embedding': domain_embedding # Needed for averaging later
                    }
                    valid_positive_stmt_ids.append(stmt_id)

            if len(valid_positive_stmt_ids) < 2:
                logger.info(f"Not enough small positive statements ({len(valid_positive_stmt_ids)}) with domain embeddings to compare for VE {virtual_entity.id}")
                return []

            logger.info(f"Performing KNN search for similar domain pairs among {len(valid_positive_stmt_ids)} statements using distance threshold {domain_distance_threshold:.4f}...")

            # Step 2: Use KNN (via LATERAL join) to find similar pairs based on domain_embedding and text_embedding
            query_sql = """
                        SELECT s1.id AS stmt1_id, s2.id AS stmt2_id
                        FROM kg_statements_v2 s1
                                 JOIN kg_virt_entity_map m1 ON s1.company_id = m1.base_entity_id OR m1.base_entity_id = ANY(s1.subject_entities)
                                 CROSS JOIN LATERAL (
                            SELECT s2.id
                            FROM kg_statements_v2 s2
                                     JOIN kg_virt_entity_map m2 ON s2.company_id = m2.base_entity_id OR m2.base_entity_id = ANY(s2.subject_entities)
                            WHERE
                                m2.virt_entity_id = %s -- s2 same virt_entity
                              AND s2.id = ANY(%s::int[]) -- s2 in candidate list
                              AND s1.id < s2.id -- Avoid self/duplicates
                              AND s2.domain_embedding IS NOT NULL
                              -- KNN condition using text + domain embedding distance
                              AND ((s1.domain_embedding <=> s2.domain_embedding) ) <= %s
                            ) AS s2
                        WHERE
                            m1.virt_entity_id = %s -- s1 same virt_entity
                          AND s1.id = ANY(%s::int[]) -- s1 in candidate list
                          AND s1.domain_embedding IS NOT NULL; \
                        """
            query_params = (
                virtual_entity.id,             # Param for m2.virt_entity_id
                valid_positive_stmt_ids,    # Param for s2.id = ANY
                domain_distance_threshold,  # Param for distance check
                virtual_entity.id,             # Param for m1.virt_entity_id
                valid_positive_stmt_ids     # Param for s1.id = ANY
            )

            cur.execute(query_sql, query_params)

            similar_pairs = cur.fetchall()
            logger.info(f"Found {len(similar_pairs)} similar domain pairs using KNN search.")

            # Step 3: Build graph and find clusters (Same as before)
            graph = defaultdict(set)
            for stmt1_id, stmt2_id in similar_pairs:
                if stmt1_id in stmt_data and stmt2_id in stmt_data:
                    graph[stmt1_id].add(stmt2_id)
                    graph[stmt2_id].add(stmt1_id)

            clusters = []
            visited = set()
            all_cluster_ids = set(graph.keys()) | {nid for neighbors in graph.values() for nid in neighbors}

            for stmt_id in valid_positive_stmt_ids: # Iterate through all candidates
                if stmt_id in visited or stmt_id not in all_cluster_ids: # Skip visited or isolated
                    visited.add(stmt_id)
                    continue

                cluster = []
                queue = [stmt_id]
                visited.add(stmt_id)
                while queue:
                    current_id = queue.pop(0)
                    if current_id not in stmt_data: continue
                    cluster.append(current_id)
                    if current_id in graph:
                        for neighbor_id in graph[current_id]:
                            if neighbor_id not in visited:
                                visited.add(neighbor_id)
                                queue.append(neighbor_id)

                if len(cluster) >= settings.flooding_min_positive_cluster_size:
                    clusters.append(cluster)

            logger.info(
                f"Found {len(clusters)} clusters meeting size threshold ({settings.flooding_min_positive_cluster_size}).")

            # Step 4: Find related negative statements (Same as before, uses average domain vector)
            for cluster in clusters:
                cluster_domain_vectors = [stmt_data[stmt_id]['domain_embedding'] for stmt_id in cluster if stmt_id in stmt_data and stmt_data[stmt_id]['domain_embedding'] is not None]
                if not cluster_domain_vectors: continue

                avg_domain_vector = calculate_average_vector(cluster_domain_vectors)
                if avg_domain_vector is None: continue
                avg_domain_vector_list = avg_domain_vector.tolist()

                avg_positive_impact = sum(stmt_data[stmt_id]['impact_value'] for stmt_id in cluster if stmt_id in stmt_data) / len(cluster)

                # Find negative statements in the same domain (using avg domain vector)
                # Assumes index on domain_embedding exists
                cur.execute("""
                            SELECT s.id, s.impact_value
                            FROM kg_statements_v2 s
                                     JOIN kg_virt_entity_map m ON s.company_id = m.base_entity_id OR m.base_entity_id = ANY(s.subject_entities)
                            WHERE m.virt_entity_id = %s
                              AND s.impact_value <= %s
                              AND s.statement_category = 'action'
                              AND s.domain_embedding IS NOT NULL
                              AND s.id <> ALL (%s::int[]) -- Exclude statements in positive cluster
                              -- Compare with the average domain vector using distance threshold
                              AND s.domain_embedding <=> %s::vector <= %s
                            """, (
                                virtual_entity.id,
                                (settings.flooding_negative_impact_threshold),
                                cluster,
                                avg_domain_vector_list,
                                domain_distance_threshold  # Use domain distance threshold
                            ))
                negative_statements = cur.fetchall()

                if not negative_statements: continue
                logger.info(f"Found {len(negative_statements)} negative statements for cluster {cluster[:3]}...")

                negative_stmt_ids = [stmt[0] for stmt in negative_statements]
                avg_negative_impact = sum(stmt[1] for stmt in negative_statements) / len(negative_statements)

                # Get full statement objects for positive and negative statements
                from eko.db.data.statement import StatementData
                with get_bo_conn() as conn:
                    positive_statements = list(StatementData.get_statements_by_ids(conn, cluster).values())
                    negative_statements = list(StatementData.get_statements_by_ids(conn, negative_stmt_ids).values())

                # Create the initial model
                instance = FloodingModel(
                    virt_entity_id=virtual_entity.id,
                    positive_statements=positive_statements,
                    negative_statements=negative_statements,
                    domain_vector=avg_domain_vector_list,
                    average_positive_impact=float(avg_positive_impact),
                    negative_impact=float(avg_negative_impact),
                    positive_count=len(cluster),
                    run_id=run_id,
                    analysis="",
                    reason="",
                    label=""
                )

                from eko.analysis_v2.selective_highlighting.llm_analysis import analyze_flooding
                instance = analyze_flooding(virtual_entity,instance)
                logger.info(f"LLM analysis completed for flooding instance. Label: {instance.label}")
                flooding_instances.append(instance)



    end_time = time.time()
    elapsed_time = end_time - start_time
    logger.info(f"Flooding detection (KNN) for VE {virtual_entity.id} completed in {elapsed_time:.2f}s. Found {len(flooding_instances)} instances.")
    if tracker:
        # Use record_stat instead of log_metric
        from eko.models.vector.derived.enums import PipelineStage
        tracker.record_stat(
            entity=None,
            stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
            processing_time_ms=int(elapsed_time * 1000),
            count=len(flooding_instances),
            metadata={
                "virt_entity_id": virtual_entity.id,
                "detection_method": "flooding_pgvector_knn"
            }
        )

    return flooding_instances
