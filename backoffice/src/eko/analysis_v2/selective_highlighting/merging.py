"""
Merging functionality for selective highlighting models.

This module provides functions to merge similar cherry picking and flooding models
to avoid duplicates, similar to how effect models and effect flags are merged.
"""

import numpy as np
from loguru import logger
from sklearn.cluster import DBSCAN
from typing import List, Dict, Tuple, Optional, cast

from eko.analysis_v2.pipeline_tracker_extended import TraceabilityTracker
from eko.analysis_v2.selective_highlighting.models import CherryPickingModel, FloodingModel
from eko.llm import PROMPT_PRESERVE_CITATIONS, PROMPT_KEEP_FACTS, token_count
from eko.llm.llm_nlp import combine_text
from eko.llm.main import get_embedding
from eko.models.citation_model import Citation
from eko.models.statement_metadata import StatementAndMetadata
from eko.models.vector.derived.enums import PipelineStage
from eko.settings import settings


def extract_vectors_from_cherry_picking(models: List[CherryPickingModel]) -> Tuple[List[np.ndarray], List[CherryPickingModel]]:
    """
    Extract domain vectors from cherry picking models.

    Args:
        models: List of cherry picking models to extract vectors from

    Returns:
        Tuple containing (list of domain vectors, list of corresponding valid models)
    """
    domain_vectors = []
    valid_models = []

    for model in models:
        domain_vectors.append(np.array(model.domain_vector))

    return domain_vectors, valid_models


def extract_vectors_from_flooding(models: List[FloodingModel]) -> Tuple[List[np.ndarray], List[FloodingModel]]:
    """
    Extract domain vectors from flooding models.

    Args:
        models: List of flooding models to extract vectors from

    Returns:
        Tuple containing (list of domain vectors, list of corresponding valid models)
    """
    domain_vectors = []
    valid_models = []

    for model in models:
        domain_vectors.append(np.array(model.domain_vector))
        valid_models.append(model)
     
    return domain_vectors, valid_models


def get_text_embeddings_for_models(models: List[CherryPickingModel]) -> Tuple[List[np.ndarray], List[int]]:
    """
    Get text embeddings for a list of cherry picking models.

    Args:
        models: List of models to get embeddings for

    Returns:
        Tuple of (list of embeddings, list of valid indices)
    """
    embeddings = []
    valid_indices = []

    for i, model in enumerate(models):
        # Use label for embedding, fallback to analysis if label is None
        text_for_embedding = model.reason if model.reason else model.analysis
        embedding = get_embedding(text_for_embedding)
        if embedding:
            embeddings.append(embedding)
            valid_indices.append(i)

    return embeddings, valid_indices


def get_text_embeddings_for_flooding(models: List[FloodingModel]) -> Tuple[List[np.ndarray], List[int]]:
    """
    Get text embeddings for a list of flooding models.

    Args:
        models: List of models to get embeddings for

    Returns:
        Tuple of (list of embeddings, list of valid indices)
    """
    embeddings = []
    valid_indices = []

    for i, model in enumerate(models):
        # Use label for embedding, fallback to analysis if label is None
        text_for_embedding = model.reason if model.reason else model.analysis
        embedding = get_embedding(text_for_embedding)
        if embedding:
            embeddings.append(embedding)
            valid_indices.append(i)

    return embeddings, valid_indices


def cluster_by_domain(domain_vectors: List[np.ndarray]) -> Dict[int, List[int]]:
    """
    Cluster models by their domain vectors using DBSCAN.

    Args:
        domain_vectors: List of domain vectors extracted from models

    Returns:
        Dictionary mapping cluster IDs to lists of model indices
    """
    # Convert to numpy array
    domain_vectors_array = np.array(domain_vectors)

    # Parameters for DBSCAN for domain clustering
    domain_min_samples = 1  # Minimum cluster size
    # Use specific settings for cherry picking and flooding if available, otherwise fall back to effect flag settings
    domain_eps = getattr(settings, 'cherry_picking_merging_domain_eps', settings.effect_flag_merging_domain_eps)

    # Apply DBSCAN for domain clustering
    domain_dbscan = DBSCAN(eps=domain_eps, min_samples=domain_min_samples, metric='cosine')
    domain_clusters = domain_dbscan.fit_predict(domain_vectors_array)

    # Group models by domain cluster
    domain_cluster_groups = {}
    for i, cluster_id in enumerate(domain_clusters):
        if cluster_id not in domain_cluster_groups:
            domain_cluster_groups[cluster_id] = []
        domain_cluster_groups[cluster_id].append(i)

    return domain_cluster_groups


def cluster_by_text(models: List) -> List[List[int]]:
    """
    Cluster models by their text embeddings using DBSCAN.

    Args:
        models: List of models to cluster (either CherryPickingModel or FloodingModel)

    Returns:
        List of lists, where each inner list contains indices of models to merge
    """
    # Get text embeddings based on model type
    if isinstance(models[0], CherryPickingModel):
        text_embeddings, valid_indices = get_text_embeddings_for_models(cast(List[CherryPickingModel], models))
    else:
        text_embeddings, valid_indices = get_text_embeddings_for_flooding(cast(List[FloodingModel], models))

    if len(valid_indices) <= 1:
        return []

    # Convert to numpy array
    text_vectors_array = np.array(text_embeddings)

    # Parameters for DBSCAN for text embedding clustering
    # Use specific settings for cherry picking and flooding if available, otherwise fall back to effect flag settings
    if isinstance(models[0], CherryPickingModel):
        text_eps = getattr(settings, 'cherry_picking_merging_text_eps', settings.effect_flag_merging_text_eps)
    else:
        text_eps = getattr(settings, 'flooding_merging_text_eps', settings.effect_flag_merging_text_eps)
    text_min_samples = 1  # Minimum cluster size

    # Apply DBSCAN clustering on text embeddings
    text_dbscan = DBSCAN(eps=text_eps, min_samples=text_min_samples, metric='cosine')
    text_clusters = text_dbscan.fit_predict(text_vectors_array)

    # Group by text cluster
    text_cluster_groups = {}
    for i, cluster_id in enumerate(text_clusters):
        if cluster_id not in text_cluster_groups:
            text_cluster_groups[cluster_id] = []
        text_cluster_groups[cluster_id].append(valid_indices[i])

    to_merge = []
    for text_cluster_id, indices in text_cluster_groups.items():
        if text_cluster_id != -1 and len(indices) > 1:  # Skip noise points and single-item clusters
            to_merge.append(indices)

    return to_merge


def merge_cherry_picking_group(models_to_merge: List[CherryPickingModel]) -> CherryPickingModel:
    """
    Merge a group of cherry picking models into a single model.

    Args:
        models_to_merge: List of cherry picking models to merge

    Returns:
        A new merged cherry picking model
    """
    if not models_to_merge:
        raise ValueError("Cannot merge empty list of cherry picking models")

    # Use the first model as the base
    primary_model = models_to_merge[0]

    # Combine all statements from merging models
    all_positive_statements: Dict[int, StatementAndMetadata] = {}
    all_negative_statements: Dict[int, StatementAndMetadata] = {}

    for model in models_to_merge:
        # Add positive statements
        for stmt in model.positive_statements:
            if stmt.id and stmt.id not in all_positive_statements:
                all_positive_statements[stmt.id] = stmt

        # Add negative statements
        for stmt in model.negative_statements:
            if stmt.id and stmt.id not in all_negative_statements:
                all_negative_statements[stmt.id] = stmt

    # Combine analyses with a separator
    combined_analysis = combine_text(
        [model.analysis for model in models_to_merge if model.analysis]
    )

    # Combine reasons with a separator
    combined_reason = combine_text(
        [model.reason for model in models_to_merge if model.reason], has_citations=False, target_token_count=250
    )

    # Calculate average domain vector if available
    merged_domain_vector = None
    valid_domain_vectors = [model.domain_vector for model in models_to_merge
                           if model.domain_vector and len(model.domain_vector) > 0]

    if valid_domain_vectors:
        # Calculate average domain vector
        merged_domain_vector = np.mean(np.array(valid_domain_vectors), axis=0).tolist()

    # Calculate average impact scores
    avg_positive_impact = sum(model.average_positive_impact for model in models_to_merge) / len(models_to_merge)
    avg_negative_impact = sum(model.negative_impact for model in models_to_merge) / len(models_to_merge)

    # Use maximum repeat count
    max_repeat_count = max(model.repeat_count for model in models_to_merge)

    # Combine all citations from merging models
    all_citations: Dict[str, Citation] = {}
    for model in models_to_merge:
        for citation in model.citations:
            # Use doc_page_id as key to avoid duplicates
            if citation.doc_page_id is not None:
                # Convert doc_page_id to string for dictionary key
                key = str(citation.doc_page_id)
                if key not in all_citations:
                    all_citations[key] = citation

    # Use maximum scores from all models
    max_severity = max(model.severity for model in models_to_merge)
    max_confidence = max(model.confidence for model in models_to_merge)
    max_authenticity = max(model.authenticity for model in models_to_merge)

    # Create the merged model
    merged_model = CherryPickingModel(
        id=None,  # Will be assigned when saved to database
        virt_entity_id=primary_model.virt_entity_id,
        positive_statements=list(all_positive_statements.values()),
        negative_statements=list(all_negative_statements.values()),
        domain_vector=merged_domain_vector if merged_domain_vector is not None else [],
        average_positive_impact=avg_positive_impact,
        negative_impact=avg_negative_impact,
        repeat_count=max_repeat_count,
        run_id=primary_model.run_id,
        analysis=combined_analysis,
        reason=combined_reason,
        label=primary_model.label,  # Use the label from the primary model
        citations=list(all_citations.values()),
        severity=max_severity,
        confidence=max_confidence,
        authenticity=max_authenticity,
        # Merging information
        merged_from_ids=[model.id for model in models_to_merge if model.id is not None],
        is_merged=True,
        merge_type="domain",  # Will be updated to "domain+text" if text merging is also applied
    )

    return merged_model


def merge_flooding_group(models_to_merge: List[FloodingModel]) -> FloodingModel:
    """
    Merge a group of flooding models into a single model.

    Args:
        models_to_merge: List of flooding models to merge

    Returns:
        A new merged flooding model
    """
    if not models_to_merge:
        raise ValueError("Cannot merge empty list of flooding models")

    # Use the first model as the base
    primary_model = models_to_merge[0]

    # Combine all statements from merging models
    all_positive_statements: Dict[int, StatementAndMetadata] = {}
    all_negative_statements: Dict[int, StatementAndMetadata] = {}

    for model in models_to_merge:
        # Add positive statements
        for stmt in model.positive_statements:
            if stmt.id and stmt.id not in all_positive_statements:
                all_positive_statements[stmt.id] = stmt

        # Add negative statements
        for stmt in model.negative_statements:
            if stmt.id and stmt.id not in all_negative_statements:
                all_negative_statements[stmt.id] = stmt

    # Combine analyses with a separator
    combined_analysis = combine_text(
        [model.analysis for model in models_to_merge if model.analysis],
        has_citations=True, target_token_count=int(max([token_count(model.analysis) for model in models_to_merge if model.analysis]) * 1.5)
    )

    # Combine reasons with a separator
    combined_reason = combine_text(
        [model.reason for model in models_to_merge if model.reason],
        has_citations=False, target_token_count=250
    )

    # Calculate average domain vector if available
    merged_domain_vector = None
    valid_domain_vectors = [model.domain_vector for model in models_to_merge
                           if model.domain_vector and len(model.domain_vector) > 0]

    if valid_domain_vectors:
        # Calculate average domain vector
        merged_domain_vector = np.mean(np.array(valid_domain_vectors), axis=0).tolist()

    # Calculate average impact scores
    avg_positive_impact = sum(model.average_positive_impact for model in models_to_merge) / len(models_to_merge)
    avg_negative_impact = sum(model.negative_impact for model in models_to_merge) / len(models_to_merge)

    # Sum positive counts
    total_positive_count = sum(model.positive_count for model in models_to_merge)

    # Combine all citations from merging models
    all_citations: Dict[str, Citation] = {}
    for model in models_to_merge:
        for citation in model.citations:
            # Use doc_page_id as key to avoid duplicates
            if citation.doc_page_id is not None:
                # Convert doc_page_id to string for dictionary key
                key = str(citation.doc_page_id)
                if key not in all_citations:
                    all_citations[key] = citation

    # Use maximum scores from all models
    max_severity = max(model.severity for model in models_to_merge)
    max_confidence = max(model.confidence for model in models_to_merge)
    max_authenticity = max(model.authenticity for model in models_to_merge)

    # Create the merged model
    merged_model = FloodingModel(
        id=None,  # Will be assigned when saved to database
        virt_entity_id=primary_model.virt_entity_id,
        positive_statements=list(all_positive_statements.values()),
        negative_statements=list(all_negative_statements.values()),
        domain_vector=merged_domain_vector,
        average_positive_impact=avg_positive_impact,
        negative_impact=avg_negative_impact,
        positive_count=total_positive_count,
        run_id=primary_model.run_id,
        analysis=combined_analysis,
        reason=combined_reason,
        label=primary_model.label,  # Use the label from the primary model
        citations=list(all_citations.values()),
        severity=max_severity,
        confidence=max_confidence,
        authenticity=max_authenticity,
        # Merging information
        merged_from_ids=[model.id for model in models_to_merge if model.id is not None],
        is_merged=True,
        merge_type="domain"  # Will be updated to "domain+text" if text merging is also applied
    )

    return merged_model


def cluster_cherry_picking_by_domain(models: List[CherryPickingModel], tracker: Optional[TraceabilityTracker] = None) -> List[CherryPickingModel]:
    """
    Cluster and merge cherry picking models by their domain vectors.

    Args:
        models: List of cherry picking models to cluster and merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged models (domain-based merging)
    """
    if len(models) <= 1:
        return models

    # Extract domain vectors and get valid models
    domain_vectors, valid_models = extract_vectors_from_cherry_picking(models)

    if len(valid_models) <= 1:
        logger.warning("Not enough valid cherry picking models with domain vectors for clustering")
        return models

    # Cluster by domain
    domain_cluster_groups = cluster_by_domain(domain_vectors)

    # Prepare for merging
    merged_models = []
    merged_indices = set()
    domain_merge_groups = []

    # Collect domain clusters to merge
    for domain_cluster_id, domain_indices in domain_cluster_groups.items():
        # Only process domain clusters with more than one model
        if len(domain_indices) > 1 and domain_cluster_id != -1:  # Skip noise points (cluster_id = -1)
            domain_merge_groups.append(domain_indices)

    # Perform domain-based merging
    for model_indices in domain_merge_groups:
        # Collect all models to be merged
        models_to_merge = [valid_models[idx] for idx in model_indices]

        # Find the original indices in the input models list
        original_indices = []
        for model in models_to_merge:
            for i, orig_model in enumerate(models):
                if orig_model.id == model.id:
                    original_indices.append(i)
                    break

        # Add indices to the merged set
        for idx in original_indices:
            merged_indices.add(idx)

        # Create a merged model
        merged_model = merge_cherry_picking_group(models_to_merge)

        # Track the merge operation if tracker is provided
        if tracker:
            tracker.record_stat(
                entity=None,
                stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
                count=1,
                metadata={
                    "merge_type": "domain",
                    "models_merged": len(models_to_merge),
                    "similarity_threshold": settings.effect_flag_merging_domain_eps
                }
            )

        merged_models.append(merged_model)

    # Add the models that weren't merged
    for i, model in enumerate(models):
        if i not in merged_indices:
            merged_models.append(model)

    logger.info(f"After domain merging: {len(merged_models)} cherry picking models (from {len(models)})")

    return merged_models


def cluster_flooding_by_domain(models: List[FloodingModel], tracker: Optional[TraceabilityTracker] = None) -> List[FloodingModel]:
    """
    Cluster and merge flooding models by their domain vectors.

    Args:
        models: List of flooding models to cluster and merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged models (domain-based merging)
    """
    if len(models) <= 1:
        return models

    # Extract domain vectors and get valid models
    domain_vectors, valid_models = extract_vectors_from_flooding(models)

    if len(valid_models) <= 1:
        logger.warning("Not enough valid flooding models with domain vectors for clustering")
        return models

    # Cluster by domain
    domain_cluster_groups = cluster_by_domain(domain_vectors)

    # Prepare for merging
    merged_models = []
    merged_indices = set()
    domain_merge_groups = []

    # Collect domain clusters to merge
    for domain_cluster_id, domain_indices in domain_cluster_groups.items():
        # Only process domain clusters with more than one model
        if len(domain_indices) > 1 and domain_cluster_id != -1:  # Skip noise points (cluster_id = -1)
            domain_merge_groups.append(domain_indices)

    # Perform domain-based merging
    for model_indices in domain_merge_groups:
        # Collect all models to be merged
        models_to_merge = [valid_models[idx] for idx in model_indices]

        # Find the original indices in the input models list
        original_indices = []
        for model in models_to_merge:
            for i, orig_model in enumerate(models):
                if orig_model.id == model.id:
                    original_indices.append(i)
                    break

        # Add indices to the merged set
        for idx in original_indices:
            merged_indices.add(idx)

        # Create a merged model
        merged_model = merge_flooding_group(models_to_merge)

        # Track the merge operation if tracker is provided
        if tracker:
            tracker.record_stat(
                entity=None,
                stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
                count=1,
                metadata={
                    "merge_type": "domain",
                    "models_merged": len(models_to_merge),
                    "similarity_threshold": settings.effect_flag_merging_domain_eps
                }
            )

        merged_models.append(merged_model)

    # Add the models that weren't merged
    for i, model in enumerate(models):
        if i not in merged_indices:
            merged_models.append(model)

    logger.info(f"After domain merging: {len(merged_models)} flooding models (from {len(models)})")

    return merged_models


def cluster_cherry_picking_by_text(models: List[CherryPickingModel], tracker: Optional[TraceabilityTracker] = None) -> List[CherryPickingModel]:
    """
    Cluster and merge cherry picking models by their text embeddings.

    Args:
        models: List of cherry picking models to cluster and merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged models (text-based merging)
    """
    if len(models) <= 1:
        return models

    # Cluster by text
    text_merge_groups = cluster_by_text(models)

    if not text_merge_groups:
        return models

    # Prepare for merging
    merged_models = []
    merged_indices = set()

    # Perform text-based merging
    for model_indices in text_merge_groups:
        # Collect all models to be merged
        models_to_merge = [models[idx] for idx in model_indices]

        # Add indices to the merged set
        for idx in model_indices:
            merged_indices.add(idx)

        # Create a merged model
        merged_model = merge_cherry_picking_group(models_to_merge)

        # Track the merge operation if tracker is provided
        if tracker:
            tracker.record_stat(
                entity=None,
                stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
                count=1,
                metadata={
                    "merge_type": "text",
                    "models_merged": len(models_to_merge),
                    "similarity_threshold": settings.effect_flag_merging_text_eps
                }
            )

        merged_models.append(merged_model)

    # Add the models that weren't merged
    for i, model in enumerate(models):
        if i not in merged_indices:
            merged_models.append(model)

    logger.info(f"After text merging: {len(merged_models)} cherry picking models (from {len(models)})")

    return merged_models


def cluster_flooding_by_text(models: List[FloodingModel], tracker: Optional[TraceabilityTracker] = None) -> List[FloodingModel]:
    """
    Cluster and merge flooding models by their text embeddings.

    Args:
        models: List of flooding models to cluster and merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged models (text-based merging)
    """
    if len(models) <= 1:
        return models

    # Cluster by text
    text_merge_groups = cluster_by_text(models)

    if not text_merge_groups:
        return models

    # Prepare for merging
    merged_models = []
    merged_indices = set()

    # Perform text-based merging
    for model_indices in text_merge_groups:
        # Collect all models to be merged
        models_to_merge = [models[idx] for idx in model_indices]

        # Add indices to the merged set
        for idx in model_indices:
            merged_indices.add(idx)

        # Create a merged model
        merged_model = merge_flooding_group(models_to_merge)

        # Track the merge operation if tracker is provided
        if tracker:
            tracker.record_stat(
                entity=None,
                stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
                count=1,
                metadata={
                    "merge_type": "text",
                    "models_merged": len(models_to_merge),
                    "similarity_threshold": settings.effect_flag_merging_text_eps
                }
            )

        merged_models.append(merged_model)

    # Add the models that weren't merged
    for i, model in enumerate(models):
        if i not in merged_indices:
            merged_models.append(model)

    logger.info(f"After text merging: {len(merged_models)} flooding models (from {len(models)})")

    return merged_models


def merge_similar_cherry_picking_models(models: List[CherryPickingModel], tracker: Optional[TraceabilityTracker] = None) -> List[CherryPickingModel]:
    """
    Merge similar cherry picking models in a two-step process:
    1. First cluster all models by domain and merge them
    2. Then cluster all merged models by text similarity and merge again

    Args:
        models: List of cherry picking models to merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged cherry picking models
    """
    if len(models) <= 1:
        return models

    logger.info(f"Starting with {len(models)} cherry picking models before merging")

    # Step 1: Cluster by domain and merge
    domain_merged_models = cluster_cherry_picking_by_domain(models, tracker)

    # If no domain merging occurred, return the original models
    if len(domain_merged_models) == len(models):
        logger.info("No domain-based merging occurred for cherry picking models")
    else:
        logger.info(f"Domain-based merging: {len(models)} -> {len(domain_merged_models)} cherry picking models")

    # Step 2: Cluster by text and merge
    text_merged_models = cluster_cherry_picking_by_text(domain_merged_models, tracker)

    # If no text merging occurred, return the domain-merged models
    if len(text_merged_models) == len(domain_merged_models):
        logger.info("No text-based merging occurred for cherry picking models")
    else:
        logger.info(f"Text-based merging: {len(domain_merged_models)} -> {len(text_merged_models)} cherry picking models")

    # Log final clustering statistics
    if tracker:
        tracker.record_stat(
            entity=None,
            stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
            count=len(text_merged_models),
            metadata={
                "clustering_method": "Sequential Domain-Text DBSCAN",
                "clustering_metric": "cosine",
                "domain_eps": getattr(settings, 'cherry_picking_merging_domain_eps', settings.effect_flag_merging_domain_eps),
                "text_eps": getattr(settings, 'cherry_picking_merging_text_eps', settings.effect_flag_merging_text_eps),
                "domain_min_samples": 1,
                "text_min_samples": 1,
                "initial_count": len(models),
                "after_domain_merge_count": len(domain_merged_models),
                "final_count": len(text_merged_models),
                "total_merged_count": len(models) - len(text_merged_models),
                "model_type": "cherry_picking"
            }
        )

    return text_merged_models


def merge_similar_flooding_models(models: List[FloodingModel], tracker: Optional[TraceabilityTracker] = None) -> List[FloodingModel]:
    """
    Merge similar flooding models in a two-step process:
    1. First cluster all models by domain and merge them
    2. Then cluster all merged models by text similarity and merge again

    Args:
        models: List of flooding models to merge
        tracker: Optional traceability tracker instance

    Returns:
        List of merged flooding models
    """
    if len(models) <= 1:
        return models

    logger.info(f"Starting with {len(models)} flooding models before merging")

    # Step 1: Cluster by domain and merge
    domain_merged_models = cluster_flooding_by_domain(models, tracker)

    # If no domain merging occurred, return the original models
    if len(domain_merged_models) == len(models):
        logger.info("No domain-based merging occurred for flooding models")
    else:
        logger.info(f"Domain-based merging: {len(models)} -> {len(domain_merged_models)} flooding models")

    # Step 2: Cluster by text and merge
    text_merged_models = cluster_flooding_by_text(domain_merged_models, tracker)

    # If no text merging occurred, return the domain-merged models
    if len(text_merged_models) == len(domain_merged_models):
        logger.info("No text-based merging occurred for flooding models")
    else:
        logger.info(f"Text-based merging: {len(domain_merged_models)} -> {len(text_merged_models)} flooding models")

    # Log final clustering statistics
    if tracker:
        tracker.record_stat(
            entity=None,
            stage=PipelineStage.STATEMENT_EXTRACTED,  # Using an existing stage
            count=len(text_merged_models),
            metadata={
                "clustering_method": "Sequential Domain-Text DBSCAN",
                "clustering_metric": "cosine",
                "domain_eps": getattr(settings, 'flooding_merging_domain_eps', settings.effect_flag_merging_domain_eps),
                "text_eps": getattr(settings, 'flooding_merging_text_eps', settings.effect_flag_merging_text_eps),
                "domain_min_samples": 1,
                "text_min_samples": 1,
                "initial_count": len(models),
                "after_domain_merge_count": len(domain_merged_models),
                "final_count": len(text_merged_models),
                "total_merged_count": len(models) - len(text_merged_models),
                "model_type": "flooding"
            }
        )

    return text_merged_models
