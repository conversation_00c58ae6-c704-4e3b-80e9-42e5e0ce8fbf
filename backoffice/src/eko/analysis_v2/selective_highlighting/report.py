from typing import List, Dict, Optional

from loguru import logger

from eko.analysis_v2.selective_highlighting.models import CherryPickingModel, FloodingModel
from eko.entities.virtual_queries import get_virtual_entity_by_id


def get_statement_info_by_id(statement_id: int) -> tuple:
    """
    Get the statement text and category for a given statement ID.

    Args:
        statement_id: The ID of the statement to fetch

    Returns:
        Tuple of (statement_text, statement_category, impact_value)
    """
    from eko.db import get_bo_conn

    try:
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT statement_text, statement_category, impact_value
                    FROM kg_statements_v2
                    WHERE id = %s
                    """,
                    (statement_id,)
                )
                result = cur.fetchone()
                if result:
                    return result[0], result[1], result[2]
    except Exception as e:
        logger.error(f"Error fetching statement info for ID {statement_id}: {e}")

    return f"[Statement ID: {statement_id} - text not available]", "unknown", 0.0


def get_statement_text_by_id(statement_id: int) -> str:
    """
    Get the statement text for a given statement ID.

    Args:
        statement_id: The ID of the statement to fetch

    Returns:
        The statement text or a placeholder if not found
    """
    text, _, _ = get_statement_info_by_id(statement_id)
    return text


def get_statement_sample(statement_ids: List[int], max_sample: int = 3) -> List[dict]:
    """
    Get a sample of statement information for a list of statement IDs.

    Args:
        statement_ids: The list of statement IDs
        max_sample: Maximum number of statements to sample

    Returns:
        A list of statement info dictionaries (limited to max_sample)
    """
    # Sample up to max_sample statements
    sample_size = min(max_sample, len(statement_ids))
    sample_ids = statement_ids[:sample_size]

    # Get the info for each statement
    statements = []
    for stmt_id in sample_ids:
        text, category, impact = get_statement_info_by_id(stmt_id)
        statements.append({
            "id": stmt_id,
            "text": text,
            "category": category,
            "impact": impact
        })

    return statements


def generate_report(cherry_picking_results: List[CherryPickingModel], flooding_results: List[FloodingModel]) -> str:
    """
    Generate a human-readable report from analysis results.

    Args:
        cherry_picking_results: List of cherry picking instances
        flooding_results: List of flooding instances

    Returns:
        String containing the formatted report
    """
    report = []
    report.append("=" * 80)
    report.append("SELECTIVE HIGHLIGHTING ANALYSIS REPORT")
    report.append("=" * 80)
    report.append("")

    # Count merged models
    merged_cherry_picking = sum(1 for cp in cherry_picking_results if hasattr(cp, 'is_merged') and cp.is_merged)
    merged_flooding = sum(1 for fl in flooding_results if hasattr(fl, 'is_merged') and fl.is_merged)

    # Cherry picking report
    report.append("-" * 80)
    report.append(f"CHERRY PICKING INSTANCES: {len(cherry_picking_results)} (including {merged_cherry_picking} merged instances)")
    report.append("(Note: Only action statements with non-zero impact values are considered)")
    report.append("-" * 80)

    for i, cp in enumerate(cherry_picking_results, 1):
        entity = get_virtual_entity_by_id(cp.virt_entity_id)
        entity_name = entity.name if entity else f"Entity ID: {cp.virt_entity_id}"

        report.append(f"Instance #{i}: {entity_name}")
        report.append(f"  Positive Statements: {len(cp.positive_statement_ids)}")
        report.append(f"  Negative Statements: {len(cp.negative_statement_ids)}")
        report.append(f"  Average Positive Impact: {cp.average_positive_impact:.2f}")
        report.append(f"  Negative Impact: {cp.negative_impact:.2f}")
        report.append(f"  Repeat Count: {cp.repeat_count}")

        # Add merging information if this is a merged model
        if hasattr(cp, 'is_merged') and cp.is_merged:
            # For models that were merged during clustering, we don't have the original IDs
            # because they were created during the clustering process
            if hasattr(cp, 'merged_from_ids') and cp.merged_from_ids:
                report.append(f"  [MERGED MODEL] - Merged from {len(cp.merged_from_ids)} instances")
                report.append(f"  Merged Model IDs: {', '.join(str(id) for id in cp.merged_from_ids)}")
            else:
                report.append(f"  [MERGED MODEL] - Created during clustering")

            report.append(f"  Merge Type: {cp.merge_type}")

        report.append("")

        # Sample of positive statements
        pos_samples = get_statement_sample(cp.positive_statement_ids)
        report.append(f"  Sample of Positive Statements:")
        for s, stmt in enumerate(pos_samples, 1):
            report.append(f"    +{s}. [Category: {stmt['category']}, Impact: {stmt['impact']:.2f}] \"{stmt['text']}\"")

        # Sample of negative statements
        neg_samples = get_statement_sample(cp.negative_statement_ids)
        report.append(f"  Sample of Negative Statements:")
        for s, stmt in enumerate(neg_samples, 1):
            report.append(f"    -{s}. [Category: {stmt['category']}, Impact: {stmt['impact']:.2f}] \"{stmt['text']}\"")

        report.append("")

    # Flooding report
    report.append("-" * 80)
    report.append(f"FLOODING INSTANCES: {len(flooding_results)} (including {merged_flooding} merged instances)")
    report.append("(Note: Only action statements with non-zero impact values are considered)")
    report.append("-" * 80)

    for i, fl in enumerate(flooding_results, 1):
        entity = get_virtual_entity_by_id(fl.virt_entity_id)
        entity_name = entity.name if entity else f"Entity ID: {fl.virt_entity_id}"

        report.append(f"Instance #{i}: {entity_name}")
        report.append(f"  Positive Statements: {len(fl.positive_statement_ids)} ({fl.positive_count} mentions)")
        report.append(f"  Negative Statements: {len(fl.negative_statement_ids)}")
        report.append(f"  Average Positive Impact: {fl.average_positive_impact:.2f}")
        report.append(f"  Negative Impact: {fl.negative_impact:.2f}")

        # Add merging information if this is a merged model
        if hasattr(fl, 'is_merged') and fl.is_merged:
            # For models that were merged during clustering, we don't have the original IDs
            # because they were created during the clustering process
            if hasattr(fl, 'merged_from_ids') and fl.merged_from_ids:
                report.append(f"  [MERGED MODEL] - Merged from {len(fl.merged_from_ids)} instances")
                report.append(f"  Merged Model IDs: {', '.join(str(id) for id in fl.merged_from_ids)}")
            else:
                report.append(f"  [MERGED MODEL] - Created during clustering")

            report.append(f"  Merge Type: {fl.merge_type}")

        report.append("")

        # Sample of positive statements
        pos_samples = get_statement_sample(fl.positive_statement_ids)
        report.append(f"  Sample of Positive Statements:")
        for s, stmt in enumerate(pos_samples, 1):
            report.append(f"    +{s}. [Category: {stmt['category']}, Impact: {stmt['impact']:.2f}] \"{stmt['text']}\"")

        # Sample of negative statements
        neg_samples = get_statement_sample(fl.negative_statement_ids)
        report.append(f"  Sample of Negative Statements:")
        for s, stmt in enumerate(neg_samples, 1):
            report.append(f"    -{s}. [Category: {stmt['category']}, Impact: {stmt['impact']:.2f}] \"{stmt['text']}\"")

        report.append("")

    # Merging summary
    if merged_cherry_picking > 0 or merged_flooding > 0:
        report.append("-" * 80)
        report.append("MERGING SUMMARY")
        report.append("-" * 80)
        report.append(f"Cherry Picking: {merged_cherry_picking} of {len(cherry_picking_results)} instances are merged models")
        report.append(f"Flooding: {merged_flooding} of {len(flooding_results)} instances are merged models")
        report.append("")

        # Merging details by type
        domain_merged_cp = sum(1 for cp in cherry_picking_results if hasattr(cp, 'is_merged') and cp.is_merged and cp.merge_type == "domain")
        text_merged_cp = sum(1 for cp in cherry_picking_results if hasattr(cp, 'is_merged') and cp.is_merged and cp.merge_type == "text")
        combined_merged_cp = sum(1 for cp in cherry_picking_results if hasattr(cp, 'is_merged') and cp.is_merged and cp.merge_type == "domain+text")

        domain_merged_fl = sum(1 for fl in flooding_results if hasattr(fl, 'is_merged') and fl.is_merged and fl.merge_type == "domain")
        text_merged_fl = sum(1 for fl in flooding_results if hasattr(fl, 'is_merged') and fl.is_merged and fl.merge_type == "text")
        combined_merged_fl = sum(1 for fl in flooding_results if hasattr(fl, 'is_merged') and fl.is_merged and fl.merge_type == "domain+text")

        report.append("Cherry Picking Merging Details:")
        report.append(f"  - Domain-based merging: {domain_merged_cp} instances")
        report.append(f"  - Text-based merging: {text_merged_cp} instances")
        report.append(f"  - Combined domain+text merging: {combined_merged_cp} instances")
        report.append("")

        report.append("Flooding Merging Details:")
        report.append(f"  - Domain-based merging: {domain_merged_fl} instances")
        report.append(f"  - Text-based merging: {text_merged_fl} instances")
        report.append(f"  - Combined domain+text merging: {combined_merged_fl} instances")
        report.append("")

    report.append("=" * 80)
    report.append("END OF REPORT")
    report.append("=" * 80)

    return "\n".join(report)
