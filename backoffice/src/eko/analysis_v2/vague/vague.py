from loguru import logger
from pydantic import BaseModel, Field
from typing import List

from eko.analysis_v2.citations import verify_analysis_entity, extract_valid_citations, valid_citations
from eko.analysis_v2.vague.vague_models import VagueTermAnalysis
from eko.config import MAX_RANK_OVERALL_FOR_VAGUE_TERMS, \
    SHORTENED_TEXT_LENGTH_FOR_LLMS, MAX_RELEVANCE_COUNT, MIN_LLM_SCORE_FOR_VAGUE_TERMS
from eko.db import get_bo_conn
from eko.llm import LLMModel
from eko.llm.main import call_llms
from eko.llm.prompts import load_prompt
from eko.models import Entity
from eko.nlp.core import remove_duplicate_lines


class VagueTermsList(BaseModel):
    terms: List[str] = Field(..., description="List of vague terms.")


def summarize_analyses(cur, analyses: list[VagueTermAnalysis]) -> VagueTermAnalysis:
    terms = [x.phrase for x in analyses]
    text = "\n\n".join([f"## {x.phrase}\n\n{x.analysis}" for x in analyses])

    messages = [{
        "role": "system",
        "content": "You are a knowledgeable researcher in corporate adherence to ESG guidelines, you have read extensively on the subject and have access to almost unlimited information. "
                   "You are not conversational you just produce the output as requested."

    }
        , {
            "role": "user",
            "content": f"""
            Please merge these analyses into a single analysis, keeping the analysis of each term: {terms}.
            Preserve all references to the source reports and keep all citation markers (in the form of [^789123]).
            The 'analysis' field should be a substantial markdown document with extensive analysis of each term separated by a heading. Typically this will be about 1-5 pages of text depending on the number of analyses being summarized.

            Analyses to summarize:
            {text}
        """
        }]
    from eko.llm.main import LLMOptions
    options = LLMOptions(
        no_cache=False,
        metadata={"label":"merge-vague-term"},
        eval=lambda x: valid_citations(cur, x.analysis)
    )
    response = call_llms(
        [LLMModel.GPT_4O],
        messages, 4000, response_model=VagueTermAnalysis, options=options
    )
    logger.info(f"Using LLM to merge analyses: {response}")

    return response


def vague_term_analysis(cur, entity: Entity, text: str, phrase: str) -> VagueTermAnalysis:
    instructions = f"""
            <instructions>
            You are only interested in the {entity.type} {entity.name} and the term '{phrase}' if the text is not about the {entity.type} {entity.name} {' or it''s subsidiaries ' if entity.type == "company" else ""} but instead just about the term '{phrase}' YOU MUST score 0.

            If the claim of '{phrase}' is not in the supplied text YOU MUST score 0.

            In the context of greenwashing please identify if the following text contains could be classed as greenwashing by vagueness in regards to the phrase '{phrase}'. YOU MUST ALWAYS provide citations.

            If the phrase '{phrase}' requires certification,  and {entity.name} has certification for the term, please IGNORE it.

            If {entity.name} does not have valid certification for '{phrase}', please INCLUDE it.

            If the term is used in a way that is not related to ESG, please IGNORE it.

            Please quote the part of the text that contains the term and cite the page id (in the form [^page_id]). The page id is supplied in the text in the form of ```<page id='798114'>...</page>```. So for example:

            <example-citation>"nearly two-thirds (65%) contained synthetics, with 54% containing polyester" [^798114]</example-citation>

            YOU MUST ALWAYS provide citations.

            </instructions>
"""

    prompt = load_prompt("vague_terms_analysis", True, text=text, instructions=instructions)

    options2 = LLMOptions(
        metadata={"label":"vague-term-analysis"},
        eval=lambda x: x is not None and (x.score <= MIN_LLM_SCORE_FOR_VAGUE_TERMS or
                                          valid_citations(cur, x.analysis))
    )
    response = call_llms([LLMModel.GPT_4O], prompt, max_tokens=4000, response_model=VagueTermAnalysis,
                         options=options2)
    logger.info(f"Using LLM response to vague terms: {response}")
    return response


def vague_term_analysis_for_doc(cur, entity: Entity, text: str) -> VagueTermAnalysisList:
    instructions = f"""
            <instructions>
            You are only interested in the {entity.type} {entity.name} and any vague and/opr unsubstrantiated ESG terms used by them in the supplied
            text.

            In the context of greenwashing please identify if the following text contains terms which could be classed
            as greenwashing by vagueness. YOU MUST ALWAYS provide citations.

            If a phrase requires certification,  and {entity.name} has certification for the term, please IGNORE it.

            If {entity.name} does not have valid certification but uses the phrase, please INCLUDE it.

            If the phrase is used in a way that is not related to ESG, please IGNORE it.

            Please quote the part of the text that contains the phrase and cite the page id (in the form [^page_id]).
            The page id is supplied in the text in the form of ```<page id='798114'>...</page>```. So for example:

            <example-citation>"nearly two-thirds (65%) contained synthetics, with 54% containing polyester" [^798114]</example-citation>

            YOU MUST ALWAYS provide citations.

            </instructions>
"""

    prompt = load_prompt("vague_terms_analysis", True, text=text, instructions=instructions)

    from eko.llm.main import LLMOptions
    options = LLMOptions(
        metadata={"label":"vague-term-analysis-doc"},
        eval=lambda y: y is not None and all([x.score <= MIN_LLM_SCORE_FOR_VAGUE_TERMS or
                                              valid_citations(cur, x.analysis) for x in y.analyses])
    )
    response = call_llms([LLMModel.GPT_4O], prompt, max_tokens=4000, response_model=VagueTermAnalysisList,
                         options=options)
    logger.info(f"Using LLM response to vague terms: {response}")
    return response


def vague_terms_command(run_id: int, entity: Entity):
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            analyses = []
            summary_ids = []
            # phrases = get_phrases_for_entity(conn, entity)
            phrases = greenwashing_terms_text_list

            # TODO: CREATE A REAL TABLE
            # Create a temporary table for terms
            cur.execute("CREATE TEMPORARY TABLE temp_search_terms (term TEXT) ON COMMIT DROP;")
            # Insert all phrases into the temporary table
            cur.executemany("INSERT INTO temp_search_terms (term) VALUES (%s);", [(phrase,) for phrase in phrases])

            cur.execute(
                """
                SELECT ARRAY_AGG(id),
                       STRING_AGG(page_text, '\n\n' ORDER BY rank, id) as doc_text,
                       ts_rank_cd(to_tsvector('english', STRING_AGG(page_text, '\n\n' ORDER BY rank, id)), plainto_tsquery(data.phrase)) as combined_rank,
                       data.phrase
                FROM (
                    SELECT DISTINCT
                           ana_runs.id as run_id,
                           prox.entity_id,
                           chunks.id as id,
                           '<page id="' || chunks.doc_page_id || '" report="' || rep.title ||  '" page="' || (rp.page + 1) || '">' || rp.page_text || '</page><!--' || chunks.doc_page_id || '-->' as page_text,
                           ts_rank_cd(chunks.searchable_text, plainto_tsquery(temp_search_terms.term)) AS rank,
                           temp_search_terms.term as phrase
                    FROM view_entity_issue_proximity prox
                    JOIN kg_document_chunks chunks ON prox.doc_chunk_id = chunks.id
                    JOIN temp_search_terms ON chunks.keyword_search_text ~* ('\\y' || temp_search_terms.term || '\\y')
                    JOIN kg_document_pages rp ON chunks.doc_page_id = rp.id
                    JOIN kg_documents rep ON rp.doc_id = rep.id
                    JOIN kg_document_entity_map rem ON rep.id = rem.doc_id AND rem.entity_id = prox.entity_id,
                         ana_runs
                    WHERE ana_runs.id = %s
                      AND (ana_runs.start_year IS NULL OR ana_runs.start_year <= prox.year::integer)
                      AND (ana_runs.end_year IS NULL OR ana_runs.end_year >= prox.year::integer)
                      AND relevance_count < %s
                      AND rep.credibility > 0
                      AND prox.entity_id = %s
                      AND (chunks.text_type = 'prose' OR chunks.text_type = 'list')
                      AND 'disclosure' = ANY(rep.research_categories)
                ) data
                GROUP BY run_id, entity_id, data.phrase
                ORDER BY combined_rank DESC, doc_text, data.phrase;
                """,
                (run_id, MAX_RELEVANCE_COUNT, entity.id)
                )

            rows = cur.fetchall()
            for ids, text, combined_rank,phrase in rows:
                text = remove_duplicate_lines(text)
                if combined_rank > MAX_RANK_OVERALL_FOR_VAGUE_TERMS:
                    logger.info(
                        f"Low match for vague term {phrase} in report chunks, text length {len(text)} {combined_rank}")
                else:
                    logger.info(
                        f"Good match for vague term {phrase} in report chunks, text length {len(text)} {combined_rank}")
                    shortened_text = text[-SHORTENED_TEXT_LENGTH_FOR_LLMS:]

                    analysis = vague_term_analysis(cur, entity, shortened_text, phrase)
                    if analysis is None:
                        logger.info(f"Analysis not found for {entity.name} and {phrase}")
                        continue
                    if phrase.lower() not in analysis.phrase.lower():
                        logger.info(f"Phrase mismatch for {analysis.phrase.lower()} and {phrase.lower()}")
                        continue
                    # logger.info(f"Terms found: {analysis}")
                    logger.info(f"Terms found {analysis.score} {analysis.analysis}")
                    if analysis.score > MIN_LLM_SCORE_FOR_VAGUE_TERMS:
                        logger.info(f"Vague terms found for {entity.name} and {phrase}")
                        analysis.citations = extract_valid_citations(cur, analysis.analysis)
                        if len(analysis.citations) == 0:
                            logger.info(f"No citations found for {entity.name} and {phrase}")
                            continue
                        analysis = verify_analysis_entity(analysis, entity)
                        if analysis is None:
                            logger.info(f"Analysis not verified for {entity.name} and {phrase}")
                            continue
                        cur.execute("""
                        INSERT INTO ana_gw_vague (run_id, entity_id, phrase, doc_chunk_ids, citations, analysis, score, rank, explanation, summary)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (run_id, entity_id, phrase)
                        DO UPDATE SET   doc_chunk_ids = EXCLUDED.doc_chunk_ids,
                                        citations = EXCLUDED.citations,
                                        analysis = EXCLUDED.analysis,
                                        score = EXCLUDED.score,
                                        rank = EXCLUDED.rank,
                                        explanation = EXCLUDED.explanation,
                                        summary = EXCLUDED.summary""",
                                    (run_id, entity.id, phrase, list(set(ids)), analysis.citations,
                                     analysis.analysis, analysis.score, combined_rank, analysis.explanation,
                                     analysis.summary))
                        analysis.phrase = phrase
                        analyses.append(analysis)
                        summary_ids.extend(ids)
                        conn.commit()
                    else:
                        logger.info(f"No vague use of the vague term '{phrase}' found")

            if len(analyses) > 1:
                summarized = summarize_analyses(cur, analyses)
                logger.info(summarized)
                citations = extract_valid_citations(cur, summarized.analysis)
                summarized = verify_analysis_entity(summarized, entity)

                if len(citations) == 0:
                    logger.info(f"No summary citations found for {entity.name} and {phrase}")
                    return

                cur.execute("""
                            INSERT INTO ana_gw_vague (run_id, entity_id, phrase, doc_chunk_ids, citations, analysis, score, rank, explanation, summary)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (run_id, entity_id, phrase)
                            DO UPDATE SET   doc_chunk_ids = EXCLUDED.doc_chunk_ids,
                                            citations = EXCLUDED.citations,
                                            analysis = EXCLUDED.analysis,
                                            score = EXCLUDED.score,
                                            rank = EXCLUDED.rank,
                                            explanation = EXCLUDED.explanation,
                                            summary = EXCLUDED.summary""",
                            (run_id, entity.id, "__summary__", list(set(summary_ids)), citations,
                             summarized.analysis, summarized.score, combined_rank, summarized.explanation,
                             summarized.summary))
