"""
Vague terms analysis module.

This module provides functionality for analyzing vague terms in text.
"""
from typing import List, Optional, Dict, Any, Set

from loguru import logger
from psycopg import Connection

from eko.analysis_v2.citations import extract_valid_citations, valid_citations, verify_analysis_entity
from eko.config import MIN_LLM_SCORE_FOR_VAGUE_TERMS, SHORTENED_TEXT_LENGTH_FOR_LLMS, MAX_RANK_OVERALL_FOR_VAGUE_TERMS
from eko.db.data.vague_term import VagueTermData
from eko.entities.virtual_queries import get_virtual_entity_by_id
from eko.llm import LLMModel
from eko.llm.main import call_llms
from eko.llm.prompts import load_prompt
from eko.models.vague import VagueTermModel, VagueTermSummaryModel, VagueTermListModel
from eko.nlp.core import remove_duplicate_lines


# List of common greenwashing terms
greenwashing_terms_text_list = [
    "sustainable", "eco-friendly", "green", "environmentally friendly", "natural", "organic",
    "biodegradable", "renewable", "clean", "zero waste", "carbon neutral", "carbon negative",
    "net zero", "climate positive", "climate neutral", "eco-conscious", "planet-friendly",
    "earth-friendly", "environmentally responsible", "environmentally conscious", "eco",
    "green energy", "clean energy", "renewable energy", "sustainable energy", "green power",
    "clean power", "renewable power", "sustainable power", "green business", "green company",
    "green product", "green solution", "green technology", "green initiative", "green program",
    "green project", "green policy", "green practice", "green process", "green packaging",
    "green manufacturing", "green production", "green supply chain", "green logistics",
    "green transportation", "green building", "green construction", "green development",
    "green design", "green architecture", "green infrastructure", "green city", "green community",
    "green neighborhood", "green home", "green house", "green living", "green lifestyle",
    "green consumer", "green consumption", "green purchasing", "green procurement",
    "green investment", "green finance", "green banking", "green bond", "green fund",
    "green portfolio", "green economy", "green growth", "green jobs", "green collar",
    "green skills", "green education", "green training", "green certification", "green label",
    "green seal", "green standard", "green rating", "green ranking", "green award",
    "green recognition", "green reputation", "green brand", "green marketing", "green advertising",
    "green PR", "green communication", "green message", "green claim", "green promise",
    "green commitment", "green pledge", "green goal", "green target", "green objective",
    "green strategy", "green plan", "green roadmap", "green vision", "green mission",
    "green value", "green ethic", "green principle", "green philosophy", "green thinking",
    "green mindset", "green attitude", "green behavior", "green action", "green practice",
    "green habit", "green choice", "green decision", "green option", "green alternative",
    "green solution", "green approach", "green method", "green technique", "green technology",
    "green innovation", "green invention", "green discovery", "green breakthrough",
    "green advancement", "green progress", "green improvement", "green upgrade",
    "green enhancement", "green optimization", "green efficiency", "green productivity",
    "green performance", "green result", "green outcome", "green impact", "green effect",
    "green benefit", "green advantage", "green value", "green worth", "green merit",
    "green quality", "green feature", "green attribute", "green characteristic",
    "green property", "green aspect", "green element", "green component", "green ingredient",
    "green material", "green substance", "green compound", "green chemical", "green formula",
    "green composition", "green content", "green makeup", "green constitution", "green structure",
    "green construction", "green formation", "green arrangement", "green organization",
    "green system", "green process", "green procedure", "green operation", "green function",
    "green activity", "green task", "green job", "green work", "green duty", "green responsibility",
    "green obligation", "green commitment", "green dedication", "green devotion", "green loyalty",
    "green allegiance", "green fidelity", "green faithfulness", "green constancy", "green steadfastness",
    "green resolution", "green determination", "green persistence", "green perseverance",
    "green tenacity", "green endurance", "green stamina", "green fortitude", "green courage",
    "green bravery", "green valor", "green gallantry", "green heroism", "green intrepidity",
    "green boldness", "green audacity", "green daring", "green fearlessness", "green confidence",
    "green assurance", "green certainty", "green conviction", "green belief", "green faith",
    "green trust", "green reliance", "green dependence", "green credence", "green credit",
    "green credibility", "green reliability", "green dependability", "green trustworthiness",
    "green honesty", "green integrity", "green probity", "green rectitude", "green righteousness",
    "green virtue", "green goodness", "green excellence", "green merit", "green worth",
    "green value", "green importance", "green significance", "green consequence", "green moment",
    "green weight", "green gravity", "green seriousness", "green earnestness", "green sincerity",
    "green genuineness", "green authenticity", "green realness", "green trueness", "green veracity",
    "green verity", "green actuality", "green factuality", "green objectivity", "green impartiality",
    "green fairness", "green justice", "green equity", "green equality", "green parity",
    "green balance", "green equilibrium", "green harmony", "green concord", "green agreement",
    "green accord", "green consensus", "green unanimity", "green unity", "green solidarity",
    "green cohesion", "green coherence", "green consistency", "green constancy", "green regularity",
    "green uniformity", "green homogeneity", "green sameness", "green similarity", "green likeness",
    "green resemblance", "green affinity", "green kinship", "green relationship", "green connection",
    "green association", "green correlation", "green correspondence", "green parallel", "green analogy",
    "green comparison", "green contrast", "green difference", "green distinction", "green divergence",
    "green deviation", "green variation", "green variety", "green diversity", "green heterogeneity",
    "green multiplicity", "green plurality", "green multitude", "green abundance", "green plenty",
    "green profusion", "green copiousness", "green richness", "green wealth", "green affluence",
    "green opulence", "green luxury", "green sumptuousness", "green lavishness", "green extravagance",
    "green prodigality", "green wastefulness", "green squandering", "green dissipation", "green depletion",
    "green exhaustion", "green consumption", "green using up", "green spending", "green expending",
    "green utilizing", "green employing", "green applying", "green exercising", "green practicing",
    "green implementing", "green executing", "green performing", "green accomplishing", "green achieving",
    "green attaining", "green reaching", "green gaining", "green acquiring", "green obtaining",
    "green procuring", "green securing", "green ensuring", "green guaranteeing", "green warranting",
    "green certifying", "green confirming", "green affirming", "green asserting", "green declaring",
    "green stating", "green expressing", "green articulating", "green formulating", "green phrasing",
    "green wording", "green verbalizing", "green voicing", "green uttering", "green pronouncing",
    "green enunciating", "green saying", "green telling", "green speaking", "green talking",
    "green communicating", "green conveying", "green transmitting", "green imparting", "green sharing",
    "green divulging", "green revealing", "green disclosing", "green exposing", "green uncovering",
    "green discovering", "green finding", "green locating", "green spotting", "green detecting",
    "green identifying", "green recognizing", "green distinguishing", "green discerning", "green perceiving",
    "green noticing", "green observing", "green seeing", "green viewing", "green watching",
    "green looking at", "green examining", "green inspecting", "green scrutinizing", "green studying",
    "green analyzing", "green investigating", "green researching", "green exploring", "green probing",
    "green delving into", "green inquiring into", "green questioning", "green interrogating", "green quizzing",
    "green interviewing", "green surveying", "green polling", "green canvassing", "green sampling",
    "green testing", "green trying", "green attempting", "green endeavoring", "green striving",
    "green struggling", "green laboring", "green working", "green toiling", "green slaving",
    "green sweating", "green exerting oneself", "green applying oneself", "green dedicating oneself",
    "green devoting oneself", "green committing oneself", "green pledging oneself", "green promising",
    "green vowing", "green swearing", "green affirming", "green asserting", "green declaring",
    "green stating", "green expressing", "green articulating", "green formulating", "green phrasing",
    "green wording", "green verbalizing", "green voicing", "green uttering", "green pronouncing",
    "green enunciating", "green saying", "green telling", "green speaking", "green talking",
    "green communicating", "green conveying", "green transmitting", "green imparting", "green sharing"
]


def analyze_vague_term(cur, virt_entity_id: int, text: str, phrase: str) -> Optional[VagueTermModel]:
    """
    Analyze a specific vague term in text.

    Args:
        cur: Database cursor
        virt_entity_id: ID of the virtual entity
        text: Text to analyze
        phrase: Vague term to analyze

    Returns:
        VagueTermModel if analysis is successful, None otherwise
    """
    # Get the virtual entity
    entity = get_virtual_entity_by_id(virt_entity_id)
    if not entity:
        logger.error(f"Virtual entity with ID {virt_entity_id} not found")
        return None

    # Shorten text if needed
    shortened_text = text[:SHORTENED_TEXT_LENGTH_FOR_LLMS]

    # Create instructions for the LLM
    instructions = f"""
        <instructions>
        You are only interested in the {entity.entity_type} {entity.name} and the term '{phrase}' if the text is not about the {entity.entity_type} {entity.name} {' or it''s subsidiaries ' if entity.entity_type == "company" else ""} but instead just about the term '{phrase}' YOU MUST score 0.

        If the claim of '{phrase}' is not in the supplied text YOU MUST score 0.

        In the context of greenwashing please identify if the following text contains could be classed as greenwashing by vagueness in regards to the phrase '{phrase}'. YOU MUST ALWAYS provide citations.

        If the phrase is used in a way that is not related to ESG, please score 0.

        If the phrase is used in a way that is specific and measurable, please score 0.

        If the phrase is used in a way that is vague and unmeasurable, please score higher.

        If the phrase is used in a way that is misleading, please score higher.

        If the phrase is used in a way that is deceptive, please score higher.

        If the phrase is used in a way that is false, please score higher.

        If the phrase is used in a way that is exaggerated, please score higher.

        If the phrase is used in a way that is unsubstantiated, please score higher.

        If the phrase is used in a way that is unsupported, please score higher.

        If the phrase is used in a way that is unverified, please score higher.

        If the phrase is used in a way that is unverifiable, please score higher.

        If the phrase is used in a way that is not transparent, please score higher.

        If the phrase is used in a way that is not clear, please score higher.

        If the phrase is used in a way that is not specific, please score higher.

        If the phrase is used in a way that is not measurable, please score higher.

        If the phrase is used in a way that is not quantifiable, please score higher.

        If the phrase is used in a way that is not verifiable, please score higher.

        If the phrase is used in a way that is not transparent, please score higher.

        If the phrase is used in a way that is not clear, please score higher.

        If the phrase is used in a way that is not specific, please score higher.

        If the phrase is used in a way that is not measurable, please score higher.

        If the phrase is used in a way that is not quantifiable, please score higher.

        If the phrase is used in a way that is not verifiable, please score higher.
        </instructions>
    """

    # Load the prompt template
    prompt = load_prompt("vague_terms_analysis", True, text=shortened_text, instructions=instructions)

    # Call the LLM
    from eko.llm.main import LLMOptions
    options = LLMOptions(
        metadata={"label": "vague-term-analysis"},
        eval=lambda x: x is not None and (
            x.score <= MIN_LLM_SCORE_FOR_VAGUE_TERMS or valid_citations(cur, x.analysis)
        )
    )
    response = call_llms(
        [LLMModel.GPT_4O],
        prompt,
        max_tokens=4000,
        response_model=VagueTermModel,
        options=options
    )

    if not response:
        logger.info(f"No response from LLM for vague term analysis of '{phrase}'")
        return None

    # Set the phrase and virtual entity ID
    response.phrase = phrase
    response.virt_entity_id = virt_entity_id

    logger.info(f"Vague term analysis for '{phrase}': score={response.score}")

    return response


def analyze_document_for_vague_terms(cur, virt_entity_id: int, text: str) -> Optional[VagueTermListModel]:
    """
    Analyze a document for vague terms.

    Args:
        cur: Database cursor
        virt_entity_id: ID of the virtual entity
        text: Text to analyze

    Returns:
        VagueTermListModel if analysis is successful, None otherwise
    """
    # Get the virtual entity
    entity = get_virtual_entity_by_id(virt_entity_id)
    if not entity:
        logger.error(f"Virtual entity with ID {virt_entity_id} not found")
        return None

    # Shorten text if needed
    shortened_text = text[:SHORTENED_TEXT_LENGTH_FOR_LLMS]

    # Create instructions for the LLM
    instructions = f"""
        <instructions>
        You are only interested in the {entity.entity_type} {entity.name} and any vague and/or unsubstantiated ESG terms used by them in the supplied
        text.

        In the context of greenwashing please identify if the following text contains terms which could be classed
        as greenwashing by vagueness. YOU MUST ALWAYS provide citations.

        If a phrase requires certification, and {entity.name} has certification for the term, please IGNORE it.

        If {entity.name} does not have valid certification but uses the phrase, please INCLUDE it.

        If the phrase is used in a way that is not related to ESG, please IGNORE it.

        If the phrase is used in a way that is specific and measurable, please IGNORE it.

        If the phrase is used in a way that is vague and unmeasurable, please INCLUDE it.

        If the phrase is used in a way that is misleading, please INCLUDE it.

        If the phrase is used in a way that is deceptive, please INCLUDE it.

        If the phrase is used in a way that is false, please INCLUDE it.

        If the phrase is used in a way that is exaggerated, please INCLUDE it.

        If the phrase is used in a way that is unsubstantiated, please INCLUDE it.

        If the phrase is used in a way that is unsupported, please INCLUDE it.

        If the phrase is used in a way that is unverified, please INCLUDE it.

        If the phrase is used in a way that is unverifiable, please INCLUDE it.

        If the phrase is used in a way that is not transparent, please INCLUDE it.

        If the phrase is used in a way that is not clear, please INCLUDE it.

        If the phrase is used in a way that is not specific, please INCLUDE it.

        If the phrase is used in a way that is not measurable, please INCLUDE it.

        If the phrase is used in a way that is not quantifiable, please INCLUDE it.

        If the phrase is used in a way that is not verifiable, please INCLUDE it.
        </instructions>
    """

    # Load the prompt template
    prompt = load_prompt("vague_terms_analysis", True, text=shortened_text, instructions=instructions)

    # Call the LLM
    options2 = LLMOptions(
        metadata={"label": "vague-term-analysis-doc"},
        eval=lambda y: y is not None and all([
            x.score <= MIN_LLM_SCORE_FOR_VAGUE_TERMS or valid_citations(cur, x.analysis)
            for x in y.analyses
        ])
    )
    response = call_llms(
        [LLMModel.GPT_4O],
        prompt,
        max_tokens=4000,
        response_model=VagueTermListModel,
        options=options2
    )

    if not response:
        logger.info("No response from LLM for document vague term analysis")
        return None

    # Set the virtual entity ID for each analysis
    for analysis in response.analyses:
        analysis.virt_entity_id = virt_entity_id

    logger.info(f"Document vague term analysis: found {len(response.analyses)} vague terms")

    return response


def create_vague_term_summary(analyses: List[VagueTermModel], doc_chunk_ids: List[int], virt_entity_id: int, run_id: int) -> VagueTermSummaryModel:
    """
    Create a summary of vague term analyses.

    Args:
        analyses: List of vague term analyses
        doc_chunk_ids: List of document chunk IDs
        virt_entity_id: ID of the virtual entity
        run_id: ID of the analysis run

    Returns:
        Summary model
    """
    # Get the virtual entity
    entity = get_virtual_entity_by_id(virt_entity_id)
    if not entity:
        logger.error(f"Virtual entity with ID {virt_entity_id} not found")
        return None

    # Create instructions for the LLM
    instructions = f"""
        <instructions>
        You are analyzing vague terms used by {entity.name} in their ESG communications.

        Please create a summary of the vague terms analysis, focusing on the most significant instances of vague language.

        Your summary should include:
        1. An overall assessment of how {entity.name} uses vague language in their ESG communications
        2. The most significant instances of vague language
        3. Patterns in the use of vague language
        4. Recommendations for how {entity.name} could be more specific and transparent

        Your summary should be comprehensive but concise, focusing on the most important findings.
        </instructions>
    """

    # Prepare the analyses text
    analyses_text = "\n\n".join([
        f"Term: {analysis.phrase}\nScore: {analysis.score}\nExplanation: {analysis.explanation}\nSummary: {analysis.summary}"
        for analysis in analyses
    ])

    # Load the prompt template
    prompt = load_prompt("vague_terms_summary", True, text=analyses_text, instructions=instructions)

    # Call the LLM
    options3 = LLMOptions(
        metadata={"label": "vague-term-summary"}
    )
    response = call_llms(
        [LLMModel.GPT_4O],
        prompt,
        max_tokens=4000,
        response_model=VagueTermSummaryModel,
        options=options3
    )

    if not response:
        logger.info("No response from LLM for vague term summary")
        # Create a default summary
        response = VagueTermSummaryModel(
            virt_entity_id=virt_entity_id,
            run_id=run_id,
            score=50,
            explanation="No summary could be generated.",
            analysis="No analysis could be generated.",
            summary="No summary could be generated.",
            doc_chunk_ids=doc_chunk_ids
        )
    else:
        # Set the virtual entity ID and run ID
        response.virt_entity_id = virt_entity_id
        response.run_id = run_id
        response.doc_chunk_ids = doc_chunk_ids

    logger.info(f"Vague term summary: score={response.score}")

    return response


def analyze_vague_terms_command(virt_entity_id: int, run_id: int) -> Dict[str, Any]:
    """
    Analyze vague terms for a virtual entity.

    Args:
        virt_entity_id: ID of the virtual entity
        run_id: ID of the analysis run

    Returns:
        Dictionary with analysis results
    """
    logger.info(f"Starting vague terms analysis for virtual entity {virt_entity_id} and run {run_id}")

    # Get the virtual entity
    entity = get_virtual_entity_by_id(virt_entity_id)
    if not entity:
        logger.error(f"Virtual entity with ID {virt_entity_id} not found")
        return {"success": False, "error": f"Virtual entity with ID {virt_entity_id} not found"}

    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            analyses = []
            summary_ids = []

            # Get the list of vague terms to analyze
            phrases = greenwashing_terms_text_list

            # Create a temporary table for terms
            cur.execute("CREATE TEMPORARY TABLE temp_search_terms (term TEXT) ON COMMIT DROP;")
            # Insert all phrases into the temporary table
            cur.executemany("INSERT INTO temp_search_terms (term) VALUES (%s);", [(phrase,) for phrase in phrases])

            # Find document chunks containing the terms
            cur.execute("""
                SELECT
                    dc.id,
                    dc.text,
                    ts.term,
                    dc.rank
                FROM
                    kg_doc_chunks dc
                JOIN
                    temp_search_terms ts ON dc.text ILIKE '%' || ts.term || '%'
                JOIN
                    kg_documents d ON dc.doc_id = d.id
                JOIN
                    kg_doc_entity_rels der ON d.id = der.doc_id
                JOIN
                    kg_virt_entity_base_entities vebe ON der.entity_id = vebe.base_entity_id
                WHERE
                    vebe.virt_entity_id = %s
                    AND d.status != 'deleted'
                ORDER BY
                    dc.rank DESC
                LIMIT 1000
            """, (virt_entity_id,))

            # Group document chunks by term
            term_chunks = {}
            for row in cur.fetchall():
                chunk_id, text, term, rank = row
                if term not in term_chunks:
                    term_chunks[term] = []
                term_chunks[term].append((chunk_id, text, rank))

            # Analyze each term
            for phrase, chunks in term_chunks.items():
                if not chunks:
                    continue

                # Sort chunks by rank and take the top ones
                chunks.sort(key=lambda x: x[2], reverse=True)
                top_chunks = chunks[:MAX_RANK_OVERALL_FOR_VAGUE_TERMS]

                # Combine text from all chunks
                combined_text = "\n\n".join([chunk[1] for chunk in top_chunks])
                combined_text = remove_duplicate_lines(combined_text)

                # Get chunk IDs
                ids = [chunk[0] for chunk in top_chunks]

                # Calculate combined rank
                combined_rank = sum([chunk[2] for chunk in top_chunks]) / len(top_chunks) if top_chunks else 0

                # Analyze the term
                analysis = analyze_vague_term(cur, virt_entity_id, combined_text, phrase)
                if not analysis:
                    logger.info(f"Analysis not found for {entity.name} and {phrase}")
                    continue

                if phrase.lower() not in analysis.phrase.lower():
                    logger.info(f"Phrase mismatch for {analysis.phrase.lower()} and {phrase.lower()}")
                    continue

                logger.info(f"Terms found {analysis.score} {analysis.analysis}")

                if analysis.score > MIN_LLM_SCORE_FOR_VAGUE_TERMS:
                    logger.info(f"Vague terms found for {entity.name} and {phrase}")

                    # Extract and validate citations
                    analysis.citations = extract_valid_citations(cur, analysis.analysis)
                    if len(analysis.citations) == 0:
                        logger.info(f"No citations found for {entity.name} and {phrase}")
                        continue

                    # Verify the analysis mentions the entity
                    analysis = verify_analysis_entity(analysis, entity)
                    if not analysis:
                        logger.info(f"Analysis not verified for {entity.name} and {phrase}")
                        continue

                    # Set additional fields
                    analysis.run_id = run_id
                    analysis.rank = combined_rank
                    analysis.doc_chunk_ids = ids

                    # Save to database
                    saved_analysis = VagueTermData.create(conn, analysis)

                    analyses.append(saved_analysis)
                    summary_ids.extend(ids)

            # Create summary if we have analyses
            if analyses:
                # Remove duplicates from summary IDs
                summary_ids = list(set(summary_ids))

                # Create summary
                summary = create_vague_term_summary(analyses, summary_ids, virt_entity_id, run_id)

                # Save summary to database
                saved_summary = VagueTermData.create(conn, summary)

                logger.info(f"Created vague term summary with ID {saved_summary.id}")

            return {
                "success": True,
                "entity": entity.name,
                "virt_entity_id": virt_entity_id,
                "run_id": run_id,
                "analyses_count": len(analyses),
                "summary_ids_count": len(summary_ids)
            }
