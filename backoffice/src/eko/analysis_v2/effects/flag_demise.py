from typing import Optional

import json

from loguru import logger

from eko import console
from eko.llm import LLMModel, PROMPT_NOT_CONVERSATIONAL
from eko.llm.main import call_llms_typed, call_llms_str, call_llms_json
from eko.models.vector.demise.demise_model import DEMISEModel

def create_effect_flag_prompt(flag_text, flag_summary, flag_title, entity_name, effect_type):
    """Create a prompt for extracting DEMISE model from an effect flag.

    Args:
        flag_text: The detailed analysis text of the flag
        flag_summary: A brief summary of the flag
        flag_title: The title of the flag
        entity_name: The name of the entity the flag is about
        effect_type: The type of effect (RED or GREEN)
        prompt_desc: Description of the specific DEMISE component to extract

    Returns:
        List of message dictionaries for the LLM
    """
    return [
        {
            "role": "user",
            "content": f"""{PROMPT_NOT_CONVERSATIONAL}

<demise-keys>
<taxonomy>
The DEMISE taxonomy uses dot notation (category.subcategory.specific_type) with these categories allowed and no-others:
{",".join(DEMISEModel.get_feature_names())}
</taxonomy>
</demise-keys>
<impact-scale>
### Harm Scale (-0.0 to -1.0)
-0.0: No impact (all categories)
-0.1: Minor discomfort (few animals/humans), slight environmental disturbance
-0.3: Moderate suffering (many animals/humans), notable environmental damage
-0.5: Animal death, severe emotional suffering/discrimination, major environmental damage
-0.7: Large-scale animal deaths, moderate physical harm to many humans, ecosystem destruction
-0.9: Multiple species extinction, significant loss of human life, irreversible ecosystem damage
-1.0: Mass extinction, global human catastrophe, global environmental collapse

### Benefit Scale (0.0 to 1.0)
0.0: No impact (all categories)
0.1: Minor benefit (few animals/humans), slight environmental improvement
0.3: Moderate improvement for many, notable environmental restoration
0.5: Major welfare improvement, quality of life enhancement for large groups, ecosystem restoration
0.7: Prevention of mass suffering/death, substantial health improvements, ecosystem regeneration
0.9: Endangered species preservation, life-changing benefits at scale, major global environmental shifts
1.0: Global improvement in biodiversity/welfare, unprecedented human well-being, global ecological balance
</impact-scale>
<instructions>

Analyse the statement within the <statement> tags, using the details in <context> to provide context only.
The result should be JSON, and be a DEMISE map of keys and values (possible keys listed above) values can only be between 0.0 and 1.0, and a metadata object.

The following text describes an {"adverse/harmful" if effect_type.lower() == "red" else "beneficial/positive"} effect flag for the entity {entity_name}.

<flag_title>{flag_title}</flag_title>
<flag_summary>{flag_summary}</flag_summary>
<flag_analysis>
{flag_text}
</flag_analysis>

<instructions>
For the supplied effect flag, please return a DEMISE model.

This is an effect flag that represents a {"negative/harmful" if effect_type.lower() == "red" else "positive/beneficial"} impact by the entity.
The flag is not a statement from a report but an analysis of the entity's actions and their impacts.

If you would set a value to 0.0 just skip it, because 0.0 is the default value.
If a field is not applicable to the flag skip it, don't set it to 0.0.

Sometimes errors occur because when the key should be a.b.c but you try to reference a.b - this will fail you cannot use subsets of the keys from the taxonomy, use them exactly as listed.

The flag to analyse is summarized as:

<flag>{flag_title}: {flag_summary}</flag>""",
        }
    ]


def extract_effect_flag_demise(flag_text, flag_summary, flag_title, entity_name, effect_type) -> Optional[DEMISEModel]:
    """Extract DEMISE model from an effect flag.

    This function is specifically designed for effect flags, which are different from statements
    in reports. Effect flags represent analyzed impacts of entity actions.

    Args:
        flag_text: The detailed analysis text of the flag
        flag_summary: A brief summary of the flag
        flag_title: The title of the flag
        entity_name: The name of the entity the flag is about
        effect_type: The type of effect (RED or GREEN)

    Returns:
        DEMISEModel: The extracted DEMISE model
    """
    demise = DEMISEModel.model_construct()  # Using model_construct instead of deprecated construct
    prompt = create_effect_flag_prompt(flag_text, flag_summary, flag_title, entity_name, effect_type)

    try:
        for attempt in range(3):  # Fewer attempts than for statements since flags are more structured
            from eko.llm.main import LLMOptions
            demise_json = call_llms_json(
                [LLMModel.GEMINI_FLASH_LITE_FINETUNED],
                prompt,
                4000,
                options=(
                    LLMOptions(
                        cache_key=f"effect_flag_demise:v2:{entity_name}:{flag_title}{(':' + str(attempt)) if attempt > 0 else ''}",
                        escalate_to=[LLMModel.GEMINI_FLASH_FINETUNED],
                        accept_final_eval_fail=True,
                    )
                ),
            )

            try:
                return DEMISEModel.from_sparse_kv(demise_json)
            except Exception as validation_error:
                # logger.error(type(validation_error))
                logger.warning(f"Error validating DEMISE model for effect flag '{flag_title}': {str(validation_error)}")
                prompt+=[{"role": "user", "content": f"Please try again, the response was not valid because of {str(validation_error)}, sometimes errors occur because when the key should be a.b.c but you reference a.b - you cannot use subsets of the keys from the taxonomy."}]
                # console.print_exception(show_locals=False)

        return demise
    except Exception as e:
        logger.exception(e)
        logger.error(f"Error extracting DEMISE model for effect flag '{flag_title}': {str(e)}")
        return None
