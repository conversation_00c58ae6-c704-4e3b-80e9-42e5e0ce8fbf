"""
Pydantic models for effect analysis.

This module provides Pydantic models for representing effect flags and responses.
"""

from pydantic import BaseModel, Field
from pydantic.json_schema import SkipJsonSchema
from typing import Optional

from eko.models.vector.derived.categories import EffectCategory


class EffectFlagResponse(BaseModel):
    """
    Response model for the LLM when creating an effect flag
    """
    title: str= Field(..., description="Title or short description of this effect flag")
    short_title: str = Field(..., description="A concise 2-3 word title suitable for use as a label in UI elements like badges")
    summary: str= Field(..., description="One or two sentence summary of the analysis")
    impact_description: str= Field(..., description="One or two sentence summary of the event purely in terms of it's impact.")
    reason: str= Field(..., description="Reason or justification for this flag")
    describes_company_causing_harm: bool= Field(..., description="Does this flag describe the company causing harm?")
    describes_company_doing_good: bool= Field(..., description="Does this flag describe the company doing good?")
    analysis: SkipJsonSchema[Optional[str]] = None
    category: EffectCategory = Field(..., description="The primary category this flag relates to (Ecological, Social, or Governance)")
    impact: int = Field(..., ge=0, le=100, description="Impact rating (0-100) please see the provided impact scale.")
    authentic: int = Field(..., ge=0, le=100,
                       description="Authenticity rating (0-100) how genuine was the action, low ratings means superficial/marketing/accidental/unintentional etc. high ratings means authentic, deliberate and intentional.")
    contribution: int = Field(..., ge=0, le=100,
                          description="Contribution rating (0-100) how much of the impact was directly due to the entity? If it was not caused by the entity, then this should be 0. If the entity was one of many contributors, then this should be low. If the entity was the sole contributor, then this should be high.")
    confidence: int = Field(..., ge=0, le=100, description="Confidence in this flag (0-100)")
    credibility: int = Field(0, ge=0, le=100, description="Credibility score (0-100) based on the source report's credibility and domain credibility")
    start_year: int = Field(..., ge=2000, le=2100,
                        description="Start year of the action/event, closest or most relevant to the event")
    end_year: int = Field(..., ge=2000, le=2100,
                      description="End year of the action/event, if it spans multiple years, otherwise the same as start_year")

    def cited_text(self) -> str:
        """Returns text containing citations for verification."""
        return self.analysis or ""

    def clean_dump(self) -> dict:
        """Returns a clean dictionary representation of the analysis."""
        return {
            "title": self.title,
            "reason": self.reason,
            "category": self.category.value,
            "authentic": self.authentic,
            "contribution": self.contribution,
            "confidence": self.confidence,
            "start_year": self.start_year,
            "end_year": self.end_year if self.end_year is not None else self.start_year
        }

    def no_confidence(self) -> bool:
        """Returns True if there is no confidence in the analysis."""
        return self.confidence == 0
