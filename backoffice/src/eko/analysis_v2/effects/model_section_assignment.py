"""
Module for assigning model sections to effect flags.
"""
from functools import lru_cache

from typing import Dict, List

from loguru import logger
from pydantic import BaseModel, Field

from eko.cache.pg_cache import MultiLevelCache
from eko.db import get_bo_conn
from eko.llm import LLMModel
from eko.llm.main import call_llms_typed, LLMOptions
from eko.models.vector.derived.effect import EffectFlagModel
from eko.typing import not_none

cache=MultiLevelCache("model_section_assignment")

class ModelSectionAssignmentResponse(BaseModel):
    """Response model for model section assignment."""
    assignments: Dict[str, str] = Field(
        default_factory=dict,
        description="Mapping of model name to section ID"
    )


@lru_cache(maxsize=1)
def get_model_sections() -> Dict[str, List[Dict[str, str]]]:
    """
    Fetch all model sections from the database.

    Args:
        conn: Database connection

    Returns:
        Dictionary mapping model names to lists of section dictionaries
    """
    models = ["eko", "sdg", "doughnut", "plant_based_treaty"]
    model_sections={}
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT model, section, title, description, level, icon
                FROM kg_model_sections
                WHERE status != 'deleted' and model = ANY(%s)
                ORDER BY model, section
            """, (models,))

            for row in cur.fetchall():
                model, section, title, description, level, icon = row

                if model not in model_sections:
                    model_sections[model] = []

                model_sections[model].append({
                    "section": section,
                    "title": title or "",
                    "description": description or "",
                    "level": level or "",
                    "icon": icon or ""
                })


    return model_sections


def assign_model_sections(flag: EffectFlagModel, model_sections: Dict[str, List[Dict[str, str]]]) -> EffectFlagModel:
    """
    Assign one section per model to the effect flag using LLM.

    Args:
        flag: The effect flag to assign sections to
        model_sections: Dictionary mapping model names to lists of section dictionaries

    Returns:
        Dictionary mapping model names to section IDs
    """
    # Prepare the flag information for the LLM
    flag_info = {
        "title": flag.title,
        "summary": flag.summary or "",
        "analysis": flag.analysis or "",
        "reason": flag.reason,
        "category": flag.category.name,
        "effect_type": flag.effect_type.name,
        "domains": sorted(flag.domains),
    }

    # Create a prompt for the LLM
    system_prompt = """
    You are an expert in ESG (Environmental, Social, and Governance) analysis and ethical frameworks.
    Your task is to assign the most appropriate model section for each ethical model to a given effect flag.

    For each model, you must select EXACTLY ONE section that best represents the flag's content and impact.

    The flag represents an action or behavior that has been identified as having either a positive (green)
    or negative (red) impact on ESG factors.
    """

    user_prompt = f"""
    # Effect Flag Information
    - Title: {flag_info['title']}
    - Summary: {flag_info['summary']}
    - Analysis: {flag_info['analysis']}
    - Reason: {flag_info['reason']}
    - Category: {flag_info['category']}
    - Effect Type: {flag_info['effect_type']}
    - Domains: {', '.join(sorted(flag_info['domains']))}

    # Available Model Sections

    """

    models=set()
    # Add each model and its sections to the prompt in sorted order
    for model in sorted(model_sections.keys()):
        models.add(model)
        user_prompt += f"\n## The `{model}` model\n"
        for section in sorted(model_sections[model], key=lambda s: s["title"]):
            section_id = section["section"]
            title = section["title"] or section_id
            description = section["description"] or "No description available"
            user_prompt += f"- Section ID: {section_id}\n"
            user_prompt += f"  Title: {title}\n"
            user_prompt += f"  Description: {description}\n\n"
    if len(models) == 0:
        raise ValueError(str(f"No models found for {model_sections}"))

    user_prompt += """
    # Assignment Task

    For each model listed above, select  a MAXIMUM OF ONE section that best represents the flag's content and impact.

    Return your answer as a JSON object with the following structure:
    {
        "assignments": {
            "model_name_1": "section_id_1",
            "model_name_2": "section_id_2",
            ...
        }
    }

    If no section for a model applies to the flag, do not include it in the JSON object.

    Provide a brief explanation for each assignment, but only include the JSON object in your final answer.
    """

    def validate(x: ModelSectionAssignmentResponse):
        for model, sections in model_sections.items():
            if model in x.assignments:
                section_id = x.assignments[model]
                if not any(s["section"] == section_id for s in sections):
                    logger.warning(f"Invalid section '{section_id}' assigned to model '{model}', please fix.")
                    return f"Invalid section '{section_id}' assigned to model '{model}'"
            else:
                logger.warning(f"No section assigned to model '{model}', please fix.")
                if model == "eko":
                    return f"No section assigned to model '{model}', please fix."
                # return f"No section assigned to model '{model}', please fix."
        for item in x.assignments.items():
            if item[0] not in model_sections.keys():
                logger.warning(f"Invalid model '{item[0]}', valid models are {models} please fix.")
                return f"Invalid model '{item[0]}', valid models are {models} please fix."
        return True



    # Call the LLM to assign sections
    # Create messages directly without using prompt function
    messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}]
    response = call_llms_typed(
        [LLMModel.NORMAL_ALL_ROUNDER],
        messages,
        8000,
        response_model=ModelSectionAssignmentResponse,

        options=LLMOptions(eval=lambda x: validate( not_none(x)), thinking=True,),
    )

    # Validate the response - ensure one section per model
    valid_response = {}
    for model, sections in model_sections.items():
        if model in response.assignments:
            section_id = response.assignments[model]
            # Check if the assigned section exists for this model
            if any(s["section"] == section_id for s in sections):
                valid_response[model] = section_id
            else:
                logger.warning(f"Invalid section '{section_id}' assigned to model '{model}', skipping")
    flag.model_sections= valid_response
    return flag
