"""
Models for vague term analysis.
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from eko.models.citation_model import Citation


class VagueTermModel(BaseModel):
    """
    Model for vague term analysis.
    """
    id: Optional[int] = Field(None, description="Database ID of this vague term analysis")
    virt_entity_id: int = Field(..., description="ID of the virtual entity this analysis is for")
    run_id: Optional[int] = Field(None, description="ID of the analysis run")
    phrase: str = Field(..., description="The vague term or phrase being analyzed")
    score: int = Field(..., ge=0, le=100, description="Score from 0-100 indicating how vague the term is (higher = more vague)")
    explanation: str = Field(..., description="Explanation of why this term is considered vague")
    analysis: str = Field(..., description="Detailed analysis of the vague term usage")
    summary: str = Field(..., description="Summary of the analysis")
    doc_chunk_ids: List[int] = Field(default_factory=list, description="IDs of document chunks containing this term")
    citations: List[Citation] = Field(default_factory=list, description="Citations supporting the analysis")
    rank: Optional[int] = Field(None, description="Rank of this term compared to others")
    created_at: Optional[datetime] = Field(None, description="When this analysis was created")
    
    def valid_score(self) -> bool:
        """Check if the score indicates a valid vague term."""
        return self.score > 50
    
    def cited_text(self) -> str:
        """Get the text with citations."""
        return self.analysis
    
    def no_confidence(self) -> bool:
        """Check if there is no confidence in this analysis."""
        return self.score == 0


class VagueTermSummaryModel(VagueTermModel):
    """
    Model for a summary of vague terms.
    """
    phrase: str = Field("__summary__", description="Special phrase indicating this is a summary")


class VagueTermListModel(BaseModel):
    """
    Model for a list of vague term analyses.
    """
    analyses: List[VagueTermModel] = Field(default_factory=list, description="List of vague term analyses")
