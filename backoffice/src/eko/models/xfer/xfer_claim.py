"""
Model class for transferring claim data from the analytics database to the customer database.
"""
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field

from eko.models.citation_model import Citation


class XferClaimModel(BaseModel):
    """
    A model class for transferring claim data from the analytics database
    to the customer database with all relevant data bundled as JSON.
    """
    id: int = Field(..., description="The database ID of this claim")
    statement_id: int = Field(..., description="The statement ID this claim is based on")
    entity_xid: str = Field(..., description="Short ID of the entity this claim relates to")
    valid_claim: bool = Field(..., description="Whether the claim is valid")
    greenwashing: bool = Field(..., description="Whether this is considered greenwashing")
    verdict: str = Field(..., description="The verdict text")
    summary: str = Field(..., description="Summary of the claim analysis")
    conclusion: str = Field(..., description="Conclusion of the claim analysis")
    confidence: int = Field(..., ge=0, le=100, description="Confidence in this verdict (0-100)")

    # Document information
    text: str = Field(..., description="The text of the claim")
    statement_text: Optional[str] = Field(None, description="The text of the statement the claim is about")
    context: Optional[str] = Field(None, description="The context of the claim")
    claim_doc: str = Field(..., description="The document title containing the claim")
    claim_doc_year: int = Field(..., description="The year of the document containing the claim")

    # Additional data
    counters: Dict[str, Any] = Field(default_factory=dict, description="Counter-evidence for the claim")
    citations: List[Citation] = Field(default_factory=list, description="Citations supporting the analysis")
    llm_greenwashing: bool = Field(False, description="Whether the LLM identified this as greenwashing")
    importance: int = Field(0, description="Importance of the claim (0-100)")

    # Metadata
    company: str = Field(..., description="The company name")
    company_id: int = Field(..., description="The company ID")
    esg_claim: bool = Field(True, description="Whether this is an ESG-related claim")
    created_at: Optional[str] = Field(None, description="When this claim was created")
