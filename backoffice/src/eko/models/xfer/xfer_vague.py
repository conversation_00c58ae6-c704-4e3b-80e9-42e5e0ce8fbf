"""
Model class for transferring vague term analysis data from the analytics database to the customer database.
"""
from typing import List, Optional

from pydantic import BaseModel, Field

from eko.models.citation_model import Citation


class XferVagueModel(BaseModel):
    """
    A model class for transferring vague term analysis data from the analytics database
    to the customer database with all relevant data bundled as JSON.
    """
    id: int = Field(..., description="The database ID of this vague term analysis")
    entity_xid: str = Field(..., description="Short ID of the entity this analysis relates to")
    phrase: str = Field(..., description="The vague term or phrase being analyzed")
    score: int = Field(..., ge=0, le=100, description="Score from 0-100 indicating how vague the term is (higher = more vague)")
    explanation: str = Field(..., description="Explanation of why this term is considered vague")
    analysis: str = Field(..., description="Detailed analysis of the vague term usage")
    summary: str = Field(..., description="Summary of the analysis")
    citations: List[Citation] = Field(default_factory=list, description="Citations supporting the analysis")
    rank: Optional[int] = Field(None, description="Rank of this term compared to others")
    created_at: Optional[str] = Field(None, description="When this analysis was created")
