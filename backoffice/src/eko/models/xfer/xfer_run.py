"""
Model class for transferring run data from the analytics database to the customer database.
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class XferRunModel(BaseModel):
    """
    A model class for transferring run data from the analytics database
    to the customer database with all relevant data bundled as JSON.
    """
    id: int = Field(..., description="The database ID of this run")
    run_type: str = Field(..., description="Type of run (full, inc, hist, hist-inc)")
    scope: str = Field(..., description="Scope of the run (all, entity)")
    target: Optional[str] = Field(None, description="Target of the run (entity short_id if scope is entity)")
    start_year: int = Field(..., description="Start year of the analysis")
    end_year: Optional[int] = Field(None, description="End year of the analysis")
    models: List[str] = Field(default_factory=list, description="Models used in the analysis")
    status: str = Field(..., description="Status of the run (completed, failed, etc.)")
    created_at: datetime = Field(..., description="When the run was created")
    updated_at: Optional[datetime] = Field(None, description="When the run was last updated")
    completed_at: Optional[datetime] = Field(None, description="When the run was completed")
    prev_run_id: Optional[int] = Field(None, description="ID of the previous run")
    version: Optional[str] = Field(None, description="Version of the run")
