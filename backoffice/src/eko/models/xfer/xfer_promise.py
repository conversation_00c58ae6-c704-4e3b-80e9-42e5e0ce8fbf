"""
Model class for transferring promise data from the analytics database to the customer database.
"""
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field

from eko.models.citation_model import Citation


class XferPromiseModel(BaseModel):
    """
    A model class for transferring promise data from the analytics database
    to the customer database with all relevant data bundled as JSON.
    """
    id: int = Field(..., description="The database ID of this promise")
    statement_id: int = Field(..., description="The statement ID this promise is based on")
    entity_xid: str = Field(..., description="Short ID of the entity this promise relates to")
    promise_kept: bool = Field(..., description="Whether the promise was kept")
    greenwashing: bool = Field(..., description="Whether this is considered greenwashing")
    verdict: str = Field(..., description="The verdict text")
    summary: str = Field(..., description="Summary of the promise analysis")
    conclusion: str = Field(..., description="Conclusion of the promise analysis")
    confidence: int = Field(..., ge=0, le=100, description="Confidence in this verdict (0-100)")

    # Document information
    text: str = Field(..., description="The text of the promise")
    statement_text: Optional[str] = Field(None, description="The text of the statement the promise is about")
    context: Optional[str] = Field(None, description="The context of the promise")
    promise_doc: str = Field(..., description="The document title containing the promise")
    promise_doc_year: int = Field(..., description="The year of the document containing the promise")

    # Additional data
    evidence: Dict[str, Any] = Field(default_factory=dict, description="Evidence for the promise")
    citations: List[Citation] = Field(default_factory=list, description="Citations supporting the analysis")
    llm_greenwashing: bool = Field(False, description="Whether the LLM identified this as greenwashing")

    # Metadata
    company: str = Field(..., description="The company name")
    company_id: int = Field(..., description="The company ID")
    esg_promise: bool = Field(True, description="Whether this is an ESG-related promise")
    created_at: Optional[str] = Field(None, description="When this promise was created")
