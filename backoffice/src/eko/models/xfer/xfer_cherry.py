"""
Model class for transferring cherry picking data from the analytics database to the customer database.
"""
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field

from eko.models.citation_model import Citation
from eko.models.statement_metadata import StatementAndMetadata


class XferCherryModel(BaseModel):
    """
    A model class for transferring cherry picking data from the analytics database
    to the customer database with all relevant data bundled as JSON.
    """
    id: int = Field(..., description="The database ID of this cherry picking analysis")
    entity_xid: str = Field(..., description="Short ID of the entity this analysis relates to")
    label: str = Field(..., description="Label for this cherry picking group")

    # Statement data
    negative_statements: List[StatementAndMetadata] = Field(default_factory=list, description="Negative statements included in this analysis")
    positive_statements: List[StatementAndMetadata] = Field(default_factory=list, description="Positive statements included in this analysis")

    # Analysis data
    score: int = Field(..., description="Score for this cherry picking analysis (0-100)")
    explanation: str = Field(..., description="Explanation of the cherry picking analysis")
    analysis: str = Field(..., description="Detailed analysis of the cherry picking")
    reason: Optional[str] = Field(None, description="Reason for the cherry picking")

    # Additional data
    model: str = Field(..., description="Model used for the analysis")
    citations: List[Citation] = Field(default_factory=list, description="Citations supporting the analysis")
    analysis_model: Optional[str] = Field(None, description="Analysis model used")

    # LLM-generated metrics
    severity: int = Field(0, description="Severity score from LLM analysis (0-100)")
    confidence: int = Field(0, description="Confidence score from LLM analysis (0-100)")
    authenticity: int = Field(0, description="Authenticity score from LLM analysis (0-100)")

    # Metadata
    created_at: Optional[str] = Field(None, description="When this analysis was created")
