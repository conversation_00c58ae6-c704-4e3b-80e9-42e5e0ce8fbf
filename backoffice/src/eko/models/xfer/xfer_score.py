"""
Model class for transferring entity score data from the analytics database to the customer database.
"""
from typing import Optional, Dict, Any

from pydantic import BaseModel, Field


class XferScoreModel(BaseModel):
    """
    A model class for transferring entity score data from the analytics database
    to the customer database with all relevant data bundled as JSON.
    """
    entity_xid: str = Field(..., description="Short ID of the entity this score relates to")
    score: int = Field(..., ge=0, le=100, description="Score from 0-100 (higher = better)")
    rating_text: str = Field(..., description="Text rating (e.g., 'Great', 'Good', 'Poor')")
    minor_major_text: str = Field(..., description="Severity text (e.g., 'Very Serious', 'Minor')")
    red_flags_count: int = Field(..., description="Number of red flags")
    green_flags_count: int = Field(..., description="Number of green flags")
    red_flags_score: float = Field(..., description="Total score of red flags")
    green_flags_score: float = Field(..., description="Total score of green flags")
    average_red: float = Field(..., description="Average score of red flags")
    average_green: float = Field(..., description="Average score of green flags")
    median_red: float = Field(..., description="Median score of red flags")
    median_green: float = Field(..., description="Median score of green flags")
    created_at: Optional[str] = Field(None, description="When this score was created (ISO format)")
