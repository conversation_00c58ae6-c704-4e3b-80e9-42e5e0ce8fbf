from pydantic import BaseModel
from typing import Optional, List, Dict, Any


class Citation(BaseModel):  # type: ignore
    """
    Represents a document citation with metadata.
    """
    doc_name: str
    doc_id: int
    doc_page_id: int
    url: str
    public_url: str
    page: int
    score: float
    title: str
    credibility: float
    year: int
    publish_date: Optional[str] = None
    type: str
    authors: Optional[List[Dict[str, Any]]] = None
    extract: Optional[str] = None
    referenced: bool = False  # Flag to indicate if this citation is referenced in the text
