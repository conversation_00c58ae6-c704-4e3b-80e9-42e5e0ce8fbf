from typing import List, Dict, get_origin, get_args, TypeVar, Any, Type, Optional

from pydantic import BaseModel, Field

# Import these only when needed to avoid circular imports
# from eko.llm import LLMModel, PROMPT_NOT_CONVERSATIONAL
# from eko.llm.main import call_llms_str
# from eko.llm.prompts import prompt
from eko.models.vector.to_markdown import generate_model_markdown_docs, recreate_markdown_table, \
    view_markdown_in_browser

T = TypeVar('T', bound=BaseModel)


def to_fixed_size_vector(original_vector: List[float], size: int) -> List[float]:
    """
    Returns a vector of the specified size, regardless of the actual vector size.
    If the original vector is smaller, it's padded with zeros.
    If larger, it's truncated.

    Args:
        original_vector: The original vector to resize
        size: The desired vector size

    Returns:
        A list of floats with exactly the specified size
    """
    if len(original_vector) >= size:
        return original_vector[:size]
    else:
        return original_vector + [0.0] * (size - len(original_vector))


def get_field_order_key(model_instance: BaseModel, key: str) -> tuple:
    """
    Helper to get the sort key for a field based on its added_date or name

    Args:
        model_instance: The model instance to get the field order key for
        key: The field key to get the order key for

    Returns:
        A tuple that can be used for sorting fields
    """
    # Find the field and check for added_date
    field_path = key.split('.')

    # First try to get added_date for the entire compound key (for backward compatibility)
    model_class = model_instance.__class__
    if len(field_path) == 1 and field_path[0] in model_class.model_fields:
        field_info = model_class.model_fields[field_path[0]]
        if field_info.json_schema_extra and "added_date" in field_info.json_schema_extra:
            return (1, field_info.json_schema_extra["added_date"], key)

    # For nested fields, we need to traverse the model class hierarchy, not instances
    current_class = model_class
    field_info = None
    final_field_name = field_path[-1]  # The actual field we care about

    # Navigate through the nested model classes
    for i, field_name in enumerate(field_path[:-1]):  # All except the last part
        if (hasattr(current_class, 'model_fields') and
                field_name in current_class.model_fields):
            field_info = current_class.model_fields[field_name]
            # Get the type of the field
            field_type = field_info.annotation

            # Handle direct BaseModel subclass
            if isinstance(field_type, type) and issubclass(field_type, BaseModel):
                current_class = field_type
            # Handle List[BaseModel]
            elif get_origin(field_type) is list:
                args = get_args(field_type)
                if args and isinstance(args[0], type) and issubclass(args[0], BaseModel):
                    current_class = args[0]
                else:
                    # Not a nested model we can traverse
                    break
            else:
                # Not a nested model we can traverse
                break
        else:
            # Field not found in current model
            break

    # Now check if the final field has added_date
    if (hasattr(current_class, 'model_fields') and
            final_field_name in current_class.model_fields):
        field_info = current_class.model_fields[final_field_name]
        if field_info.json_schema_extra and "added_date" in field_info.json_schema_extra:
            return (1, field_info.json_schema_extra["added_date"], key)

    # Default sorting for fields without added_date
    return (0, "", key)


def flatten_desc(model_instance: BaseModel, parent_key: str = "", separator: str = ".") -> Dict[str, str]:
    """
    Returns a dictionary mapping flattened keys to their field descriptions.

    Args:
        model_instance: The model instance to flatten
        parent_key: The parent key for nested fields
        separator: The separator to use for nested keys

    Returns:
        A dictionary mapping flattened keys to their field descriptions
    """
    fields_desc: Dict[str, str] = {}
    for field_name, model_field in model_instance.model_fields.items():
        if field_name.endswith('_'):
            continue
        # Create a flattened key for this field.
        full_key = f"{parent_key}{separator}{field_name}" if parent_key else field_name
        # Check if the field type is a subclass of BaseModel (i.e. a nested model)
        field_type = model_field.annotation
        if isinstance(field_type, type) and issubclass(field_type, BaseModel):
            # Create an instance (or you can work with the class, if you prefer)
            nested_instance = field_type.model_construct()  # use model_construct to avoid validation
            nested_desc = flatten_desc(nested_instance, parent_key=full_key, separator=separator)
            fields_desc.update(nested_desc)
        # Also handle cases where the field is a list of nested models
        elif get_origin(field_type) is list:
            inner_type = get_args(field_type)[0]
            if isinstance(inner_type, type) and issubclass(inner_type, BaseModel):
                nested_instance = inner_type.model_construct()
                nested_desc = flatten_desc(nested_instance, parent_key=full_key, separator=separator)
                fields_desc.update(nested_desc)
        else:
            # Get the field description or a default message.
            description = model_field.description or "No description provided"
            fields_desc[full_key] = description

            for meta in model_field.metadata:
                if hasattr(meta, "ge"):
                    fields_desc[full_key] += f" (min: {meta.ge})"
                if hasattr(meta, "le"):
                    fields_desc[full_key] += f" (max: {meta.le})"

    return fields_desc


def flatten_json(model_instance: BaseModel, parent_key: str = "", separator: str = ".", weighted=False) -> Dict[str, float]:
    """
    Returns a dictionary mapping flattened keys to their field values.

    Args:
        model_instance: The model instance to flatten
        parent_key: The parent key for nested fields
        separator: The separator to use for nested keys
        weighted: Whether to apply weights to field values

    Returns:
        A dictionary mapping flattened keys to their field values
    """
    field_json: Dict[str, float] = {}
    for field_name, model_field in sorted(model_instance.model_fields.items()):
        if field_name.endswith('_'):
            continue
        # Create a flattened key for this field.
        full_key = f"{parent_key}{separator}{field_name}" if parent_key else field_name
        # Check if the field type is a subclass of BaseModel (i.e. a nested model)
        field_type = model_field.annotation
        if isinstance(field_type, type) and issubclass(field_type, BaseModel):
            # Get the field value
            nested_value = getattr(model_instance, field_name)
            if nested_value is not None:
                # Recursively flatten the nested model
                nested_json = flatten_json(nested_value, parent_key=full_key, separator=separator, weighted=weighted)
                field_json.update(nested_json)
        # Also handle cases where the field is a list of nested models
        elif get_origin(field_type) is list:
            inner_type = get_args(field_type)[0]
            if isinstance(inner_type, type) and issubclass(inner_type, BaseModel):
                list_values = getattr(model_instance, field_name)
                for i, item in enumerate(list_values):
                    nested_json = flatten_json(item, parent_key=f"{full_key}{separator}{i}", separator=separator, weighted=weighted)
                    field_json.update(nested_json)
        # Handle dictionary fields
        elif get_origin(field_type) is dict:
            dict_value = getattr(model_instance, field_name)
            if dict_value:
                for k, v in dict_value.items():
                    if isinstance(v, (int, float)):
                        field_json[f"{full_key}{separator}{k}"] = v
        # Handle scalar float fields
        elif model_field.annotation == float:
            # Get the field value or 0.0
            value = getattr(model_instance, field_name, 0.0)
            if weighted and model_field.json_schema_extra is not None:
                weight = model_field.json_schema_extra.get("weight", 1.0)
                value = value * weight
            field_json[full_key] = value

    return field_json


def get_key_descriptions(cls: Type[BaseModel]) -> Dict[str, str]:
    """
    Returns a dictionary mapping flattened keys to their field descriptions.

    Args:
        cls: The model class to get key descriptions for

    Returns:
        A dictionary mapping flattened keys to their field descriptions
    """
    return flatten_desc(cls.model_construct())


def get_key_descriptions_text(cls: Type[BaseModel]) -> str:
    """
    Returns a string representation of the key descriptions.

    Args:
        cls: The model class to get key descriptions for

    Returns:
        A string representation of the key descriptions
    """
    return "\n".join([item[0] + " -> " + item[1] for item in get_key_descriptions(cls).items()])


def get_feature_names(cls: Type[BaseModel]) -> List[str]:
    """
    Returns a list of feature names.

    Args:
        cls: The model class to get feature names for

    Returns:
        A list of feature names
    """
    return list(get_key_descriptions(cls).keys())


def get_vector_size(cls: Type[BaseModel]) -> int:
    """
    Returns the size of the vector representation of the model.

    Args:
        cls: The model class to get the vector size for

    Returns:
        The size of the vector representation of the model
    """
    # This is a bit of a hack, but we need to avoid circular imports
    # The BaseVectorModel.to_vector method will call this function
    # So we need to get the vector size without calling to_vector
    empty = cls.model_construct()
    kv = flatten_json(empty)
    return len(kv)


def create_kv_from_vector(cls: Type[BaseModel], vector: List[float]) -> dict[str, float]:
    """
    Creates a key-value dictionary from a vector, using the same ordering as to_vector.
    This ensures roundtrip compatibility between to_vector and from_vector.

    Args:
        cls: The model class to create the key-value dictionary for
        vector: List of float values to convert to key-value pairs

    Returns:
        Dict mapping field keys to vector values
    """
    if type(vector) != list:
        raise TypeError("vector must be a list")

    # Create an empty model instance
    empty = cls.model_construct()
    empty_dict = flatten_json(empty)

    # Sort keys using the same ordering logic as to_vector
    ordered_keys = sorted(empty_dict.keys(), key=lambda k: get_field_order_key(empty, k))

    # Map vector values to keys
    for i, key in enumerate(ordered_keys):
        if i < len(vector):
            empty_dict[key] = vector[i]

    return empty_dict


def vector_from_kv(cls: Type[BaseModel], kv: dict[str, float]) -> List[float]:
    """
    Creates a vector from a key-value dictionary, using the same ordering as to_vector.

    Args:
        cls: The model class to create the vector for
        kv: Dict mapping field keys to values

    Returns:
        List of values in the order determined by added_date or alphabetical
    """
    # Create a model instance to use for ordering
    empty = cls.model_construct()

    # Sort using the same logic as to_vector
    return [float(v) for k, v in sorted(kv.items(), key=lambda item: get_field_order_key(empty, item[0]))]


def kv_from_sparse_kv(cls: Type[BaseModel], sparse_kv: dict[str, float]) -> dict[str, float]:
    """
    Creates a full key-value dictionary from a sparse key-value dictionary.

    Args:
        cls: The model class to create the key-value dictionary for
        sparse_kv: Dict mapping field keys to values (sparse)

    Returns:
        Dict mapping field keys to values (full)
    """
    empty = cls.model_construct()
    empty_dict = flatten_json(empty)
    for key in empty_dict.keys():
        if key in sparse_kv:
            empty_dict[key] = sparse_kv[key]
    return empty_dict


def unflatten_kv(cls: Type[BaseModel], flat_dict: dict[str, float]) -> dict:
    """
    Converts a flattened key-value dictionary to a nested dictionary.

    Args:
        cls: The model class to unflatten the dictionary for
        flat_dict: Dict mapping flattened keys to values

    Returns:
        A nested dictionary
    """
    nested = cls.model_construct().model_dump()
    for compound_key, value in flat_dict.items():
        keys = compound_key.split('.')

        # Start at the top-level dictionary
        current = nested
        # For each key except the last, create or traverse into a nested dictionary
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value
    return nested


def from_sparse_kv(cls: Type[BaseModel], sparse_kv: dict[str, float]):
    """
    Creates a model instance from a sparse key-value dictionary.

    Args:
        cls: The model class to create the instance for
        sparse_kv: Dict mapping field keys to values (sparse)

    Returns:
        A model instance
    """
    return cls.model_validate(unflatten_kv(cls, sparse_kv), strict=True)


def from_kv(cls: Type[BaseModel], kv: dict):
    """
    Creates a model instance from a key-value dictionary.

    Args:
        cls: The model class to create the instance for
        kv: Dict mapping field keys to values (can be flattened or nested)

    Returns:
        A model instance
    """
    # Check if the dictionary is flattened (contains keys with dots)
    if any('.' in key for key in kv.keys()):
        # If it's flattened, unflatten it first
        return cls.model_validate(unflatten_kv(cls, kv))
    else:
        # If it's already nested, use it directly
        return cls.model_validate(kv)


def from_vector(cls: Type[BaseModel], vector: List[float]):
    """
    Creates a model instance from a vector.

    Args:
        cls: The model class to create the instance for
        vector: List of float values

    Returns:
        A model instance
    """
    return cls.model_validate(unflatten_kv(cls, create_kv_from_vector(cls, vector)))


def from_np(cls: Type[BaseModel], np_array, minimum: float = 0.0, maximum: float = 1.0):
    """
    Creates a model instance from a numpy array.

    Args:
        cls: The model class to create the instance for
        np_array: Numpy array of float values or string representation of array

    Returns:
        A model instance
    """
    from loguru import logger
    import re
    import numpy as np

    # Handle string representation of arrays
    if isinstance(np_array, str):
        try:
            # Handle np.str_('[values]') format or regular array string like '[0.1, 0.2, 0.3]'
            if '[' in np_array and ']' in np_array:
                # Extract the numbers from inside the brackets
                match = re.search(r'\[(.*)\]', np_array)
                if match:
                    values_str = match.group(1)
                    vector = [float(x.strip()) for x in values_str.split(',') if x.strip()]
                    logger.info(f"Converted string array representation to vector of length {len(vector)}")
                else:
                    logger.warning(f"Could not parse array string: {np_array}")
                    vector = []
            else:
                logger.warning(f"Unrecognized string format for array: {np_array}")
                vector = []
        except Exception as e:
            logger.exception(f"Error parsing string array: {str(e)}")
            vector = []
    else:
        # Convert numpy array to list
        vector = np_array.tolist() if hasattr(np_array, 'tolist') else list(np_array)

    # Ensure all values are floats and above minimum
    processed_vector = []
    for v in vector:
        float_val = float(v)
        float_val = min(maximum, float_val)
        processed_vector.append(float_val if float_val >= minimum else minimum)


    return from_vector(cls, processed_vector)


def sparse_kv_from_kv(cls: Type[BaseModel], kv: dict[str, float]) -> dict[str, float]:
    """
    Creates a sparse key-value dictionary from a full key-value dictionary.

    Args:
        cls: The model class to create the sparse key-value dictionary for
        kv: Dict mapping field keys to values

    Returns:
        Dict mapping field keys to non-zero values
    """
    return {k: v for k, v in kv.items() if v != 0.0}


def sparse_kv_text_from_kv(cls: Type[BaseModel], kv: dict[str, float], min: float = 0.0) -> str:
    """
    Creates a string representation of a sparse key-value dictionary.

    Args:
        cls: The model class to create the string representation for
        kv: Dict mapping field keys to values
        min: Minimum absolute value to include in the string

    Returns:
        A string representation of the sparse key-value dictionary
    """
    return ",".join([f"{k}={v:.2f}" for k, v in kv.items() if abs(v) > min])


def generate_docs(cls: Type[BaseModel], depth=1) -> str:
    """
    Generates markdown documentation for a model class.

    Args:
        cls: The model class to generate documentation for
        depth: The heading depth

    Returns:
        Markdown documentation for the model class
    """
    return generate_model_markdown_docs(cls, depth)


def flatten_json_sparse(model_instance: BaseModel, json_obj: dict, parent_key='', separator='.') -> dict:
    """
    Returns a dictionary mapping flattened keys to their non-zero field values.

    Args:
        model_instance: The model instance to flatten
        json_obj: The JSON object to flatten
        parent_key: The parent key for nested fields
        separator: The separator to use for nested keys

    Returns:
        A dictionary mapping flattened keys to their non-zero field values
    """
    flattened = {}
    # First, flatten the JSON
    for k, v in json_obj.items():
        if isinstance(v, dict):
            nested = flatten_json_sparse(model_instance, v, f"{parent_key}{separator}{k}" if parent_key else k, separator)
            flattened.update(nested)
        else:
            key = f"{parent_key}{separator}{k}" if parent_key else k
            flattened[key] = v

    # Then return only non-zero values
    return {k: v for k, v in flattened.items() if v != 0}


def dict_to_vector(model_instance: BaseModel, flat_model: dict[str, float]) -> List[float]:
    """
    Converts a flattened dictionary to a vector.

    Args:
        model_instance: The model instance to convert the dictionary for
        flat_model: Dict mapping flattened keys to values

    Returns:
        A vector representation of the dictionary
    """
    return [float(v) for k, v in sorted(flat_model.items())]


def to_hash(model_instance: BaseModel) -> str:
    """
    Creates a hash string representation of a model instance.

    Args:
        model_instance: The model instance to create the hash for

    Returns:
        A hash string representation of the model instance
    """
    return ":".join([f"{k}={v}" for k, v in flatten_json_sparse(model_instance, model_instance.model_dump()).items()])


# This function is no longer used and has been removed to avoid circular imports


def generate_matrix_docs(cls: Type[BaseModel], depth=1) -> str:
    """
    Generates markdown documentation for a matrix model class.

    Args:
        cls: The matrix model class to generate documentation for
        depth: The heading depth

    Returns:
        Markdown documentation for the matrix model class
    """
    metadata = cls._markdown_metadata  # Regular class attribute  # pyright: ignore [reportAttributeAccessIssue]
    row_desc = ""
    for i in range(len(metadata.rows)):
        row_desc += (f"- **{metadata.rows[i]}** - {metadata.row_descriptions[i]}\n")
    col_desc = ""
    for i in range(len(metadata.cols)):
        col_desc += (f"- **{metadata.cols[i]}** - {metadata.col_descriptions[i]}\n")
    return f"""
{'#' * depth} {metadata.title}

{cls.__doc__}

{'#' * (depth + 1)} Rows
{row_desc}


{'#' * (depth + 1)} Columns
{col_desc}

{recreate_markdown_table(cls)}
"""


def display_markdown(cls: Type[BaseModel], filename="markdown.html"):
    """
    Open a window with the rendered markdown.

    Args:
        cls: The model class to display documentation for
        filename: The filename to save the HTML to
    """
    metadata = cls._markdown_metadata  # pyright: ignore [reportAttributeAccessIssue]
    # Convert Markdown to HTML
    markdown_content = generate_matrix_docs(cls)
    print(markdown_content)
    title = metadata.title
    view_markdown_in_browser(markdown_content, title, filename=filename)
