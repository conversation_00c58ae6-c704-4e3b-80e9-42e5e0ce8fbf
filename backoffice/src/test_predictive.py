"""
Test script for predictive analytics.
"""

from eko.predictive.regression import predict_future_clusters
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.db import get_bo_conn

def main():
    """Main function to test predictive analytics."""
    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            # Get a valid entity ID
            cursor.execute('SELECT id FROM kg_virtual_entities LIMIT 1')
            entity_id = cursor.fetchone()[0]
            
            # Create a simple entity model
            entity = VirtualEntityExpandedModel(
                id=entity_id,
                name='Test Entity',
                short_id='TEST',
                base_entities=[]
            )
            
            # Run the prediction
            predict_future_clusters(
                entity,
                [2022, 2023, 2024],
                [2026, 2027, 2028],
                1
            )

if __name__ == "__main__":
    main()
