"""
Script to sync model sections from kg_model_sections to xfer_model_sections_v2.
"""
import json
from typing import Dict, List, Optional

from loguru import logger

from eko.db import get_bo_conn, get_cus_conn


def create_xfer_model_sections_v2_table(conn) -> None:
    """
    Create the xfer_model_sections_v2 table if it doesn't exist.
    
    Args:
        conn: Database connection
    """
    with conn.cursor() as cursor:
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS xfer_model_sections_v2 (
                id SERIAL PRIMARY KEY,
                model TEXT NOT NULL,
                section TEXT NOT NULL,
                title TEXT,
                description TEXT,
                level TEXT,
                icon TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
                UNIQUE (model, section)
            )
        """)
    conn.commit()


def get_model_sections(conn) -> List[Dict]:
    """
    Get all model sections from the kg_model_sections table.
    
    Args:
        conn: Database connection
        
    Returns:
        List of model section dictionaries
    """
    model_sections = []
    
    with conn.cursor() as cursor:
        cursor.execute("""
            SELECT model, section, title, description, level, icon, status
            FROM kg_model_sections
            WHERE status != 'deleted'
        """)
        
        for row in cursor.fetchall():
            model, section, title, description, level, icon, status = row
            
            model_sections.append({
                "model": model,
                "section": section,
                "title": title,
                "description": description,
                "level": level,
                "icon": icon,
                "status": status
            })
    
    return model_sections


def sync_model_sections_to_xfer_v2(conn, customer_conn: Optional = None) -> int:
    """
    Sync model sections from kg_model_sections to xfer_model_sections_v2.
    
    Args:
        conn: Analytics database connection
        customer_conn: Optional customer database connection
        
    Returns:
        Number of model sections synced
    """
    # Get model sections from kg_model_sections
    model_sections = get_model_sections(conn)
    
    if not model_sections:
        logger.warning("No model sections found in kg_model_sections")
        return 0
    
    # Create the xfer_model_sections_v2 table if it doesn't exist
    create_xfer_model_sections_v2_table(conn)
    
    # Insert model sections into xfer_model_sections_v2
    with conn.cursor() as cursor:
        for section in model_sections:
            cursor.execute("""
                INSERT INTO xfer_model_sections_v2 (
                    model, section, title, description, level, icon, status
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s
                ) ON CONFLICT (model, section) DO UPDATE SET
                    title = EXCLUDED.title,
                    description = EXCLUDED.description,
                    level = EXCLUDED.level,
                    icon = EXCLUDED.icon,
                    status = EXCLUDED.status,
                    updated_at = now()
            """, (
                section["model"],
                section["section"],
                section["title"],
                section["description"],
                section["level"],
                section["icon"],
                section["status"]
            ))
    
    conn.commit()
    
    # If customer_conn is provided, sync to customer database
    if customer_conn:
        # Check the structure of the xfer_model_sections_v2 table in customer database
        with customer_conn.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'xfer_model_sections_v2'
                )
            """)
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                # Check if the table has a 'data' column (different structure)
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_name = 'xfer_model_sections_v2' AND column_name = 'data'
                    )
                """)
                has_data_column = cursor.fetchone()[0]
                
                if has_data_column:
                    # The table has a different structure with a 'data' column
                    # Clear existing data
                    cursor.execute("TRUNCATE xfer_model_sections_v2")
                    
                    # Insert model sections with the data column
                    for section in model_sections:
                        data = {
                            "title": section["title"],
                            "description": section["description"],
                            "level": section["level"],
                            "icon": section["icon"],
                            "status": section["status"]
                        }
                        
                        cursor.execute("""
                            INSERT INTO xfer_model_sections_v2 (
                                model, section, data
                            ) VALUES (
                                %s, %s, %s
                            ) ON CONFLICT (model, section) DO UPDATE SET
                                data = EXCLUDED.data
                        """, (
                            section["model"],
                            section["section"],
                            json.dumps(data)
                        ))
                else:
                    # The table has the same structure as in the analytics database
                    for section in model_sections:
                        cursor.execute("""
                            INSERT INTO xfer_model_sections_v2 (
                                model, section, title, description, level, icon, status
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s
                            ) ON CONFLICT (model, section) DO UPDATE SET
                                title = EXCLUDED.title,
                                description = EXCLUDED.description,
                                level = EXCLUDED.level,
                                icon = EXCLUDED.icon,
                                status = EXCLUDED.status,
                                updated_at = now()
                        """, (
                            section["model"],
                            section["section"],
                            section["title"],
                            section["description"],
                            section["level"],
                            section["icon"],
                            section["status"]
                        ))
            else:
                # Create the table with the same structure as in the analytics database
                create_xfer_model_sections_v2_table(customer_conn)
                
                # Insert model sections
                for section in model_sections:
                    cursor.execute("""
                        INSERT INTO xfer_model_sections_v2 (
                            model, section, title, description, level, icon, status
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s
                        )
                    """, (
                        section["model"],
                        section["section"],
                        section["title"],
                        section["description"],
                        section["level"],
                        section["icon"],
                        section["status"]
                    ))
        
        customer_conn.commit()
    
    return len(model_sections)


def migrate_model_sections_to_v2() -> None:
    """
    Migrate model sections from kg_model_sections to xfer_model_sections_v2.
    """
    try:
        with get_bo_conn() as conn:
            with get_cus_conn() as customer_conn:
                count = sync_model_sections_to_xfer_v2(conn, customer_conn)
                print(f"Synced {count} model sections to xfer_model_sections_v2")
    except Exception as e:
        print(f"Error migrating model sections to xfer_model_sections_v2: {e}")
        raise


if __name__ == "__main__":
    migrate_model_sections_to_v2()
