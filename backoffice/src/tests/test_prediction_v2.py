"""
Tests for the prediction_v2 package.
"""

import os
import unittest
from unittest.mock import patch, MagicMock

import numpy as np

from eko.prediction_v2.models import (
    DSO_ClusterModel,
    ComponentPredictionModel,
    PredictiveComponentAnalysisModel,
    ComponentType,
    RegressionModelType
)
from eko.prediction_v2.clustering import extract_dso_components
from eko.prediction_v2.regression import extract_component_vector
from eko.models.vector.demise.demise_model import DEMISEModel


class TestPredictionV2Models(unittest.TestCase):
    """Tests for the prediction_v2 models."""

    def test_dso_cluster_model(self):
        """Test the DSO_ClusterModel."""
        model = DSO_ClusterModel(
            run_id=1,
            virtual_entity_id=2,
            year=2023,
            domain_centroid=[0.1, 0.2, 0.3],
            subject_centroid=[0.4, 0.5, 0.6],
            object_centroid=[0.7, 0.8, 0.9],
            statement_ids=[1, 2, 3],
            size=3,
            coherence=0.8
        )
        
        # Test the dso_centroid property
        self.assertEqual(model.dso_centroid, [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9])
        
        # Test model attributes
        self.assertEqual(model.run_id, 1)
        self.assertEqual(model.virtual_entity_id, 2)
        self.assertEqual(model.year, 2023)
        self.assertEqual(model.size, 3)
        self.assertEqual(model.coherence, 0.8)
        self.assertEqual(model.statement_ids, [1, 2, 3])

    def test_component_prediction_model(self):
        """Test the ComponentPredictionModel."""
        model = ComponentPredictionModel(
            run_id=1,
            virtual_entity_id=2,
            cluster_id=3,
            component_type=ComponentType.MOTIVATION,
            predicted_year=2025,
            final_historical_year=2023,
            predicted_vector=[0.1, 0.2, 0.3],
            historical_vector=[0.4, 0.5, 0.6],
            confidence=0.8,
            model_type=RegressionModelType.ENSEMBLE
        )
        
        # Test model attributes
        self.assertEqual(model.run_id, 1)
        self.assertEqual(model.virtual_entity_id, 2)
        self.assertEqual(model.cluster_id, 3)
        self.assertEqual(model.component_type, ComponentType.MOTIVATION)
        self.assertEqual(model.predicted_year, 2025)
        self.assertEqual(model.final_historical_year, 2023)
        self.assertEqual(model.predicted_vector, [0.1, 0.2, 0.3])
        self.assertEqual(model.historical_vector, [0.4, 0.5, 0.6])
        self.assertEqual(model.confidence, 0.8)
        self.assertEqual(model.model_type, RegressionModelType.ENSEMBLE)

    def test_predictive_component_analysis_model(self):
        """Test the PredictiveComponentAnalysisModel."""
        model = PredictiveComponentAnalysisModel(
            run_id=1,
            prediction_id=2,
            virtual_entity_id=3,
            cluster_id=4,
            component_type=ComponentType.MOTIVATION,
            year=2025,
            summary="Test summary",
            detailed_analysis="Test detailed analysis",
            potential_risks=["Risk 1", "Risk 2"],
            potential_opportunities=["Opportunity 1", "Opportunity 2"],
            confidence=0.8
        )
        
        # Test model attributes
        self.assertEqual(model.run_id, 1)
        self.assertEqual(model.prediction_id, 2)
        self.assertEqual(model.virtual_entity_id, 3)
        self.assertEqual(model.cluster_id, 4)
        self.assertEqual(model.component_type, ComponentType.MOTIVATION)
        self.assertEqual(model.year, 2025)
        self.assertEqual(model.summary, "Test summary")
        self.assertEqual(model.detailed_analysis, "Test detailed analysis")
        self.assertEqual(model.potential_risks, ["Risk 1", "Risk 2"])
        self.assertEqual(model.potential_opportunities, ["Opportunity 1", "Opportunity 2"])
        self.assertEqual(model.confidence, 0.8)


class TestPredictionV2Functions(unittest.TestCase):
    """Tests for the prediction_v2 functions."""

    def test_extract_dso_components(self):
        """Test the extract_dso_components function."""
        # Create a mock DEMISE embedding
        demise_model = DEMISEModel.model_construct()
        
        # Set some values in the domain, subject, and object components
        demise_model.domain.environment = 0.8
        demise_model.domain.social = 0.6
        demise_model.subject.company = 0.9
        demise_model.subject.government = 0.3
        demise_model.object.environment = 0.7
        demise_model.object.society = 0.5
        
        # Convert to a vector
        demise_embedding = demise_model.to_vector()
        
        # Extract the components
        domain_vector, subject_vector, object_vector = extract_dso_components(demise_embedding)
        
        # Check that the vectors have the expected values
        self.assertAlmostEqual(domain_vector[demise_model.domain.get_index('environment')], 0.8)
        self.assertAlmostEqual(domain_vector[demise_model.domain.get_index('social')], 0.6)
        self.assertAlmostEqual(subject_vector[demise_model.subject.get_index('company')], 0.9)
        self.assertAlmostEqual(subject_vector[demise_model.subject.get_index('government')], 0.3)
        self.assertAlmostEqual(object_vector[demise_model.object.get_index('environment')], 0.7)
        self.assertAlmostEqual(object_vector[demise_model.object.get_index('society')], 0.5)

    def test_extract_component_vector(self):
        """Test the extract_component_vector function."""
        # Create a mock DEMISE embedding
        demise_model = DEMISEModel.model_construct()
        
        # Set some values in the components
        demise_model.motivation.genuine = 0.8
        demise_model.motivation.compliant = 0.6
        demise_model.statement.describes_action = 0.9
        demise_model.statement.future_statement = 0.7
        demise_model.engagement.proactive = 0.8
        demise_model.engagement.reactive = 0.3
        demise_model.impact.benefit_to_human_life = 0.7
        demise_model.impact.harm_to_environment = 0.2
        
        # Convert to a vector
        demise_embedding = demise_model.to_vector()
        
        # Extract each component
        motivation_vector = extract_component_vector(demise_embedding, ComponentType.MOTIVATION)
        statement_vector = extract_component_vector(demise_embedding, ComponentType.STATEMENT_TYPE)
        engagement_vector = extract_component_vector(demise_embedding, ComponentType.ENGAGEMENT)
        impact_vector = extract_component_vector(demise_embedding, ComponentType.IMPACT)
        
        # Check that the vectors have the expected values
        motivation_model = DEMISEModel.motivation.from_np(np.array(motivation_vector))
        statement_model = DEMISEModel.statement.from_np(np.array(statement_vector))
        engagement_model = DEMISEModel.engagement.from_np(np.array(engagement_vector))
        impact_model = DEMISEModel.impact.from_np(np.array(impact_vector))
        
        self.assertAlmostEqual(motivation_model.genuine, 0.8)
        self.assertAlmostEqual(motivation_model.compliant, 0.6)
        self.assertAlmostEqual(statement_model.describes_action, 0.9)
        self.assertAlmostEqual(statement_model.future_statement, 0.7)
        self.assertAlmostEqual(engagement_model.proactive, 0.8)
        self.assertAlmostEqual(engagement_model.reactive, 0.3)
        self.assertAlmostEqual(impact_model.benefit_to_human_life, 0.7)
        self.assertAlmostEqual(impact_model.harm_to_environment, 0.2)


if __name__ == '__main__':
    unittest.main()
