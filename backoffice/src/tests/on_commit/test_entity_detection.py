"""
Unit tests for the entity detection functionality in document processors.
"""
import unittest
from unittest.mock import MagicMock, patch

from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.scrape.webpage_processor import WebpageProcessor
from eko.scrape.pdf_processor import PDFProcessor


class TestEntityDetection(unittest.TestCase):
    """Tests for the entity detection functionality in document processors."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock expanded entity
        self.expanded_entity = MagicMock(spec=VirtualEntityExpandedModel)
        self.expanded_entity.title = "Test Virtual Entity"
        self.expanded_entity.for_referencing_in_prompts.return_value = "company 'Test Virtual Entity' (described as A test company)"

    def test_is_entity_mentioned_in_webpage_processor(self):
        """Test that the is_entity_mentioned method works correctly in WebpageProcessor."""
        # Create a WebpageProcessor with the expanded entity
        processor = WebpageProcessor(
            url="https://example.com/test.html",
            research_types=["journalism"],
            name=None,  # No name, only expanded entity
            expanded_entity=self.expanded_entity
        )

        # Test when entity is mentioned
        with patch.object(processor, 'is_entity_mentioned', return_value=True) as mock_is_entity_mentioned:
            result = processor.is_entity_mentioned("This is a test article about Test Virtual Entity")
            self.assertTrue(result)

        # Test when entity is not mentioned
        with patch.object(processor, 'is_entity_mentioned', return_value=False) as mock_is_entity_mentioned:
            result = processor.is_entity_mentioned("This is a test article about something else")
            self.assertFalse(result)

    def test_is_entity_mentioned_in_pdf_processor(self):
        """Test that the is_entity_mentioned method works correctly in PDFProcessor."""
        # Create a PDFProcessor with the expanded entity
        processor = PDFProcessor(
            url="https://example.com/test.pdf",
            research_types=["journalism"],
            name=None,  # No name, only expanded entity
            expanded_entity=self.expanded_entity
        )

        # Test when entity is mentioned
        with patch.object(processor, 'is_entity_mentioned', return_value=True) as mock_is_entity_mentioned:
            result = processor.is_entity_mentioned("This is a test article about Test Virtual Entity")
            self.assertTrue(result)

        # Test when entity is not mentioned
        with patch.object(processor, 'is_entity_mentioned', return_value=False) as mock_is_entity_mentioned:
            result = processor.is_entity_mentioned("This is a test article about something else")
            self.assertFalse(result)


if __name__ == '__main__':
    unittest.main()
