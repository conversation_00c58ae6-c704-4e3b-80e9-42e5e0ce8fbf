"""
Tests for BaseVectorModel using DEMISEModel as the concrete implementation.
"""
import unittest
import json
import numpy as np
from pydantic import ValidationError
from typing import Dict, List

from eko.models.vector.base_vector_model import BaseVectorModel
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.demise.domain import DomainModel
from eko.models.vector.demise.entity import EntityModel
from eko.models.vector.demise.motivation import MotivationModel
from eko.models.vector.demise.impact import ImpactModel
from eko.models.vector.demise.statement import StatementTypeModel
from eko.models.vector.demise.engagement import EngagementModel


class TestBaseVectorModel(unittest.TestCase):
    """Test suite for BaseVectorModel using DEMISEModel as the concrete implementation."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a DEMISEModel with some values set
        self.model = DEMISEModel.model_construct()

        # Set values in domain component
        self.model.domain.environment.climate = 0.8
        self.model.domain.industry.manufacturing = 0.6

        # Set values in subject component
        self.model.subject.entity_type.organizations = 0.9  # Using organizations instead of company
        self.model.subject.qualities.size = 0.7

        # Set values in object component
        self.model.object.entity_type.biological_systems = 0.7  # Using biological_systems instead of environment
        self.model.object.qualities.quantity = 0.5

        # Set values in motivation component
        self.model.motivation.genuine = 0.8
        self.model.motivation.compliant = 0.6

        # Set values in impact component
        self.model.impact.benefit_to_human_life = 0.7
        self.model.impact.harm_to_environment = 0.2

        # Set values in statement component
        self.model.statement.describes_action = 0.9
        self.model.statement.future_statement = 0.7

        # Set values in engagement component
        self.model.engagement.proactive = 0.8
        self.model.engagement.reactive = 0.3

    def test_is_environmental(self):
        """Test is_environmental method."""
        self.assertTrue(self.model.is_environmental())

    def test_has_domain(self):
        """Test has_domain method."""
        self.assertTrue(any(self.model.domain.to_vector()))

    def test_bad_model(self):
        """Test bad model."""

        model = DEMISEModel.model_validate({"domain": {"non_existent": 0.8}})
        print(model.__pydantic_extra__)
        assert model.domain.__pydantic_extra__["non_existent"] == 0.8

    def test_from_sparse_kv_manual(self):
        """Test bad model."""
        bad_model = DEMISEModel.from_sparse_kv({"domain.society.employment": 0.8})
        self.assertEqual(bad_model.domain.society.employment, 0.8)


    def test_to_vector(self):
        """Test conversion to vector."""
        vector = self.model.to_vector()
        self.assertIsInstance(vector, list)
        self.assertTrue(all(isinstance(v, float) for v in vector))
        self.assertGreater(len(vector), 0)

        # Verify that the vector contains our set values
        # We can't check exact positions without knowing the field order,
        # but we can check that the values we set are in the vector
        self.assertIn(0.8, vector)  # domain.environment.climate
        self.assertIn(0.6, vector)  # domain.industry.manufacturing
        self.assertIn(0.9, vector)  # subject.entity_type.company
        self.assertIn(0.7, vector)  # impact.benefit_to_human_life

        self.assertTrue(any(self.model.domain.to_vector()))

    def test_to_fixed_size_vector(self):
        """Test conversion to fixed size vector."""
        # Test with smaller size
        small_size = 10
        small_vector = self.model.to_fixed_size_vector(small_size)
        self.assertEqual(len(small_vector), small_size)

        # Test with larger size
        large_size = 2048
        large_vector = self.model.to_fixed_size_vector(large_size)
        self.assertEqual(len(large_vector), large_size)

        # Test with default size
        default_vector = self.model.to_fixed_size_vector()
        self.assertEqual(len(default_vector), 1024)

    def test_to_kv(self):
        """Test conversion to key-value pairs."""
        kv = self.model.to_kv()
        self.assertIsInstance(kv, dict)

        # Check that our set values are in the key-value pairs
        self.assertEqual(kv.get("domain.environment.climate"), 0.8)
        self.assertEqual(kv.get("domain.industry.manufacturing"), 0.6)
        self.assertEqual(kv.get("subject.entity_type.organizations"), 0.9)
        self.assertEqual(kv.get("subject.qualities.size"), 0.7)
        self.assertEqual(kv.get("object.entity_type.biological_systems"), 0.7)
        self.assertEqual(kv.get("object.qualities.quantity"), 0.5)
        self.assertEqual(kv.get("motivation.genuine"), 0.8)
        self.assertEqual(kv.get("motivation.compliant"), 0.6)
        self.assertEqual(kv.get("impact.benefit_to_human_life"), 0.7)
        self.assertEqual(kv.get("impact.harm_to_environment"), 0.2)
        self.assertEqual(kv.get("statement.describes_action"), 0.9)
        self.assertEqual(kv.get("statement.future_statement"), 0.7)
        self.assertEqual(kv.get("engagement.proactive"), 0.8)
        self.assertEqual(kv.get("engagement.reactive"), 0.3)

    def test_to_kv_sparse(self):
        """Test conversion to sparse key-value pairs."""
        sparse_kv = self.model.to_kv_sparse()
        self.assertIsInstance(sparse_kv, dict)

        # Check that our set values are in the sparse key-value pairs
        self.assertEqual(sparse_kv.get("domain.environment.climate"), 0.8)
        self.assertEqual(sparse_kv.get("domain.industry.manufacturing"), 0.6)
        self.assertEqual(sparse_kv.get("subject.entity_type.organizations"), 0.9)

        # Check that zero values are not in the sparse key-value pairs
        self.assertNotIn("domain.environment.biodiversity", sparse_kv)

    def test_from_vector(self):
        """Test creation from vector."""
        vector = self.model.to_vector()
        new_model = DEMISEModel.from_vector(vector)

        # Check that the new model has the same values as the original
        self.assertEqual(new_model.domain.environment.climate, self.model.domain.environment.climate)
        self.assertEqual(new_model.domain.industry.manufacturing, self.model.domain.industry.manufacturing)
        self.assertEqual(new_model.subject.entity_type.organizations, self.model.subject.entity_type.organizations)
        self.assertEqual(new_model.subject.qualities.size, self.model.subject.qualities.size)
        self.assertEqual(new_model.motivation.genuine, self.model.motivation.genuine)
        self.assertEqual(new_model.impact.benefit_to_human_life, self.model.impact.benefit_to_human_life)
        self.assertEqual(new_model.statement.describes_action, self.model.statement.describes_action)
        self.assertEqual(new_model.engagement.proactive, self.model.engagement.proactive)

    def test_from_kv(self):
        """Test creation from key-value pairs."""
        kv = self.model.to_kv()

        # First unflatten the key-value pairs, then create the model
        unflattened_kv = DEMISEModel.unflatten_kv(kv)
        new_model = DEMISEModel.model_validate(unflattened_kv)

        # Check that the new model has the same values as the original
        self.assertEqual(new_model.domain.environment.climate, self.model.domain.environment.climate)
        self.assertEqual(new_model.domain.industry.manufacturing, self.model.domain.industry.manufacturing)
        self.assertEqual(new_model.subject.entity_type.organizations, self.model.subject.entity_type.organizations)
        self.assertEqual(new_model.subject.qualities.size, self.model.subject.qualities.size)
        self.assertEqual(new_model.motivation.genuine, self.model.motivation.genuine)
        self.assertEqual(new_model.impact.benefit_to_human_life, self.model.impact.benefit_to_human_life)
        self.assertEqual(new_model.statement.describes_action, self.model.statement.describes_action)
        self.assertEqual(new_model.engagement.proactive, self.model.engagement.proactive)

    def test_from_sparse_kv(self):
        """Test creation from sparse key-value pairs."""
        sparse_kv = self.model.to_kv_sparse()
        new_model = DEMISEModel.from_sparse_kv(sparse_kv)

        # Check that the new model has the same non-zero values as the original
        self.assertEqual(new_model.domain.environment.climate, self.model.domain.environment.climate)
        self.assertEqual(new_model.domain.industry.manufacturing, self.model.domain.industry.manufacturing)
        self.assertEqual(new_model.subject.entity_type.organizations, self.model.subject.entity_type.organizations)
        self.assertEqual(new_model.subject.qualities.size, self.model.subject.qualities.size)
        self.assertEqual(new_model.motivation.genuine, self.model.motivation.genuine)
        self.assertEqual(new_model.impact.benefit_to_human_life, self.model.impact.benefit_to_human_life)
        self.assertEqual(new_model.statement.describes_action, self.model.statement.describes_action)
        self.assertEqual(new_model.engagement.proactive, self.model.engagement.proactive)

    def test_from_np(self):
        """Test creation from numpy array."""
        vector = self.model.to_vector()
        np_array = np.array(vector)
        new_model = DEMISEModel.from_np(np_array)

        # Check that the new model has the same values as the original
        self.assertEqual(new_model.domain.environment.climate, self.model.domain.environment.climate)
        self.assertEqual(new_model.domain.industry.manufacturing, self.model.domain.industry.manufacturing)
        self.assertEqual(new_model.subject.entity_type.organizations, self.model.subject.entity_type.organizations)
        self.assertEqual(new_model.subject.qualities.size, self.model.subject.qualities.size)
        self.assertEqual(new_model.motivation.genuine, self.model.motivation.genuine)
        self.assertEqual(new_model.impact.benefit_to_human_life, self.model.impact.benefit_to_human_life)
        self.assertEqual(new_model.statement.describes_action, self.model.statement.describes_action)
        self.assertEqual(new_model.engagement.proactive, self.model.engagement.proactive)

    def test_get_vector_size(self):
        """Test getting vector size."""
        size = DEMISEModel.get_vector_size()
        self.assertIsInstance(size, int)
        self.assertGreater(size, 0)
        self.assertEqual(size, len(self.model.to_vector()))

    def test_has_value(self):
        """Test has_value method."""
        # Our model has values
        self.assertTrue(self.model.has_value())

        # Empty model should not have values
        empty_model = DEMISEModel.model_construct()
        self.assertFalse(empty_model.has_value())

    def test_to_hash(self):
        """Test to_hash method."""
        hash_str = self.model.to_hash()
        self.assertIsInstance(hash_str, str)
        self.assertGreater(len(hash_str), 0)

    def test_nested_model_conversion(self):
        """Test conversion with nested models."""
        # Create a model with deeply nested values
        nested_model = DEMISEModel.model_construct()
        nested_model.domain.industry.manufacturing = 0.9
        nested_model.domain.industry.technology = 0.8
        nested_model.domain.industry.finance = 0.7

        # Convert to vector and back
        vector = nested_model.to_vector()
        new_model = DEMISEModel.from_vector(vector)

        # Check that the nested values are preserved
        self.assertEqual(new_model.domain.industry.manufacturing, nested_model.domain.industry.manufacturing)
        self.assertEqual(new_model.domain.industry.technology, nested_model.domain.industry.technology)
        self.assertEqual(new_model.domain.industry.finance, nested_model.domain.industry.finance)

    def test_vector_from_kv(self):
        """Test vector_from_kv class method."""
        kv = self.model.to_kv()
        vector = DEMISEModel.vector_from_kv(kv)

        # Check that the vector has the same values as the original model's vector
        original_vector = self.model.to_vector()
        self.assertEqual(len(vector), len(original_vector))
        self.assertEqual(vector, original_vector)

    def test_create_kv_from_vector(self):
        """Test create_kv_from_vector class method."""
        vector = self.model.to_vector()
        kv = DEMISEModel.create_kv_from_vector(vector)

        # Check that the key-value pairs have the same values as the original model's key-value pairs
        original_kv = self.model.to_kv()
        self.assertEqual(len(kv), len(original_kv))
        for key, value in original_kv.items():
            self.assertAlmostEqual(kv[key], value, places=6)

    def test_kv_from_sparse_kv(self):
        """Test kv_from_sparse_kv class method."""
        sparse_kv = self.model.to_kv_sparse()
        kv = DEMISEModel.kv_from_sparse_kv(sparse_kv)

        # Check that the key-value pairs have the same non-zero values as the original model's key-value pairs
        original_kv = self.model.to_kv()
        for key, value in sparse_kv.items():
            self.assertAlmostEqual(kv[key], value, places=6)
            self.assertAlmostEqual(original_kv[key], value, places=6)

    def test_unflatten_kv(self):
        """Test unflatten_kv class method."""
        kv = self.model.to_kv()
        unflattened = DEMISEModel.unflatten_kv(kv)

        # Check that the unflattened dictionary has the same structure as the original model
        self.assertEqual(unflattened["domain"]["environment"]["climate"], self.model.domain.environment.climate)
        self.assertEqual(unflattened["domain"]["industry"]["manufacturing"], self.model.domain.industry.manufacturing)
        self.assertEqual(unflattened["subject"]["entity_type"]["organizations"], self.model.subject.entity_type.organizations)
        self.assertEqual(unflattened["subject"]["qualities"]["size"], self.model.subject.qualities.size)


if __name__ == "__main__":
    unittest.main()
