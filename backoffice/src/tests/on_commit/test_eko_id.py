import unittest

from eko.models import name_to_eko_id


class TestNameToEkoId(unittest.TestCase):
    def test_valid_type_and_name(self):
        self.assertEqual(name_to_eko_id('test', 'Hello World'), 'eko:test:name:other')

    def test_name_with_special_characters(self):
        self.assertEqual(name_to_eko_id('test', 'Hello! World@#$'), 'eko:test:name:other')

    def test_name_with_multiple_consecutive_special_characters(self):
        self.assertEqual(name_to_eko_id('test', 'Hello!!! World@@#$'), 'eko:test:name:other')

    def test_name_with_leading_trailing_special_characters(self):
        self.assertEqual(name_to_eko_id('test', '!@#$Hello World!@#$'), 'eko:test:name:other')

    def test_empty_name(self):
        self.assertEqual(name_to_eko_id('test', ''), 'eko:test:name:other')

    def test_empty_type(self):
        self.assertEqual(name_to_eko_id('', 'Hello World'), 'eko::name:other')

if __name__ == '__main__':
    unittest.main()
