import unittest
from eko.nlp.util import extract_eko_citations, has_eko_citations


class TestCitationExtraction(unittest.TestCase):
    """Test cases for citation extraction functionality."""

    def test_single_citations(self):
        """Test extraction of single citations in the format [^123]."""
        # Basic single citation
        text = "This is a statement [^123] with a citation."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123])
        
        # Multiple single citations
        text = "First citation [^123] and second [^456] and third [^789]."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])
        
        # Duplicate citations should be deduplicated
        text = "Citation [^123] appears twice [^123] in text."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123])

    def test_multiple_citations_with_carets(self):
        """Test extraction of multiple citations with carets: [^123, ^456]."""
        # Basic multiple citations with carets
        text = "This statement has multiple sources [^123, ^456]."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456])
        
        # Multiple citations with more than two IDs
        text = "Many sources support this [^123, ^456, ^789]."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])
        
        # Multiple citations with spaces
        text = "Sources with spaces [^123,  ^456,   ^789]."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])

    def test_multiple_citations_mixed_format(self):
        """Test extraction of multiple citations in mixed format: [^123, 456]."""
        # Mixed format with and without carets
        text = "Mixed format citations [^123, 456, ^789]."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])
        
        # All without carets after first
        text = "First with caret [^123, 456, 789]."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])

    def test_mixed_single_and_multiple_citations(self):
        """Test text with both single and multiple citation formats."""
        text = "Single citation [^123] and multiple [^456, ^789] in same text."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])
        
        # Complex mix
        text = "Start [^100], then multiple [^200, 300, ^400], and single [^500]."
        result = extract_eko_citations(text)
        self.assertEqual(result, [100, 200, 300, 400, 500])

    def test_edge_cases(self):
        """Test edge cases and malformed citations."""
        # Empty text
        result = extract_eko_citations("")
        self.assertEqual(result, [])
        
        # No citations
        text = "This text has no citations at all."
        result = extract_eko_citations(text)
        self.assertEqual(result, [])
        
        # Malformed citations (should be ignored)
        text = "Malformed [^] and [^abc] should be ignored but [^123] should work."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123])
        
        # Empty multiple citation brackets
        text = "Empty brackets [^] should be ignored."
        result = extract_eko_citations(text)
        self.assertEqual(result, [])

    def test_citation_sorting_and_deduplication(self):
        """Test that citations are sorted and deduplicated correctly."""
        # Unsorted citations
        text = "Unsorted [^789, ^123, ^456] citations."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])
        
        # Duplicates across single and multiple citations
        text = "Duplicate [^123] and [^456, ^123, ^789] citations."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])
        
        # Duplicates within multiple citations
        text = "Internal duplicates [^123, ^456, ^123] should be removed."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456])

    def test_has_eko_citations_single(self):
        """Test has_eko_citations function with single citations."""
        # Text with single citation
        self.assertTrue(has_eko_citations("Text with citation [^123]."))
        
        # Text without citations
        self.assertFalse(has_eko_citations("Text without citations."))
        
        # Multiple single citations
        self.assertTrue(has_eko_citations("Multiple [^123] single [^456] citations."))

    def test_has_eko_citations_multiple(self):
        """Test has_eko_citations function with multiple citations."""
        # Text with multiple citations
        self.assertTrue(has_eko_citations("Text with multiple [^123, ^456] citations."))
        
        # Mixed format
        self.assertTrue(has_eko_citations("Mixed format [^123, 456] citations."))
        
        # Complex multiple citations
        self.assertTrue(has_eko_citations("Complex [^123, ^456, 789] citations."))

    def test_has_eko_citations_mixed(self):
        """Test has_eko_citations function with mixed citation formats."""
        # Both single and multiple
        self.assertTrue(has_eko_citations("Single [^123] and multiple [^456, ^789]."))
        
        # Empty text
        self.assertFalse(has_eko_citations(""))
        
        # Malformed citations
        self.assertFalse(has_eko_citations("Malformed [^] citations."))

    def test_large_citation_numbers(self):
        """Test with large citation numbers."""
        text = "Large numbers [^999999, ^1000000] should work."
        result = extract_eko_citations(text)
        self.assertEqual(result, [999999, 1000000])

    def test_whitespace_variations(self):
        """Test various whitespace patterns in multiple citations."""
        # No spaces
        text = "No spaces [^123,^456,^789]."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])
        
        # Extra spaces
        text = "Extra spaces [^123,   ^456,     ^789]."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456, 789])
        
        # Tabs and newlines (should not work, but test anyway)
        text = "Normal spaces [^123, ^456] work fine."
        result = extract_eko_citations(text)
        self.assertEqual(result, [123, 456])


if __name__ == '__main__':
    unittest.main()
