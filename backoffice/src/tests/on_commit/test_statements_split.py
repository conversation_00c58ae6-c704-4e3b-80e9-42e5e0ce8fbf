import unittest
from unittest.mock import patch, MagicMock

from eko.statements.split import split_into_statements, StatementExtractionList, StatementExtraction


class TestStatementsSplit(unittest.TestCase):

    @patch('eko.statements.split.call_llms_typed')
    def test_split_into_statements(self, mock_call_llms_typed):
        # Test case 1: Normal text with multiple statements
        test_text = """
        Acme Corp has committed to reducing carbon emissions by 30% by 2030.
        The company is investing in renewable energy sources.
        This initiative will help combat climate change.
        """

        # Create a mock response object
        mock_response = StatementExtractionList(
            statements=[
                StatementExtraction(
                    statements=["Acme Corp has committed to reducing carbon emissions by 30% by 2030."],
                    original_sentence="Acme Corp has committed to reducing carbon emissions by 30% by 2030.",
                    ambiguous=False
                ),
                StatementExtraction(
                    statements=["The company is investing in renewable energy sources."],
                    original_sentence="The company is investing in renewable energy sources.",
                    ambiguous=False
                ),
                StatementExtraction(
                    statements=["This initiative will help combat climate change."],
                    original_sentence="This initiative will help combat climate change.",
                    ambiguous=False
                )
            ]
        )

        # Set up the mock to return our mock response
        mock_call_llms_typed.return_value = mock_response

        result = split_into_statements(test_text)

        # Verify the mock was called
        mock_call_llms_typed.assert_called_once()

        # Verify split_into_statements returns the expected result
        self.assertEqual(result, mock_response.statements)

        # Verify markdown headings are removed before processing
        test_text_with_headings = """
        ### Company Profile

        Acme Corp has committed to reducing carbon emissions by 30% by 2030.

        ## Sustainability Initiatives

        The company is investing in renewable energy sources.
        """

        # Reset mock
        mock_call_llms_typed.reset_mock()

        # Create a new mock response
        mock_response2 = StatementExtractionList(
            statements=[
                StatementExtraction(
                    statements=["Acme Corp has committed to reducing carbon emissions by 30% by 2030."],
                    original_sentence="Acme Corp has committed to reducing carbon emissions by 30% by 2030.",
                    ambiguous=False
                ),
                StatementExtraction(
                    statements=["The company is investing in renewable energy sources."],
                    original_sentence="The company is investing in renewable energy sources.",
                    ambiguous=False
                )
            ]
        )

        # Set up the mock to return our new mock response
        mock_call_llms_typed.return_value = mock_response2

        result = split_into_statements(test_text_with_headings)

        # Verify the mock was called
        mock_call_llms_typed.assert_called_once()

        # Verify the expected results
        self.assertEqual(result, mock_response2.statements)

        # Verify that clean_chunk (with removed headings) is passed to the LLM
        # The content passed to the LLM should not have the headings
        call_args = mock_call_llms_typed.call_args[0][1]
        self.assertNotIn("### Company Profile", call_args)
        self.assertNotIn("## Sustainability Initiatives", call_args)

        # Test case 3: LLM returns None
        mock_call_llms_typed.reset_mock()
        mock_call_llms_typed.return_value = None

        result = split_into_statements("Some text")

        # Verify the function handles None response correctly
        self.assertEqual(result, [])

    @patch('eko.statements.split.call_llms_typed')
    def test_split_into_statements_follows_prompt_rules(self, mock_call_llms_typed):
        """Test that the function respects the rules specified in the prompt"""
        test_text = """
        Acme Corp plans to use sustainable materials in all packaging by 2025.
        This is part of their broader ESG initiative.
        The company will also eliminate single-use plastics in their offices.

        Financial performance in Q1 was strong with revenue up 15%.
        """

        # Create a mock response that follows the prompt guidelines
        # Specifically focus on only including ESG-related statements and ignoring financial info
        mock_response = StatementExtractionList(
            statements=[
                StatementExtraction(
                    statements=["Acme Corp plans to use sustainable materials in all packaging by 2025."],
                    original_sentence="Acme Corp plans to use sustainable materials in all packaging by 2025.",
                    ambiguous=False
                ),
                StatementExtraction(
                    statements=["This is part of their broader ESG initiative."],
                    original_sentence="This is part of their broader ESG initiative.",
                    ambiguous=False
                ),
                StatementExtraction(
                    statements=["The company will also eliminate single-use plastics in their offices."],
                    original_sentence="The company will also eliminate single-use plastics in their offices.",
                    ambiguous=False
                )
            ]
        )

        # Set up the mock to return our mock response
        mock_call_llms_typed.return_value = mock_response

        result = split_into_statements(test_text)

        # Verify the mock was called
        mock_call_llms_typed.assert_called_once()

        # Verify the expected results
        self.assertEqual(result, mock_response.statements)

        # Verify that none of the financial statements (non-ESG) are included
        self.assertFalse(any("Financial performance" in stmt.original_sentence for stmt in result))

        # Test that statements are coherent and about single facts/actions
        test_text_mixed = """
        Acme Corp is investing in renewable energy. The CEO announced this at the annual meeting.
        They are also working on reducing water usage in manufacturing.
        """

        # Reset mock
        mock_call_llms_typed.reset_mock()

        # Create a new mock response
        mock_response2 = StatementExtractionList(
            statements=[
                StatementExtraction(
                    statements=["Acme Corp is investing in renewable energy. The CEO announced this at the annual meeting."],
                    original_sentence="Acme Corp is investing in renewable energy. The CEO announced this at the annual meeting.",
                    ambiguous=False
                ),
                StatementExtraction(
                    statements=["They are also working on reducing water usage in manufacturing."],
                    original_sentence="They are also working on reducing water usage in manufacturing.",
                    ambiguous=False
                )
            ]
        )

        # Set up the mock to return our new mock response
        mock_call_llms_typed.return_value = mock_response2

        result = split_into_statements(test_text_mixed)

        # Verify the mock was called
        mock_call_llms_typed.assert_called_once()

        # Verify related sentences about the same topic are kept together
        self.assertEqual(
            result[0].statements[0],
            "Acme Corp is investing in renewable energy. The CEO announced this at the annual meeting."
        )


if __name__ == '__main__':
    unittest.main()
