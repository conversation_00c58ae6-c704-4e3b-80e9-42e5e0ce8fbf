# LLM Evaluation Tests

This directory contains pytest tests for evaluating the metadata extraction capabilities of different LLM models.

## Setup

Make sure you have the required dependencies installed:

```bash
uv add pytest pandas matplotlib
```

## Running the Tests

### Run All Tests

To run all LLM evaluation tests:

```bash
cd backoffice/src
uv run python -m pytest tests/llm_evals -v
```

### Run Specific Test Files

To run a specific test file:

```bash
cd backoffice/src
uv run python -m pytest tests/llm_evals/test_metadata_extraction.py -v
uv run python -m pytest tests/llm_evals/test_model_comparison.py -v
uv run python -m pytest tests/llm_evals/test_field_extraction.py -v
```

### Run Individual Tests

To run a specific test:

```bash
cd backoffice/src
uv run python -m pytest tests/llm_evals/test_metadata_extraction.py::test_metadata_extraction -v
uv run python -m pytest tests/llm_evals/test_model_comparison.py::test_model_comparison -v
```

## Customizing Models

You can customize which models to test by modifying the `favorite_models` fixture in `conftest.py`:

```python
@pytest.fixture(scope="session")
def favorite_models():
    """Define your favorite models to test."""
    return [
        "vertex_ai/gemini-2.0-flash-lite",
        "vertex_ai/gemini-1.5-flash",
        "claude-3-5-sonnet-20240620",
        "groq/llama3-70b-8192",
        # Add more models as needed
    ]
```

## Model Configuration

You can customize model parameters by editing the `model_config.json` file that will be created in the test output directory (`eko_var/reports/llm_evals/tests/`).

## Test Output

Test results and visualizations are saved to:

```
eko_var/reports/llm_evals/tests/
```

This directory will contain:
- JSON files with detailed metrics for each model
- CSV files with comparison data
- Visualizations (PNG files) showing model performance
- Reports on field-specific accuracy

## Adding New Test Statements

To add new test statements, modify the `test_data.py` file in the `llm_evals` package.
