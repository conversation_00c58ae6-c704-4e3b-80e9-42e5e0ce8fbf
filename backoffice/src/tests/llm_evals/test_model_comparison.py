"""
Tests for comparing metadata extraction capabilities across multiple LLM models.
"""

import os
import json
import pytest
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional
from pathlib import Path

from eko import eko_var_path
from llm_evals.metadata_eval import MetadataEvaluator
from llm_evals.test_data import get_test_statements


@pytest.fixture
def evaluator(gold_standard_model):
    """Fixture for the metadata evaluator."""
    return MetadataEvaluator(gold_standard_model=gold_standard_model)


@pytest.mark.parametrize("model_name", [
    pytest.param("vertex_ai/gemini-2.0-flash-lite", id="gemini-flash-lite"),
    pytest.param("vertex_ai/gemini-1.5-flash", id="gemini-flash"),
    pytest.param("claude-3-5-sonnet-20240620", id="claude-sonnet"),
    pytest.param("groq/llama3-70b-8192", id="llama3-70b"),
])
def test_individual_model(evaluator, model_name, test_output_dir):
    """
    Test individual model performance.
    
    Args:
        evaluator: The metadata evaluator
        model_name: The name of the model to test
        test_output_dir: The output directory
    """
    try:
        # Create model-specific output directory
        model_dir = os.path.join(test_output_dir, model_name.replace("/", "_"))
        os.makedirs(model_dir, exist_ok=True)
        
        # Run evaluation
        metrics = evaluator.evaluate_model(model_name, output_dir=model_dir)
        
        # Save results
        results_file = os.path.join(model_dir, "test_results.json")
        with open(results_file, "w") as f:
            json.dump(metrics, f, indent=2)
        
        # Basic assertions
        assert metrics is not None
        assert "overall_accuracy" in metrics
        assert metrics["successful_extractions"] > 0
        
        # Print results
        print(f"\nResults for {model_name}:")
        print(f"Overall accuracy: {metrics['overall_accuracy']:.2f}")
        print(f"Successful extractions: {metrics['successful_extractions']}/{metrics['total_statements']}")
        
    except Exception as e:
        pytest.skip(f"Error testing {model_name}: {str(e)}")


def test_model_comparison(evaluator, favorite_models, test_output_dir):
    """
    Test comparing multiple models.
    
    Args:
        evaluator: The metadata evaluator
        favorite_models: List of models to compare
        test_output_dir: The output directory
    """
    # Dictionary to store results
    results = {}
    
    # Run evaluations for each model
    for model_name in favorite_models:
        try:
            # Check if results already exist
            model_dir = os.path.join(test_output_dir, model_name.replace("/", "_"))
            results_file = os.path.join(model_dir, "test_results.json")
            
            if os.path.exists(results_file):
                # Load existing results
                with open(results_file, "r") as f:
                    metrics = json.load(f)
            else:
                # Run evaluation
                os.makedirs(model_dir, exist_ok=True)
                metrics = evaluator.evaluate_model(model_name, output_dir=model_dir)
                
                # Save results
                with open(results_file, "w") as f:
                    json.dump(metrics, f, indent=2)
            
            results[model_name] = metrics
            
        except Exception as e:
            print(f"Error evaluating {model_name}: {str(e)}")
    
    # Generate comparison report if we have results
    if results:
        _generate_comparison_report(results, test_output_dir)
    
    # Skip the test if no results
    if not results:
        pytest.skip("No models were successfully evaluated")
    
    # Basic assertion
    assert len(results) > 0, "No models were successfully evaluated"


def _generate_comparison_report(results: Dict[str, Dict[str, Any]], output_dir: str):
    """
    Generate a comparison report for multiple models.
    
    Args:
        results: Dictionary mapping model names to their metrics
        output_dir: Output directory for the report
    """
    # Create comparison data
    comparison_data = []
    
    for model_name, metrics in results.items():
        model_data = {
            "model": model_name,
            "overall_accuracy": metrics.get("overall_accuracy", 0.0),
            "success_rate": metrics.get("successful_extractions", 0) / metrics.get("total_statements", 1),
            "failed_extractions": metrics.get("failed_extractions", 0)
        }
        
        # Add field accuracy if available
        if "field_accuracy" in metrics and metrics["field_accuracy"]:
            for field, accuracy in metrics["field_accuracy"].items():
                model_data[f"field_{field}"] = accuracy
        
        comparison_data.append(model_data)
    
    # Create DataFrame
    df = pd.DataFrame(comparison_data)
    
    # Save to CSV
    csv_file = os.path.join(output_dir, "model_comparison.csv")
    df.to_csv(csv_file, index=False)
    
    # Generate visualizations
    _generate_comparison_visualizations(df, output_dir)
    
    # Print comparison table
    print("\nModel Comparison:")
    print("=" * 80)
    print(df[["model", "overall_accuracy", "success_rate"]].to_string(index=False))
    print("=" * 80)
    print(f"Full comparison saved to {csv_file}")


def _generate_comparison_visualizations(df: pd.DataFrame, output_dir: str):
    """
    Generate visualizations for model comparison.
    
    Args:
        df: DataFrame with comparison data
        output_dir: Output directory for visualizations
    """
    # 1. Overall accuracy comparison
    plt.figure(figsize=(12, 6))
    plt.bar(df["model"], df["overall_accuracy"], color="skyblue")
    plt.ylim(0, 1)
    plt.title("Overall Accuracy Comparison")
    plt.ylabel("Accuracy (0-1)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "overall_accuracy_comparison.png"))
    plt.close()
    
    # 2. Success rate comparison
    plt.figure(figsize=(12, 6))
    plt.bar(df["model"], df["success_rate"], color="green")
    plt.ylim(0, 1)
    plt.title("Success Rate Comparison")
    plt.ylabel("Success Rate (0-1)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "success_rate_comparison.png"))
    plt.close()
    
    # 3. Field accuracy comparison (if available)
    field_columns = [col for col in df.columns if col.startswith("field_")]
    
    if field_columns:
        # Reshape data for field comparison
        field_data = []
        
        for _, row in df.iterrows():
            model = row["model"]
            for field in field_columns:
                field_name = field.replace("field_", "")
                field_data.append({
                    "model": model,
                    "field": field_name,
                    "accuracy": row[field]
                })
        
        field_df = pd.DataFrame(field_data)
        
        # Create pivot table
        pivot_df = field_df.pivot(index="field", columns="model", values="accuracy")
        
        # Plot heatmap
        plt.figure(figsize=(14, 10))
        plt.imshow(pivot_df, cmap="YlGnBu", aspect="auto", vmin=0, vmax=1)
        plt.colorbar(label="Accuracy")
        plt.xticks(range(len(pivot_df.columns)), pivot_df.columns, rotation=45, ha="right")
        plt.yticks(range(len(pivot_df.index)), pivot_df.index)
        plt.title("Field Accuracy Comparison")
        
        # Add text annotations
        for i in range(len(pivot_df.index)):
            for j in range(len(pivot_df.columns)):
                value = pivot_df.iloc[i, j]
                if not pd.isna(value):
                    plt.text(j, i, f"{value:.2f}", ha="center", va="center", 
                             color="black" if value > 0.5 else "white")
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "field_accuracy_comparison.png"))
        plt.close()


if __name__ == "__main__":
    # This allows running the tests directly
    pytest.main(["-xvs", __file__])
