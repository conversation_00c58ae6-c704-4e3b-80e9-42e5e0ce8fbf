"""
Test script to verify the fix for the clustering issue.
"""

from loguru import logger
from eko.db import get_bo_conn
from eko.entities.virtual_queries import get_virtual_entity_for_analytics
from eko.prediction_v2.clustering import get_statements_by_year

def test_get_statements_by_year():
    """Test the get_statements_by_year function."""
    # Get a virtual entity
    virtual_entity = get_virtual_entity_for_analytics("NxEZa0dXLn")
    if not virtual_entity:
        logger.error("Virtual entity not found")
        return

    logger.info(f"Testing with virtual entity: {virtual_entity.name} (ID: {virtual_entity.id})")

    # Test the function
    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            try:
                statements_by_year = get_statements_by_year(
                    cursor, virtual_entity.id, 2019, 2025
                )

                logger.info(f"Successfully retrieved statements by year")
                logger.info(f"Years found: {list(statements_by_year.keys())}")

                for year, statements in statements_by_year.items():
                    logger.info(f"Year {year}: {len(statements)} statements")

                    # Print the first statement for each year
                    if statements:
                        logger.info(f"Sample statement: {statements[0]['text'][:100]}...")

                logger.info("Test completed successfully")

            except Exception as e:
                logger.exception(f"Error in get_statements_by_year: {e}")

if __name__ == "__main__":
    test_get_statements_by_year()
