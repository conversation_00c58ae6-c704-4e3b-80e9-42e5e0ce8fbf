"""
CLI commands for LLM evaluations.
"""

import os
import json
import click
from loguru import logger

from eko import eko_var_path
from llm_evals.metadata_eval import MetadataEvaluator
from llm_evals.utils import setup_logging
from llm_evals.html_report import generate_html_report


@click.group(name="llm-evals")
def llm_evals_group():
    """Commands for evaluating LLM models."""
    pass


@llm_evals_group.command(name="evaluate-metadata")
@click.option("--model", "-m", multiple=True, help="Model(s) to evaluate")
@click.option("--gold-standard", default="gemini-2.5-pro-preview-05-06", help="Gold standard model")
@click.option("--output-dir", default=None, help="Directory to save evaluation results")
@click.option("--debug", is_flag=True, help="Enable debug logging")
@click.option("--html-report", is_flag=True, help="Generate HTML report with comparisons")
@click.option("--open-browser", is_flag=True, default=True, help="Open HTML report in browser")
def evaluate_metadata_command(model, gold_standard, output_dir, debug, html_report, open_browser):
    """
    Evaluate metadata extraction capabilities of LLM models.

    Args:
        model: Model(s) to evaluate
        gold_standard: Gold standard model
        output_dir: Directory to save evaluation results
        debug: Enable debug logging
        html_report: Generate HTML report with comparisons
        open_browser: Open HTML report in browser
    """
    # Setup logging
    setup_logging(debug)

    # Set default output directory if not provided
    if output_dir is None:
        output_dir = os.path.join("/Users/<USER>/IdeaProjects/mono-repo/apps/docs/public/technical/evals/demise_metadata")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Set default models if not provided
    if not model:
        model = ["gemini-1.5-pro", "claude-3-5-sonnet-20240620"]

    # Initialize evaluator
    evaluator = MetadataEvaluator(gold_standard_model=gold_standard)

    # Dictionary to store metrics for each model
    all_metrics = {}

    # Get test statements for detailed comparison
    from llm_evals.test_data import get_test_statements
    test_statements = get_test_statements()

    # Extract gold standard results once
    gold_results = evaluator._extract_metadata_with_gold_standard(test_statements)

    # Dictionary to store test results for each model
    test_results_by_model = {}

    # Evaluate each model
    for model_name in model:
        try:
            logger.info(f"Evaluating model: {model_name}")

            # Modify the evaluate_model method to not generate individual reports
            metrics = evaluator.evaluate_model(model_name, output_dir=None)  # Pass None to avoid generating individual reports
            logger.info(f"Evaluation complete for {model_name}")

            # Store metrics for HTML report
            all_metrics[model_name] = metrics

            # Store test results for detailed comparison
            test_results_by_model[model_name] = evaluator._extract_metadata_with_test_model(model_name, test_statements)

        except Exception as e:
            logger.exception(f"Error evaluating model {model_name}: {str(e)}")

    # Save combined results
    combined_results_path = os.path.join(output_dir, "metadata_eval_results.json")
    with open(combined_results_path, "w") as f:
        json.dump(all_metrics, f, indent=2)

    logger.info(f"All model evaluations complete. Results saved to {combined_results_path}")

    # Generate a single HTML report if requested
    if html_report and all_metrics:
        try:
            # Use the first model's test results for detailed comparison
            first_model = next(iter(test_results_by_model.keys())) if test_results_by_model else None
            test_results = test_results_by_model.get(first_model, []) if first_model else []

            report_path = generate_html_report(
                all_metrics,
                output_dir=output_dir,
                open_browser=open_browser,
                gold_results=gold_results,
                test_results=test_results
            )

            logger.info(f"Single aggregate HTML report generated at {report_path}")

        except Exception as e:
            logger.exception(f"Error generating HTML report: {str(e)}")


@llm_evals_group.command(name="evaluate-with-deepeval")
@click.option("--model", "-m", multiple=True, help="Model(s) to evaluate")
@click.option("--gold-standard", default="gemini-2.5-pro-preview-05-06", help="Gold standard model")
@click.option("--output-dir", default=None, help="Directory to save evaluation results")
@click.option("--debug", is_flag=True, help="Enable debug logging")
def evaluate_with_deepeval_command(model, gold_standard, output_dir, debug):
    """
    Evaluate metadata extraction using DeepEval metrics.

    Args:
        model: Model(s) to evaluate
        gold_standard: Gold standard model
        output_dir: Directory to save evaluation results
        debug: Enable debug logging
    """
    # Setup logging
    setup_logging(debug)

    # Set default output directory if not provided
    if output_dir is None:
        output_dir = os.path.join(eko_var_path, "reports/llm_evals/deepeval")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Set default models if not provided
    if not model:
        model = ["gemini-1.5-pro", "claude-3-5-sonnet-20240620"]

    # Initialize evaluator
    evaluator = MetadataEvaluator(gold_standard_model=gold_standard)

    # Evaluate each model
    for model_name in model:
        try:
            logger.info(f"Evaluating model with DeepEval: {model_name}")
            metrics = evaluator.evaluate_with_deepeval(model_name, output_dir=output_dir)
            logger.info(f"DeepEval evaluation complete for {model_name}. Average score: {metrics.get('avg_deepeval_score', 0)}")
        except Exception as e:
            logger.exception(f"Error evaluating model with DeepEval {model_name}: {str(e)}")


@llm_evals_group.command(name="evaluate-all")
@click.option("--model", "-m", multiple=True, help="Model(s) to evaluate")
@click.option("--gold-standard", default="gemini-2.5-pro-preview-05-06", help="Gold standard model")
@click.option("--output-dir", default=None, help="Directory to save evaluation results")
@click.option("--debug", is_flag=True, help="Enable debug logging")
@click.option("--html-report", is_flag=True, default=True, help="Generate HTML report with comparisons")
@click.option("--open-browser", is_flag=True, default=True, help="Open HTML report in browser")
def evaluate_all_command(model, gold_standard, output_dir, debug, html_report, open_browser):
    """
    Run all evaluations on the specified models.

    Args:
        model: Model(s) to evaluate
        gold_standard: Gold standard model
        output_dir: Directory to save evaluation results
        debug: Enable debug logging
        html_report: Generate HTML report with comparisons
        open_browser: Open HTML report in browser
    """
    # Setup logging
    setup_logging(debug)

    # Set default output directory if not provided
    if output_dir is None:
        output_dir = os.path.join(eko_var_path, "reports/llm_evals")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Set default models if not provided
    if not model:
        model = ["gemini-1.5-pro", "claude-3-5-sonnet-20240620"]

    # Initialize evaluators
    metadata_evaluator = MetadataEvaluator(gold_standard_model=gold_standard)

    # Import DEMISEEvaluator here to avoid circular imports
    from llm_evals.demise_eval import DEMISEEvaluator
    demise_evaluator = DEMISEEvaluator(gold_standard_model=gold_standard)

    # Get test statements for detailed comparison
    from llm_evals.test_data import get_test_statements
    test_statements = get_test_statements()

    # Extract gold standard results once for each evaluator
    metadata_gold_results = metadata_evaluator._extract_metadata_with_gold_standard(test_statements)
    demise_gold_results = demise_evaluator._extract_demise_with_gold_standard(test_statements)

    # Dictionaries to store metrics and test results for each model
    metadata_metrics = {}
    demise_metrics = {}
    metadata_test_results = {}
    demise_test_results = {}

    # Evaluate each model
    for model_name in model:
        try:
            # Metadata evaluation
            logger.info(f"Evaluating metadata extraction for model: {model_name}")
            metrics = metadata_evaluator.evaluate_model(model_name, output_dir=None)
            metadata_metrics[model_name] = metrics
            metadata_test_results[model_name] = metadata_evaluator._extract_metadata_with_test_model(model_name, test_statements)

            # DEMISE evaluation
            logger.info(f"Evaluating DEMISE extraction for model: {model_name}")
            metrics = demise_evaluator.evaluate_model(model_name, output_dir=None)
            demise_metrics[model_name] = metrics
            demise_test_results[model_name] = demise_evaluator._extract_demise_with_test_model(model_name, test_statements)

        except Exception as e:
            logger.exception(f"Error evaluating model {model_name}: {str(e)}")

    # Save combined results
    metadata_results_path = os.path.join(output_dir, "metadata_eval_results.json")
    with open(metadata_results_path, "w") as f:
        json.dump(metadata_metrics, f, indent=2)

    demise_results_path = os.path.join(output_dir, "demise_eval_results.json")
    with open(demise_results_path, "w") as f:
        json.dump(demise_metrics, f, indent=2)

    logger.info(f"Metadata evaluation results saved to {metadata_results_path}")
    logger.info(f"DEMISE evaluation results saved to {demise_results_path}")

    # Generate a single HTML report if requested
    if html_report:
        try:
            # Combine all results
            combined_results = {}

            # Add metadata results
            for model_name in metadata_metrics:
                combined_results[f"{model_name}_metadata"] = metadata_metrics[model_name]

            # Add DEMISE results
            for model_name in demise_metrics:
                combined_results[f"{model_name}_demise"] = demise_metrics[model_name]

            # Use the first model's test results for detailed comparison
            first_model = next(iter(metadata_test_results.keys())) if metadata_test_results else None

            # Generate HTML report
            from llm_evals.html_report import generate_html_report
            report_path = generate_html_report(
                combined_results,
                output_dir=output_dir,
                open_browser=open_browser,
                gold_results={
                    "metadata": metadata_gold_results,
                    "demise": demise_gold_results
                },
                test_results={
                    "metadata": metadata_test_results.get(first_model, []) if first_model else [],
                    "demise": demise_test_results.get(first_model, []) if first_model else []
                }
            )

            logger.info(f"Single aggregate HTML report generated at {report_path}")

        except Exception as e:
            logger.exception(f"Error generating HTML report: {str(e)}")

    # Run DeepEval evaluation if requested
    try:
        logger.info("Running DeepEval evaluation...")
        for model_name in model:
            metrics = metadata_evaluator.evaluate_with_deepeval(model_name, output_dir)
            logger.info(f"DeepEval evaluation complete for {model_name}. Average score: {metrics.get('avg_deepeval_score', 0)}")
    except Exception as e:
        logger.exception(f"Error running DeepEval evaluation: {str(e)}")

    logger.info("All evaluations complete!")
