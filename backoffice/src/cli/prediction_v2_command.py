"""
CLI commands for predictive analytics v2.
"""

import click

from eko.prediction_v2.cli import (
    analyze_component_trends,
    create_dso_clusters_cmd,
    generate_component_analysis_cmd,
    visualize_cluster_trends_cmd,
    generate_report_cmd
)


@click.group(name="prediction-v2")
def prediction_v2():
    """Commands for predictive analytics v2."""
    pass


# Register commands from eko.prediction_v2.cli
prediction_v2.add_command(analyze_component_trends)
prediction_v2.add_command(create_dso_clusters_cmd)
prediction_v2.add_command(generate_component_analysis_cmd)
prediction_v2.add_command(visualize_cluster_trends_cmd)
prediction_v2.add_command(generate_report_cmd)
