"""
Utility functions for LLM evaluations.
"""

import os
import json
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path
from loguru import logger

from eko import eko_var_path
from eko.models import SimpleEntity, QuantifiedEntity
from eko.models.vector.demise.domain import DomainModel, DOMAIN_FEATURE_NAMES


def setup_logging(log_level: str = "INFO") -> None:
    """
    Set up logging configuration.

    Args:
        log_level: Logging level (INFO, DEBUG, etc.)
    """
    log_dir = os.path.join(eko_var_path, "logs", "llm_evals")
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, f"llm_eval_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    logger.remove()  # Remove default handler
    logger.add(log_file, level=log_level)
    logger.add(lambda msg: print(msg), level=log_level)

    logger.info(f"Logging set up at {log_file}")

#
# @deprecated
# def compare_models(gold_results: List[Dict[str, Any]], test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
#     """
#     Compare the results from the gold standard model with the test model.
#
#     Args:
#         gold_results: Results from the gold standard model
#         test_results: Results from the test model
#
#     Returns:
#         Dictionary containing comparison metrics
#     """
#     if len(gold_results) != len(test_results):
#         raise ValueError(f"Gold and test results have different lengths: {len(gold_results)} vs {len(test_results)}")
#
#     metrics = {
#         "total_samples": len(gold_results),
#         "metadata_scores": [],
#         "demise_scores": [],
#         "overall_scores": [],
#         "field_scores": {},
#         "vector_similarities": []
#     }
#
#     for i, (gold, test) in enumerate(zip(gold_results, test_results)):
#         # Calculate metadata similarity score
#         metadata_score = calculate_metadata_similarity(gold.get("metadata", {}), test.get("metadata", {}))
#         metrics["metadata_scores"].append(metadata_score)
#
#         # Calculate DEMISE model similarity score
#         demise_score, vector_sim = calculate_demise_similarity(gold.get("demise", {}), test.get("demise", {}))
#         metrics["demise_scores"].append(demise_score)
#         metrics["vector_similarities"].append(vector_sim)
#
#         # Calculate overall score (weighted average)
#         overall_score = 0.5 * metadata_score + 0.5 * demise_score
#         metrics["overall_scores"].append(overall_score)
#
#         # Track field-level scores
#         field_scores = calculate_field_scores(gold.get("metadata", {}), test.get("metadata", {}))
#         for field, score in field_scores.items():
#             if field not in metrics["field_scores"]:
#                 metrics["field_scores"][field] = []
#             metrics["field_scores"][field].append(score)
#
#     # Calculate aggregate statistics
#     metrics["avg_metadata_score"] = np.mean(metrics["metadata_scores"])
#     metrics["avg_demise_score"] = np.mean(metrics["demise_scores"])
#     metrics["avg_overall_score"] = np.mean(metrics["overall_scores"])
#     metrics["avg_vector_similarity"] = np.mean(metrics["vector_similarities"])
#
#     # Calculate field-level averages
#     metrics["avg_field_scores"] = {
#         field: np.mean(scores) for field, scores in metrics["field_scores"].items()
#     }
#
#     return metrics
#

def compare_metadata_results(model_name:str, gold_results: List[Dict[str, Any]], test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Compare metadata extraction results between gold standard and test model.

    Args:
        gold_results: List of gold standard results
        test_results: List of test model results

    Returns:
        Dictionary containing comparison metrics
    """
    logger.info("Comparing metadata extraction results")

    # Initialize metrics
    metrics = {
        "total_statements": len(gold_results),
        "successful_extractions": 0,
        "failed_extractions": 0,
        "field_metrics": {},
        "overall_accuracy": 0.0,
        "field_accuracy": {},
        "statement_metrics": []
    }

    # Create a mapping of statement IDs to results
    gold_map = {result["statement_id"]: result for result in gold_results}
    test_map = {result["statement_id"]: result for result in test_results}

    # Get common statement IDs
    common_ids = set(gold_map.keys()).intersection(set(test_map.keys()))

    # Count successful and failed extractions
    for statement_id in common_ids:
        if test_map[statement_id]["success"]:
            metrics["successful_extractions"] += 1
        else:
            metrics["failed_extractions"] += 1

    # Calculate field-level metrics
    field_counts = {}
    field_matches = {}

    for statement_id in common_ids:
        gold_result = gold_map[statement_id]
        test_result = test_map[statement_id]

        # Skip failed extractions
        if not test_result["success"]:
            continue

        gold_metadata = gold_result["metadata"]
        test_metadata = test_result["metadata"]

        # Calculate statement-level metrics
        statement_metric = {
            "statement_id": statement_id,
            "field_matches": {},
            "overall_match": 0.0
        }

        # Compare fields
        all_fields = set(gold_metadata.keys()).union(set(test_metadata.keys()))
        matched_fields = 0

        for field in all_fields:
            # Initialize field counts if not already done
            if field not in field_counts:
                field_counts[field] = 0
                field_matches[field] = 0

            field_counts[field] += 1

            # Check if field exists in both results
            if field in gold_metadata and field in test_metadata:
                # Compare field values
                gold_value = gold_metadata[field]
                test_value = test_metadata[field]
                match = calculate_field_similarity(gold_value, test_value, field)
                if match is None:
                    # Handle different field types
                    if isinstance(gold_value, list) and isinstance(test_value, list):
                        # For lists, calculate Jaccard similarity
                        if not gold_value and not test_value:
                            match = 1.0
                        else:
                            gold_set = set(str(item) for item in gold_value)
                            test_set = set(str(item) for item in test_value)
                            intersection = len(gold_set.intersection(test_set))
                            union = len(gold_set.union(test_set))
                            match = intersection / union if union > 0 else 0.0
                    elif isinstance(gold_value, dict) and isinstance(test_value, dict):
                        # For dictionaries, calculate key-value match ratio
                        all_keys = set(gold_value.keys()).union(set(test_value.keys()))
                        matched_keys = 0

                        for key in all_keys:
                            if key in gold_value and key in test_value and gold_value[key] == test_value[key]:
                                matched_keys += 1

                        match = matched_keys / len(all_keys) if all_keys else 1.0
                    else:
                        # For simple values, check exact match
                        match = 1.0 if gold_value == test_value else 0.0

                # Update field matches
                field_matches[field] += match
                statement_metric["field_matches"][field] = match

                if match > 0.5:
                    matched_fields += 1
                else:
                    logger.warning(f"{model_name}/{statement_id}: Field mismatch for {field}: {gold_value} vs {test_value}")

        # Calculate overall match for the statement
        statement_metric["overall_match"] = matched_fields / len(all_fields) if all_fields else 1.0
        metrics["statement_metrics"].append(statement_metric)

    # Calculate field accuracy
    for field in field_counts:
        if field_counts[field] > 0:
            metrics["field_accuracy"][field] = field_matches[field] / field_counts[field]

    # Calculate overall accuracy
    if metrics["field_accuracy"]:
        metrics["overall_accuracy"] = sum(metrics["field_accuracy"].values()) / len(metrics["field_accuracy"])

    logger.info(f"Comparison complete. Overall accuracy: {metrics['overall_accuracy']:.2f}")
    return metrics


def compare_demise_results(gold_results: List[Dict[str, Any]], test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Compare DEMISE extraction results between gold standard and test model.

    Args:
        gold_results: List of gold standard results
        test_results: List of test model results

    Returns:
        Dictionary containing comparison metrics
    """
    logger.info("Comparing DEMISE extraction results")

    # Initialize metrics
    metrics = {
        "total_statements": len(gold_results),
        "successful_extractions": 0,
        "failed_extractions": 0,
        "component_metrics": {},
        "overall_accuracy": 0.0,
        "component_accuracy": {},
        "statement_metrics": []
    }

    # Create a mapping of statement IDs to results
    gold_map = {result["statement_id"]: result for result in gold_results}
    test_map = {result["statement_id"]: result for result in test_results}

    # Get common statement IDs
    common_ids = set(gold_map.keys()).intersection(set(test_map.keys()))

    # Count successful and failed extractions
    for statement_id in common_ids:
        if test_map[statement_id]["success"]:
            metrics["successful_extractions"] += 1
        else:
            metrics["failed_extractions"] += 1

    # Define DEMISE components
    components = [
        "domain", "impact", "subject", "object",
        "statement", "engagement", "motivation"
    ]

    # Calculate component-level metrics
    component_counts = {component: 0 for component in components}
    component_matches = {component: 0 for component in components}

    for statement_id in common_ids:
        gold_result = gold_map[statement_id]
        test_result = test_map[statement_id]

        # Skip failed extractions
        if not test_result["success"]:
            continue

        gold_demise = gold_result["demise"]
        test_demise = test_result["demise"]

        # Calculate statement-level metrics
        statement_metric = {
            "statement_id": statement_id,
            "component_matches": {},
            "overall_match": 0.0
        }

        # Compare components
        matched_components = 0

        for component in components:
            component_counts[component] += 1

            # Check if component exists in both results
            if component in gold_demise and component in test_demise:
                gold_component = gold_demise[component]
                test_component = test_demise[component]

                # Calculate component similarity
                match = calculate_component_similarity(gold_component, test_component)

                # Update component matches
                component_matches[component] += match
                statement_metric["component_matches"][component] = match

                if match > 0.5:
                    matched_components += 1

        # Calculate overall match for the statement
        statement_metric["overall_match"] = matched_components / len(components) if components else 1.0
        metrics["statement_metrics"].append(statement_metric)

    # Calculate component accuracy
    for component in component_counts:
        if component_counts[component] > 0:
            metrics["component_accuracy"][component] = component_matches[component] / component_counts[component]

    # Calculate overall accuracy
    if metrics["component_accuracy"]:
        metrics["overall_accuracy"] = sum(metrics["component_accuracy"].values()) / len(metrics["component_accuracy"])

    logger.info(f"Comparison complete. Overall accuracy: {metrics['overall_accuracy']:.2f}")
    return metrics


def calculate_component_similarity(gold_component: Dict[str, float], test_component: Dict[str, float]) -> float:
    """
    Calculate similarity between two DEMISE components.

    Args:
        gold_component: Gold standard component
        test_component: Test component

    Returns:
        Similarity score between 0 and 1
    """
    # Get all keys from both components
    all_keys = set(gold_component.keys()).union(set(test_component.keys()))

    if not all_keys:
        return 1.0  # Both empty, perfect match

    total_diff = 0.0

    for key in all_keys:
        gold_value = gold_component.get(key, 0.0)
        test_value = test_component.get(key, 0.0)

        # Calculate absolute difference
        diff = abs(gold_value - test_value)
        total_diff += diff

    # Calculate average difference
    avg_diff = total_diff / len(all_keys)

    # Convert to similarity score (1 - normalized difference)
    similarity = 1.0 - min(avg_diff, 1.0)

    return similarity


def calculate_metadata_similarity(gold_metadata: Dict[str, Any], test_metadata: Dict[str, Any]) -> float:
    """
    Calculate similarity score between gold and test metadata.

    Args:
        gold_metadata: Metadata from gold standard model
        test_metadata: Metadata from test model

    Returns:
        Similarity score between 0 and 1
    """
    if not gold_metadata or not test_metadata:
        return 0.0

    # Define important fields and their weights
    fields = {
        "statement_category": 0.2,
        "domain": 0.15,
        "impact": 0.15,
        "impact_value": 0.2,
        "action": 0.1,
        "is_impactful_action": 0.1,
        "subject_entities": 0.05,
        "object_entities": 0.05
    }

    total_score = 0.0
    total_weight = 0.0

    for field, weight in fields.items():
        if field in gold_metadata and field in test_metadata:
            field_score = calculate_field_similarity(gold_metadata[field], test_metadata[field], field)
            total_score += field_score * weight
            total_weight += weight

    if total_weight == 0:
        return 0.0

    return total_score / total_weight


def calculate_field_similarity(gold_value: Any, test_value: Any, field_name: str) -> Optional[float]:
    """
    Calculate similarity score for a specific metadata field.

    Args:
        gold_value: Field value from gold standard model
        test_value: Field value from test model
        field_name: Name of the field

    Returns:
        Similarity score between 0 and 1
    """
    # Handle different field types
    if field_name == "impact_value":
        # For numerical values, calculate similarity based on absolute difference
        max_diff = 2.0  # Maximum possible difference is 2 (-1 to 1)
        diff = abs(gold_value - test_value)/(gold_value or 1.0)
        return max(0, 1 - (diff / max_diff))
    elif field_name == "impact":
        if gold_value and test_value or not gold_value and not test_value:
            return 1.0
        else:
            return 0.0
    elif field_name == "action":
        if gold_value and test_value or not gold_value and not test_value:
            return 1.0
        else:
            return 0.0
    elif field_name == "domain":
        if gold_value == test_value:
            return 1.0
        elif test_value in DOMAIN_FEATURE_NAMES:
            return 0.8
        else:

            return 0.0
    elif field_name == "quantities":
        if gold_value and test_value:
            if len(gold_value) != len(test_value):
                return 0.1
            for gold_quantity, test_quantity in zip(gold_value, test_value):
                value = 0.0
                if gold_quantity.get("amount") == test_quantity.get("amount"):
                    value += 0.5
                if gold_quantity.get("quantity_type") == test_quantity.get("quantity_type"):
                    value += 0.2
                if gold_quantity.get("unit") == test_quantity.get("unit"):
                    value += 0.2
                if gold_quantity.get("delta") == test_quantity.get("delta"):
                    value += 0.1
                return value
        else:
           if not gold_value and not test_value:
               return 1.0
           else:
               return 0.0

    elif field_name in ["subject_entities", "object_entities", "authors"]:
        # For entity lists, calculate Jaccard similarity
        if not gold_value and not test_value:
            return 1.0
        if not gold_value or not test_value:
            return 0.0

        # Extract entity names for comparison
        gold_names = []
        test_names = []

        # Debug log the input values
        logger.debug(f"Comparing {field_name}: gold={gold_value}, test={test_value}")

        # Process gold_value
        for item in gold_value:
            name = item["name"]
            type = item["entity_type"]
            if name:
                gold_names.append(name.lower()+":"+type)

        # Process test_value
        for item in test_value:
            name = item["name"]
            type = item["entity_type"]
            if name:
                test_names.append(name.lower()+":"+type)

        # Debug log the extracted names
        logger.debug(f"Extracted names for {field_name}: gold_names={gold_names}, test_names={test_names}")

        # If both lists are empty after extraction, consider it a match
        if not gold_names and not test_names:
            return 1.0

        # If either list is empty after extraction, consider it a mismatch
        if not gold_names or not test_names:
            return 0.0

        # Calculate Jaccard similarity
        intersection = len(set(gold_names) & set(test_names))
        union = len(set(gold_names) | set(test_names))

        similarity = intersection / union if union > 0 else 0.0
        logger.debug(f"Similarity for {field_name}: {similarity} (intersection={intersection}, union={union})")
        if similarity < 0.5:
            logger.warning(f"Failed match for {field_name}: gold={json.dumps(gold_value)}, test={json.dumps(test_value)}")
        return similarity

    elif field_name == "locations":
        # For entity lists, calculate Jaccard similarity
        if not gold_value and not test_value:
            return 1.0
        if not gold_value or not test_value:
            return 0.0

        # Extract entity names for comparison
        gold_names = []
        test_names = []

        # Debug log the input values
        logger.debug(f"Comparing {field_name}: gold={gold_value}, test={test_value}")

        # Process gold_value
        for item in gold_value:
            name = item["name"]
            type = item["location_type"]
            if name:
                gold_names.append(name.lower()+":"+type)

        # Process test_value
        for item in test_value:
            name = item["name"]
            type = item["location_type"]
            if name:
                test_names.append(name.lower()+":"+type)

        # Debug log the extracted names
        logger.debug(f"Extracted names for {field_name}: gold_names={gold_names}, test_names={test_names}")

        # If both lists are empty after extraction, consider it a match
        if not gold_names and not test_names:
            return 1.0

        # If either list is empty after extraction, consider it a mismatch
        if not gold_names or not test_names:
            return 0.0

        # Calculate Jaccard similarity
        intersection = len(set(gold_names) & set(test_names))
        union = len(set(gold_names) | set(test_names))

        similarity = intersection / union if union > 0 else 0.0
        logger.debug(f"Similarity for {field_name}: {similarity} (intersection={intersection}, union={union})")
        if similarity < 0.5:
            logger.warning(f"Failed match for {field_name}: gold={json.dumps(gold_value)}, test={json.dumps(test_value)}")
        return similarity

    elif field_name == "is_impactful_action":
        # For boolean values, exact match
        return 1.0 if gold_value == test_value else 0.0

    # Use default similarity calculation for other fields
    return None


def calculate_field_scores(gold_metadata: Dict[str, Any], test_metadata: Dict[str, Any]) -> Dict[str, float]:
    """
    Calculate similarity scores for each field in the metadata.

    Args:
        gold_metadata: Metadata from gold standard model
        test_metadata: Metadata from test model

    Returns:
        Dictionary of field names to similarity scores
    """
    fields = [
        "statement_category", "domain", "impact", "impact_value",
        "action", "is_impactful_action", "subject_entities", "object_entities"
    ]

    field_scores = {}

    for field in fields:
        if field in gold_metadata and field in test_metadata:
            field_scores[field] = calculate_field_similarity(
                gold_metadata[field], test_metadata[field], field
            )
        else:
            field_scores[field] = 0.0

    return field_scores


def calculate_demise_similarity(gold_demise: Dict[str, Any], test_demise: Dict[str, Any]) -> Tuple[float, float]:
    """
    Calculate similarity score between gold and test DEMISE models.

    Args:
        gold_demise: DEMISE model from gold standard
        test_demise: DEMISE model from test model

    Returns:
        Tuple of (overall similarity score, vector similarity)
    """
    if not gold_demise or not test_demise:
        return 0.0, 0.0

    # Convert sparse KV representations to vectors if needed
    gold_vector = gold_demise.get("vector", None)
    test_vector = test_demise.get("vector", None)

    # If vectors are not available, use component-wise comparison
    if not gold_vector or not test_vector:
        # Define DEMISE components and their weights
        components = {
            "domain": 0.2,
            "entity": 0.15,
            "motivation": 0.15,
            "impact": 0.2,
            "statement": 0.15,
            "engagement": 0.15
        }

        total_score = 0.0
        total_weight = 0.0

        for component, weight in components.items():
            if component in gold_demise and component in test_demise:
                # Calculate component similarity (simplified)
                component_score = 0.5  # Placeholder for component-wise comparison
                total_score += component_score * weight
                total_weight += weight

        if total_weight == 0:
            return 0.0, 0.0

        return total_score / total_weight, 0.0

    # Calculate cosine similarity between vectors
    gold_vector = np.array(gold_vector)
    test_vector = np.array(test_vector)

    # Normalize vectors
    gold_norm = np.linalg.norm(gold_vector)
    test_norm = np.linalg.norm(test_vector)

    if gold_norm == 0 or test_norm == 0:
        return 0.0, 0.0

    gold_vector = gold_vector / gold_norm
    test_vector = test_vector / test_norm

    # Calculate cosine similarity
    cosine_sim = np.dot(gold_vector, test_vector)

    # Scale to 0-1 range
    similarity = (cosine_sim + 1) / 2

    return similarity, cosine_sim


def generate_report(
    metrics: Dict[str, Any],
    model_name: str,
    output_dir: Optional[str] = None,
    gold_results: Optional[List[Dict[str, Any]]] = None,
    test_results: Optional[List[Dict[str, Any]]] = None
) -> str:
    """
    Generate a report from the evaluation metrics.

    Args:
        metrics: Evaluation metrics
        model_name: Name of the model being evaluated
        output_dir: Directory to save the report (default: eko_var_path/reports/llm_evals)
        gold_results: Optional list of gold standard results for detailed comparison
        test_results: Optional list of test model results for detailed comparison

    Returns:
        Path to the generated report
    """
    if output_dir is None:
        output_dir = os.path.join(eko_var_path, "reports", "llm_evals")

    os.makedirs(output_dir, exist_ok=True)

    # Clean model name for file path
    safe_model_name = model_name.replace("/", "_")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = os.path.join(output_dir, f"{safe_model_name}_eval_{timestamp}.json")

    # Save metrics to JSON file
    with open(report_file, "w") as f:
        json.dump(metrics, f, indent=2)

    # Generate visualizations
    generate_visualizations(metrics, model_name, output_dir, timestamp)

    # Create a model_metrics dictionary for HTML report
    model_metrics = {model_name: metrics}

    # Generate HTML report
    from llm_evals.html_report import generate_html_report
    html_report_path = generate_html_report(
        model_metrics,
        output_dir,
        open_browser=True,
        gold_results=gold_results,
        test_results=test_results
    )

    logger.info(f"Report generated at {report_file}")
    logger.info(f"HTML report generated at {html_report_path}")

    return report_file


def generate_visualizations(metrics: Dict[str, Any], model_name: str, output_dir: str, timestamp: str) -> None:
    """
    Generate visualizations from the evaluation metrics.

    Args:
        metrics: Evaluation metrics
        model_name: Name of the model being evaluated
        output_dir: Directory to save the visualizations
        timestamp: Timestamp for file naming
    """
    # Create a directory for visualizations
    viz_dir = os.path.join(output_dir, f"{model_name.replace('/', '_')}_eval_{timestamp}")
    os.makedirs(viz_dir, exist_ok=True)

    # 1. Overall accuracy
    plt.figure(figsize=(10, 6))
    plt.bar(["Overall Accuracy"], [metrics.get("overall_accuracy", 0.0)], color="blue")
    plt.ylim(0, 1)
    plt.title(f"Overall Accuracy for {model_name}")
    plt.ylabel("Accuracy (0-1)")
    plt.savefig(os.path.join(viz_dir, "overall_accuracy.png"))
    plt.close()

    # 2. Field-level accuracy
    if metrics.get("field_accuracy"):
        plt.figure(figsize=(12, 6))
        field_accuracy = metrics["field_accuracy"]
        plt.bar(field_accuracy.keys(), field_accuracy.values(), color="skyblue")
        plt.ylim(0, 1)
        plt.title(f"Field-level Accuracy for {model_name}")
        plt.ylabel("Accuracy (0-1)")
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()
        plt.savefig(os.path.join(viz_dir, "field_accuracy.png"))
        plt.close()

    # 3. Extraction success rate
    plt.figure(figsize=(10, 6))
    total = metrics.get("total_statements", 0)
    if total > 0:
        success_rate = metrics.get("successful_extractions", 0) / total
        failure_rate = metrics.get("failed_extractions", 0) / total
        plt.bar(["Success Rate", "Failure Rate"], [success_rate, failure_rate], color=["green", "red"])
        plt.ylim(0, 1)
        plt.title(f"Extraction Success Rate for {model_name}")
        plt.ylabel("Rate (0-1)")
        plt.savefig(os.path.join(viz_dir, "extraction_success_rate.png"))
        plt.close()

    logger.info(f"Visualizations generated in {viz_dir}")
