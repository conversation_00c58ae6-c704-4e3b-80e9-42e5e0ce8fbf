"""
LLM Evaluation module for testing various models against metadata and DEMISE extraction.

This module provides tools to evaluate different LLM models against a gold standard
(gemini-2.5-pro-preview-05-06) for metadata and DEMISE extraction tasks.
"""

from .test_data import get_test_statements
from .utils import setup_logging, compare_metadata_results, generate_report
from .metadata_eval import MetadataEvaluator
# from .demise_eval import DEMISEEvaluator
# from .run_evals import run_metadata_eval, run_demise_eval
