"""
Static test data for LLM evaluations.

This module contains static test statements and their metadata for evaluating
LLM models on metadata and DEMISE extraction tasks.
"""

from typing import List, Dict, Any

# Test statements for metadata and DEMISE extraction
TEST_STATEMENTS = [
    {
        "id": 1630246,
        "statement_text": "Sidonios and Impetus have shipped ATHLETA-branded clothing to Athleta in the US.",
        "context": "<PERSON> (sitting as a Deputy High Court Judge) Athleta v Sports Group Denmark\n\nApproved Judgment\n\n63. SGD accepted the following for the purposes of these proceedings:\n\ni) Sidonios and Impetus have manufactured ATHLETA -branded clothing in Portugal and have then shipped that clothing to Athleta in the US or, on a small number of occasions, the United Kingdom (to Gap (UK) for onward distribution). Sidonios shipped 180 tops to Gap (UK) as part of two orders; and Impetus shipped 200 pullovers to Gap (UK) as part of five orders. However, SGDs counsel submitted that this amounts to internal use and cannot assist Athleta in proving use of the ATHLETA Marks in the European Union or in the United Kingdom;",
        "title": "Athleta v Sports Group Denmark",
        "authors": ["<PERSON>"],
        "doc_year": 2022,
        "gold_model_json": {
            "domain": {
                "legal": {"transnational_law": 0.5, "intellectual_property": 0.6},
                "industry": {"commerce": 0.8, "manufacturing": 0.7},
            },
            "object": {
                "entity_type": {
                    "geopolitical": 1.0,
                }
            },
            "subject": {"entity_type": {"organizations": 0.9}},
            "statement": {
                "descriptive": 1.0,
                "past_statement": 1.0,
            },
            "motivation": {"genuine": 0.0, "compliant": 0.0, "pressured": 0.0, "superficial": 0.0, "opportunistic": 0.0},
        },
        "gold_metadata": {
            "company": "Athleta",
            "subject_entities": [
                {"name": "Sidonios", "entity_type": "company"},
                {"name": "Impetus", "entity_type": "company"},
            ],
            "object_entities": [
                {"name": "Athleta", "entity_type": "company"},
                {"name": "ATHLETA-branded clothing", "entity_type": "product"},
            ],
            "authors": [{"name": "David Stone", "entity_type": "person"}],
            "domain": "industry.commerce",
            "is_disclosure": False,
            "is_impactful_action": False,
            "impact_value": 0.0,
            "action": "shipped",
            "locations": [{"name": "US", "location_type": "country"}],
            "time": None,
            "start_year": 2022,
            "end_year": 2022,
            "quantities": [],
            "is_environmental": False,
            "is_social": False,
            "is_vague": False,
            "is_governance": False,
            "is_animal_welfare": False,
            "statement_category": "fact",
        },
    },
    {
        "id": 1580715,
        "statement_text": "The Partnership Finance team works closely with the E&S team to support the delivery of the E&S strategy.",
        "context": "ESC member Brangre Michel, Executive Director, Finance, sponsors the Partnerships TCFD programme.\n\nAs defined by its terms of reference, the purpose of the ESC, chaired by the Partnerships Chairman, is to assist the Board in fulfilling its responsibilities for setting the Ethics and Sustainability (E&S) strategy in accordance with the Partnership's Purpose and Values, as well as ensuring that the E&S strategy is embedded into the Partnerships operations. For more detail on the full role of the ESC, see page 64. Full terms of reference can be found at www.johnlewispartnership.co.uk.",
        "title": "John Lewis Partnership plc Annual Report and Accounts 2022",
        "authors": ["John Lewis Partnership plc"],
        "doc_year": 2022,
        "gold_model_json": {
            "domain": {
                "governance": {"general": 0.7, "communication": 0.6, "stakeholder_engagement": 0.5},
                "society": {"general": 0.3},
                "industry": {"finance": 0.8},
            },
            "impact": {"benefit_to_object_entity": 0.4},
            "object": {"entity_type": {"organizations": 0.8}},
            "subject": {"entity_type": {"organizations": 0.9}},
            "statement": {"descriptive": 1.0, "present_statement": 1.0, "describes_action": 0.8},
            "engagement": {"proactive": 0.7, "responsive": 0.5},
            "motivation": {"genuine": 0.6, "compliant": 0.4},
        },
        "gold_metadata": {
            "company": "John Lewis Partnership",
            "subject_entities": [{"name": "Partnership Finance team", "entity_type": "group_of_people"}],
            "object_entities": [
                {"name": "E&S team", "entity_type": "organisation"},
                {"name": "E&S strategy", "entity_type": "strategy"},
            ],
            "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company"}],
            "domain": "governance.general",
            "is_impactful_action": True,
            "impact_value": 0.4,
            "impact": "support",
            "action": "works closely with",
            "locations": [],
            "time": None,
            "start_year": 2022,
            "end_year": 2022,
            "quantities": [],
            "is_environmental": False,
            "is_social": False,
            "is_governance": True,
            "is_animal_welfare": False,
            "is_vague": False,
            "statement_category": "action",
            "is_disclosure": True,
        },
    },
    {
        "id": 1234567,
        "statement_text": "We have reduced our Scope 1 and 2 carbon emissions by 45% since 2015.",
        "context": "Climate Change and Carbon Emissions\n\nWe are committed to reducing our environmental impact and have made significant progress in recent years. Our carbon reduction initiatives have been recognized by industry experts and we continue to invest in renewable energy and energy efficiency measures across our operations.",
        "title": "Sustainability Report 2023",
        "authors": ["Example Corporation"],
        "doc_year": 2023,
        "gold_model_json": {
            "domain": {
                "environment": {"climate": 0.9, "climate_change": 0.8, "climate_change_c_o2": 0.9, "sustainability": 0.7},
                "energy": {"efficiency": 0.6},
            },
            "impact": {"benefit_to_environment": 0.8, "duration": 0.7},
            "object": {"entity_type": {"environment": 0.9}},
            "subject": {"entity_type": {"organizations": 0.9}},
            "statement": {"descriptive": 0.9, "past_statement": 1.0, "describes_action": 0.8, "beneficial_statement": 0.8},
            "engagement": {"proactive": 0.8},
            "motivation": {"genuine": 0.7},
        },
        "gold_metadata": {
            "company": "Example Corporation",
            "subject_entities": [{"name": "Example Corporation", "entity_type": "company"}],
            "object_entities": [{"name": "Scope 1 and 2 carbon emissions", "entity_type": "concept"}],
            "domain": "environment.climate",
            "is_impactful_action": True,
            "impact_value": 0.4,
            "impact": "reduction",
            "action": "reduced",
            "locations": [],
            "time": {"from_year": 2015, "to_year": 2023, "precision": "year"},
            "start_year": 2015,
            "end_year": 2023,
            "quantities": [{"amount": 45, "quantity_type": "percentage", "unit": "%", "delta": "decrease"}],
            "is_environmental": True,
            "is_social": False,
            "is_governance": False,
            "is_animal_welfare": False,
            "statement_category": "action",
            "is_disclosure": True,
            "authors": [{"name": "Example Corporation", "entity_type": "company"}],
            "is_vague": False,
        },
    },
    {
        "id": 1634385,
        "statement_text": "Morrisons included photographs of a group of employees who had participated in a Sue Ryder Charity Cycle Event astride exercise bikes and holding collecting buckets.",
        "context": "European Journal of Sustainable Development Research, 2(4), 46\n\n 2018 by Author/s 5 / 11 chain. Tescos update on its environmental strategy was prefaced by an underwater photograph of a shoal of fish\ndesigned to complement the story of the companys commitment to natural ecosystems.\n\nPhotographs and images were also used to illustrate a number of specific environmental, social and economic\nthemes as part of the corporate social responsibility reporting process. Dixons Carphone employed a photograph\nof an array of solar panels to illustrate its commitment to the environment and more specifically to energy\nmanagement. Details of Dixons Carphones partnership with The Mix, which works with young people facing\nproblems from bullying, debt, drink and drugs, mental health, sexual health, homelessness, money, to relationships,\nincluded a picture of HRH Prince Harry at a charity musical event organised by The Mix. Morrisons illustrated its\ncommitment to help British farmers to be competitive, profitable and sustainable with pictures of sheep and cattle grazing\nnaturally. The John Lewis Partnership outlined Waitroses description of its commitment to source and sell with\nintegrity which was illustrated by a photograph of a named lamb supplier pictured holding a traditional shepherds\ncrook in front of a flock of sheep in a rural landscape. More generally, a number of the photographs of people\nmodelling the companys range of clothes, in Nexts 2018 Corporate Responsibility Report are in natural settings.\nThese photographs included a young boy running on a beach with mountains in the background, two young girls surrounded by flowers in an open woodland environment and a young woman walking to a sand dune coastline\nwith a blue sea in the background. The underlying story here is of a company in harmony with the natural and\ncultural environment\n\nA number of the selected retailers used photographs, images and video clips in describing their commitment\nto their workforce. Sainsburys, for example illustrated its commitment to harness the talent, creativity and diversity of\nour colleagues, with a number of photographs of smiling employees in their uniforms within the workplace\nenvironment. Morrisons commitment to look after our colleagues was illustrated by photographs of a smiling\ncheckout operator scanning products and of two employees smiling and seemingly chatting while refilling shelves\nin store. The Cooperative illustrated its commitment to diversity and inclusion with a photograph of a group of\ncolleagues participating in the companys Diversity and Inclusion Pioneers programme in a seminar room. The general\nstory here is of a relaxed and happy working environment which offers a range of personal and professional\ndevelopment opportunities. Photographs were also used to emphasise the ways in which a number of the selected\nretailers work with communities as part of their corporate social responsibility commitments. Morrisons, for\nexample, included photographs of a group of employees who had participated in a Sue Ryder Charity Cycle Event\nastride exercise bikes and holding collecting buckets.",
        "title": "Storytelling and Corporate Social Responsibility Reporting: A Case Study of Leading UK Retailers",
        "authors": ["Peter Jones", "Daphne Comfort"],
        "doc_year": 2018,
        "gold_model_json": {
            "domain": {"society": {"social_good": 0.5, "community_development": 0.5}},
            "object": {"entity_type": {"organizations": 0.8}},
            "subject": {"entity_type": {"organizations": 0.9}},
            "statement": {"descriptive": 1.0, "past_statement": 1.0},
            "engagement": {"proactive": 0.7, "altruistic": 0.6},
            "motivation": {"opportunistic": 0.6},
        },
        "gold_metadata": {
            "company": "Morrisons",
            "subject_entities": [{"name": "Morrisons", "entity_type": "company"}],
            "object_entities": [
                {"name": "group of employees", "entity_type": "group_of_people"},
                {"name": "Sue Ryder Charity Cycle Event", "entity_type": "event"},
            ],
            "domain": "society.social_good",
            "is_impactful_action": False,
            "impact_value": 0.0,
            "impact": "charity support",
            "action": "included photographs",
            "locations": [],
            "time": None,
            "start_year": 2018,
            "end_year": 2018,
            "quantities": [],
            "is_environmental": False,
            "is_social": False,
            "is_governance": False,
            "is_animal_welfare": False,
            "statement_category": "fact",
            "is_disclosure": False,
            "authors": [{"name": "Peter Jones", "entity_type": "person"}, {"name": "Daphne Comfort", "entity_type": "person"}],
            "is_vague": False,
        },
    },
    {
        "id": 1603975,
        "statement_text": "The Partnership invested significantly more at the 2016 Pay Review.",
        "context": "John Lewis Partnership plc Annual Report and Accounts 2017\n\n## Its Your Business 22\n\n## Pay Review 2016\n\nWe invested significantly more at the 2016 Pay Review with a total spend of 4.36% across the Partnership compared to 2.56% in 2015. Additional annualised pay costs for our non-management Partners were 36m greater (3m of this was to fund the NLW).\n\n### Better pay\n\n### What have we done so far?\n\n### Beyond pay: exclusive benefits for all Partners\n\nAfter holding a number of roles in Waitrose, Kevin Rosales joined John Lewis as a Marketing Executive in 2016. Hes also the communications lead for Unity  the Partnerships Black, Asian and Minority Ethnic network  which officially launched in April 2016.\n\nAs a Partner with nine years service, Kevin values the Partnerships exclusive benefits. I use lots of the benefits available to me  particularly the discount! he says. I spend a lot of money on fashion annually and I really benefit from being able to shop brands that I love for less in John Lewis. I also visit Waitrose often and the discount is a great benefit.\n\nKevin also makes good use of the annual ticket subsidy discount he receives as part of his Partner package: a scheme where the Partnership contributes 50% towards the cost of Partners tickets for events and attractions up to an annual limit of 60. He also enjoys having access to the Partnerships suite at the O2 arena. Ive seen some fantastic artists from the comfort of the Partnerships suite at Londons O2 arena, including Jay Z and The Weeknd. Its an amazing experience for Partners!",
        "title": "John Lewis Partnership plc Annual Report and Accounts 2017",
        "authors": ["John Lewis Partnership plc"],
        "doc_year": 2017,
        "gold_model_json": {
            "domain": {"society": {"employment": 1.0, "workers_rights": 1.0}, "industry": {"commerce": 1.0}},
            "impact": {"duration": 0.6, "benefit_to_human_life": 0.3, "benefit_to_object_entity": 0.5},
            "object": {"qualities": {"quantity": 1.0}, "entity_type": {"individual": 1.0}},
            "subject": {"entity_type": {"organizations": 1.0}},
            "statement": {"descriptive": 1.0, "past_statement": 1.0, "describes_action": 1.0},
            "engagement": {"proactive": 0.6, "responsive": 0.6},
            "motivation": {"genuine": 0.7, "compliant": 0.3},
        },
        "gold_metadata": {
            "company": "John Lewis Partnership plc",
            "subject_entities": [{"name": "John Lewis Partnership plc", "entity_type": "company"}],
            "object_entities": [{"name": "Partners", "entity_type": "group_of_people"}],
            "domain": "society.employment",
            "is_impactful_action": True,
            "impact_value": 0.3,
            "impact": "increased compensation",
            "action": "invested",
            "locations": [],
            "time": {"year": 2016, "precision": "year"},
            "start_year": 2016,
            "end_year": 2016,
            "quantities": [{"amount": None, "quantity_type": "financial", "unit": None, "qualifier": "significantly more"}],
            "is_environmental": False,
            "is_social": True,
            "is_governance": True,
            "is_animal_welfare": False,
            "statement_category": "action",
            "is_disclosure": True,
            "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company"}],
            "is_vague": False,
        },
    },
    {
        "id": 1559888,
        "statement_text": "Rushden Lakes is set within 200 acres of protected Nene Wetlands.",
        "context": "Rushden Lakes is a major retail, leisure and tourism destination in Northamptonshire, which combines shopping with beautiful views across the lake. The site is set within 200 acres of protected Nene Wetlands, which is managed by The Wildlife Trust.",
        "title": "The Crown Estate Annual Report 2022",
        "authors": ["The Crown Estate"],
        "doc_year": 2022,
        "gold_model_json": {
            "domain": {"environment": {"conservation": 0.8, "sustainability": 0.2}, "realms": {"overground": 1.0}},
            "impact": {},
            "object": {"entity_type": {"environment": 1.0, "biological_systems": 1.0}},
            "subject": {"entity_type": {"place": 1.0}},
            "statement": {"descriptive": 1.0, "describes_fact": 1.0, "present_statement": 1.0},
            "engagement": {},
            "motivation": {},
        },
        "gold_metadata": {
            "company": "The Crown Estate",
            "subject_entities": [{"name": "Rushden Lakes", "entity_type": "facility"}],
            "object_entities": [{"name": "Nene Wetlands", "entity_type": "biome"}],
            "domain": "environment.conservation",
            "is_impactful_action": False,
            "impact_value": 0.8,
            "action": "is set within",
            "time": None,
            "start_year": 2022,
            "end_year": 2022,
            "quantities": [{"amount": 200, "quantity_type": "area", "unit": "acres", "qualifier": "protected"}],
            "is_environmental": True,
            "is_social": False,
            "is_governance": False,
            "is_animal_welfare": False,
            "statement_category": "fact",
            "is_vague": False,
            "authors": [{"name": "The Crown Estate", "entity_type": "organisation"}],
            "locations": [{"name": "Northamptonshire", "entity_type": "region", "proper_noun": True, "location_type": "region"}],
            "is_disclosure": True,
        },
    },
    {
        "id": 1561537,
        "statement_text": "Waitrose saw single use packaging reduced by 98%.",
        "context": "Waitrose has been working to reduce packaging waste across its operations. Through innovative approaches to packaging design and materials, the company saw single use packaging reduced by 98% in certain product categories, significantly decreasing its environmental footprint.",
        "title": "John Lewis Partnership Sustainability Report 2023",
        "authors": ["John Lewis Partnership plc"],
        "doc_year": 2023,
        "gold_model_json": {
            "domain": {
                "environment": {"plastic_waste": 1.0, "sustainability": 1.0, "circular_economy": 1.0},
                "industry": {"retail": 1.0},
            },
            "impact": {"duration": 0.4, "proportion": 0.98, "benefit_to_environment": 0.3, "benefit_to_object_entity": 0.7},
            "subject": {"entity_type": {"organizations": 1.0}},
            "object": {"qualities": {"quantity": 0.98}, "entity_type": {"chemical": 1.0}},
            "statement": {
                "descriptive": 1.0,
                "describes_fact": 1.0,
                "past_statement": 1.0,
            },
            "engagement": {"proactive": 1.0},
            "motivation": {
                "genuine": 0.8,
            },
        },
        "gold_metadata": {
            "company": "Waitrose",
            "subject_entities": [{"name": "Waitrose", "entity_type": "company"}],
            "object_entities": [{"name": "single use packaging", "entity_type": "product"}],
            "domain": "environment.sustainability",
            "is_impactful_action": True,
            "impact_value": 0.2,
            "impact": "waste reduction",
            "action": "reduced",
            "locations": [],
            "time": None,
            "start_year": 2023,
            "end_year": 2023,
            "quantities": [{"amount": 98, "quantity_type": "percentage", "unit": "%", "delta": "decrease"}],
            "is_environmental": True,
            "is_social": False,
            "is_governance": False,
            "is_animal_welfare": False,
            "statement_category": "action",
            "authors": [{"name": "John Lewis Partnership plc", "entity_type": "company"}],
            "is_disclosure": True,
            "is_vague": False,
        },
    },
    {
        "id": 1558364,
        "statement_text": "The pandemic is the predominant recent source of stress for physical retail.",
        "context": "The retail sector has faced unprecedented challenges in recent years. The pandemic is the predominant recent source of stress for physical retail, with lockdowns and social distancing measures severely impacting foot traffic and sales volumes across the industry.",
        "title": "Retail Sector Analysis 2020",
        "authors": ["Retail Research Institute"],
        "doc_year": 2020,
        "gold_model_json": {
            "domain": {
                "society": {"disease": 0.8, "public_health": 0.7},
                "industry": {"retail": 0.9, "commerce": 0.8, "consumer": 0.7},
            },
            "subject": {"entity_type": {"event": 1.0}},
            "object": {"entity_type": {"industry": 1.0}},
            "impact": {"harm_to_object_entity": 0.7},
            "statement": {"descriptive": 1.0, "past_statement": 1.0},
        },
        "gold_metadata": {
            "company": "Retail Industry",
            "subject_entities": [{"name": "pandemic", "entity_type": "event"}],
            "object_entities": [{"name": "physical retail", "entity_type": "sector"}],
            "domain": "society.disease",
            "is_impactful_action": False,
            "impact_value": 0.0,
            "impact": "stress",
            "action": "is source of stress",
            "locations": [],
            "time": None,
            "start_year": 2020,
            "end_year": 2020,
            "quantities": [],
            "is_environmental": False,
            "is_social": False,
            "is_governance": False,
            "is_animal_welfare": False,
            "statement_category": "fact",
            "authors": [{"name": "Retail Research Institute", "entity_type": "organisation"}],
            "is_disclosure": False,
            "is_vague": False,
        },
    },
    {
        "id": 1558373,
        "statement_text": "The US will likely see a drop of 22 to 27 percent in offline sales compared to 2019 levels.",
        "context": "The retail landscape is changing rapidly due to the pandemic. The US will likely see a drop of 22 to 27 percent in offline sales compared to 2019 levels, while e-commerce is expected to grow significantly during the same period.",
        "title": "Retail Sector Analysis 2021",
        "authors": ["Retail Research Institute"],
        "doc_year": 2021,
        "gold_model_json": {
            "domain": {"industry": {"retail": 0.8, "commerce": 0.6}, "society": {"consumer_protection": 0.2}},
            "impact": {"duration": 0.6, "harm_to_object_entity": 0.7},
            "subject": {
                "qualities": {"quantity": 0.245},
                "entity_type": {
                    "geopolitical": 1.0,
                },
            },
            "object": {
                "entity_type": {"processes": 1.0, "immaterial": 1.0, "immaterial_systems": 1.0, "multiple_immaterials": 1.0}
            },
            "statement": {"predictive": 1.0, "future_statement": 1.0},
            "engagement": {"passive": 0.3, "reactive": 0.7},
            "motivation": {"genuine": 0.8},
        },
        "gold_metadata": {
            "company": None,
            "subject_entities": [{"name": "US", "entity_type": "country"}],
            "object_entities": [{"name": "offline sales", "entity_type": "market"}],
            "domain": "industry.retail",
            "is_impactful_action": True,
            "impact_value": -0.1,
            "impact": "sales decline",
            "action": "will see a drop",
            "locations": [{"name": "US", "location_type": "country"}],
            "time": {"from_year": 2019, "to_year": 2021, "precision": "year"},
            "start_year": 2019,
            "end_year": 2021,
            "quantities": [
                {"amount": 22, "quantity_type": "percentage", "unit": "%", "qualifier": "to 27 percent", "delta": "decrease"}
            ],
            "is_environmental": False,
            "is_social": False,
            "is_governance": False,
            "is_animal_welfare": False,
            "statement_category": "prediction",
            "authors": [{"name": "Retail Research Institute", "entity_type": "organisation"}],
            "is_disclosure": False,
            "is_vague": False,
        },
    },
    {
        "id": 1558383,
        "statement_text": "Brands should be working to improve the low wages they have helped to create",
        "context": "The garment industry has long been criticized for poor labor practices. Brands should be working to improve the low wages they have helped to create in the traditional garment producing hubs and fulfilling living wage commitments to relieve the suffering of millions of workers in their supply chains. This is not just a moral imperative but also a business necessity as consumers increasingly demand ethical production.",
        "title": "Fashion Revolution Report 2020",
        "authors": ["Fashion Magazine"],
        "doc_year": 2020,
        "gold_model_json": {
            "domain": {"society": {"workers_rights": 1.0}},
            "impact": {"proportion": 0.5, "benefit_to_human_life": 0.7},
            "object": {"qualities": {"quantity": 0.5}, "entity_type": {"individual": 1.0}},
            "subject": {"qualities": {"quantity": 0.5}, "entity_type": {"organisation": 1.0}},
            "statement": {"directive": 1.0, "beneficial_statement": 1.0},
            "engagement": {"proactive": 1.0, "altruistic": 1.0},
            "motivation": {"genuine": 1.0},
        },
        "gold_metadata": {
            "company": "Fashion Magazine",
            "subject_entities": [{"name": "Brands", "entity_type": "organisation"}],
            "object_entities": [
                {"name": "workers", "entity_type": "person"},
                {"name": "garment producing hubs", "entity_type": "place"},
            ],
            "domain": "society.workers_rights",
            "is_impactful_action": False,
            "impact_value": 0.7,
            "impact": "wage improvement",
            "action": "should be working to improve",
            "locations": [],
            "time": None,
            "start_year": 2020,
            "end_year": 2020,
            "quantities": [{"amount": None, "quantity_type": "people", "unit": "millions", "qualifier": "workers"}],
            "is_environmental": False,
            "is_social": True,
            "is_governance": False,
            "is_animal_welfare": False,
            "statement_category": "directive",
            "authors": [{"name": "Fashion Magazine", "entity_type": "organisation"}],
            "is_disclosure": False,
            "is_vague": False,
        },
    },
]


def get_test_statements() -> List[Dict[str, Any]]:
    """
    Get the static test statements for evaluation.

    Returns:
        List of test statements
    """
    return TEST_STATEMENTS


def get_statement_by_id(statement_id: int) -> Dict[str, Any]:
    """
    Get a specific test statement by ID.

    Args:
        statement_id: ID of the statement to retrieve

    Returns:
        Test statement dictionary or empty dict if not found
    """
    for statement in TEST_STATEMENTS:
        if statement["id"] == statement_id:
            return statement
    return {}
