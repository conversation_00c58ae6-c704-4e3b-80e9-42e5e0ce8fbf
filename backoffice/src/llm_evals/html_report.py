"""
Module for generating HTML reports for LLM evaluations.
"""

import os
import json
import base64
from io import BytesIO
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import pandas as pd
import numpy as np
from loguru import logger
import webbrowser

from eko import eko_var_path
from eko.models.vector.demise.demise_model import DEMISEModel


def generate_html_report(
    model_metrics: Dict[str, Dict[str, Any]],
    output_dir: str,
    open_browser: bool = True,
    gold_results: Optional[Union[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]] = None,
    test_results: Optional[Union[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]] = None
) -> str:
    """
    Generate an HTML report comparing multiple models.

    Args:
        model_metrics: Dictionary mapping model names to their metrics
        output_dir: Directory to save the report
        open_browser: Whether to open the report in a browser
        gold_results: Optional list of gold standard results for detailed comparison
        test_results: Optional list of test model results for detailed comparison

    Returns:
        Path to the generated HTML report
    """
    logger.info("Generating HTML report")

    # Create timestamp for the report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create report directory
    report_dir = os.path.join(output_dir, f"report_{timestamp}")
    os.makedirs(report_dir, exist_ok=True)

    # Generate visualizations
    visualizations = _generate_visualizations(model_metrics)

    # Create HTML content
    html_content = _create_html_content(model_metrics, visualizations, timestamp, gold_results, test_results)

    # Save HTML report
    report_path = os.path.join(report_dir, "report.html")
    with open(report_path, "w") as f:
        f.write(html_content)

    # Save raw data
    data_path = os.path.join(report_dir, "data.json")
    with open(data_path, "w") as f:
        json.dump(model_metrics, f, indent=2)

    logger.info(f"HTML report generated at {report_path}")

    # Open in browser if requested
    if open_browser:
        webbrowser.open(f"file://{os.path.abspath(report_path)}")

    return report_path


def _generate_visualizations(model_metrics: Dict[str, Dict[str, Any]]) -> Dict[str, str]:
    """
    Generate visualizations for the HTML report.

    Args:
        model_metrics: Dictionary mapping model names to their metrics

    Returns:
        Dictionary mapping visualization names to their base64-encoded images
    """
    visualizations = {}

    # Extract DEMISE model data if available
    demise_data = _extract_demise_data(model_metrics)

    # Add DEMISE model comparison if data is available
    if demise_data and len(demise_data) > 0:
        visualizations.update(_generate_demise_visualizations(demise_data))

    # Generate per-statement visualizations
    visualizations.update(_generate_per_statement_visualizations(model_metrics))

    # Generate per-model field visualizations with test cases on x-axis
    visualizations.update(_generate_per_model_field_visualizations(model_metrics))

    # 1. Overall accuracy comparison
    plt.figure(figsize=(10, 6))
    models = list(model_metrics.keys())
    accuracies = [metrics.get("overall_accuracy", 0.0) for metrics in model_metrics.values()]

    # Create a color map from red (0.0) to green (1.0)
    colors = []
    for acc in accuracies:
        # Ensure accuracy is within 0-1 range
        acc = max(0.0, min(1.0, float(acc)))
        # Linear interpolation between red (1,0,0) and green (0,1,0)
        r = max(0.0, min(1.0, 1.0 - 2.0 * acc)) if acc > 0.5 else 1.0
        g = max(0.0, min(1.0, 2.0 * acc - 1.0)) if acc > 0.5 else min(1.0, 2.0 * acc)
        colors.append((r, g, 0))  # RGB tuple

    plt.bar(models, accuracies, color=colors)
    plt.ylim(0, 1)
    plt.title("Overall Accuracy Comparison")
    plt.ylabel("Accuracy (0-1)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()

    # Convert to base64
    img_data = BytesIO()
    plt.savefig(img_data, format="png")
    img_data.seek(0)
    visualizations["overall_accuracy"] = base64.b64encode(img_data.read()).decode("utf-8")
    plt.close()

    # 2. Success rate comparison
    plt.figure(figsize=(10, 6))
    success_rates = []

    for metrics in model_metrics.values():
        total = metrics.get("total_statements", 0)
        successful = metrics.get("successful_extractions", 0)
        rate = successful / total if total > 0 else 0
        success_rates.append(rate)

    # Create a color map from red (0.0) to green (1.0)
    colors = []
    for rate in success_rates:
        # Ensure rate is within 0-1 range
        rate = max(0.0, min(1.0, float(rate)))
        # Linear interpolation between red (1,0,0) and green (0,1,0)
        r = max(0.0, min(1.0, 1.0 - 2.0 * rate)) if rate > 0.5 else 1.0
        g = max(0.0, min(1.0, 2.0 * rate - 1.0)) if rate > 0.5 else min(1.0, 2.0 * rate)
        colors.append((r, g, 0))  # RGB tuple

    plt.bar(models, success_rates, color=colors)
    plt.ylim(0, 1)
    plt.title("Success Rate Comparison")
    plt.ylabel("Success Rate (0-1)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()

    # Convert to base64
    img_data = BytesIO()
    plt.savefig(img_data, format="png")
    img_data.seek(0)
    visualizations["success_rate"] = base64.b64encode(img_data.read()).decode("utf-8")
    plt.close()

    # 3. Field accuracy comparison
    # Collect field accuracy data
    field_data = {}

    for model_name, metrics in model_metrics.items():
        if "field_accuracy" in metrics and metrics["field_accuracy"]:
            for field, accuracy in metrics["field_accuracy"].items():
                if field not in field_data:
                    field_data[field] = {}
                field_data[field][model_name] = accuracy

    # Create heatmap for field accuracy
    if field_data:
        # Convert to DataFrame
        df = pd.DataFrame(field_data)

        # Fill NaN values with 0
        df = df.fillna(0)

        # Create heatmap with red-to-green color scale
        plt.figure(figsize=(12, 8))
        # Create a custom colormap from red to green
        cmap = LinearSegmentedColormap.from_list('RedGreen', [(1,0,0), (1,1,0), (0,1,0)])
        plt.imshow(df.values, cmap=cmap, aspect="auto", vmin=0, vmax=1)
        plt.colorbar(label="Accuracy")
        plt.xticks(range(len(df.columns)), df.columns, rotation=45, ha="right")
        plt.yticks(range(len(df.index)), df.index)
        plt.title("Field Accuracy Comparison")

        # Add text annotations
        for i in range(len(df.index)):
            for j in range(len(df.columns)):
                value = df.iloc[i, j]
                plt.text(j, i, f"{value:.2f}", ha="center", va="center",
                         color="black" if value > 0.5 else "white")

        plt.tight_layout()

        # Convert to base64
        img_data = BytesIO()
        plt.savefig(img_data, format="png")
        img_data.seek(0)
        visualizations["field_accuracy"] = base64.b64encode(img_data.read()).decode("utf-8")
        plt.close()

    # 4. Top fields by accuracy for each model
    for model_name, metrics in model_metrics.items():
        if "field_accuracy" in metrics and metrics["field_accuracy"]:
            # Sort fields by accuracy
            sorted_fields = sorted(
                metrics["field_accuracy"].items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]  # Top 10 fields

            fields = [field for field, _ in sorted_fields]
            accuracies = [accuracy for _, accuracy in sorted_fields]

            plt.figure(figsize=(10, 6))

            # Create a color map from red (0.0) to green (1.0)
            colors = []
            for acc in accuracies:
                # Ensure accuracy is within 0-1 range
                acc = max(0.0, min(1.0, float(acc)))
                # Linear interpolation between red (1,0,0) and green (0,1,0)
                r = max(0.0, min(1.0, 1.0 - 2.0 * acc)) if acc > 0.5 else 1.0
                g = max(0.0, min(1.0, 2.0 * acc - 1.0)) if acc > 0.5 else min(1.0, 2.0 * acc)
                colors.append((r, g, 0))  # RGB tuple

            plt.bar(fields, accuracies, color=colors)
            plt.ylim(0, 1)
            plt.title(f"Top Fields by Accuracy for {model_name}")
            plt.ylabel("Accuracy (0-1)")
            plt.xticks(rotation=45, ha="right")
            plt.tight_layout()

            # Convert to base64
            img_data = BytesIO()
            plt.savefig(img_data, format="png")
            img_data.seek(0)
            visualizations[f"top_fields_{model_name}"] = base64.b64encode(img_data.read()).decode("utf-8")
            plt.close()

    return visualizations


def _extract_demise_data(model_metrics: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, float]]:
    """
    Extract DEMISE model data from the metrics.

    Args:
        model_metrics: Dictionary mapping model names to their metrics

    Returns:
        Dictionary mapping model names to their DEMISE component data
    """
    demise_data = {}

    # Define DEMISE components to look for
    demise_components = ["domain", "impact", "subject", "object", "statement", "engagement", "motivation"]

    for model_name, metrics in model_metrics.items():
        # Check if DEMISE component accuracy is available
        if "component_accuracy" in metrics and metrics["component_accuracy"]:
            demise_data[model_name] = metrics["component_accuracy"]
        elif "statement_metrics" in metrics:
            # Try to extract DEMISE data from statement metrics
            component_data = {}

            for statement in metrics["statement_metrics"]:
                if "component_matches" in statement:
                    for component, value in statement["component_matches"].items():
                        if component not in component_data:
                            component_data[component] = []
                        component_data[component].append(float(value) if isinstance(value, (int, float, str)) else value)

            # Calculate average for each component
            component_accuracy: Dict[str, float] = {}
            for component, values in component_data.items():
                if values:
                    # Calculate average of numeric values only
                    numeric_values = [v for v in values if isinstance(v, (int, float))]
                    if numeric_values:
                        component_accuracy[component] = sum(numeric_values) / len(numeric_values)

            if component_accuracy:
                demise_data[model_name] = component_accuracy

        # If we still don't have DEMISE data, try to create it from raw results
        if model_name not in demise_data or not demise_data[model_name]:
            if "raw_results" in metrics:
                component_accuracy = {}

                # Process each raw result
                for result in metrics["raw_results"]:
                    if "demise" in result and result["success"]:
                        demise_dict = result["demise"]

                        # Extract component values from the DEMISE dictionary
                        for component in demise_components:
                            component_values = {}

                            # Look for keys that start with this component
                            for key, value in demise_dict.items():
                                if key.startswith(f"{component}."):
                                    component_values[key] = value

                            # Calculate average value for this component
                            if component_values:
                                # Calculate average directly without storing intermediate values
                                numeric_values = [v for v in component_values.values() if isinstance(v, (int, float))]
                                if numeric_values:
                                    avg_value = sum(numeric_values) / len(numeric_values)
                                    if component not in component_accuracy:
                                        component_accuracy[component] = avg_value
                                    else:
                                        # Average with existing value
                                        component_accuracy[component] = (component_accuracy[component] + avg_value) / 2

                # No need to calculate averages again, component_accuracy already has the averages
                final_component_accuracy = component_accuracy

                if final_component_accuracy:
                    demise_data[model_name] = final_component_accuracy

    # No mocking - if we don't have DEMISE data, we don't show DEMISE visualizations

    return demise_data


def _generate_per_statement_visualizations(model_metrics: Dict[str, Dict[str, Any]]) -> Dict[str, str]:
    """
    Generate visualizations for per-statement performance across models.

    Args:
        model_metrics: Dictionary mapping model names to their metrics

    Returns:
        Dictionary mapping visualization names to their base64-encoded images
    """
    visualizations = {}

    # Collect statement metrics from all models
    statement_metrics_by_id = {}

    for model_name, metrics in model_metrics.items():
        if "statement_metrics" in metrics:
            for statement in metrics["statement_metrics"]:
                statement_id = statement.get("statement_id")
                if statement_id:
                    if statement_id not in statement_metrics_by_id:
                        statement_metrics_by_id[statement_id] = {}

                    # Store the overall match score for this model and statement
                    statement_metrics_by_id[statement_id][model_name] = statement.get("overall_match", 0.0)

    # Generate a bar chart for each statement showing performance across models
    for statement_id, model_scores in statement_metrics_by_id.items():
        if not model_scores:
            continue

        plt.figure(figsize=(10, 6))

        # Sort models by score for better visualization
        sorted_items = sorted(model_scores.items(), key=lambda x: x[1], reverse=True)
        models = [item[0] for item in sorted_items]
        scores = [item[1] for item in sorted_items]

        # Create a color map from red (0.0) to green (1.0)
        colors = []
        for score in scores:
            # Ensure score is within 0-1 range
            score = max(0.0, min(1.0, float(score)))
            # Linear interpolation between red (1,0,0) and green (0,1,0)
            r = max(0.0, min(1.0, 1.0 - 2.0 * score)) if score > 0.5 else 1.0
            g = max(0.0, min(1.0, 2.0 * score - 1.0)) if score > 0.5 else min(1.0, 2.0 * score)
            colors.append((r, g, 0))  # RGB tuple

        plt.bar(models, scores, color=colors)
        plt.ylim(0, 1)
        plt.title(f"Model Performance for Statement ID: {statement_id}")
        plt.ylabel("Match Score (0-1)")
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()

        # Convert to base64
        img_data = BytesIO()
        plt.savefig(img_data, format="png")
        img_data.seek(0)
        visualizations[f"statement_{statement_id}"] = base64.b64encode(img_data.read()).decode("utf-8")
        plt.close()

        # Generate field-level comparison for this statement if available
        field_data = {}
        for model_name, metrics in model_metrics.items():
            if "statement_metrics" in metrics:
                for statement in metrics["statement_metrics"]:
                    if statement.get("statement_id") == statement_id and "field_matches" in statement:
                        for field, score in statement["field_matches"].items():
                            if field not in field_data:
                                field_data[field] = {}
                            field_data[field][model_name] = score

        if field_data:
            # Create a heatmap for field accuracy across models
            plt.figure(figsize=(12, max(8.0, float(len(field_data) * 0.4))))  # Adjust height based on number of fields

            # Convert to DataFrame for visualization
            df = pd.DataFrame(field_data)

            # Fill NaN values with 0
            df = df.fillna(0)

            # Create heatmap with red-to-green color scale
            cmap = LinearSegmentedColormap.from_list('RedGreen', [(1,0,0), (1,1,0), (0,1,0)])
            plt.imshow(df.values, cmap=cmap, aspect="auto", vmin=0, vmax=1)
            plt.colorbar(label="Match Score")
            plt.xticks(range(len(df.columns)), df.columns, rotation=45, ha="right")
            plt.yticks(range(len(df.index)), df.index)
            plt.title(f"Field-level Performance for Statement ID: {statement_id}")

            # Add text annotations
            for i in range(len(df.index)):
                for j in range(len(df.columns)):
                    value = df.iloc[i, j]
                    if not pd.isna(value):
                        plt.text(j, i, f"{value:.2f}", ha="center", va="center",
                                color="black" if value > 0.5 else "white")

            plt.tight_layout()

            # Convert to base64
            img_data = BytesIO()
            plt.savefig(img_data, format="png")
            img_data.seek(0)
            visualizations[f"statement_{statement_id}_fields"] = base64.b64encode(img_data.read()).decode("utf-8")
            plt.close()

    return visualizations


def _generate_per_model_field_visualizations(model_metrics: Dict[str, Dict[str, Any]]) -> Dict[str, str]:
    """
    Generate visualizations for per-model performance with test cases on the x-axis.

    Args:
        model_metrics: Dictionary mapping model names to their metrics

    Returns:
        Dictionary mapping visualization names to their base64-encoded images
    """
    visualizations = {}

    # Collect field-level metrics for each model
    for model_name, metrics in model_metrics.items():
        if "statement_metrics" not in metrics or not metrics["statement_metrics"]:
            continue

        # Collect field scores across all statements for this model
        field_scores_by_statement = {}
        statement_ids = []

        for statement in metrics["statement_metrics"]:
            statement_id = statement.get("statement_id")
            if not statement_id or "field_matches" not in statement:
                continue

            statement_ids.append(statement_id)

            for field, score in statement["field_matches"].items():
                if field not in field_scores_by_statement:
                    field_scores_by_statement[field] = {}
                field_scores_by_statement[field][statement_id] = score

        # Generate a bar chart for each field showing performance across statements
        for field, statement_scores in field_scores_by_statement.items():
            if not statement_scores or len(statement_scores) < 2:  # Skip fields with too few data points
                continue

            plt.figure(figsize=(12, 6))

            # Sort statements by score for better visualization
            sorted_items = sorted(statement_scores.items(), key=lambda x: x[1], reverse=True)
            statements = [f"ID: {item[0]}" for item in sorted_items]
            scores = [item[1] for item in sorted_items]

            # Create a color map from red (0.0) to green (1.0)
            colors = []
            for score in scores:
                # Ensure score is within 0-1 range
                score = max(0.0, min(1.0, float(score)))
                # Linear interpolation between red (1,0,0) and green (0,1,0)
                r = max(0.0, min(1.0, 1.0 - 2.0 * score)) if score > 0.5 else 1.0
                g = max(0.0, min(1.0, 2.0 * score - 1.0)) if score > 0.5 else min(1.0, 2.0 * score)
                colors.append((r, g, 0))  # RGB tuple

            plt.bar(statements, scores, color=colors)
            plt.ylim(0, 1)
            plt.title(f"{field} Field Performance for {model_name}")
            plt.ylabel("Match Score (0-1)")
            plt.xticks(rotation=45, ha="right")
            plt.tight_layout()

            # Convert to base64
            img_data = BytesIO()
            plt.savefig(img_data, format="png")
            img_data.seek(0)
            visualizations[f"field_{field}_{model_name}"] = base64.b64encode(img_data.read()).decode("utf-8")
            plt.close()

        # Generate a heatmap showing all fields vs all statements for this model
        if field_scores_by_statement and statement_ids:
            # Create a DataFrame with statements as columns and fields as rows
            data = {}
            for field, statement_scores in field_scores_by_statement.items():
                data[field] = {}
                for statement_id in statement_ids:
                    data[field][f"ID: {statement_id}"] = statement_scores.get(statement_id, 0.0)

            # Convert to DataFrame
            df = pd.DataFrame(data).T  # Transpose to get fields as rows

            # Fill NaN values with 0
            df = df.fillna(0)

            # Create heatmap
            width = max(12.0, float(len(statement_ids)) * 0.5)
            height = max(8.0, float(len(field_scores_by_statement)) * 0.4)
            plt.figure(figsize=(width, height))
            cmap = LinearSegmentedColormap.from_list('RedGreen', [(1,0,0), (1,1,0), (0,1,0)])
            plt.imshow(df.values, cmap=cmap, aspect="auto", vmin=0, vmax=1)
            plt.colorbar(label="Match Score")
            plt.xticks(range(len(df.columns)), df.columns, rotation=45, ha="right")
            plt.yticks(range(len(df.index)), df.index)
            plt.title(f"Field Performance Across Test Cases for {model_name}")

            # Add text annotations
            for i in range(len(df.index)):
                for j in range(len(df.columns)):
                    value = df.iloc[i, j]
                    if not pd.isna(value):
                        plt.text(j, i, f"{value:.2f}", ha="center", va="center",
                                color="black" if value > 0.5 else "white")

            plt.tight_layout()

            # Convert to base64
            img_data = BytesIO()
            plt.savefig(img_data, format="png")
            img_data.seek(0)
            visualizations[f"model_heatmap_{model_name}"] = base64.b64encode(img_data.read()).decode("utf-8")
            plt.close()

    return visualizations


def _generate_demise_visualizations(demise_data: Dict[str, Dict[str, float]]) -> Dict[str, str]:
    """
    Generate visualizations for DEMISE model comparison.

    Args:
        demise_data: Dictionary mapping model names to their DEMISE component data

    Returns:
        Dictionary mapping visualization names to their base64-encoded images
    """
    visualizations = {}

    # 1. DEMISE component comparison across models
    if demise_data:
        # Create DataFrame for visualization
        df = pd.DataFrame(demise_data)

        # Create heatmap with red-to-green color scale
        plt.figure(figsize=(12, 8))
        cmap = LinearSegmentedColormap.from_list('RedGreen', [(1,0,0), (1,1,0), (0,1,0)])
        plt.imshow(df.values, cmap=cmap, aspect="auto", vmin=0, vmax=1)
        plt.colorbar(label="Accuracy")
        plt.xticks(range(len(df.columns)), df.columns, rotation=45, ha="right")
        plt.yticks(range(len(df.index)), df.index)
        plt.title("DEMISE Component Accuracy Comparison")

        # Add text annotations
        for i in range(len(df.index)):
            for j in range(len(df.columns)):
                value = df.iloc[i, j]
                if not pd.isna(value):
                    plt.text(j, i, f"{value:.2f}", ha="center", va="center",
                             color="black" if value > 0.5 else "white")

        plt.tight_layout()

        # Convert to base64
        img_data = BytesIO()
        plt.savefig(img_data, format="png")
        img_data.seek(0)
        visualizations["demise_component_accuracy"] = base64.b64encode(img_data.read()).decode("utf-8")
        plt.close()

        # 2. Average DEMISE component accuracy by model
        plt.figure(figsize=(10, 6))
        avg_accuracy = df.mean(axis=0)

        # Create a color map from red (0.0) to green (1.0)
        colors = []
        for acc in avg_accuracy:
            # Ensure accuracy is within 0-1 range
            acc = max(0.0, min(1.0, float(acc)))
            # Linear interpolation between red (1,0,0) and green (0,1,0)
            r = max(0.0, min(1.0, 1.0 - 2.0 * acc)) if acc > 0.5 else 1.0
            g = max(0.0, min(1.0, 2.0 * acc - 1.0)) if acc > 0.5 else min(1.0, 2.0 * acc)
            colors.append((r, g, 0))  # RGB tuple

        avg_accuracy.plot(kind="bar", color=colors)
        plt.ylim(0, 1)
        plt.title("Average DEMISE Component Accuracy by Model")
        plt.ylabel("Average Accuracy (0-1)")
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()

        # Convert to base64
        img_data = BytesIO()
        plt.savefig(img_data, format="png")
        img_data.seek(0)
        visualizations["avg_demise_accuracy"] = base64.b64encode(img_data.read()).decode("utf-8")
        plt.close()

        # 3. Component accuracy by model
        for component in df.index:
            plt.figure(figsize=(10, 6))
            component_data = df.loc[component]

            # Create a color map from red (0.0) to green (1.0)
            colors = []
            for acc in component_data:
                # Ensure accuracy is within 0-1 range
                acc = max(0.0, min(1.0, float(acc)))
                # Linear interpolation between red (1,0,0) and green (0,1,0)
                r = max(0.0, min(1.0, 1.0 - 2.0 * acc)) if acc > 0.5 else 1.0
                g = max(0.0, min(1.0, 2.0 * acc - 1.0)) if acc > 0.5 else min(1.0, 2.0 * acc)
                colors.append((r, g, 0))  # RGB tuple

            component_data.plot(kind="bar", color=colors)
            plt.ylim(0, 1)
            plt.title(f"DEMISE {component.capitalize()} Component Accuracy by Model")
            plt.ylabel("Accuracy (0-1)")
            plt.xticks(rotation=45, ha="right")
            plt.tight_layout()

            # Convert to base64
            img_data = BytesIO()
            plt.savefig(img_data, format="png")
            img_data.seek(0)
            visualizations[f"demise_{component}_accuracy"] = base64.b64encode(img_data.read()).decode("utf-8")
            plt.close()

    return visualizations


def _format_value_for_display(value: Any) -> str:
    """
    Format a value for display in the HTML report.

    Args:
        value: The value to format

    Returns:
        Formatted string representation of the value
    """
    if value == "N/A":
        return "N/A"

    if isinstance(value, (list, tuple)):
        if not value:
            return "[]"

        # Handle lists of entities (dicts with name field)
        if isinstance(value[0], dict) and "name" in value[0]:
            names = []
            for item in value:
                name = item.get("name", "")
                if not name and "legal_name" in item:
                    name = item.get("legal_name", "")
                if name:
                    names.append(name)
            return ", ".join(names) if names else "[]"

        # Handle simple lists
        return ", ".join(str(item) for item in value[:3]) + ("..." if len(value) > 3 else "")

    elif isinstance(value, dict):
        # For dictionaries, show key-value pairs
        if "name" in value:
            return value.get("name", "")

        # Limit to a few key-value pairs
        items = list(value.items())[:3]
        return "{" + ", ".join(f"{k}: {v}" for k, v in items) + ("..." if len(value) > 3 else "") + "}"

    elif isinstance(value, bool):
        return "Yes" if value else "No"

    elif isinstance(value, (int, float)):
        return f"{value:.2f}" if isinstance(value, float) else str(value)

    else:
        # For strings and other types
        str_value = str(value)
        if len(str_value) > 100:
            return str_value[:100] + "..."
        return str_value


def _create_html_content(
    model_metrics: Dict[str, Dict[str, Any]],
    visualizations: Dict[str, str],
    timestamp: str,
    gold_results: Optional[Union[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]] = None,
    test_results: Optional[Union[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]] = None
) -> str:
    """
    Create HTML content for the report.

    Args:
        model_metrics: Dictionary mapping model names to their metrics
        visualizations: Dictionary mapping visualization names to their base64-encoded images
        timestamp: Timestamp for the report

    Returns:
        HTML content as a string
    """
    # Create HTML header
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>LLM Evaluation Report - {timestamp}</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }}
            h1, h2, h3 {{
                color: #333;
            }}
            .section {{
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 1px solid #eee;
            }}
            .visualization {{
                margin: 20px 0;
                text-align: center;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            th, td {{
                padding: 10px;
                border: 1px solid #ddd;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            .model-card {{
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
                margin-bottom: 20px;
                background-color: #fff;
            }}
            .model-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }}
            .model-name {{
                font-size: 1.2em;
                font-weight: bold;
            }}
            .model-accuracy {{
                font-size: 1.2em;
                font-weight: bold;
                color: #4CAF50;
            }}
            .field-table {{
                max-height: 300px;
                overflow-y: auto;
            }}
            .field-comparison {{
                margin-top: 20px;
            }}
            .statement-comparison {{
                margin-bottom: 30px;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
            }}
            .comparison-table {{
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
            }}
            .comparison-table th, .comparison-table td {{
                padding: 8px;
                border: 1px solid #ddd;
            }}
            .comparison-table th {{
                background-color: #f2f2f2;
                text-align: left;
            }}
            .statement-card {{
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
                margin-bottom: 30px;
                background-color: #fff;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }}
            .statement-text {{
                background-color: #f9f9f9;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 15px;
                border-left: 4px solid #4CAF50;
            }}
            .statement-card h3 {{
                margin-top: 0;
                color: #333;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
            }}
            .statement-card h4 {{
                color: #555;
                margin-top: 20px;
                margin-bottom: 10px;
            }}
            /* Tab styles */
            .tabs {{
                display: flex;
                flex-wrap: wrap;
                margin-bottom: 20px;
                border-bottom: 1px solid #ddd;
            }}
            .tab-button {{
                padding: 10px 20px;
                cursor: pointer;
                background-color: #f1f1f1;
                border: 1px solid #ddd;
                border-bottom: none;
                border-radius: 5px 5px 0 0;
                margin-right: 5px;
                transition: background-color 0.3s;
            }}
            .tab-button:hover {{
                background-color: #ddd;
            }}
            .tab-button.active {{
                background-color: white;
                border-bottom: 1px solid white;
                margin-bottom: -1px;
                font-weight: bold;
            }}
            .tab-content {{
                display: none;
                padding: 15px;
                border: 1px solid #ddd;
                border-top: none;
                border-radius: 0 0 5px 5px;
            }}
            .tab-content.active {{
                display: block;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>LLM Evaluation Report</h1>
            <p>Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>

            <div class="section">
                <h2>Overview</h2>
                <p>This report compares the metadata extraction capabilities of {len(model_metrics)} LLM models.</p>

                <div class="visualization">
                    <h3>Overall Accuracy Comparison</h3>
                    <img src="data:image/png;base64,{visualizations.get('overall_accuracy', '')}" alt="Overall Accuracy Comparison">
                </div>

                <div class="visualization">
                    <h3>Success Rate Comparison</h3>
                    <img src="data:image/png;base64,{visualizations.get('success_rate', '')}" alt="Success Rate Comparison">
                </div>
    """

    # Add field accuracy comparison if available
    if "field_accuracy" in visualizations:
        html += f"""
                <div class="visualization">
                    <h3>Field Accuracy Comparison</h3>
                    <img src="data:image/png;base64,{visualizations['field_accuracy']}" alt="Field Accuracy Comparison">
                </div>
        """

    # Add DEMISE component accuracy comparison if available
    if "demise_component_accuracy" in visualizations:
        html += f"""
                <div class="visualization">
                    <h3>DEMISE Component Accuracy Comparison</h3>
                    <img src="data:image/png;base64,{visualizations['demise_component_accuracy']}" alt="DEMISE Component Accuracy Comparison">
                </div>
        """

    # Add average DEMISE accuracy if available
    if "avg_demise_accuracy" in visualizations:
        html += f"""
                <div class="visualization">
                    <h3>Average DEMISE Component Accuracy by Model</h3>
                    <img src="data:image/png;base64,{visualizations['avg_demise_accuracy']}" alt="Average DEMISE Component Accuracy">
                </div>
        """

    # Add summary table
    html += """
                <h3>Summary Table</h3>
                <table>
                    <tr>
                        <th>Model</th>
                        <th>Overall Accuracy</th>
                        <th>Success Rate</th>
                        <th>Failed Extractions</th>
                    </tr>
    """

    for model_name, metrics in model_metrics.items():
        total = metrics.get("total_statements", 0)
        successful = metrics.get("successful_extractions", 0)
        failed = metrics.get("failed_extractions", 0)
        success_rate = successful / total if total > 0 else 0

        html += f"""
                    <tr>
                        <td>{model_name}</td>
                        <td>{metrics.get("overall_accuracy", 0.0):.2f}</td>
                        <td>{success_rate:.2f} ({successful}/{total})</td>
                        <td>{failed}</td>
                    </tr>
        """

    html += """
                </table>
            </div>
    """

    # Add individual model sections
    html += """
            <div class="section">
                <h2>Model Details</h2>
                <div class="tabs">
    """

    # Create tab buttons for each model
    for i, model_name in enumerate(model_metrics.keys()):
        active_class = "active" if i == 0 else ""
        html += f"""
                    <div class="tab-button {active_class}" onclick="openModelTab(event, '{model_name}')">{model_name}</div>
        """

    html += """
                </div>
    """

    # Create tab content for each model
    for i, (model_name, metrics) in enumerate(model_metrics.items()):
        active_class = "active" if i == 0 else ""
        html += f"""
                <div id="{model_name}" class="tab-content {active_class}">
                    <div class="model-header">
                        <div class="model-name">{model_name}</div>
                        <div class="model-accuracy">Accuracy: {metrics.get("overall_accuracy", 0.0):.2f}</div>
                    </div>

                    <p>
                        <strong>Total Statements:</strong> {metrics.get("total_statements", 0)}<br>
                        <strong>Successful Extractions:</strong> {metrics.get("successful_extractions", 0)}<br>
                        <strong>Failed Extractions:</strong> {metrics.get("failed_extractions", 0)}
                    </p>
        """

        # Add top fields visualization if available
        if f"top_fields_{model_name}" in visualizations:
            html += f"""
                    <div class="visualization">
                        <h3>Top Fields by Accuracy</h3>
                        <img src="data:image/png;base64,{visualizations[f'top_fields_{model_name}']}" alt="Top Fields for {model_name}">
                    </div>
            """

        # Add model heatmap visualization if available
        if f"model_heatmap_{model_name}" in visualizations:
            html += f"""
                    <div class="visualization">
                        <h3>Field Performance Across Test Cases</h3>
                        <img src="data:image/png;base64,{visualizations[f'model_heatmap_{model_name}']}" alt="Field Performance Across Test Cases for {model_name}">
                    </div>
            """

        # Add DEMISE component visualizations if available
        for component in ["domain", "impact", "subject", "object", "statement", "engagement", "motivation"]:
            if f"demise_{component}_accuracy" in visualizations:
                html += f"""
                    <div class="visualization">
                        <h3>DEMISE {component.capitalize()} Component Accuracy</h3>
                        <img src="data:image/png;base64,{visualizations[f'demise_{component}_accuracy']}" alt="DEMISE {component.capitalize()} for {model_name}">
                    </div>
                """

        # Add field-specific visualizations with test cases on x-axis
        field_visualizations = [key for key in visualizations.keys() if key.startswith(f"field_") and key.endswith(f"_{model_name}")]
        if field_visualizations:
            html += f"""
                    <h3>Field Performance by Test Case</h3>
                    <p>These graphs show how each field performs across different test cases.</p>
            """

            for viz_key in field_visualizations:
                field_name = viz_key.replace(f"field_", "").replace(f"_{model_name}", "")
                html += f"""
                    <div class="visualization">
                        <h4>{field_name} Field Performance Across Test Cases</h4>
                        <img src="data:image/png;base64,{visualizations[viz_key]}" alt="{field_name} Field Performance for {model_name}">
                    </div>
                """

        # Add field accuracy table if available
        if "field_accuracy" in metrics and metrics["field_accuracy"]:
            html += """
                    <h3>Field Accuracy</h3>
                    <div class="field-table">
                        <table>
                            <tr>
                                <th>Field</th>
                                <th>Accuracy</th>
                            </tr>
            """

            # Sort fields by accuracy
            sorted_fields = sorted(
                metrics["field_accuracy"].items(),
                key=lambda x: x[1],
                reverse=True
            )

            for field, accuracy in sorted_fields:
                html += f"""
                            <tr>
                                <td>{field}</td>
                                <td>{accuracy:.2f}</td>
                            </tr>
                """

            html += """
                        </table>
                    </div>
            """

            # Add detailed field value comparison if statement metrics are available
            # Skip this section for now as it requires more extensive fixes
            if False and "statement_metrics" in metrics and metrics["statement_metrics"] and gold_results is not None and test_results is not None:
                html += """
                    <h3>Field Value Comparison</h3>
                    <p>This section shows a detailed breakdown of how field values compare to the gold standard values.</p>
                    <div class="field-comparison">
                """

                # Get a sample of statement metrics (up to 5)
                sample_statements = metrics["statement_metrics"][:5]

                for statement in sample_statements:
                    statement_id = statement.get("statement_id", "Unknown")

                    # Find the corresponding gold and test results
                    gold_result = None
                    test_result = None

                    # Handle different formats of gold_results and test_results
                    if isinstance(gold_results, list):
                        for result in gold_results:
                            if result.get("statement_id") == statement_id:
                                gold_result = result
                                break
                    elif isinstance(gold_results, dict) and "metadata" in gold_results and gold_results["metadata"]:
                        for result in gold_results["metadata"]:
                            if result.get("statement_id") == statement_id:
                                gold_result = result
                                break

                    if isinstance(test_results, list):
                        for result in test_results:
                            if result.get("statement_id") == statement_id:
                                test_result = result
                                break
                    elif isinstance(test_results, dict) and "metadata" in test_results and test_results["metadata"]:
                        for result in test_results["metadata"]:
                            if result.get("statement_id") == statement_id:
                                test_result = result
                                break

                    if gold_result and test_result and "metadata" in gold_result and "metadata" in test_result:
                        html += f"""
                            <div class="statement-comparison">
                                <h4>Statement ID: {statement_id}</h4>
                                <table class="comparison-table">
                                    <tr>
                                        <th>Field</th>
                                        <th>Gold Value</th>
                                        <th>Test Value</th>
                                        <th>Match Score</th>
                                    </tr>
                        """

                        gold_metadata = gold_result["metadata"]
                        test_metadata = test_result["metadata"]

                        # Get all fields from both results
                        all_fields = set(gold_metadata.keys()).union(set(test_metadata.keys()))

                        for field in sorted(all_fields):
                            gold_value = gold_metadata.get(field, "N/A")
                            test_value = test_metadata.get(field, "N/A")
                            match_score = statement.get("field_matches", {}).get(field, 0.0)

                            # Format values for display
                            gold_display = _format_value_for_display(gold_value)
                            test_display = _format_value_for_display(test_value)

                            # Determine row color based on match score
                            row_color = ""
                            if match_score >= 0.8:
                                row_color = "background-color: #d4edda;"  # Green
                            elif match_score >= 0.5:
                                row_color = "background-color: #fff3cd;"  # Yellow
                            else:
                                row_color = "background-color: #f8d7da;"  # Red

                            html += f"""
                                <tr style="{row_color}">
                                    <td>{field}</td>
                                    <td>{gold_display}</td>
                                    <td>{test_display}</td>
                                    <td>{match_score:.2f}</td>
                                </tr>
                            """

                        html += """
                                </table>
                            </div>
                        """

                html += """
                    </div>
                """

        html += """
                </div>
        """

    # Add per-statement section
    html += """
            <div class="section">
                <h2>Per-Statement Analysis</h2>
                <p>This section shows how each model performed on individual test statements.</p>
                <div class="tabs statement-tabs">
    """

    # Get all statement IDs from visualizations
    statement_ids = set()
    for key in visualizations.keys():
        if key.startswith("statement_") and not key.endswith("_fields"):
            statement_id = key.replace("statement_", "")
            statement_ids.add(statement_id)

    # Create tab buttons for each statement
    sorted_statement_ids = sorted(statement_ids)
    for i, statement_id in enumerate(sorted_statement_ids):
        active_class = "active" if i == 0 else ""
        html += f"""
                    <div class="tab-button {active_class}" onclick="openStatementTab(event, 'statement_{statement_id}')">Statement {statement_id}</div>
        """

    html += """
                </div>
    """

    # Create tab content for each statement
    for i, statement_id in enumerate(sorted_statement_ids):
        active_class = "active" if i == 0 else ""

        # Get the statement text if available
        statement_text = ""
        for model_name, metrics in model_metrics.items():
            if "statement_metrics" in metrics:
                for statement in metrics["statement_metrics"]:
                    if str(statement.get("statement_id")) == statement_id:
                        # Try to get statement text from test data
                        from llm_evals.test_data import get_statement_by_id
                        test_statement = get_statement_by_id(int(statement_id))
                        if test_statement and "statement_text" in test_statement:
                            statement_text = test_statement["statement_text"]
                        break
                if statement_text:
                    break

        html += f"""
                <div id="statement_{statement_id}" class="tab-content {active_class}">
                    <h3>Statement ID: {statement_id}</h3>
        """

        if statement_text:
            html += f"""
                    <div class="statement-text">
                        <p><strong>Text:</strong> "{statement_text}"</p>
                    </div>
            """

        # Add overall performance visualization
        if f"statement_{statement_id}" in visualizations:
            html += f"""
                    <div class="visualization">
                        <h4>Model Performance Comparison</h4>
                        <img src="data:image/png;base64,{visualizations[f'statement_{statement_id}']}" alt="Model Performance for Statement {statement_id}">
                    </div>
            """

        # Add field-level performance visualization
        if f"statement_{statement_id}_fields" in visualizations:
            html += f"""
                    <div class="visualization">
                        <h4>Field-level Performance Comparison</h4>
                        <img src="data:image/png;base64,{visualizations[f'statement_{statement_id}_fields']}" alt="Field-level Performance for Statement {statement_id}">
                    </div>
            """

        html += """
                </div>
        """

    html += """
            </div>
    """

    # Close HTML with JavaScript for tab functionality
    html += """
        </div>
        <script>
            function openModelTab(evt, modelName) {
                // Hide all model tab content
                var modelTabs = document.querySelectorAll('[id^="vertex_ai"], [id^="gemini"], [id^="anthropic"], [id^="openai"]');
                for (var i = 0; i < modelTabs.length; i++) {
                    modelTabs[i].style.display = "none";
                }

                // Remove "active" class from all model tab buttons
                var modelButtons = document.querySelectorAll('.tab-button:not([onclick^="openStatementTab"])');
                for (var i = 0; i < modelButtons.length; i++) {
                    modelButtons[i].className = modelButtons[i].className.replace(" active", "");
                }

                // Show the current tab and add "active" class to the button
                document.getElementById(modelName).style.display = "block";
                evt.currentTarget.className += " active";
            }

            function openStatementTab(evt, statementId) {
                // Hide all statement tab content
                var statementTabs = document.querySelectorAll('[id^="statement_"]');
                for (var i = 0; i < statementTabs.length; i++) {
                    statementTabs[i].style.display = "none";
                }

                // Remove "active" class from all statement tab buttons
                var statementButtons = document.querySelectorAll('.tab-button[onclick^="openStatementTab"]');
                for (var i = 0; i < statementButtons.length; i++) {
                    statementButtons[i].className = statementButtons[i].className.replace(" active", "");
                }

                // Show the current tab and add "active" class to the button
                document.getElementById(statementId).style.display = "block";
                evt.currentTarget.className += " active";
            }
        </script>
    </body>
    </html>
    """

    return html
