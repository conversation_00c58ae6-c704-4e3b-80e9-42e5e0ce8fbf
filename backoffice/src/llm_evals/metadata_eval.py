"""
Module for evaluating metadata extraction capabilities of different LLM models.
"""

import os
import json
from typing import Dict, List, Any, Optional, Tu<PERSON>
from loguru import logger
import litellm
from deepeval.metrics import GEval
from deepeval.test_case import LLMTestCase, LLMTestCaseParams

from eko.models.statement_metadata import StatementMetadata
from eko.statements.metadata import validate_metadata_json
from eko.statements.prompts import get_minimal_prompt_for_metadata
from eko.models.vector.demise.demise_model import DEMISEModel

from .test_data import get_test_statements
from .utils import setup_logging, compare_metadata_results, generate_report


class MetadataEvaluator:
    """
    Evaluator for metadata extraction capabilities of LLM models.
    """

    def __init__(self, gold_standard_model: str = "gemini-2.5-pro-preview-05-06"):
        """
        Initialize the evaluator.

        Args:
            gold_standard_model: Name of the gold standard model
        """
        self.gold_standard_model = gold_standard_model
        setup_logging()

        # Set litellm to modify params for Anthropic models
        litellm.modify_params = True

        logger.info(f"Initialized MetadataEvaluator with gold standard model: {gold_standard_model}")

    def evaluate_model(
        self,
        model_name: str,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate a model's metadata extraction capabilities.

        Args:
            model_name: Name of the model to evaluate
            output_dir: Directory to save the evaluation results

        Returns:
            Dictionary containing evaluation metrics
        """
        # Get test statements
        test_statements = get_test_statements()
        logger.info(f"Evaluating model: {model_name} on {len(test_statements)} statements")

        # Get gold standard results
        gold_results = self._extract_metadata_with_gold_standard(test_statements)

        # Get test model results
        test_results = self._extract_metadata_with_test_model(model_name, test_statements)

        # Compare results
        metrics = compare_metadata_results(model_name, gold_results, test_results)
        metrics["model_name"] = model_name
        metrics["gold_standard_model"] = self.gold_standard_model

        # Include raw results for DEMISE extraction
        metrics["raw_results"] = test_results

        # Generate report only if output_dir is provided
        if output_dir:
            report_path = generate_report(metrics, model_name, output_dir, gold_results, test_results)
            metrics["report_path"] = report_path

        return metrics

    def _extract_metadata_with_gold_standard(self, test_statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract metadata using the gold standard model or use pre-defined gold standard results.

        Args:
            test_statements: List of test statements

        Returns:
            List of extraction results
        """
        logger.info(f"Extracting metadata with gold standard model: {self.gold_standard_model}")

        results = []
        for statement in test_statements:
            try:
                # Use the existing gold_metadata if available
                if "gold_metadata" in statement:
                    metadata = statement["gold_metadata"]

                    results.append({
                        "statement_id": statement["id"],
                        "metadata": metadata,
                        "success": True
                    })
                else:
                    # Extract metadata using the gold standard model
                    metadata, _ = self._extract_metadata(
                        self.gold_standard_model,
                        statement["statement_text"],
                        statement.get("context", ""),
                        statement.get("title", ""),
                        statement.get("extract", ""),
                        statement.get("authors", []),
                        statement.get("doc_year", 2023)
                    )

                    results.append({
                        "statement_id": statement["id"],
                        "metadata": metadata.model_dump() if metadata else {},
                        "success": True
                    })
            except Exception as e:
                logger.error(f"Error extracting metadata for statement {statement['id']} with gold standard: {str(e)}")
                results.append({
                    "statement_id": statement["id"],
                    "metadata": {},
                    "success": False,
                    "error": str(e)
                })

        logger.info(f"Extracted metadata for {len(results)} statements with gold standard model")
        return results

    def _extract_metadata_with_test_model(
        self,
        model_name: str,
        test_statements: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Extract metadata using the test model.

        Args:
            model_name: Name of the test model
            test_statements: List of test statements

        Returns:
            List of extraction results
        """
        logger.info(f"Extracting metadata with test model: {model_name}")

        results = []
        for statement in test_statements:
            try:
                # Extract metadata using the test model
                metadata, demise = self._extract_metadata(
                    model_name,
                    statement["statement_text"],
                    statement.get("context", ""),
                    statement.get("title", ""),
                    statement.get("extract", ""),
                    statement.get("authors", []),
                    statement.get("doc_year", 2023)
                )

                # Convert DEMISE model to sparse key-value representation
                demise_dict = {}
                if demise:
                    try:
                        # Get the sparse representation using the to_kv_sparse method
                        demise_dict = demise.to_kv_sparse()
                    except Exception as e:
                        logger.warning(f"Error extracting DEMISE model: {str(e)}")
                        # Try to access the sparse_kv attribute directly
                        if hasattr(demise, "sparse_kv"):
                            demise_dict = demise.sparse_kv

                results.append({
                    "statement_id": statement["id"],
                    "metadata": metadata.model_dump() if metadata else {},
                    "demise": demise_dict,
                    "success": True
                })
            except Exception as e:
                logger.error(f"Error extracting metadata for statement {statement['id']} with {model_name}: {str(e)}")
                results.append({
                    "statement_id": statement["id"],
                    "metadata": {},
                    "success": False,
                    "error": str(e)
                })

        logger.info(f"Extracted metadata for {len(results)} statements with test model")
        return results

    def _extract_metadata(
        self,
        model_name: str,
        statement_text: str,
        context: str = "",
        title: str = "",
        extract: str = "",
        authors: Optional[List[str]] = None,
        doc_year: Optional[int] = None
    ) -> Tuple[Optional[StatementMetadata], Optional[DEMISEModel]]:
        """
        Extract metadata from a statement using the specified model.

        Args:
            model_name: Name of the model to use
            statement_text: Statement text
            context: Context text
            title: Document title
            extract: Extract text
            authors: List of authors
            doc_year: Document year

        Returns:
            Tuple of (metadata, DEMISE model)
        """
        if authors is None:
            authors = []

        if doc_year is None:
            doc_year = 2023

        # Create the prompt for metadata extraction
        minimal_prompt = get_minimal_prompt_for_metadata(
            statement_text, context, title, extract, authors, doc_year
        )

        # minimal_prompt = f"\n\nThe schema for the metadata is:\n {json.dumps(StatementMetadata.model_json_schema())} and the schema for the DEMISE model is:\n {json.dumps(DEMISEModel.model_json_schema())}\n\n{minimal_prompt}"

        # Use litellm to call the model
        response = litellm.completion(
            model=model_name,
            messages=[{"role": "user", "content": minimal_prompt}],
            temperature=0.1,
            max_tokens=8000
        )

        # Extract the response text
        response_text = response.choices[0].message.content

        try:
            # Parse the response as JSON
            result_dict = self._extract_json_from_response(response_text)

            # Validate the metadata
            validation_result = validate_metadata_json(statement_text, result_dict)
            if not validation_result:
                logger.warning(f"Metadata validation failed for model {model_name}")

            # Extract metadata and DEMISE model
            metadata = StatementMetadata.model_validate(result_dict["metadata"])

            # Filter out any keys that aren't in the DEMISE model schema
            # This prevents validation errors when models return extra fields
            try:
                demise = DEMISEModel.from_sparse_kv(result_dict["demise"])
            except Exception as e:
                logger.warning(f"Error creating DEMISE model from response: {str(e)}")
                # Create a clean DEMISE model with only valid keys
                valid_demise_data = {}
                for key, value in result_dict["demise"].items():
                    # Only include keys that follow the expected pattern
                    if key.startswith(("domain.", "impact.", "subject.", "object.",
                                      "statement.", "engagement.", "motivation.")):
                        # Split the key to check if it's a valid DEMISE field
                        parts = key.split(".")
                        if len(parts) >= 2:
                            category = parts[0]
                            # Only include if the category is valid
                            if category in ["domain", "impact", "subject", "object",
                                           "statement", "engagement", "motivation"]:
                                valid_demise_data[key] = value

                demise = DEMISEModel.from_sparse_kv(valid_demise_data)

            return metadata, demise
        except Exception as e:
            logger.error(f"Error parsing response from {model_name}: {str(e)}")
            logger.error(f"Response text: {response_text}")
            return None, None

    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """
        Extract JSON from the LLM response, handling markdown code blocks and other formatting.

        Args:
            response_text: Response text from the LLM

        Returns:
            Extracted JSON as a dictionary
        """
        # Strip markdown code blocks if present
        cleaned_text = response_text

        # Handle ```json or ```JSON blocks
        json_block_markers = ["```json", "```JSON", "```"]

        for marker in json_block_markers:
            if marker in cleaned_text:
                # Find the start of the JSON block
                start_idx = cleaned_text.find(marker) + len(marker)
                # Find the end of the JSON block
                end_marker = "```"
                end_idx = cleaned_text.rfind(end_marker)

                if end_idx > start_idx:
                    # Extract the JSON content between the markers
                    cleaned_text = cleaned_text[start_idx:end_idx].strip()
                    break

        # Try to find JSON object in the cleaned text
        try:
            # Look for the first { and last }
            start_brace = cleaned_text.find("{")
            end_brace = cleaned_text.rfind("}")

            if start_brace >= 0 and end_brace > start_brace:
                json_str = cleaned_text[start_brace:end_brace + 1]
                return json.loads(json_str)

            # If no braces found, try to parse the entire text
            return json.loads(cleaned_text)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to extract JSON from response: {e}")
            logger.error(f"Cleaned text: {cleaned_text}")
            raise

    def evaluate_with_deepeval(
        self,
        model_name: str,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate a model using DeepEval metrics.

        Args:
            model_name: Name of the model to evaluate
            output_dir: Directory to save the evaluation results

        Returns:
            Dictionary containing evaluation metrics
        """
        logger.info(f"Evaluating model {model_name} with DeepEval")

        # Get test statements
        test_statements = get_test_statements()

        # Create DeepEval metrics
        correctness_metric = GEval(
            name="Metadata Extraction Correctness",
            criteria="Determine if the metadata extracted by the model is correct and complete based on the expected metadata.",
            evaluation_params=[LLMTestCaseParams.ACTUAL_OUTPUT, LLMTestCaseParams.EXPECTED_OUTPUT],
            threshold=0.7
        )

        test_cases = []
        scores = []

        for statement in test_statements[:3]:  # Limit to 3 statements for DeepEval
            try:
                # Get gold standard metadata from the static test data
                gold_metadata = statement.get("gold_metadata", {})

                # Get test model metadata
                test_metadata, _ = self._extract_metadata(
                    model_name,
                    statement["statement_text"],
                    statement.get("context", ""),
                    statement.get("title", ""),
                    statement.get("extract", ""),
                    statement.get("authors", []),
                    statement.get("doc_year", 2023)
                )

                # Create test case
                test_case = LLMTestCase(
                    input=statement["statement_text"],
                    actual_output=json.dumps(test_metadata.model_dump() if test_metadata else {}),
                    expected_output=json.dumps(gold_metadata)
                )

                # Measure correctness
                correctness_metric.measure(test_case)
                scores.append(correctness_metric.score)
                test_cases.append(test_case)

                logger.info(f"DeepEval score for statement {statement['id']}: {correctness_metric.score}")
                logger.info(f"Reason: {correctness_metric.reason}")

            except Exception as e:
                logger.error(f"Error in DeepEval for statement {statement['id']}: {str(e)}")

        # Calculate average score
        avg_score = sum(scores) / len(scores) if scores else 0

        metrics = {
            "model_name": model_name,
            "gold_standard_model": self.gold_standard_model,
            "deepeval_scores": scores,
            "avg_deepeval_score": avg_score,
            "test_cases": len(test_cases)
        }

        logger.info(f"DeepEval evaluation complete. Average score: {avg_score}")

        return metrics
