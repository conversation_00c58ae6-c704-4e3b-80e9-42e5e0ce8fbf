"""
Module for evaluating DEMISE extraction capabilities of different LLM models.
"""

import json
from typing import Dict, List, Any, Optional
from loguru import logger
import litellm
from deepeval.metrics import GEval
from deepeval.test_case import LLMTestCase, LLMTestCaseParams

from eko.models.vector.demise.demise_model import DEMISEModel
from eko.statements.demise import create_llm_prompt, validate_demise_model

from .test_data import get_test_statements
from .utils import setup_logging, compare_demise_results, generate_report


class DEMISEEvaluator:
    """
    Evaluator for DEMISE extraction capabilities of LLM models.
    """

    def __init__(self, gold_standard_model: str = "gemini-2.5-pro-preview-05-06"):
        """
        Initialize the evaluator.

        Args:
            gold_standard_model: Name of the gold standard model
        """
        self.gold_standard_model = gold_standard_model
        setup_logging()

        # Set litellm to modify params for Anthropic models
        litellm.modify_params = True

        logger.info(f"Initialized DEMISEEvaluator with gold standard model: {gold_standard_model}")

    def evaluate_model(
        self,
        model_name: str,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate a model's DEMISE extraction capabilities.

        Args:
            model_name: Name of the model to evaluate
            output_dir: Directory to save the evaluation results

        Returns:
            Dictionary containing evaluation metrics
        """
        # Get test statements
        test_statements = get_test_statements()
        logger.info(f"Evaluating model: {model_name} on {len(test_statements)} statements")

        # Get gold standard results
        gold_results = self._extract_demise_with_gold_standard(test_statements)

        # Get test model results
        test_results = self._extract_demise_with_test_model(model_name, test_statements)

        # Compare results
        metrics = compare_demise_results(gold_results, test_results)
        metrics["model_name"] = model_name
        metrics["gold_standard_model"] = self.gold_standard_model

        # Generate report only if output_dir is provided
        if output_dir:
            report_path = generate_report(metrics, model_name, output_dir, gold_results, test_results)
            metrics["report_path"] = report_path

        return metrics

    def _extract_demise_with_gold_standard(self, test_statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract DEMISE models using the gold standard model or use pre-defined gold standard results.

        Args:
            test_statements: List of test statements

        Returns:
            List of extraction results
        """
        logger.info(f"Extracting DEMISE with gold standard model: {self.gold_standard_model}")

        results = []
        for statement in test_statements:
            try:
                # Use the existing gold_model_json if available
                if "gold_model_json" in statement:
                    demise = statement["gold_model_json"]

                    results.append({
                        "statement_id": statement["id"],
                        "demise": demise,
                        "success": True
                    })
                # else:
                #     # Extract DEMISE using the gold standard model
                #     demise = self._extract_demise(
                #         self.gold_standard_model,
                #         statement["statement_text"],
                #         statement.get("context", ""),
                #         statement.get("title", ""),
                #         statement.get("extract", "")
                #     )
                #
                #     results.append({
                #         "statement_id": statement["id"],
                #         "demise": demise.to_kv_sparse() if demise else {},
                #         "success": True
                #     })
            except Exception as e:
                logger.error(f"Error extracting DEMISE for statement {statement['id']} with gold standard: {str(e)}")
                results.append({
                    "statement_id": statement["id"],
                    "demise": {},
                    "success": False,
                    "error": str(e)
                })

        logger.info(f"Extracted DEMISE for {len(results)} statements with gold standard model")
        return results

    def _extract_demise_with_test_model(
        self,
        model_name: str,
        test_statements: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Extract DEMISE models using the test model.

        Args:
            model_name: Name of the test model
            test_statements: List of test statements

        Returns:
            List of extraction results
        """
        logger.info(f"Extracting DEMISE with test model: {model_name}")

        results = []
        for statement in test_statements:
            try:
                # Extract DEMISE using the test model
                demise = self._extract_demise(
                    model_name,
                    statement["statement_text"],
                    statement.get("context", ""),
                    statement.get("title", ""),
                    statement.get("extract", "")
                )

                results.append({
                    "statement_id": statement["id"],
                    "demise": demise.to_kv_sparse() if demise else {},
                    "success": True
                })
            except Exception as e:
                logger.error(f"Error extracting DEMISE for statement {statement['id']} with {model_name}: {str(e)}")
                results.append({
                    "statement_id": statement["id"],
                    "demise": {},
                    "success": False,
                    "error": str(e)
                })

        logger.info(f"Extracted DEMISE for {len(results)} statements with test model")
        return results

    def _extract_demise(
        self,
        model_name: str,
        statement: str,
        context: str,
        title: str,
        extract: str
    ) -> Optional[DEMISEModel]:
        """
        Extract DEMISE model from a statement using the specified model.

        Args:
            model_name: Name of the model to use
            statement: Statement text
            context: Context text
            title: Document title
            extract: Extract text

        Returns:
            DEMISE model or None if extraction failed
        """
        # Create the prompt for DEMISE extraction
        prompt = create_llm_prompt(context, extract, statement, title, "Please return the full DEMISE model.")

        # Use litellm to call the model
        response = litellm.completion(
            model=model_name,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=4000
        )

        # Extract the response text
        response_text = response.choices[0].message.content

        try:
            # Parse the response as JSON
            result_dict = json.loads(response_text)

            # Create DEMISE model from the response
            demise = DEMISEModel.from_sparse_kv(result_dict)

            # Validate the DEMISE model
            if not validate_demise_model(demise, statement):
                logger.warning(f"DEMISE model validation failed for model {model_name}")

            return demise
        except Exception as e:
            logger.error(f"Error parsing response from {model_name}: {str(e)}")
            logger.error(f"Response text: {response_text}")
            return None

    def evaluate_with_deepeval(
        self,
        model_name: str,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate a model using DeepEval metrics.

        Args:
            model_name: Name of the model to evaluate
            output_dir: Directory to save the evaluation results

        Returns:
            Dictionary containing evaluation metrics
        """
        logger.info(f"Evaluating model {model_name} with DeepEval")

        # Get test statements
        test_statements = get_test_statements()

        # Create DeepEval metrics
        demise_quality_metric = GEval(
            name="DEMISE Extraction Quality",
            criteria="Determine if the DEMISE model extracted by the model is accurate, complete, and consistent with the statement.",
            evaluation_params=[LLMTestCaseParams.ACTUAL_OUTPUT, LLMTestCaseParams.EXPECTED_OUTPUT],
            threshold=0.7
        )

        test_cases = []
        scores = []

        for statement in test_statements[:3]:  # Limit to 3 statements for DeepEval
            try:
                # Get gold standard DEMISE
                gold_demise = statement.get("gold_model_json", {})

                # Get test model DEMISE
                test_demise = self._extract_demise(
                    model_name,
                    statement["statement_text"],
                    statement.get("context", ""),
                    statement.get("title", ""),
                    statement.get("extract", "")
                )

                # Create test case
                test_case = LLMTestCase(
                    input=statement["statement_text"],
                    actual_output=json.dumps(test_demise.to_kv_sparse() if test_demise else {}),
                    expected_output=json.dumps(gold_demise)
                )

                # Measure quality
                demise_quality_metric.measure(test_case)
                scores.append(demise_quality_metric.score)
                test_cases.append(test_case)

                logger.info(f"DeepEval score for statement {statement['id']}: {demise_quality_metric.score}")
                logger.info(f"Reason: {demise_quality_metric.reason}")

            except Exception as e:
                logger.error(f"Error in DeepEval for statement {statement['id']}: {str(e)}")

        # Calculate average score
        avg_score = sum(scores) / len(scores) if scores else 0

        metrics = {
            "model_name": model_name,
            "gold_standard_model": self.gold_standard_model,
            "deepeval_scores": scores,
            "avg_deepeval_score": avg_score,
            "test_cases": len(test_cases)
        }

        logger.info(f"DeepEval evaluation complete. Average score: {avg_score}")

        return metrics
