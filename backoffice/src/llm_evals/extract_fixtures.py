#!/usr/bin/env python
"""
Script to extract test data from the database and save it as a fixture file.
This is used to create static test data for LLM evaluations.
"""

import json
import os
import sys
from typing import List, Dict, Any
import argparse

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from eko import eko_var_path
from eko.db import get_bo_conn
from loguru import logger


def extract_statements(limit: int = 10, include_demise: bool = True, include_metadata: bool = True) -> List[Dict[str, Any]]:
    """
    Extract statements from the database with their metadata and DEMISE models.

    Args:
        limit: Maximum number of statements to extract
        include_demise: Whether to include DEMISE models
        include_metadata: Whether to include metadata

    Returns:
        List of statement dictionaries
    """
    logger.info(f"Extracting {limit} statements from the database")

    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            # Query to get statements with model_json
            query = """
            SELECT
                s.id,
                s.statement_text,
                s.context,
                s.model_json,
                d.title,
                d.authors,
                d.year
            FROM
                kg_statements_v2 s
            JOIN
                kg_documents d ON s.doc_id = d.id
            WHERE
                s.model_json IS NOT NULL
                AND s.model_json::text != '{}'
            ORDER BY
                RANDOM()
            LIMIT %s
            """

            cursor.execute(query, (limit,))
            rows = cursor.fetchall()

            statements = []
            for row in rows:
                statement_id = row[0]
                statement_text = row[1]
                context = row[2]
                model_json = row[3]
                title = row[4]
                authors = row[5] if row[5] else []
                doc_year = row[6]

                # Create statement dictionary
                statement = {
                    "id": statement_id,
                    "statement_text": statement_text,
                    "context": context,
                    "title": title,
                    "authors": authors,
                    "doc_year": doc_year
                }

                # Add model_json if requested
                if include_demise and model_json:
                    statement["gold_model_json"] = model_json

                # Add metadata if requested
                if include_metadata:
                    metadata = extract_metadata(cursor, statement_id)
                    if metadata:
                        statement["gold_metadata"] = metadata

                statements.append(statement)

            logger.info(f"Extracted {len(statements)} statements")
            return statements


def extract_metadata(cursor, statement_id: int) -> Dict[str, Any]:
    """
    Extract metadata for a statement.

    Args:
        cursor: Database cursor
        statement_id: Statement ID

    Returns:
        Metadata dictionary or None if not found
    """
    # This is a simplified version - in a real implementation, you would
    # extract actual metadata from the database

    # For now, we'll create a placeholder metadata structure
    # In a real implementation, you would query the appropriate tables

    # Example query to get metadata (replace with actual query)
    query = """
    SELECT
        model_json->'metadata' as metadata
    FROM
        kg_statements_v2
    WHERE
        id = %s
        AND model_json->'metadata' IS NOT NULL
    """

    cursor.execute(query, (statement_id,))
    row = cursor.fetchone()

    if row and row[0]:
        return row[0]

    return None


def save_fixture(statements: List[Dict[str, Any]], output_path: str) -> None:
    """
    Save statements to a fixture file.

    Args:
        statements: List of statement dictionaries
        output_path: Path to save the fixture file
    """
    logger.info(f"Saving {len(statements)} statements to {output_path}")

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Save statements to file
    with open(output_path, 'w') as f:
        json.dump(statements, f, indent=2)

    logger.info(f"Saved fixture file to {output_path}")


def main():
    """Main function to extract and save fixtures."""
    parser = argparse.ArgumentParser(description='Extract test data from the database and save as a fixture file')
    parser.add_argument('--limit', type=int, default=10, help='Maximum number of statements to extract')
    parser.add_argument('--output', type=str, default=os.path.join(eko_var_path, 'fixtures/test_statements.json'),
                        help='Path to save the fixture file')
    parser.add_argument('--no-demise', action='store_true', help='Exclude DEMISE models')
    parser.add_argument('--no-metadata', action='store_true', help='Exclude metadata')

    args = parser.parse_args()

    # Extract statements
    statements = extract_statements(
        limit=args.limit,
        include_demise=not args.no_demise,
        include_metadata=not args.no_metadata
    )

    # Save fixture
    save_fixture(statements, args.output)


if __name__ == '__main__':
    main()
