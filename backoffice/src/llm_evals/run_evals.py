"""
Main script to run LLM evaluations for metadata and DEMISE extraction.
"""

import os
import json
from typing import List, Dict, Any
from loguru import logger
import click

from eko import eko_var_path

from .metadata_eval import MetadataEvaluator
from .demise_eval import DEMISEEvaluator
from .utils import setup_logging


def run_metadata_eval(
    models: List[str],
    gold_standard: str,
    output_dir: str,
    use_deepeval: bool = False
) -> Dict[str, Any]:
    """
    Run metadata extraction evaluations.

    Args:
        models: List of models to evaluate
        gold_standard: Gold standard model
        output_dir: Output directory for results
        use_deepeval: Whether to use DeepEval for additional evaluation

    Returns:
        Dictionary of evaluation results by model
    """
    logger.info(f"Running metadata extraction evaluations for {len(models)} models")

    evaluator = MetadataEvaluator(gold_standard_model=gold_standard)
    results = {}

    for model in models:
        logger.info(f"Evaluating model: {model}")

        # Run standard evaluation
        eval_results = evaluator.evaluate_model(model, output_dir)
        results[model] = eval_results

        # Run DeepEval evaluation if requested
        if use_deepeval:
            deepeval_results = evaluator.evaluate_with_deepeval(model, output_dir)
            results[f"{model}_deepeval"] = deepeval_results

    # Save combined results
    combined_results_path = os.path.join(output_dir, "metadata_eval_results.json")
    with open(combined_results_path, "w") as f:
        json.dump(results, f, indent=2)

    logger.info(f"Metadata evaluation complete. Results saved to {combined_results_path}")

    return results


def run_demise_eval(
    models: List[str],
    gold_standard: str,
    output_dir: str,
    use_deepeval: bool = False
) -> Dict[str, Any]:
    """
    Run DEMISE extraction evaluations.

    Args:
        models: List of models to evaluate
        gold_standard: Gold standard model
        output_dir: Output directory for results
        use_deepeval: Whether to use DeepEval for additional evaluation

    Returns:
        Dictionary of evaluation results by model
    """
    logger.info(f"Running DEMISE extraction evaluations for {len(models)} models")

    evaluator = DEMISEEvaluator(gold_standard_model=gold_standard)
    results = {}

    for model in models:
        logger.info(f"Evaluating model: {model}")

        # Run standard evaluation
        eval_results = evaluator.evaluate_model(model, output_dir)
        results[model] = eval_results

        # Run DeepEval evaluation if requested
        if use_deepeval:
            deepeval_results = evaluator.evaluate_with_deepeval(model, output_dir)
            results[f"{model}_deepeval"] = deepeval_results

    # Save combined results
    combined_results_path = os.path.join(output_dir, "demise_eval_results.json")
    with open(combined_results_path, "w") as f:
        json.dump(results, f, indent=2)

    logger.info(f"DEMISE evaluation complete. Results saved to {combined_results_path}")

    return results


@click.group()
def cli():
    """Run LLM evaluations for metadata and DEMISE extraction."""
    pass


@cli.command()
@click.option(
    "--models",
    "-m",
    multiple=True,
    default=["gemini-1.5-pro", "claude-3-5-sonnet-20240620", "gpt-4o"],
    help="Models to evaluate (can be specified multiple times)"
)
@click.option(
    "--gold-standard",
    "-g",
    default="gemini-2.5-pro-preview-05-06",
    help="Gold standard model to compare against"
)
@click.option(
    "--output-dir",
    "-o",
    default=os.path.join(eko_var_path, "reports", "llm_evals"),
    help="Directory to save evaluation results"
)
@click.option(
    "--use-deepeval",
    "-d",
    is_flag=True,
    help="Use DeepEval for additional evaluation"
)
def evaluate_metadata(models, gold_standard, output_dir, use_deepeval):
    """Run metadata extraction evaluations."""
    # Set up logging
    setup_logging()

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Run metadata evaluations
    run_metadata_eval(list(models), gold_standard, output_dir, use_deepeval)

    logger.info("Metadata evaluation complete")


@cli.command()
@click.option(
    "--models",
    "-m",
    multiple=True,
    default=["gemini-1.5-pro", "claude-3-5-sonnet-20240620", "gpt-4o"],
    help="Models to evaluate (can be specified multiple times)"
)
@click.option(
    "--gold-standard",
    "-g",
    default="gemini-2.5-pro-preview-05-06",
    help="Gold standard model to compare against"
)
@click.option(
    "--output-dir",
    "-o",
    default=os.path.join(eko_var_path, "reports", "llm_evals"),
    help="Directory to save evaluation results"
)
@click.option(
    "--use-deepeval",
    "-d",
    is_flag=True,
    help="Use DeepEval for additional evaluation"
)
def evaluate_demise(models, gold_standard, output_dir, use_deepeval):
    """Run DEMISE extraction evaluations."""
    # Set up logging
    setup_logging()

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Run DEMISE evaluations
    run_demise_eval(list(models), gold_standard, output_dir, use_deepeval)

    logger.info("DEMISE evaluation complete")


@cli.command()
@click.option(
    "--models",
    "-m",
    multiple=True,
    default=["gemini-1.5-pro", "claude-3-5-sonnet-20240620", "gpt-4o"],
    help="Models to evaluate (can be specified multiple times)"
)
@click.option(
    "--gold-standard",
    "-g",
    default="gemini-2.5-pro-preview-05-06",
    help="Gold standard model to compare against"
)
@click.option(
    "--output-dir",
    "-o",
    default=os.path.join(eko_var_path, "reports", "llm_evals"),
    help="Directory to save evaluation results"
)
@click.option(
    "--use-deepeval",
    "-d",
    is_flag=True,
    help="Use DeepEval for additional evaluation"
)
def evaluate_all(models, gold_standard, output_dir, use_deepeval):
    """Run both metadata and DEMISE extraction evaluations."""
    # Set up logging
    setup_logging()

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Run both evaluations
    metadata_results = run_metadata_eval(list(models), gold_standard, output_dir, use_deepeval)
    demise_results = run_demise_eval(list(models), gold_standard, output_dir, use_deepeval)

    # Generate a single combined report
    from .html_report import generate_html_report

    # Combine all results
    combined_results = {}
    for model in models:
        if model in metadata_results and model in demise_results:
            combined_results[f"{model}_metadata"] = metadata_results[model]
            combined_results[f"{model}_demise"] = demise_results[model]

    # Generate a single HTML report with all results
    html_report_path = generate_html_report(
        combined_results,
        output_dir,
        open_browser=True
    )

    logger.info(f"All evaluations complete. Combined report generated at {html_report_path}")


if __name__ == "__main__":
    cli()
