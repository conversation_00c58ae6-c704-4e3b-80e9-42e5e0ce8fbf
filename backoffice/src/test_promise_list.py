#!/usr/bin/env python
"""
Test script to verify the PromiseVerdict fix with list_by_run.
"""
from eko.db import get_bo_conn
from eko.db.data.promise import PromiseData

def main():
    """Main function to test the PromiseVerdict fix with list_by_run."""
    try:
        # Get a database connection using the context manager
        with get_bo_conn() as conn:
            # Try to list promises from a run
            run_id = 1  # Use a valid run ID
            print(f"Attempting to load promises for run_id {run_id}...")
            promises = PromiseData.list_by_run(conn, run_id)
            
            # Print the result
            print(f"Successfully loaded {len(promises)} promises")
            
            # Print the first promise's ID to verify it's working
            if promises:
                print(f"First promise ID: {promises[0].id}")
                print(f"First promise statement_id: {promises[0].statement_id}")
        
        return True
    except Exception as e:
        import traceback
        print(f"Error testing PromiseVerdict: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
