#!/usr/bin/env python3
"""
Simple test to verify LLMOptions are passed through to providers correctly.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from eko.llm.main import LLMOptions, call_llms
from eko.llm import LLMModel

def test_llm_options_passthrough():
    """Test that LLMOptions are passed through to the provider."""

    # Create test options
    options = LLMOptions(
        temperature=0.5,
        metadata={"test": "value"},
        no_cache=True
    )

    # Simple test message
    messages = [{"role": "user", "content": "Say hello"}]

    try:
        # This should work if our changes are correct
        result = call_llms(
            [LLMModel.GROQ_LLAMA3_2_3B],
            messages,
            max_tokens=10,
            options=options
        )
        print(f"✅ Test passed! Result: {result}")
        return True
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_llm_options_passthrough()
    sys.exit(0 if success else 1)
