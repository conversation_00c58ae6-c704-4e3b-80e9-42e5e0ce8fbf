#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Setting up mono-repo development environment ===${NC}"

# Check if running on Ubuntu/Debian
if ! command -v apt-get &> /dev/null; then
    echo -e "${RED}Error: This script requires apt-get (Ubuntu/Debian)${NC}"
    exit 1
fi

# Update system packages
sudo apt-get update -qq
sudo apt-get install -y software-properties-common

# Add deadsnakes PPA for Python 3.11
sudo add-apt-repository -y ppa:deadsnakes/ppa
sudo apt-get update -qq

# Install system dependencies exactly as in Dockerfile
echo "Installing system dependencies..."
sudo apt-get install -y \
    python3.11 \
    python3.11-dev \
    python3-pip \
    git-all \
    build-essential \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    libopenblas-dev \
    liblapack-dev \
    pkg-config \
    libleptonica-dev \
    tesseract-ocr \
    libtesseract-dev \
    python3-pil \
    tesseract-ocr-eng \
    tesseract-ocr-script-latn \
    libxml2-dev \
    libxslt-dev \
    libjpeg-dev \
    zlib1g-dev \
    libpng-dev \
    libatlas-base-dev \
    gfortran \
    curl \
    wget > /dev/null 2>&1

# Install Node.js 18+ for frontend
echo "Installing Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - > /dev/null 2>&1
sudo apt-get install -y nodejs > /dev/null 2>&1

# Set Python 3.11 as default python3
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1

# Install uv package manager
echo "Installing uv..."
curl -LsSf https://astral.sh/uv/install.sh | sh > /dev/null 2>&1
echo 'export PATH="$HOME/.local/bin:$PATH"' >> $HOME/.profile
export PATH="$HOME/.local/bin:$PATH"

# Install pnpm using corepack
echo "Installing pnpm..."
sudo corepack enable > /dev/null 2>&1
corepack prepare pnpm@9.0.0 --activate > /dev/null 2>&1

# Set up Python environment in backoffice/src
echo "Setting up Python environment..."
cd /mnt/persist/workspace/backoffice/src

# Create virtual environment
uv venv > /dev/null 2>&1

# Activate virtual environment and add to profile
source .venv/bin/activate
echo 'source /mnt/persist/workspace/backoffice/src/.venv/bin/activate' >> $HOME/.profile

# Pre-install critical packages in order (as per Dockerfile)
echo "Installing critical Python packages..."
export BLIS_ARCH="generic"
uv pip install numpy cython blis spacy pip > /dev/null 2>&1

# Install all dependencies from pyproject.toml
echo -e "${YELLOW}Installing Python dependencies (this may take a few minutes)...${NC}"
if ! uv sync --dev > /dev/null 2>&1; then
    echo -e "${RED}Error: Failed to install Python dependencies${NC}"
    exit 1
fi

# Download spaCy model
echo -e "${YELLOW}Downloading spaCy English model...${NC}"
if ! uv run python -m spacy download en_core_web_sm > /dev/null 2>&1; then
    echo -e "${RED}Error: Failed to download spaCy model${NC}"
    exit 1
fi

# Install additional packages as per Dockerfile
echo -e "${YELLOW}Installing additional packages...${NC}"
if ! uv add "psycopg[binary]" "lxml[html_clean]" > /dev/null 2>&1; then
    echo -e "${RED}Error: Failed to install additional packages${NC}"
    exit 1
fi

# Install Playwright (this takes time, so show progress)
echo -e "${YELLOW}Installing Playwright browsers (this may take several minutes)...${NC}"
if ! uv run playwright install > /dev/null 2>&1; then
    echo -e "${RED}Error: Failed to install Playwright browsers${NC}"
    exit 1
fi
if ! uv run playwright install-deps > /dev/null 2>&1; then
    echo -e "${RED}Error: Failed to install Playwright dependencies${NC}"
    exit 1
fi

# Create necessary directories
mkdir -p /mnt/persist/workspace/var
ln -sf /mnt/persist/workspace/var ~/eko_var

# Go back to root and install frontend dependencies
cd /mnt/persist/workspace

# Install frontend dependencies
echo -e "${YELLOW}Installing frontend dependencies...${NC}"
if ! pnpm install > /dev/null 2>&1; then
    echo -e "${RED}Error: Failed to install frontend dependencies${NC}"
    exit 1
fi

# Build shared packages
echo -e "${YELLOW}Building shared packages...${NC}"
if ! pnpm run build > /dev/null 2>&1; then
    echo -e "${RED}Error: Failed to build shared packages${NC}"
    exit 1
fi

echo -e "${GREEN}=== Setup completed successfully ===${NC}"
echo -e "${GREEN}Python environment: /mnt/persist/workspace/backoffice/src/.venv${NC}"
echo -e "${GREEN}To activate Python environment: source /mnt/persist/workspace/backoffice/src/.venv/bin/activate${NC}"
echo ""
echo -e "${GREEN}Environment is ready for development and testing!${NC}"

# Test basic functionality
echo -e "${YELLOW}Running basic tests...${NC}"
cd /mnt/persist/workspace/backoffice/src
if uv run python -c "import spacy; print('✓ spaCy working')"; then
    echo -e "${GREEN}✓ Python environment working${NC}"
else
    echo -e "${RED}✗ Python environment test failed${NC}"
fi

cd /mnt/persist/workspace
if pnpm --version > /dev/null 2>&1; then
    echo -e "${GREEN}✓ pnpm working${NC}"
else
    echo -e "${RED}✗ pnpm test failed${NC}"
fi
