<!doctype html>
<html>
<head>
  <meta charset='UTF-8'>
  <meta name='viewport' content='width=device-width initial-scale=1'>

  <link href='https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext'
        rel='stylesheet' type='text/css' />
  <style type='text/css'>html {
      overflow-x: initial !important;
  }

  :root {
      --bg-color: #ffffff;
      --text-color: #333333;
      --select-text-bg-color: #B5D6FC;
      --select-text-font-color: auto;
      --monospace: "Lucida Console", Consolas, "Courier", monospace;
      --title-bar-height: 20px;
  }

  .mac-os-11 {
      --title-bar-height: 28px;
  }

  html {
      font-size: 14px;
      background-color: var(--bg-color);
      color: var(--text-color);
      font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
      -webkit-font-smoothing: antialiased;
  }

  h1, h2, h3, h4, h5 {
      white-space: pre-wrap;
  }

  body {
      margin: 0px;
      padding: 0px;
      height: auto;
      inset: 0px;
      font-size: 1rem;
      line-height: 1.428571;
      overflow-x: hidden;
      background: inherit;
  }

  iframe {
      margin: auto;
  }

  a.url {
      word-break: break-all;
  }

  a:active, a:hover {
      outline: 0px;
  }

  .in-text-selection, ::selection {
      text-shadow: none;
      background: var(--select-text-bg-color);
      color: var(--select-text-font-color);
  }

  #write {
      margin: 0px auto;
      height: auto;
      width: inherit;
      word-break: normal;
      overflow-wrap: break-word;
      position: relative;
      white-space: normal;
      overflow-x: visible;
      padding-top: 36px;
  }

  #write.first-line-indent p {
      text-indent: 2em;
  }

  #write.first-line-indent li p, #write.first-line-indent p * {
      text-indent: 0px;
  }

  #write.first-line-indent li {
      margin-left: 2em;
  }

  .for-image #write {
      padding-left: 8px;
      padding-right: 8px;
  }

  body.typora-export {
      padding-left: 30px;
      padding-right: 30px;
  }

  .typora-export .footnote-line, .typora-export li, .typora-export p {
      white-space: pre-wrap;
  }

  .typora-export .task-list-item input {
      pointer-events: none;
  }

  @media screen and (max-width: 500px) {
      body.typora-export {
          padding-left: 0px;
          padding-right: 0px;
      }

      #write {
          padding-left: 20px;
          padding-right: 20px;
      }
  }

  #write li > figure:last-child {
      margin-bottom: 0.5rem;
  }

  #write ol, #write ul {
      position: relative;
  }

  img {
      max-width: 100%;
      vertical-align: middle;
      image-orientation: from-image;
  }

  button, input, select, textarea {
      color: inherit;
      font-style: inherit;
      font-variant-caps: inherit;
      font-weight: inherit;
      font-stretch: inherit;
      font-size: inherit;
      line-height: inherit;
      font-family: inherit;
      font-size-adjust: inherit;
      font-kerning: inherit;
      font-variant-alternates: inherit;
      font-variant-ligatures: inherit;
      font-variant-numeric: inherit;
      font-variant-east-asian: inherit;
      font-variant-position: inherit;
      font-variant-emoji: inherit;
      font-feature-settings: inherit;
      font-optical-sizing: inherit;
      font-variation-settings: inherit;
  }

  input[type="checkbox"], input[type="radio"] {
      line-height: normal;
      padding: 0px;
  }

  *, ::after, ::before {
      box-sizing: border-box;
  }

  #write h1, #write h2, #write h3, #write h4, #write h5, #write h6, #write p, #write pre {
      width: inherit;
      position: relative;
  }

  #write svg h1, #write svg h2, #write svg h3, #write svg h4, #write svg h5, #write svg h6, #write svg p {
      position: static;
  }

  p {
      line-height: inherit;
  }

  h1, h2, h3, h4, h5, h6 {
      break-after: avoid-page;
      break-inside: avoid;
      orphans: 4;
  }

  p {
      orphans: 4;
  }

  h1 {
      font-size: 2rem;
  }

  h2 {
      font-size: 1.8rem;
  }

  h3 {
      font-size: 1.6rem;
  }

  h4 {
      font-size: 1.4rem;
  }

  h5 {
      font-size: 1.2rem;
  }

  h6 {
      font-size: 1rem;
  }

  .md-math-block, .md-rawblock, h1, h2, h3, h4, h5, h6, p {
      margin-top: 1rem;
      margin-bottom: 1rem;
  }

  .hidden {
      display: none;
  }

  .md-blockmeta {
      color: rgb(204, 204, 204);
      font-weight: 700;
      font-style: italic;
  }

  a {
      cursor: pointer;
  }

  sup.md-footnote {
      padding: 2px 4px;
      background-color: rgba(238, 238, 238, 0.7);
      color: rgb(85, 85, 85);
      border-radius: 4px;
      cursor: pointer;
  }

  sup.md-footnote a, sup.md-footnote a:hover {
      color: inherit;
      text-transform: inherit;
      text-decoration: inherit;
  }

  #write input[type="checkbox"] {
      cursor: pointer;
      width: inherit;
      height: inherit;
  }

  figure {
      overflow-x: auto;
      margin: 1.2em 0px;
      max-width: calc(100% + 16px);
      padding: 0px;
  }

  figure > table {
      margin: 0px;
  }

  thead, tr {
      break-inside: avoid;
      break-after: auto;
  }

  thead {
      display: table-header-group;
  }

  table {
      border-collapse: collapse;
      border-spacing: 0px;
      width: 100%;
      overflow: auto;
      break-inside: auto;
      text-align: left;
  }

  table.md-table td {
      min-width: 32px;
  }

  .CodeMirror-gutters {
      border-right-width: 0px;
      border-right-style: none;
      border-right-color: currentcolor;
      background-color: inherit;
  }

  .CodeMirror-linenumber {
      -webkit-user-select: none;
  }

  .CodeMirror {
      text-align: left;
  }

  .CodeMirror-placeholder {
      opacity: 0.3;
  }

  .CodeMirror pre {
      padding: 0px 4px;
  }

  .CodeMirror-lines {
      padding: 0px;
  }

  div.hr:focus {
      cursor: none;
  }

  #write pre {
      white-space: pre-wrap;
  }

  #write.fences-no-line-wrapping pre {
      white-space: pre;
  }

  #write pre.ty-contain-cm {
      white-space: normal;
  }

  .CodeMirror-gutters {
      margin-right: 4px;
  }

  .md-fences {
      font-size: 0.9rem;
      display: block;
      break-inside: avoid;
      text-align: left;
      overflow: visible;
      white-space: pre;
      background: inherit;
      position: relative !important;
  }

  .md-fences-adv-panel {
      width: 100%;
      margin-top: 10px;
      text-align: center;
      padding-top: 0px;
      padding-bottom: 8px;
      overflow-x: auto;
  }

  #write .md-fences.mock-cm {
      white-space: pre-wrap;
  }

  .md-fences.md-fences-with-lineno {
      padding-left: 0px;
  }

  #write.fences-no-line-wrapping .md-fences.mock-cm {
      white-space: pre;
      overflow-x: auto;
  }

  .md-fences.mock-cm.md-fences-with-lineno {
      padding-left: 8px;
  }

  .CodeMirror-line, twitterwidget {
      break-inside: avoid;
  }

  svg {
      break-inside: avoid;
  }

  .footnotes {
      opacity: 0.8;
      font-size: 0.9rem;
      margin-top: 1em;
      margin-bottom: 1em;
  }

  .footnotes + .footnotes {
      margin-top: 0px;
  }

  .md-reset {
      margin: 0px;
      padding: 0px;
      border: 0px;
      outline: 0px;
      vertical-align: top;
      background: 0px 0px;
      text-decoration: none;
      text-shadow: none;
      float: none;
      position: static;
      width: auto;
      height: auto;
      white-space: nowrap;
      cursor: inherit;
      line-height: normal;
      font-weight: 400;
      text-align: left;
      box-sizing: content-box;
      direction: ltr;
  }

  li div {
      padding-top: 0px;
  }

  blockquote {
      margin: 1rem 0px;
  }

  li .mathjax-block, li p {
      margin: 0.5rem 0px;
  }

  li blockquote {
      margin: 1rem 0px;
  }

  li {
      margin: 0px;
      position: relative;
  }

  blockquote > :last-child {
      margin-bottom: 0px;
  }

  blockquote > :first-child, li > :first-child {
      margin-top: 0px;
  }

  .footnotes-area {
      color: rgb(136, 136, 136);
      margin-top: 0.714rem;
      padding-bottom: 0.143rem;
      white-space: normal;
  }

  #write .footnote-line {
      white-space: pre-wrap;
  }

  @media print {
      body, html {
          border: 1px solid transparent;
          height: 99%;
          break-after: avoid;
          break-before: avoid;
          font-variant-ligatures: no-common-ligatures;
      }

      #write {
          margin-top: 0px;
          border-color: transparent !important;
          padding-top: 0px !important;
          padding-bottom: 0px !important;
      }

      .typora-export * {
          print-color-adjust: exact;
      }

      .typora-export #write {
          break-after: avoid;
      }

      .typora-export #write::after {
          height: 0px;
      }

      .is-mac table {
          break-inside: avoid;
      }

      #write > p:nth-child(1) {
          margin-top: 0px;
      }

      .typora-export-show-outline .typora-export-sidebar {
          display: none;
      }

      figure {
          overflow-x: visible;
      }
  }

  .footnote-line {
      margin-top: 0.714em;
      font-size: 0.7em;
  }

  a img, img a {
      cursor: pointer;
  }

  pre.md-meta-block {
      font-size: 0.8rem;
      min-height: 0.8rem;
      white-space: pre-wrap;
      background: rgb(204, 204, 204);
      display: block;
      overflow-x: hidden;
  }

  p > .md-image:only-child:not(.md-img-error) img, p > img:only-child {
      display: block;
      margin: auto;
  }

  #write.first-line-indent p > .md-image:only-child:not(.md-img-error) img {
      left: -2em;
      position: relative;
  }

  p > .md-image:only-child {
      display: inline-block;
      width: 100%;
  }

  #write .MathJax_Display {
      margin: 0.8em 0px 0px;
  }

  .md-math-block {
      width: 100%;
  }

  .md-math-block:not(:empty)::after {
      display: none;
  }

  .MathJax_ref {
      fill: currentcolor;
  }

  [contenteditable="true"]:active, [contenteditable="true"]:focus, [contenteditable="false"]:active, [contenteditable="false"]:focus {
      outline: 0px;
      box-shadow: none;
  }

  .md-task-list-item {
      position: relative;
      list-style-type: none;
  }

  .task-list-item.md-task-list-item {
      padding-left: 0px;
  }

  .md-task-list-item > input {
      position: absolute;
      top: 0px;
      left: 0px;
      margin-left: -1.2em;
      margin-top: calc(1em - 10px);
      border: medium;
  }

  .math {
      font-size: 1rem;
  }

  .md-toc {
      min-height: 3.58rem;
      position: relative;
      font-size: 0.9rem;
      border-radius: 10px;
  }

  .md-toc-content {
      position: relative;
      margin-left: 0px;
  }

  .md-toc-content::after, .md-toc::after {
      display: none;
  }

  .md-toc-item {
      display: block;
      color: rgb(65, 131, 196);
  }

  .md-toc-item a {
      text-decoration: none;
  }

  .md-toc-inner:hover {
      text-decoration: underline;
  }

  .md-toc-inner {
      display: inline-block;
      cursor: pointer;
  }

  .md-toc-h1 .md-toc-inner {
      margin-left: 0px;
      font-weight: 700;
  }

  .md-toc-h2 .md-toc-inner {
      margin-left: 2em;
  }

  .md-toc-h3 .md-toc-inner {
      margin-left: 4em;
  }

  .md-toc-h4 .md-toc-inner {
      margin-left: 6em;
  }

  .md-toc-h5 .md-toc-inner {
      margin-left: 8em;
  }

  .md-toc-h6 .md-toc-inner {
      margin-left: 10em;
  }

  @media screen and (max-width: 48em) {
      .md-toc-h3 .md-toc-inner {
          margin-left: 3.5em;
      }

      .md-toc-h4 .md-toc-inner {
          margin-left: 5em;
      }

      .md-toc-h5 .md-toc-inner {
          margin-left: 6.5em;
      }

      .md-toc-h6 .md-toc-inner {
          margin-left: 8em;
      }
  }

  a.md-toc-inner {
      font-size: inherit;
      font-style: inherit;
      font-weight: inherit;
      line-height: inherit;
  }

  .footnote-line a:not(.reversefootnote) {
      color: inherit;
  }

  .reversefootnote {
      font-family: ui-monospace, sans-serif;
  }

  .md-attr {
      display: none;
  }

  .md-fn-count::after {
      content: ".";
  }

  code, pre, samp, tt {
      font-family: var(--monospace);
  }

  kbd {
      margin: 0px 0.1em;
      padding: 0.1em 0.6em;
      font-size: 0.8em;
      color: rgb(36, 39, 41);
      background: rgb(255, 255, 255);
      border: 1px solid rgb(173, 179, 185);
      border-radius: 3px;
      box-shadow: rgba(12, 13, 14, 0.2) 0px 1px 0px, rgb(255, 255, 255) 0px 0px 0px 2px inset;
      white-space: nowrap;
      vertical-align: middle;
  }

  .md-comment {
      color: rgb(162, 127, 3);
      opacity: 0.6;
      font-family: var(--monospace);
  }

  code {
      text-align: left;
      vertical-align: initial;
  }

  a.md-print-anchor {
      white-space: pre !important;
      border-width: medium !important;
      border-style: none !important;
      border-color: currentcolor !important;
      display: inline-block !important;
      position: absolute !important;
      width: 1px !important;
      right: 0px !important;
      outline: 0px !important;
      background: 0px 0px !important;
      text-decoration: initial !important;
      text-shadow: initial !important;
  }

  .os-windows.monocolor-emoji .md-emoji {
      font-family: "Segoe UI Symbol", sans-serif;
  }

  .md-diagram-panel > svg {
      max-width: 100%;
  }

  [lang="flow"] svg, [lang="mermaid"] svg {
      max-width: 100%;
      height: auto;
  }

  [lang="mermaid"] .node text {
      font-size: 1rem;
  }

  table tr th {
      border-bottom-width: 0px;
      border-bottom-style: none;
      border-bottom-color: currentcolor;
  }

  video {
      max-width: 100%;
      display: block;
      margin: 0px auto;
  }

  iframe {
      max-width: 100%;
      width: 100%;
      border: medium;
  }

  .highlight td, .highlight tr {
      border: 0px;
  }

  mark {
      background: rgb(255, 255, 0);
      color: rgb(0, 0, 0);
  }

  .md-html-inline .md-plain, .md-html-inline strong, mark .md-inline-math, mark strong {
      color: inherit;
  }

  .md-expand mark .md-meta {
      opacity: 0.3 !important;
  }

  mark .md-meta {
      color: rgb(0, 0, 0);
  }

  @media print {
      .typora-export h1, .typora-export h2, .typora-export h3, .typora-export h4, .typora-export h5, .typora-export h6 {
          break-inside: avoid;
      }
  }

  .md-diagram-panel .messageText {
      stroke: none !important;
  }

  .md-diagram-panel .start-state {
      fill: var(--node-fill);
  }

  .md-diagram-panel .edgeLabel rect {
      opacity: 1 !important;
  }

  .md-fences.md-fences-math {
      font-size: 1em;
  }

  .md-fences-advanced:not(.md-focus) {
      padding: 0px;
      white-space: nowrap;
      border: 0px;
  }

  .md-fences-advanced:not(.md-focus) {
      background: inherit;
  }

  .mermaid-svg {
      margin: auto;
  }

  .typora-export-show-outline .typora-export-content {
      max-width: 1440px;
      margin: auto;
      display: flex;
      flex-direction: row;
  }

  .typora-export-sidebar {
      width: 300px;
      font-size: 0.8rem;
      margin-top: 80px;
      margin-right: 18px;
  }

  .typora-export-show-outline #write {
      --webkit-flex: 2;
      flex: 2 1 0%;
  }

  .typora-export-sidebar .outline-content {
      position: fixed;
      top: 0px;
      max-height: 100%;
      overflow: hidden auto;
      padding-bottom: 30px;
      padding-top: 60px;
      width: 300px;
  }

  @media screen and (max-width: 1024px) {
      .typora-export-sidebar, .typora-export-sidebar .outline-content {
          width: 240px;
      }
  }

  @media screen and (max-width: 800px) {
      .typora-export-sidebar {
          display: none;
      }
  }

  .outline-content li, .outline-content ul {
      margin-left: 0px;
      margin-right: 0px;
      padding-left: 0px;
      padding-right: 0px;
      list-style: none;
      overflow-wrap: anywhere;
  }

  .outline-content ul {
      margin-top: 0px;
      margin-bottom: 0px;
  }

  .outline-content strong {
      font-weight: 400;
  }

  .outline-expander {
      width: 1rem;
      height: 1.428571rem;
      position: relative;
      display: table-cell;
      vertical-align: middle;
      cursor: pointer;
      padding-left: 4px;
  }

  .outline-expander::before {
      content: "";
      position: relative;
      font-family: Ionicons;
      display: inline-block;
      font-size: 8px;
      vertical-align: middle;
  }

  .outline-item {
      padding-top: 3px;
      padding-bottom: 3px;
      cursor: pointer;
  }

  .outline-expander:hover::before {
      content: "";
  }

  .outline-h1 > .outline-item {
      padding-left: 0px;
  }

  .outline-h2 > .outline-item {
      padding-left: 1em;
  }

  .outline-h3 > .outline-item {
      padding-left: 2em;
  }

  .outline-h4 > .outline-item {
      padding-left: 3em;
  }

  .outline-h5 > .outline-item {
      padding-left: 4em;
  }

  .outline-h6 > .outline-item {
      padding-left: 5em;
  }

  .outline-label {
      cursor: pointer;
      display: table-cell;
      vertical-align: middle;
      text-decoration: none;
      color: inherit;
  }

  .outline-label:hover {
      text-decoration: underline;
  }

  .outline-item:hover {
      border-color: rgb(245, 245, 245);
      background-color: var(--item-hover-bg-color);
  }

  .outline-item:hover {
      margin-left: -28px;
      margin-right: -28px;
      border-left-width: 28px;
      border-left-style: solid;
      border-left-color: transparent;
      border-right-width: 28px;
      border-right-style: solid;
      border-right-color: transparent;
  }

  .outline-item-single .outline-expander::before, .outline-item-single .outline-expander:hover::before {
      display: none;
  }

  .outline-item-open > .outline-item > .outline-expander::before {
      content: "";
  }

  .outline-children {
      display: none;
  }

  .info-panel-tab-wrapper {
      display: none;
  }

  .outline-item-open > .outline-children {
      display: block;
  }

  .typora-export .outline-item {
      padding-top: 1px;
      padding-bottom: 1px;
  }

  .typora-export .outline-item:hover {
      margin-right: -8px;
      border-right-width: 8px;
      border-right-style: solid;
      border-right-color: transparent;
  }

  .typora-export .outline-expander::before {
      content: "+";
      font-family: inherit;
      top: -1px;
  }

  .typora-export .outline-expander:hover::before, .typora-export .outline-item-open > .outline-item > .outline-expander::before {
      content: "−";
  }

  .typora-export-collapse-outline .outline-children {
      display: none;
  }

  .typora-export-collapse-outline .outline-item-open > .outline-children, .typora-export-no-collapse-outline .outline-children {
      display: block;
  }

  .typora-export-no-collapse-outline .outline-expander::before {
      content: "" !important;
  }

  .typora-export-show-outline .outline-item-active > .outline-item .outline-label {
      font-weight: 700;
  }

  .md-inline-math-container mjx-container {
      zoom: 0.95;
  }

  mjx-container {
      break-inside: avoid;
  }

  .md-alert.md-alert-note {
      border-left-color: rgb(9, 105, 218);
  }

  .md-alert.md-alert-important {
      border-left-color: rgb(130, 80, 223);
  }

  .md-alert.md-alert-warning {
      border-left-color: rgb(154, 103, 0);
  }

  .md-alert.md-alert-tip {
      border-left-color: rgb(31, 136, 61);
  }

  .md-alert.md-alert-caution {
      border-left-color: rgb(207, 34, 46);
  }

  .md-alert {
      padding: 0px 1em;
      margin-bottom: 16px;
      color: inherit;
      border-left-width: 0.25em;
      border-left-style: solid;
      border-left-color: rgb(0, 0, 0);
  }

  .md-alert-text-note {
      color: rgb(9, 105, 218);
  }

  .md-alert-text-important {
      color: rgb(130, 80, 223);
  }

  .md-alert-text-warning {
      color: rgb(154, 103, 0);
  }

  .md-alert-text-tip {
      color: rgb(31, 136, 61);
  }

  .md-alert-text-caution {
      color: rgb(207, 34, 46);
  }

  .md-alert-text {
      font-size: 0.9rem;
      font-weight: 700;
  }

  .md-alert-text svg {
      fill: currentcolor;
      position: relative;
      top: 0.125em;
      margin-right: 1ch;
      overflow: visible;
  }

  .md-alert-text-container::after {
      content: attr(data-text);
      text-transform: capitalize;
      pointer-events: none;
      margin-right: 1ch;
  }


  .CodeMirror {
      height: auto;
  }

  .CodeMirror.cm-s-inner {
      background: inherit;
  }

  .CodeMirror-scroll {
      overflow: auto hidden;
      z-index: 3;
  }

  .CodeMirror-gutter-filler, .CodeMirror-scrollbar-filler {
      background-color: rgb(255, 255, 255);
  }

  .CodeMirror-gutters {
      border-right-width: 1px;
      border-right-style: solid;
      border-right-color: rgb(221, 221, 221);
      background: inherit;
      white-space: nowrap;
  }

  .CodeMirror-linenumber {
      padding: 0px 3px 0px 5px;
      text-align: right;
      color: rgb(153, 153, 153);
  }

  .cm-s-inner .cm-keyword {
      color: rgb(119, 0, 136);
  }

  .cm-s-inner .cm-atom, .cm-s-inner.cm-atom {
      color: rgb(34, 17, 153);
  }

  .cm-s-inner .cm-number {
      color: rgb(17, 102, 68);
  }

  .cm-s-inner .cm-def {
      color: rgb(0, 0, 255);
  }

  .cm-s-inner .cm-variable {
      color: rgb(0, 0, 0);
  }

  .cm-s-inner .cm-variable-2 {
      color: rgb(0, 85, 170);
  }

  .cm-s-inner .cm-variable-3 {
      color: rgb(0, 136, 85);
  }

  .cm-s-inner .cm-string {
      color: rgb(170, 17, 17);
  }

  .cm-s-inner .cm-property {
      color: rgb(0, 0, 0);
  }

  .cm-s-inner .cm-operator {
      color: rgb(152, 26, 26);
  }

  .cm-s-inner .cm-comment, .cm-s-inner.cm-comment {
      color: rgb(170, 85, 0);
  }

  .cm-s-inner .cm-string-2 {
      color: rgb(255, 85, 0);
  }

  .cm-s-inner .cm-meta {
      color: rgb(85, 85, 85);
  }

  .cm-s-inner .cm-qualifier {
      color: rgb(85, 85, 85);
  }

  .cm-s-inner .cm-builtin {
      color: rgb(51, 0, 170);
  }

  .cm-s-inner .cm-bracket {
      color: rgb(153, 153, 119);
  }

  .cm-s-inner .cm-tag {
      color: rgb(17, 119, 0);
  }

  .cm-s-inner .cm-attribute {
      color: rgb(0, 0, 204);
  }

  .cm-s-inner .cm-header, .cm-s-inner.cm-header {
      color: rgb(0, 0, 255);
  }

  .cm-s-inner .cm-quote, .cm-s-inner.cm-quote {
      color: rgb(0, 153, 0);
  }

  .cm-s-inner .cm-hr, .cm-s-inner.cm-hr {
      color: rgb(153, 153, 153);
  }

  .cm-s-inner .cm-link, .cm-s-inner.cm-link {
      color: rgb(0, 0, 204);
  }

  .cm-negative {
      color: rgb(221, 68, 68);
  }

  .cm-positive {
      color: rgb(34, 153, 34);
  }

  .cm-header, .cm-strong {
      font-weight: 700;
  }

  .cm-del {
      text-decoration: line-through;
  }

  .cm-em {
      font-style: italic;
  }

  .cm-link {
      text-decoration: underline;
  }

  .cm-error {
      color: red;
  }

  .cm-invalidchar {
      color: red;
  }

  .cm-constant {
      color: rgb(38, 139, 210);
  }

  .cm-defined {
      color: rgb(181, 137, 0);
  }

  div.CodeMirror span.CodeMirror-matchingbracket {
      color: rgb(0, 255, 0);
  }

  div.CodeMirror span.CodeMirror-nonmatchingbracket {
      color: rgb(255, 34, 34);
  }

  .cm-s-inner .CodeMirror-activeline-background {
      background: inherit;
  }

  .CodeMirror {
      position: relative;
      overflow: hidden;
  }

  .CodeMirror-scroll {
      height: 100%;
      outline: 0px;
      position: relative;
      box-sizing: content-box;
      background: inherit;
  }

  .CodeMirror-sizer {
      position: relative;
  }

  .CodeMirror-gutter-filler, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-vscrollbar {
      position: absolute;
      z-index: 6;
      display: none;
      outline: 0px;
  }

  .CodeMirror-vscrollbar {
      right: 0px;
      top: 0px;
      overflow: hidden;
  }

  .CodeMirror-hscrollbar {
      bottom: 0px;
      left: 0px;
      overflow: auto hidden;
  }

  .CodeMirror-scrollbar-filler {
      right: 0px;
      bottom: 0px;
  }

  .CodeMirror-gutter-filler {
      left: 0px;
      bottom: 0px;
  }

  .CodeMirror-gutters {
      position: absolute;
      left: 0px;
      top: 0px;
      padding-bottom: 10px;
      z-index: 3;
      overflow-y: hidden;
  }

  .CodeMirror-gutter {
      white-space: normal;
      height: 100%;
      box-sizing: content-box;
      padding-bottom: 30px;
      margin-bottom: -32px;
      display: inline-block;
  }

  .CodeMirror-gutter-wrapper {
      position: absolute;
      z-index: 4;
      background: 0px 0px !important;
      border: medium !important;
  }

  .CodeMirror-gutter-background {
      position: absolute;
      top: 0px;
      bottom: 0px;
      z-index: 4;
  }

  .CodeMirror-gutter-elt {
      position: absolute;
      cursor: default;
      z-index: 4;
  }

  .CodeMirror-lines {
      cursor: text;
  }

  .CodeMirror pre {
      border-radius: 0px;
      border-width: 0px;
      background: 0px 0px;
      font-family: inherit;
      font-size: inherit;
      margin: 0px;
      white-space: pre;
      overflow-wrap: normal;
      color: inherit;
      z-index: 2;
      position: relative;
      overflow: visible;
  }

  .CodeMirror-wrap pre {
      overflow-wrap: break-word;
      white-space: pre-wrap;
      word-break: normal;
  }

  .CodeMirror-code pre {
      border-right-width: 30px;
      border-right-style: solid;
      border-right-color: transparent;
      width: fit-content;
  }

  .CodeMirror-wrap .CodeMirror-code pre {
      border-right-width: medium;
      border-right-style: none;
      border-right-color: currentcolor;
      width: auto;
  }

  .CodeMirror-linebackground {
      position: absolute;
      inset: 0px;
      z-index: 0;
  }

  .CodeMirror-linewidget {
      position: relative;
      z-index: 2;
      overflow: auto;
  }

  .CodeMirror-wrap .CodeMirror-scroll {
      overflow-x: hidden;
  }

  .CodeMirror-measure {
      position: absolute;
      width: 100%;
      height: 0px;
      overflow: hidden;
      visibility: hidden;
  }

  .CodeMirror-measure pre {
      position: static;
  }

  .CodeMirror div.CodeMirror-cursor {
      position: absolute;
      visibility: hidden;
      border-right-width: medium;
      border-right-style: none;
      border-right-color: currentcolor;
      width: 0px;
  }

  .CodeMirror div.CodeMirror-cursor {
      visibility: hidden;
  }

  .CodeMirror-focused div.CodeMirror-cursor {
      visibility: inherit;
  }

  .cm-searching {
      background: rgba(255, 255, 0, 0.4);
  }

  span.cm-underlined {
      text-decoration: underline;
  }

  span.cm-strikethrough {
      text-decoration: line-through;
  }

  .cm-tw-syntaxerror {
      color: rgb(255, 255, 255);
      background-color: rgb(153, 0, 0);
  }

  .cm-tw-deleted {
      text-decoration: line-through;
  }

  .cm-tw-header5 {
      font-weight: 700;
  }

  .cm-tw-listitem:first-child {
      padding-left: 10px;
  }

  .cm-tw-box {
      border-style: solid;
      border-right-width: 1px;
      border-bottom-width: 1px;
      border-left-width: 1px;
      border-color: inherit;
      border-top-width: 0px !important;
  }

  .cm-tw-underline {
      text-decoration: underline;
  }

  @media print {
      .CodeMirror div.CodeMirror-cursor {
          visibility: hidden;
      }
  }


  :root {
      --side-bar-bg-color: #fafafa;
      --control-text-color: #777;
  }

  @include-when-export url(https://fonts.googleapis.com/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext);

  /* open-sans-regular - latin-ext_latin */
  /* open-sans-italic - latin-ext_latin */
  /* open-sans-700 - latin-ext_latin */
  /* open-sans-700italic - latin-ext_latin */
  html {
      font-size: 16px;
      -webkit-font-smoothing: antialiased;
  }

  body {
      font-family: "Open Sans", "Clear Sans", "Helvetica Neue", Helvetica, Arial, 'Segoe UI Emoji', sans-serif;
      color: rgb(51, 51, 51);
      line-height: 1.6;
  }

  #write {
      max-width: 860px;
      margin: 0 auto;
      padding: 30px;
      padding-bottom: 100px;
  }

  @media only screen and (min-width: 1400px) {
      #write {
          max-width: 1024px;
      }
  }

  @media only screen and (min-width: 1800px) {
      #write {
          max-width: 1200px;
      }
  }

  #write > ul:first-child,
  #write > ol:first-child {
      margin-top: 30px;
  }

  a {
      color: #4183C4;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
      position: relative;
      margin-top: 1rem;
      margin-bottom: 1rem;
      font-weight: bold;
      line-height: 1.4;
      cursor: text;
  }

  h1:hover a.anchor,
  h2:hover a.anchor,
  h3:hover a.anchor,
  h4:hover a.anchor,
  h5:hover a.anchor,
  h6:hover a.anchor {
      text-decoration: none;
  }

  h1 tt,
  h1 code {
      font-size: inherit;
  }

  h2 tt,
  h2 code {
      font-size: inherit;
  }

  h3 tt,
  h3 code {
      font-size: inherit;
  }

  h4 tt,
  h4 code {
      font-size: inherit;
  }

  h5 tt,
  h5 code {
      font-size: inherit;
  }

  h6 tt,
  h6 code {
      font-size: inherit;
  }

  h1 {
      font-size: 2.25em;
      line-height: 1.2;
      border-bottom: 1px solid #eee;
  }

  h2 {
      font-size: 1.75em;
      line-height: 1.225;
      border-bottom: 1px solid #eee;
  }

  /*@media print {
      .typora-export h1,
      .typora-export h2 {
          border-bottom: none;
          padding-bottom: initial;
      }

      .typora-export h1::after,
      .typora-export h2::after {
          content: "";
          display: block;
          height: 100px;
          margin-top: -96px;
          border-top: 1px solid #eee;
      }
  }*/

  h3 {
      font-size: 1.5em;
      line-height: 1.43;
  }

  h4 {
      font-size: 1.25em;
  }

  h5 {
      font-size: 1em;
  }

  h6 {
      font-size: 1em;
      color: #777;
  }

  p,
  blockquote,
  ul,
  ol,
  dl,
  table {
      margin: 0.8em 0;
  }

  li > ol,
  li > ul {
      margin: 0 0;
  }

  hr {
      height: 2px;
      padding: 0;
      margin: 16px 0;
      background-color: #e7e7e7;
      border: 0 none;
      overflow: hidden;
      box-sizing: content-box;
  }

  li p.first {
      display: inline-block;
  }

  ul,
  ol {
      padding-left: 30px;
  }

  ul:first-child,
  ol:first-child {
      margin-top: 0;
  }

  ul:last-child,
  ol:last-child {
      margin-bottom: 0;
  }

  blockquote {
      border-left: 4px solid #dfe2e5;
      padding: 0 15px;
      color: #777777;
  }

  blockquote blockquote {
      padding-right: 0;
  }

  table {
      padding: 0;
      word-break: initial;
  }

  table tr {
      border: 1px solid #dfe2e5;
      margin: 0;
      padding: 0;
  }

  table tr:nth-child(2n),
  thead {
      background-color: #f8f8f8;
  }

  table th {
      font-weight: bold;
      border: 1px solid #dfe2e5;
      border-bottom: 0;
      margin: 0;
      padding: 6px 13px;
  }

  table td {
      border: 1px solid #dfe2e5;
      margin: 0;
      padding: 6px 13px;
  }

  table th:first-child,
  table td:first-child {
      margin-top: 0;
  }

  table th:last-child,
  table td:last-child {
      margin-bottom: 0;
  }

  .CodeMirror-lines {
      padding-left: 4px;
  }

  .code-tooltip {
      box-shadow: 0 1px 1px 0 rgba(0, 28, 36, .3);
      border-top: 1px solid #eef2f2;
  }

  .md-fences,
  code,
  tt {
      border: 1px solid #e7eaed;
      background-color: #f8f8f8;
      border-radius: 3px;
      padding: 0;
      padding: 2px 4px 0px 4px;
      font-size: 0.9em;
  }

  code {
      background-color: #f3f4f4;
      padding: 0 2px 0 2px;
  }

  .md-fences {
      margin-bottom: 15px;
      margin-top: 15px;
      padding-top: 8px;
      padding-bottom: 6px;
  }


  .md-task-list-item > input {
      margin-left: -1.3em;
  }

  @media print {
      html {
          font-size: 13px;
      }

      pre {
          page-break-inside: avoid;
          word-wrap: break-word;
      }
  }

  .md-fences {
      background-color: #f8f8f8;
  }

  #write pre.md-meta-block {
      padding: 1rem;
      font-size: 85%;
      line-height: 1.45;
      background-color: #f7f7f7;
      border: 0;
      border-radius: 3px;
      color: #777777;
      margin-top: 0 !important;
  }

  .mathjax-block > .code-tooltip {
      bottom: .375rem;
  }

  .md-mathjax-midline {
      background: #fafafa;
  }

  #write > h3.md-focus:before {
      left: -1.5625rem;
      top: .375rem;
  }

  #write > h4.md-focus:before {
      left: -1.5625rem;
      top: .285714286rem;
  }

  #write > h5.md-focus:before {
      left: -1.5625rem;
      top: .285714286rem;
  }

  #write > h6.md-focus:before {
      left: -1.5625rem;
      top: .285714286rem;
  }

  .md-image > .md-meta {
      /*border: 1px solid #ddd;*/
      border-radius: 3px;
      padding: 2px 0px 0px 4px;
      font-size: 0.9em;
      color: inherit;
  }

  .md-tag {
      color: #a7a7a7;
      opacity: 1;
  }

  .md-toc {
      margin-top: 20px;
      padding-bottom: 20px;
  }

  .sidebar-tabs {
      border-bottom: none;
  }

  #typora-quick-open {
      border: 1px solid #ddd;
      background-color: #f8f8f8;
  }

  #typora-quick-open-item {
      background-color: #FAFAFA;
      border-color: #FEFEFE #e5e5e5 #e5e5e5 #eee;
      border-style: solid;
      border-width: 1px;
  }

  /** focus mode */
  .on-focus-mode blockquote {
      border-left-color: rgba(85, 85, 85, 0.12);
  }

  header, .context-menu, .megamenu-content, footer {
      font-family: "Segoe UI", "Arial", sans-serif;
  }

  .file-node-content:hover .file-node-icon,
  .file-node-content:hover .file-node-open-state {
      visibility: visible;
  }

  .mac-seamless-mode #typora-sidebar {
      background-color: #fafafa;
      background-color: var(--side-bar-bg-color);
  }

  .mac-os #write {
      caret-color: AccentColor;
  }

  .md-lang {
      color: #b4654d;
  }

  /*.html-for-mac {
      --item-hover-bg-color: #E6F0FE;
  }*/

  #md-notification .btn {
      border: 0;
  }

  .dropdown-menu .divider {
      border-color: #e5e5e5;
      opacity: 0.4;
  }

  .ty-preferences .window-content {
      background-color: #fafafa;
  }

  .ty-preferences .nav-group-item.active {
      color: white;
      background: #999;
  }

  .menu-item-container a.menu-style-btn {
      background-color: #f5f8fa;
      background-image: linear-gradient(180deg, hsla(0, 0%, 100%, 0.8), hsla(0, 0%, 100%, 0));
  }


  @media print {
      @page {
          margin: 0 0 0 0;
      }

      body.typora-export {
          padding-left: 0;
          padding-right: 0;
      }

      #write {
          padding: 0;
      }
  }
  </style>
  <title>turing_main</title>
</head>
<body class='typora-export'>
<div class='typora-export-content'>
  <div id='write' class=''><h1 id='a-framework-for-analyzing-corporate-statements-actions-and-future-behavior'><span>A Framework for Analyzing Corporate Statements, Actions, and Future Behavior</span>
  </h1>
    <p><strong><span>Author</span></strong><span>: Neil Ellis  </span><strong><span>Affiliation</span></strong><span>: ekoIntelligence </span><strong><span>Contact</span></strong><span>: </span><a
      href='mailto:<EMAIL>' target='_blank' class='url'><EMAIL></a></p>
    <h2 id='abstract'><span>Abstract  </span></h2>
    <p><span>Corporate statements and actions often reveal important clues about an organization’s ethical posture, future commitments, and willingness to problem-solve. This paper proposes a structured, three-dimensional taxonomy to classify and interpret corporate behavior across the dimensions of </span><strong><span>HONESTY</span></strong><span>, </span><strong><span>INTEGRITY</span></strong><span>, and </span><strong><span>ADAPTABILITY</span></strong><span>. Within this framework, corporate communications—encompassing promises, problem-solving activities, and claims—are mapped onto </span><strong><span>Solution Orientation</span></strong><span> (ranging from </span><em><span>Solving</span></em><span> to </span><em><span>Deceptive</span></em><span>) and </span><strong><span>Responsibility</span></strong><span> (ranging from </span><em><span>Active</span></em><span> to </span><em><span>Dismissive</span></em><span>). By assigning these communications numerical vectors and tracking changes over time, the model enables predicting potential outcomes, including genuine problem-solving efforts, misleading strategies, or potential legal intimidation tactics (e.g., SLAPP suits). This paper thus provides a systematic approach for researchers, regulators, and stakeholders to evaluate how corporations act, speak, and likely will behave in the future regarding ethical issues.  </span>
    </p>
    <hr />
    <div class='md-toc' mdtype='toc'><p class="md-toc-content" role="list"><span role="listitem"
                                                                                 class="md-toc-item md-toc-h1"
                                                                                 data-ref="n241"><a class="md-toc-inner"
                                                                                                    style=""
                                                                                                    href="#a-framework-for-analyzing-corporate-statements-actions-and-future-behavior">A Framework for Analyzing Corporate Statements, Actions, and Future Behavior</a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n244"><a class="md-toc-inner" style="" href="#abstract">Abstract  </a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n250"><a class="md-toc-inner" style=""
                                                                       href="#introduction">Introduction  </a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n255"><a class="md-toc-inner" style=""
                                                                       href="#key-constructs-and-terminology">Key Constructs and Terminology  </a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n256"><a class="md-toc-inner" style=""
                                                                       href="#actions-issues-flags-and-statements">Actions, Issues, Flags, and Statements  </a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n270"><a class="md-toc-inner" style="" href="#models">Models</a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n271"><a class="md-toc-inner" style=""
                                                                       href="#introduction-2">Introduction  </a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n276"><a class="md-toc-inner" style=""
                                                                       href="#statements----domain-entities-motivation-impact-statement-type-and-ethics---demise">Statements  - Domain, Entities, Motivation, Impact, Statement Type and Ethics   (DEMISE)</a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n277"><a class="md-toc-inner" style=""
                                                                       href="#introduction-3">Introduction  </a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n313"><a class="md-toc-inner" style=""
                                                                       href="#higher-models">Higher Models</a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n315"><a class="md-toc-inner" style=""
                                                                       href="#effect-model">Effect Model  </a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n317"><a class="md-toc-inner" style=""
                                                                       href="#responsibility-model---how-well-does-a-company-respond">Responsibility Model - How Well Does a Company Respond</a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n324"><a class="md-toc-inner" style=""
                                                                       href="#reputation-scoring">Reputation Scoring</a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n325"><a class="md-toc-inner" style=""
                                                                       href="#harm-engagement-adaptability-reliability-and-transparency-heart">Harm, Engagement, Adaptability, Reliability and Transparency (HEART)</a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n329"><a class="md-toc-inner" style=""
                                                                       href="#harm">Harm</a></span><span role="listitem"
                                                                                                         class="md-toc-item md-toc-h3"
                                                                                                         data-ref="n333"><a
      class="md-toc-inner" style="" href="#ethos">Ethos</a></span><span role="listitem" class="md-toc-item md-toc-h3"
                                                                        data-ref="n337"><a class="md-toc-inner" style=""
                                                                                           href="#adaptability">Adaptability</a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n343"><a class="md-toc-inner" style=""
                                                                       href="#reliability--trust">Reliability &amp; Trust</a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n347"><a class="md-toc-inner" style=""
                                                                       href="#transparency">Transparency</a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n352"><a class="md-toc-inner" style="" href="#process">Process</a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n355"><a class="md-toc-inner" style=""
                                                                       href="#creating-and-testing-the-custom-embedding-model">Creating and Testing the Custom Embedding Model</a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n356"><a class="md-toc-inner" style="" href="#baseline">Baseline</a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n358"><a class="md-toc-inner" style="" href="#training">Training</a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n362"><a class="md-toc-inner" style="" href="#evaluation">Evaluation</a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n365"><a class="md-toc-inner" style=""
                                                                       href="#using-the-custom-embedding-model">Using the Custom Embedding Model</a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n370"><a class="md-toc-inner" style=""
                                                                       href="#statement-analysis">Statement Analysis</a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n372"><a class="md-toc-inner" style="" href="#process-2">Process</a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n376"><a class="md-toc-inner" style=""
                                                                       href="#statements-metadata">Statements Metadata</a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n402"><a class="md-toc-inner" style=""
                                                                       href="#flag-analysis">Flag Analysis</a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n404"><a class="md-toc-inner" style="" href="#process-3">Process</a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n424"><a class="md-toc-inner" style=""
                                                                       href="#mapping-to-un-sdg-doughnut-economic-and-other-models">Mapping to UN SDG, Doughnut Economic and other Models</a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n475"><a class="md-toc-inner" style=""
                                                                       href="#cherry-picking">Cherry Picking</a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n427"><a class="md-toc-inner" style=""
                                                                       href="#predictive-modeling">Predictive Modeling  </a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n430"><a class="md-toc-inner" style="" href="#processing">Processing  </a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n434"><a class="md-toc-inner" style=""
                                                                       href="#per-entity-analysis">Per Entity Analysis  </a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n435"><a class="md-toc-inner" style=""
                                                                       href="#flags---cluster-by-ethics-impact-and-action">Flags - Cluster By Ethics, Impact and Action  </a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n438"><a class="md-toc-inner" style="" href="#promises">Promises  </a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n440"><a class="md-toc-inner" style="" href="#claims">Claims  </a></span><span
      role="listitem" class="md-toc-item md-toc-h3" data-ref="n442"><a class="md-toc-inner" style=""
                                                                       href="#cherry-pickingflooding">Cherry Picking/Flooding</a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n445"><a class="md-toc-inner" style=""
                                                                       href="#industry-analysis">Industry Analysis  </a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n450"><a class="md-toc-inner" style="" href="#conclusion">Conclusion  </a></span><span
      role="listitem" class="md-toc-item md-toc-h2" data-ref="n454"><a class="md-toc-inner" style="" href="#references">References  </a></span><span
      role="listitem" class="md-toc-item md-toc-h1" data-ref="n463"><a class="md-toc-inner" style="" href="#appendix-a">Appendix A</a></span>
    </p></div>
    <p>&nbsp;</p>
    <h2 id='introduction'><span>Introduction  </span></h2>
    <p><span>In a world of increasing scrutiny over corporate social responsibility and ethical conduct, stakeholders require robust frameworks to evaluate how organizations communicate and act. Traditional approaches often treat corporate statements as either “ethical” or “unethical,” but real-world scenarios frequently occupy a more ambiguous space. Organizations may partially fulfill promises, offer misleading statements, or distract attention from core problems.  </span>
    </p>
    <p><span>We have taken the framework described by </span><strong><span>Marshall, D et al 2023</span></strong><span> and adapted it to a more generalized format for analysing and predicting corporate ethical behaviour.  </span>
    </p>
    <p>
      <span>This paper addresses these complexities by introducing a  classification scheme for corporate behavior:  </span>
    </p>
    <p>&nbsp;</p>
    <h1 id='key-constructs-and-terminology'><span>Key Constructs and Terminology  </span></h1>
    <h3 id='actions-issues-flags-and-statements'><span>Actions, Issues, Flags, and Statements  </span></h3>
    <ol start=''>
      <li><p><strong><span>Effects</span></strong><span>: What a company has done or is doing (e.g., installing wastewater treatment, lobbying for policy changes, etc.).  </span>
      </p></li>
      <li><p><strong><span>Flags</span></strong><span>: Markers that connect a set of effects and assign qualities to them.</span>
      </p></li>
      <li><p>
        <strong><span>Statements</span></strong><span>: Verbal or written communications by a company, including:  </span>
      </p>
        <ul>
          <li><p><strong><span>Promises</span></strong><span>: Declarations of intended future actions.  </span></p>
          </li>
          <li><p><strong><span>Claims</span></strong><span>: Assertions regarding past actions or outcomes.  </span></p>
          </li>
        </ul>
      </li>
    </ol>
    <p><span>These building blocks are the starting point for applying our matrix approach.  </span></p>
    <h1 id='models'><span>Models</span></h1>
    <h2 id='introduction-2'><span>Introduction  </span></h2>
    <p><span>We model the ingested documents at multiple levels of abstraction. The first level is that of individual statements from within a document. Each statement is allocated a vector, a feature list and additional query metadata. The vector is generated based on our </span><strong><span>DEMISE</span></strong><span> model (see below). The feature list is a query friendly way of aggregating statements, it is the vector with labels, and the metadata provides the data for a knowledge graph and also even easier levels of querying.</span>
    </p>
    <p><span>Next we filter and cluster these vectors to create our </span><strong><span>Effect</span></strong><span> model (action impacts) which creates the </span><strong><span>Harm</span></strong><span> score. Along with our </span><strong><span>Trust</span></strong><span> and </span><strong><span>Reliability</span></strong><span> model which create the </span><strong><span>Reliability</span></strong><span> score. Along with the </span><strong><span>Flooding</span></strong><span> and </span><strong><span>Cherry Picking</span></strong><span> models which then contribute to the </span><strong><span>Transparency</span></strong><span> rating.</span>
    </p>
    <p>
      <span>The </span><strong><span>Effect</span></strong><span> model is then further processed to provide our </span><strong><span>Adaptability</span></strong><span> and </span><strong><span>Ethos</span></strong><span> ratings.</span>
    </p>
    <p><span>Many types of analysis that can be performed across the dataset have similarities and this has lead to the devising of the following models:</span>
    </p>
    <h2 id='statements----domain-entities-motivation-impact-statement-type-and-ethics---demise'><span>Statements  - Domain, Entities, Motivation, Impact, Statement Type and Ethics   (DEMISE)</span>
    </h2>
    <h3 id='introduction-3'><span>Introduction  </span></h3>
    <p><span> </span><strong><span>DEMISE</span></strong><span> stands for </span><strong><span>Domain, Entity, Motivation, Impact, Statement Type, and Engagement</span></strong><span>. It is our conceptual framework for embedding statements against the dimensions required to capture ethical issues. It allows the system to process statements made by any entity about a fact/opinion, action or event that has ethical implications. If it is an action/event it can also be hypothetical/intended past/present etc.</span>
    </p>
    <p><span>Here is a quick summary of each DEMISE component:  </span></p>
    <ol start=''>
      <li><p><strong><span>Domain</span></strong><span></span><br /><span>The general area or topic of the action or statement (e.g. technology, finance, healthcare).  </span>
      </p>
        <ul>
          <li><p><em><span>Example:</span></em><span> A statem2ent about “carbon footprint reduction” could have the Domain “climate/energy.”  </span>
          </p></li>
        </ul>
      </li>
      <li><p><strong><span>Entity</span></strong><span></span><br /><span>The </span><em><span>subject</span></em><span> and </span><em><span>object</span></em><span> entity or entities involved in the statement, such as a person, organization, product, location, or concept.  </span>
      </p>
        <ul>
          <li><p><em><span>Example:</span></em><span> In “Company X announced a new policy,” the main Entity is “Company X.”  </span>
          </p></li>
        </ul>
      </li>
      <li><p><strong><span>Motivation</span></strong><span></span><br /><span>The reason or driving factor behind the statement or action (e.g. fear, ambition, kindness, generosity).  </span>
      </p>
        <ul>
          <li><p><em><span>Example:</span></em><span> A decision motivated by “greed” versus one motivated by “sustainability.”  </span>
          </p></li>
        </ul>
      </li>
      <li><p><strong><span>Impact</span></strong><span></span><br /><span>The effect the action or statement has on any entity, whether beneficial, harmful, or neutral.  </span>
      </p>
        <ul>
          <li><p><em><span>Example:</span></em><span> “We caused an increase in local employment” has a positive Impact on the community.  </span>
          </p></li>
        </ul>
      </li>
      <li><p><strong><span>Statement Type</span></strong><span></span><br /><span>The temporal and rhetorical nature of the statement (e.g. a prediction, a promise, an acknowledgement, or a denial).  </span>
      </p>
        <ul>
          <li><p><em><span>Example:</span></em><span> “We predict we’ll expand next year” is a </span><strong><span>Future Benefit</span></strong><span>-type statement with a </span><strong><span>Prediction</span></strong><span> label.  </span>
          </p></li>
        </ul>
      </li>
      <li><p><strong><span>Engagement</span></strong><span></span><br /><span>The engagement dimension, that is how the entity is approaching the situation described. </span>
      </p>
        <ul>
          <li><p><em><span>Example:</span></em><span> A statement describing good done for it&#39;s own sake will have an &#39;</span><strong><span>altruistic</span></strong><span>&#39;  engagement.</span>
          </p></li>
        </ul>
      </li>
    </ol>
    <p><span>The model is used to create embeddings for statements that are designed to exist in an ethical latent space that encapsulates the  motivations for the actions described in the statement and their impact.  </span>
    </p>
    <p><span>The full and detailed explanation of the model can be found in [[Appendix A]].</span></p>
    <h2 id='higher-models'><span>Higher Models</span></h2>
    <p><span>These models are calculated from re-analysis of </span><strong><span>DEMISE</span></strong><span> scored statements typically using subsets of the </span><strong><span>DEMISE</span></strong><span> vector.</span>
    </p>
    <h3 id='effect-model'><span>Effect Model  </span></h3>
    <p>
      <span>This is derived from clustering against a combination of the </span><strong><span>Domain</span></strong><span>, </span><strong><span>Engagement</span></strong><span> and </span><strong><span>Impact</span></strong><span> models and provides a resulting model of the effects a company has had. </span><strong><span>Effect Flags</span></strong><span> are created from this model.</span>
    </p>
    <h3 id='responsibility-model---how-well-does-a-company-respond'><span>Responsibility Model - How Well Does a Company Respond</span>
    </h3>
    <p><strong><span>Responsibility</span></strong><span> seeks to determine how a company reacts to issues and mistakes. It also encompasses such concerns as accountability, communication and transparency.</span>
    </p>
    <p>
      <strong><span>Responsibility</span></strong><span> is determined per </span><strong><span>Effect</span></strong><span>  by searching the </span><strong><span>DEMISE</span></strong><span> dataset for </span><strong><span>Statements</span></strong><span> in the same latent space as the Effects after the Effect took place, and then determining how the entity reacted to the Effect. Note that the Engagement and Motivation dimensions are also part of the DE</span><strong><span>M</span></strong><span>IS</span><strong><span>E</span></strong><span> model too. But here they are being applied to the </span><strong><span>Effect</span></strong><span> not a </span><strong><span>Statement</span></strong><span>, we are also not calculating these fields from the statements as they have different implications on a per statement basis as they do on an effect.</span>
    </p>
    <pre class="md-fences md-end-block ty-contain-cm modeLoaded" spellcheck="false" lang="python"
         style="break-inside: unset;"><div class="CodeMirror cm-s-inner cm-s-null-scroll CodeMirror-wrap" lang="python"><div
      style="overflow: hidden; position: relative; width: 3px; height: 0px; top: 9.5px; left: 8px;"><textarea
      autocorrect="off" autocapitalize="off" spellcheck="false" tabindex="0"
      style="position: absolute; bottom: -1em; padding: 0px; width: 1000px; height: 1em; outline: currentcolor;"></textarea></div><div
      class="CodeMirror-scrollbar-filler" cm-not-content="true"></div><div class="CodeMirror-gutter-filler"
                                                                           cm-not-content="true"></div><div
      class="CodeMirror-scroll" tabindex="-1"><div class="CodeMirror-sizer"
                                                   style="margin-left: 0px; margin-bottom: 0px; border-right-width: 0px; padding-right: 0px; padding-bottom: 0px;"><div
      style="position: relative; top: 0px;"><div class="CodeMirror-lines" role="presentation"><div role="presentation"
                                                                                                   style="position: relative; outline: currentcolor;"><div
      class="CodeMirror-measure"></div><div class="CodeMirror-measure"></div><div
      style="position: relative; z-index: 1;"></div><div class="CodeMirror-code" role="presentation" style=""><div
      class="CodeMirror-activeline" style="position: relative;"><div
      class="CodeMirror-activeline-background CodeMirror-linebackground"></div><div
      class="CodeMirror-gutter-background CodeMirror-activeline-gutter" style="left: 0px; width: 0px;"></div><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span
      class="cm-keyword">class</span> <span class="cm-def">AdaptabilityDimension</span>(<span
      class="cm-builtin">str</span>, <span class="cm-variable">Enum</span>):</span></pre></div><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-string">"""The adaptability dimension of the responsibility matrix model."""</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">PREVENT</span> <span class="cm-operator">=</span> <span class="cm-string">"prevent"</span> &nbsp;<span
      class="cm-comment"># Company takes action to prevent issues before they occur</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">SOLVE</span> <span class="cm-operator">=</span> <span class="cm-string">"solve"</span> &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company addresses issues after they occur</span></span></pre><pre class=" CodeMirror-line "
                                                                                             role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">MISDIRECT</span> <span
      class="cm-operator">=</span> <span class="cm-string">"misdirect"</span> &nbsp;<span class="cm-comment"># Company redirects attention from major issues to minor solutions</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">DIVERT</span> <span class="cm-operator">=</span> <span class="cm-string">"divert"</span> &nbsp; &nbsp;<span
      class="cm-comment"># Company diverts attention with numerous minor solutions</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">DENY</span> <span class="cm-operator">=</span> <span class="cm-string">"deny"</span> &nbsp; &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company denies responsibility for issues</span></span></pre><pre class=" CodeMirror-line "
                                                                                            role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">DECEIVE</span> <span
      class="cm-operator">=</span> <span class="cm-string">"deceive"</span> &nbsp;<span class="cm-comment"># Company makes positive statements without matching actions</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">IGNORE</span> <span class="cm-operator">=</span> <span class="cm-string">"ignore"</span> &nbsp; &nbsp;<span
      class="cm-comment"># Company ignores issues completely</span></span></pre><pre class=" CodeMirror-line "
                                                                                     role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">GASLIGHT</span> <span
      class="cm-operator">=</span> <span class="cm-string">"gaslight"</span> &nbsp;<span class="cm-comment"># Company describes the problem as not being a problem</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span
      cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation"
                                                                             style="padding-right: 0.1px;"><span
      cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation"
                                                                             style="padding-right: 0.1px;"><span
      class="cm-keyword">class</span> <span class="cm-def">EngagementDimension</span>(<span
      class="cm-builtin">str</span>, <span class="cm-variable">Enum</span>):</span></pre><pre class=" CodeMirror-line "
                                                                                              role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"""The engagement dimension of the responsibility matrix model."""</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">ALTRUISTIC</span> <span class="cm-operator">=</span> <span
      class="cm-string">"altruistic"</span>  <span class="cm-tab" role="presentation" cm-text="	"> </span><span
      class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-tab" role="presentation"
                                                                     cm-text="	">  </span><span class="cm-comment"># Company does good because it's the right thing to do</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">PREVENTATIVE</span> <span class="cm-operator">=</span> <span
      class="cm-string">"preventative"</span>  <span class="cm-tab" role="presentation" cm-text="	"> </span><span
      class="cm-comment"># Company seeks to prevent issues arising</span></span></pre><pre class=" CodeMirror-line "
                                                                                           role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">PROACTIVE</span> <span
      class="cm-operator">=</span> <span class="cm-string">"proactive"</span>  <span class="cm-tab" role="presentation"
                                                                                     cm-text="	"> </span><span
      class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-tab" role="presentation"
                                                                     cm-text="	">  </span><span class="cm-tab"
                                                                                                 role="presentation"
                                                                                                 cm-text="	">  </span><span
      class="cm-comment"># Company solves issues before they become public</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">RESPONSIVE</span> <span class="cm-operator">=</span> <span
      class="cm-string">"responsive"</span>  <span class="cm-tab" role="presentation" cm-text="	"> </span><span
      class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-tab" role="presentation"
                                                                     cm-text="	">  </span><span class="cm-comment"># Company responds to issues quickly</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">REACTIVE</span> <span class="cm-operator">=</span> <span class="cm-string">"reactive"</span>  <span
      class="cm-tab" role="presentation" cm-text="	"> </span><span class="cm-tab" role="presentation"
                                                                    cm-text="	">  </span><span class="cm-tab"
                                                                                                role="presentation"
                                                                                                cm-text="	">  </span><span
      class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-tab" role="presentation"
                                                                     cm-text="	">  </span><span class="cm-comment"># Company responds only after pressure</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span
      class="cm-tab" role="presentation" cm-text="	">  </span> &nbsp;<span class="cm-variable">DISMISSIVE</span> <span
      class="cm-operator">=</span> <span class="cm-string">"dismissive"</span>  <span class="cm-tab" role="presentation"
                                                                                      cm-text="	"> </span><span
      class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-tab" role="presentation"
                                                                     cm-text="	">  </span><span class="cm-comment"># Company dismisses importance of issues</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">PASSIVE</span> <span class="cm-operator">=</span> <span
      class="cm-string">"passive"</span>  <span class="cm-tab" role="presentation" cm-text="	"> </span><span
      class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-tab" role="presentation"
                                                                     cm-text="	">  </span><span class="cm-tab"
                                                                                                 role="presentation"
                                                                                                 cm-text="	">  </span><span
      class="cm-tab" role="presentation" cm-text="	">  </span><span class="cm-tab" role="presentation"
                                                                     cm-text="	">  </span><span class="cm-comment"># Company passively acknowledges issues</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"><span
      cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation"
                                                                             style="padding-right: 0.1px;"><span
      class="cm-keyword">class</span> <span class="cm-def">MotivationDimension</span>(<span
      class="cm-builtin">str</span>, <span class="cm-variable">Enum</span>):</span></pre><pre class=" CodeMirror-line "
                                                                                              role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"""The motivation dimension of the responsibility matrix model."""</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">GENUINE</span> <span class="cm-operator">=</span> <span class="cm-string">"genuine"</span> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company genuinely cares about addressing issues</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">COMPLIANT</span> <span class="cm-operator">=</span> <span class="cm-string">"compliant"</span> &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company acts to comply with regulations</span></span></pre><pre class=" CodeMirror-line "
                                                                                           role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">PRESSURED</span> <span
      class="cm-operator">=</span> <span class="cm-string">"pressured"</span> &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company acts due to external pressure</span></span></pre><pre class=" CodeMirror-line "
                                                                                         role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">SUPERFICIAL</span> <span
      class="cm-operator">=</span> <span class="cm-string">"superficial"</span> &nbsp;<span class="cm-comment"># Company acts superficially for appearance</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">OPPORTUNISTIC</span> <span class="cm-operator">=</span> <span class="cm-string">"opportunistic"</span> &nbsp;<span
      class="cm-comment"># Company acts to gain advantage</span></span></pre><pre class=" CodeMirror-line "
                                                                                  role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation"
                                                                             style="padding-right: 0.1px;"><span
      cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation"
                                                                             style="padding-right: 0.1px;"><span
      class="cm-keyword">class</span> <span class="cm-def">ApproachDimension</span>(<span class="cm-builtin">str</span>, <span
      class="cm-variable">Enum</span>):</span></pre><pre class=" CodeMirror-line " role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"""The approach dimension of the responsibility matrix model."""</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">INNOVATIVE</span> <span class="cm-operator">=</span> <span
      class="cm-string">"innovative"</span> &nbsp; &nbsp;<span
      class="cm-comment"># Company develops new solutions</span></span></pre><pre class=" CodeMirror-line "
                                                                                  role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">SYSTEMATIC</span> <span
      class="cm-operator">=</span> <span class="cm-string">"systematic"</span> &nbsp; &nbsp;<span class="cm-comment"># Company applies systematic methods</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">INCREMENTAL</span> <span class="cm-operator">=</span> <span
      class="cm-string">"incremental"</span> &nbsp;<span
      class="cm-comment"># Company makes small, gradual changes</span></span></pre><pre class=" CodeMirror-line "
                                                                                        role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">OPPORTUNISTIC</span> <span class="cm-operator">=</span> <span class="cm-string">"opportunistic"</span> &nbsp;<span
      class="cm-comment"># Company acts when convenient</span></span></pre><pre class=" CodeMirror-line "
                                                                                role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">HAPHAZARD</span> <span
      class="cm-operator">=</span> <span class="cm-string">"haphazard"</span> &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company's approach lacks coherence</span></span></pre><pre class=" CodeMirror-line "
                                                                                      role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"><span cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation"
                                                                             style="padding-right: 0.1px;"><span
      cm-text="" cm-zwsp="">
</span></span></pre><pre class=" CodeMirror-line " role="presentation"><span role="presentation"
                                                                             style="padding-right: 0.1px;"><span
      class="cm-keyword">class</span> <span class="cm-def">ActionDimension</span>(<span
      class="cm-builtin">str</span>, <span class="cm-variable">Enum</span>):</span></pre><pre class=" CodeMirror-line "
                                                                                              role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-string">"""The action dimension of the responsibility matrix model."""</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">MENTION</span> <span class="cm-operator">=</span> <span class="cm-string">"mention"</span> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company merely mentions the issue without substantial action</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">DISCUSS</span> <span class="cm-operator">=</span> <span class="cm-string">"discuss"</span> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company discusses the issue in more detail</span></span></pre><pre class=" CodeMirror-line "
                                                                                              role="presentation"><span
      role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span class="cm-variable">ANNOUNCE</span> <span
      class="cm-operator">=</span> <span class="cm-string">"announce"</span> &nbsp; &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company makes formal announcements about the issue</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">INVESTIGATE</span> <span class="cm-operator">=</span> <span
      class="cm-string">"investigate"</span> &nbsp;<span class="cm-comment"># Company commits resources to investigate the issue</span></span></pre><pre
      class=" CodeMirror-line " role="presentation"><span role="presentation" style="padding-right: 0.1px;"> &nbsp; &nbsp;<span
      class="cm-variable">ACT</span> <span class="cm-operator">=</span> <span class="cm-string">"act"</span> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span
      class="cm-comment"># Company takes concrete action to address the issue</span></span></pre></div></div></div></div></div><div
      style="position: absolute; height: 0px; width: 1px; border-bottom-width: 0px; border-bottom-style: solid; border-bottom-color: transparent; top: 1081px;"></div><div
      class="CodeMirror-gutters" style="display: none; height: 1081px;"></div></div></div></pre>
    <p>&nbsp;</p>
    <p>
      <span>The </span><em><strong><span>Responsibility Model</span></strong></em><span> is used to determine the </span><em><strong><span>Ethos</span></strong></em><span>  and </span><strong><span>Adaptability</span></strong><span> scores by looking at the responsibility Ethos &amp; Adaptability vs impact of Effects.</span>
    </p>
    <h1 id='reputation-scoring'><span>Reputation Scoring</span></h1>
    <h2 id='harm-engagement-adaptability-reliability-and-transparency-heart'><span>Harm, Engagement, Adaptability, Reliability and Transparency (HEART)</span>
    </h2>
    <p><span>The aim of this model is to capture the essence of an entities behaviour to both rate the entity for others to see and to allow prediction of future actions based on this personality.</span>
    </p>
    <p>
      <strong><span>HEART</span></strong><span> builds upon the dataset of </span><strong><span>DEMISE</span></strong><span> and compares related data across time and aggregated motivations and impacts. Knowledge of these factors about an entity lead to the ability to predict future behaviours.</span>
    </p>
    <p><strong><span>H</span></strong><span>arm - negative effects, positives ignored.</span>
      <strong><span>E</span></strong><span>ngagement - how a company engages with problems/issues/flags, calculated from the responsibility model weighted by effect impact</span>
      <strong><span>A</span></strong><span>daptability - are they changing their ways, making improvements, calculated from the responsibility matrix weighted by effect impact</span>
      <strong><span>R</span></strong><span>eliability/Trust - do they do what they say, have they done what they&#39;ve said - calculated from promises+claims</span>
      <strong><span>T</span></strong><span>ransparency - are they trying to hide the harm they&#39;ve done, includes cherry-picking and flooding</span>
    </p>
    <h3 id='harm'><span>Harm</span></h3>
    <blockquote><p><span>What harm has a company done</span></p></blockquote>
    <p><span>Harm is caculated from the </span><strong><span>total negative effects</span></strong><span> weighted by impact, contribution and intention. It will be normalised - </span><strong><span>TODO: WHAT FACTORS TO NORMALIZE BY</span></strong>
    </p>
    <h3 id='ethos'><span>Ethos</span></h3>
    <blockquote><p><span>How engaged and what motivates the company to solve or cause problems in the ESG field.  i.e. what is their ethos</span>
    </p></blockquote>
    <p>
      <span>This is determined via the responsibility matrix (</span><strong><span>Engagement &amp; Motivation</span></strong><span>)  which is calculated on a </span><strong><span>per effect basis</span></strong><span> and is used to determine if a company is trying to engage with ESG issues.</span>
    </p>
    <h3 id='adaptability'><span>Adaptability</span></h3>
    <blockquote><p><span>How is a company changing their ways to meet challenges in the ESG field.</span></p>
    </blockquote>
    <p><span>The framework described by </span><strong><span>Marshall, D et al 2023</span></strong><span>  was used as a basis for this. </span>
    </p>
    <p><span>This is determined via the responsibility matrix which is calculated on a </span><strong><span>per effect basis</span></strong><span> and is used to determine if a company is trying to improve itself in regards to ESG issues.</span>
    </p>
    <p><span>TODO: Continued re-occurance of the same flags over time will markedly reduce adaptability score.</span>
    </p>
    <h3 id='reliability--trust'><span>Reliability &amp; Trust</span></h3>
    <blockquote><p><span>Is the company reliable and can they be trusted?</span></p></blockquote>
    <p><span>This is determined by looking at the promises/claims made by a company and whether they are provably correct.</span>
    </p>
    <h3 id='transparency'><span>Transparency</span></h3>
    <blockquote><p><span>Is a company trying to obscure/hide their behaviour or are they actively reporting it.</span>
    </p></blockquote>
    <p><span>Are actions in 3rd party reporting also in ESG reporting? Are they using cherry-picking/flooding to obscure bad actions? Do they engage in lobbying.</span>
    </p>
    <p><span> </span></p>
    <h2 id='process'><span>Process</span></h2>
    <p><span> </span><img
      src="https://documents.lucid.app/documents/05ee122c-29a1-45e7-ad65-c95ac790de1a/pages/0_0?a=2012&amp;x=-16&amp;y=2181&amp;w=2065&amp;h=877&amp;store=1&amp;accept=image%2F*&amp;auth=LCA%203b2b095cdded2841a79b142eb1025371ff205408d3535d7752044db92f713f5a-ts%3D1748439777"
      referrerpolicy="no-referrer" alt="img"></p>
    <p><span> </span><img
      src="https://documents.lucid.app/documents/05ee122c-29a1-45e7-ad65-c95ac790de1a/pages/0_0?a=2012&amp;x=-2&amp;y=3143&amp;w=1787&amp;h=1644&amp;store=1&amp;accept=image%2F*&amp;auth=LCA%20bc6cfe7a28d1ab58e3e1223b7733e560aaacb65c527e9d434c503114d2c207d8-ts%3D1748439777"
      referrerpolicy="no-referrer" alt="img"></p>
    <p>&nbsp;</p>
    <h1 id='creating-and-testing-the-custom-embedding-model'>
      <span>Creating and Testing the Custom Embedding Model</span></h1>
    <h1 id='baseline'><span>Baseline</span></h1>
    <p><span>A large set of baseline evaluations will be created which create synthetic data to test the responses from a frontier model LLM. Once the model has been verified against the synthetic data and an LLM the training will start.</span>
    </p>
    <h1 id='training'><span>Training</span></h1>
    <p><span>A large (n=100000) random sample of a large corpus of prompt/response pairs were created using an LLM with very heavy prompting (6 calls made and about 50,000 tokens of explanation). The 6 results were combined into a single response and a brief prompt with the text sample used in the original prompt. This corpus was then used to train Mistral Small 24B quantized to 4bit using LoRA for fine tuning, the training run ran of two days on a single A200 (140GB VRAM) card. The most favourable check point was taken and uploaded onto HuggingFace in a variety of formats.</span>
    </p>
    <p><span>When we deployed this model it became clear that even with the cheapest hosting options available that the Gemini fine-tuned models would be even cheaper. So a new set of training data was produced in the format used by Google&#39;s Vertex AI.</span>
    </p>
    <p><span>During the running of the application a small number of requests were performed against frontier models, and the results of those requests were stored as further training data for the fine tuned models. As can be seen from the evaluations, a high quality dataset worked almost equally as well on Gemini Flash and Gemini Flash Lite when fine-tuned. So clearly once fine-tuned these very low cost models have enough parameters to be able to digest the language supplied into the vector representations.</span>
    </p>
    <h1 id='evaluation'><span>Evaluation</span></h1>
    <p><span>For this we produced a custom evaluation harness specifically for comparing how the extraction of the DEMISE model and the metadata compared with various different prompts and across different fine-tuned models.</span>
    </p>
    <p>&nbsp;</p>
    <h1 id='using-the-custom-embedding-model'><span>Using the Custom Embedding Model</span></h1>
    <p><span>The embeddings generated will be reverse engineered back into Python objects, these will then be used to create sub-embeddings. The sub embeddings (such as Impact or Ethics+Impact) will be stored along with the primary embedding and all the features as sparse key/value pairs in a Postgres database.</span>
    </p>
    <p><span>The embeddings can then be used for clustering, KNN etc. in a variety of latent spaces.</span></p>
    <p><img src="/Users/<USER>/Library/Mobile Documents/iCloud~md~obsidian/Documents/Turing/image-20250116185414135.png"
            referrerpolicy="no-referrer" alt="image-20250116185414135"></p>
    <h1 id='statement-analysis'><span>Statement Analysis</span></h1>
    <p><span>Each statement is processed to provide metadata and features. The features are a set of key/value pairs that are turned into a vector embedding. A statement describes an opinion, a known fact or an event or action, past present and future.</span>
    </p>
    <h2 id='process-2'><span>Process</span></h2>
    <p>
      <span>A primary page is retrieved, along with the previous and next pages. All three pages provide the </span><strong><span>context</span></strong><span> for the statements.</span>
    </p>
    <p><span>The primary page is then split using a low-cost LLM into separate chunks with one </span><strong><span>statement</span></strong><span> per chunk.</span>
    </p>
    <p>
      <span>Each </span><strong><span>statement</span></strong><span> is passed to a low-cost LLM and converted into </span><strong><span>metadata</span></strong><span>, the </span><strong><span>statement</span></strong><span>, </span><strong><span>context</span></strong><span> and </span><strong><span>metadata</span></strong><span> are passed to a BERT based transformer to create the feature embeddings. The result is serialized in the database in a form that is easily queryable by embeddings sub-embeddings or individual feature.</span>
    </p>
    <h2 id='statements-metadata'><span>Statements Metadata</span></h2>
    <p><span>The metadata is information extracted from a statement that can be used for SQL like querying.</span></p>
    <p><span>Extracted from the statement are the following:</span></p>
    <ul>
      <li><p><span>Statement Category - &#39;fact/opinion&#39;, &#39;event&#39; or &#39;action&#39;</span></p></li>
      <li><p><span>Statement Type - the type of statement this is e.g. Statement, Declaration, Supposition, Hypothesis, Assertion, Denial etc.</span>
      </p></li>
      <li><p><span>Domain - a word describing the domain to which this statement belongs</span></p></li>
      <li><p><span>Subject (Named) Entity  - the doer.</span></p></li>
      <li><p><span>Object (Named ) Entity - the done to .</span></p></li>
      <li><p><span>Quantities - unnamed entities such has dollars, rock etc. along with their quantities, these can only be the done to.</span>
      </p></li>
      <li><p><span>Impact - the impact of the subject on the object</span></p></li>
      <li><p><span>Action - the action performed on the Object Entity or the </span></p></li>
      <li><p>
        <span>Location - the location to which the statement relates, this may or may not be the affected entity. </span>
      </p></li>
      <li><p><span>Time - the time at which an event occurs</span></p></li>
    </ul>
    <p>&nbsp;</p>
    <h2 id='flag-analysis'><span>Flag Analysis</span></h2>
    <p><span>Flags exist in a subset of the overall </span><strong><span>DEMISE</span></strong><span> latent space, they use the </span><strong><span>Domain</span></strong><span>, </span><strong><span>Ethics</span></strong><span>, and </span><strong><span>Impact</span></strong><span> to create an </span><strong><span>Effect</span></strong><span> embedding that can be used to compare ethical impactful actions. The dimensionality of the embeddings is reduced and weights applied to make sure the latent space maps onto that of the impactful actions, which are referred to as flags.</span>
    </p>
    <h3 id='process-3'><span>Process</span></h3>
    <p><span> </span><img
      src="https://documents.lucid.app/documents/05ee122c-29a1-45e7-ad65-c95ac790de1a/pages/0_0?a=2012&amp;x=-43&amp;y=1289&amp;w=2664&amp;h=809&amp;store=1&amp;accept=image%2F*&amp;auth=LCA%2055192539060846f5d1ea65fe20bb329f735a8c99e820f91dcbfad65966200f30-ts%3D1748439777"
      referrerpolicy="no-referrer" alt="img"></p>
    <p><span>Flag analysis is performed by filtering all statements that are marked as an impactful action for a given entity. These are clustered - the type of clustering is selectable. The clusters are then if needed sorted in proximity to the centroid and outliers removed.</span>
    </p>
    <p><span>The Flag Analysis takes place over multiple steps, first we cluster by the reduced dimension vector, then each cluster&#39;s text is analysed to see if there are any textually similar values, they are then merged. The text is analysed and if needed split it into mulitple flags. The full set of flags are then remerged by their title and reason to make sure there are no duplicates.</span>
    </p>
    <p><span>The Coherence metric was created to analyse this cluster-&gt;merge-&gt;split-&gt;merge process. It indicates the degree to which the initial clustering managed to get the flags right and is calculated as:</span>
    </p>
    <p><span>		</span><span> 1 / ((number of flags merged+1) * (initial flags / number of clusters))</span></p>
    <p><span>Higher values represent a high &#39;coherence&#39; that is the minimum churn. The following are then tuned to get the maximum coherence value and number of flags.</span>
    </p>
    <ul>
      <li><p><span>Clustering mechanism (KMEANS, DBSCAN, SPECTRAL etc.)</span></p></li>
      <li><p><span>Clustering hyperparameters (epsilon, min_samples)</span></p></li>
      <li><p><span>Re-clustering similarity, for when two pieces of text were clustered differently by DEMISE but when analysed have a similar analysis.</span>
      </p></li>
      <li><p><span>The analysis LLM and flag prompt, this effects the splitting process.</span></p></li>
      <li><p><span>Latent space, which parts of the DEMISE vector to use, and which to reduce the dimensions on, and which parts to add weighting to.</span>
      </p></li>
      <li><p><span>Model Fine-tuning to improve the initial vectorisation.</span></p></li>
    </ul>
    <h3 id='mapping-to-un-sdg-doughnut-economic-and-other-models'><span>Mapping to UN SDG, Doughnut Economic and other Models</span>
    </h3>
    <p><span>The mapping will be done by passing the key domain characteristics and a summary of the issue to an LLM along with definitions of the specific Model. The LLM will then perform the actual mapping.</span>
    </p>
    <p>&nbsp;</p>
    <h2 id='cherry-picking'><span>Cherry Picking</span></h2>
    <p><img src="/Users/<USER>/IdeaProjects/mono-repo/docs/public/technical/turing/cherry.png"
            referrerpolicy="no-referrer" alt="image (2)"></p>
    <h1 id='predictive-modeling'><span>Predictive Modeling  </span></h1>
    <p><span>Predictions of future actions are performed by regression analysis on the timeseries data in the combined Ethical and Behavioural Models.  </span>
    </p>
    <p><img src="/Users/<USER>/Library/Mobile Documents/iCloud~md~obsidian/Documents/Turing/image-20250115185733407.png"
            referrerpolicy="no-referrer"></p>
    <h2 id='processing'><span>Processing  </span></h2>
    <p><span>Documents are cleaned and chunked.  </span></p>
    <p><span>Each chunk is split into statement + context chunks. Metadata is extracted and a vector assigned from the statement using a fine-tuned low cost LLM.  These vectors and metadata are stored along with the statement chunks.  </span>
    </p>
    <p>
      <span>The vectors are then decomposed into sub vectors. The additional subvectors are stored with the chunks.  </span>
    </p>
    <h2 id='per-entity-analysis'><span>Per Entity Analysis  </span></h2>
    <h3 id='flags---cluster-by-ethics-impact-and-action'><span>Flags - Cluster By Ethics, Impact and Action  </span>
    </h3>
    <p><span>Clustering by Ethics, Impact and Action provides us with red and green &#39;flags&#39;. Which identify good and bad behaviours.  </span>
    </p>
    <p><span>The centroid of the matching statements is then used to look up from a seperate table what is the nearest descriptive labels assigned to that space.  The labels are then passed to an LLM to describe the name of the issue.  </span>
    </p>
    <h3 id='promises'><span>Promises  </span></h3>
    <p><span>The statements are filtered so as to only include promises. Their position in the Action and Motivation latent space is calculated and K-Nearest Neighbours are identified with times after the promise. These statements are then passed to an LLM to assess whether the promise was kept or broken.  </span>
    </p>
    <h3 id='claims'><span>Claims  </span></h3>
    <p><span>The statements are filtered so as to only include claims. Their position in the Action and Motivation latent space is calculated and K-Nearest Neighbours are identified with before the claim. These statements are then passed to an LLM to assess whether the promise was kept or broken.  </span>
    </p>
    <h3 id='cherry-pickingflooding'><span>Cherry Picking/Flooding</span></h3>
    <p>
      <span>The statements are clustered in the </span><strong><span>Domain, Subject Entity, Object Entity</span></strong><span> latent space, then patterns of many small low impact positive actions around one or more high impact negative actions is looked for by clustering and filtering and this is </span><strong><span>Flooding</span></strong><span>. Alongside flooding a pattern of a few highly repeated positive actions alongside negative actions is identified and this is </span><strong><span>Cherry Picking</span></strong><span>.</span>
    </p>
    <p>&nbsp;</p>
    <h2 id='industry-analysis'><span>Industry Analysis  </span></h2>
    <p><span>For a given set of companies, changes in the entries in the Motivation latent space across all companies reflect a change in the pressures that affect the companies. Helping us to identify trends and challenges within an industry.  </span>
    </p>
    <p><span>A spike in a specific motivation type, can be used to look up the statements that are related to that motivation type. Using this we can identify possible causes of the changes in motivations.  </span>
    </p>
    <p><span>Likewise with impact.  </span></p>
    <p>&nbsp;</p>
    <h2 id='conclusion'><span>Conclusion  </span></h2>
    <p><span>This paper outlines a </span><strong><span>multidimensional, consistent framework</span></strong><span> for evaluating corporate behavior in terms of </span><strong><span>Integrity</span></strong><span> (promises), </span><strong><span>Adaptability</span></strong><span> (problem-solving), and </span><strong><span>Honesty</span></strong><span> (claims). By tracking each statement along </span><strong><span>Solution Orientation</span></strong><span> and </span><strong><span>Responsibility</span></strong><span> axes, observers generate a comprehensive view of an organization’s ethical posture. Repeated measurements over time can reveal whether a company is trending toward more </span><strong><span>transparent</span></strong><span> problem-solving or drifting into </span><strong><span>misleading</span></strong><span> or </span><strong><span>deceptive</span></strong><span> practices.  </span>
    </p>
    <p><span>Future research should refine the coding process, integrate it with automated text-analysis tools, and apply longitudinal case studies across industries. The step-by-step methodology provided here should help both scholars and practitioners systematically classify corporate communications, promoting greater accountability and transparency in organizational behavior.  </span>
    </p>
    <hr />
    <h2 id='references'><span>References  </span></h2>
    <ol start=''>
      <li><p><span>Marshall, D., Rehme, J., O’Dochartaigh, A., Kelly, S., Boojihawon, R., and Chicksand, D. (2023). Reporting controversial issues in controversial industries. </span><em><span>Accounting, Auditing &amp; Accountability Journal</span></em><span>, 36(9), pp. 483-512.  </span><a
        href='https://www.emerald.com/insight/content/doi/10.1108/aaaj-07-2020-4684/full/html' target='_blank'
        class='url'>https://www.emerald.com/insight/content/doi/10.1108/aaaj-07-2020-4684/full/html</a></p></li>
    </ol>
    <p><span>  </span></p>
    <hr />
    <p><strong><span>Acknowledgments</span></strong><span>  The author gratefully acknowledges the various scholars and practitioners whose foundational work on corporate ethics, sustainability, and organizational behavior shaped this framework.  </span>
    </p>
    <p><span>  </span></p>
    <p>&nbsp;</p>
    <h1 id='appendix-a'><span>Appendix A</span></h1>
    <p>&nbsp;</p>
    <p><span>To be added</span></p>
    <p>&nbsp;</p>
    <p><span>![[The Models]]</span>
      <span>(c) [Year], [Your Name]. All rights reserved.*</span></p></div>
</div>
</body>
</html>
