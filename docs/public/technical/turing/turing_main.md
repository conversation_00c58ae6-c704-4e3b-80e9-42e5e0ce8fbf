# A Framework for Analyzing Corporate Statements, Actions, and Future Behavior

**Author**: <PERSON>  **Affiliation**: ekoIntelligence **Contact**: <EMAIL>

## Abstract

Corporate statements and actions often reveal important clues about an organization’s ethical posture, future
commitments, and willingness to problem-solve. This paper proposes a structured, three-dimensional taxonomy to classify
and interpret corporate behavior across the dimensions of **HONESTY**, **INTEGRITY**, and **ADAPTABILITY**. Within this
framework, corporate communications—encompassing promises, problem-solving activities, and claims—are mapped onto *
*Solution Orientation** (ranging from *Solving* to *Deceptive*) and **Responsibility** (ranging from *Active* to
*Dismissive*). By assigning these communications numerical vectors and tracking changes over time, the model enables
predicting potential outcomes, including genuine problem-solving efforts, misleading strategies, or potential legal
intimidation tactics (e.g., SLAPP suits). This paper thus provides a systematic approach for researchers, regulators,
and stakeholders to evaluate how corporations act, speak, and likely will behave in the future regarding ethical issues.

---

[TOC]

## Introduction

In a world of increasing scrutiny over corporate social responsibility and ethical conduct, stakeholders require robust
frameworks to evaluate how organizations communicate and act. Traditional approaches often treat corporate statements as
either “ethical” or “unethical,” but real-world scenarios frequently occupy a more ambiguous space. Organizations may
partially fulfill promises, offer misleading statements, or distract attention from core problems.

We have taken the framework described by **Marshall, D et al 2023** and adapted it to a more generalized format for
analysing and predicting corporate ethical behaviour.

This paper addresses these complexities by introducing a classification scheme for corporate behavior:

# Key Constructs and Terminology

### Actions, Issues, Flags, and Statements

1. **Effects**: What a company has done or is doing (e.g., installing wastewater treatment, lobbying for policy changes,
   etc.).
3. **Flags**: Markers that connect a set of effects and assign qualities to them.
4. **Statements**: Verbal or written communications by a company, including:
    - **Promises**: Declarations of intended future actions.
    - **Claims**: Assertions regarding past actions or outcomes.

These building blocks are the starting point for applying our matrix approach.

# Models

## Introduction

We model the ingested documents at multiple levels of abstraction. The first level is that of individual statements from
within a document. Each statement is allocated a vector, a feature list and additional query metadata. The vector is
generated based on our **DEMISE** model (see below). The feature list is a query friendly way of aggregating statements,
it is the vector with labels, and the metadata provides the data for a knowledge graph and also even easier levels of
querying.

Next we filter and cluster these vectors to create our **Effect** model (action impacts) which creates the **Harm**
score. Along with our **Trust** and **Reliability** model which create the **Reliability** score. Along with the *
*Flooding** and **Cherry Picking** models which then contribute to the **Transparency** rating.

The **Effect** model is then further processed to provide our **Adaptability** and **Ethos** ratings.

Many types of analysis that can be performed across the dataset have similarities and this has lead to the devising of
the following models:

## Statements - Domain, Entities, Motivation, Impact, Statement Type and Ethics   (DEMISE)

### Introduction

**DEMISE** stands for **Domain, Entity, Motivation, Impact, Statement Type, and Engagement**. It is our conceptual
framework for embedding statements against the dimensions required to capture ethical issues. It allows the system to
process statements made by any entity about a fact/opinion, action or event that has ethical implications. If it is an
action/event it can also be hypothetical/intended past/present etc.

Here is a quick summary of each DEMISE component:

1. **Domain**  
   The general area or topic of the action or statement (e.g. technology, finance, healthcare).
    - *Example:* A statem2ent about “carbon footprint reduction” could have the Domain “climate/energy.”

2. **Entity**  
   The *subject* and *object* entity or entities involved in the statement, such as a person, organization, product,
   location, or concept.
    - *Example:* In “Company X announced a new policy,” the main Entity is “Company X.”

3. **Motivation**  
   The reason or driving factor behind the statement or action (e.g. fear, ambition, kindness, generosity).
    - *Example:* A decision motivated by “greed” versus one motivated by “sustainability.”

4. **Impact**  
   The effect the action or statement has on any entity, whether beneficial, harmful, or neutral.
    - *Example:* “We caused an increase in local employment” has a positive Impact on the community.

5. **Statement Type**  
   The temporal and rhetorical nature of the statement (e.g. a prediction, a promise, an acknowledgement, or a denial).
    - *Example:* “We predict we’ll expand next year” is a **Future Benefit**-type statement with a **Prediction** label.

6. **Engagement**  
   The engagement dimension, that is how the entity is approaching the situation described.
    - *Example:* A statement describing good done for it's own sake will have an '**altruistic**'  engagement.

The model is used to create embeddings for statements that are designed to exist in an ethical latent space that
encapsulates the motivations for the actions described in the statement and their impact.

The full and detailed explanation of the model can be found in [[Appendix A]].

## Higher Models

These models are calculated from re-analysis of **DEMISE** scored statements typically using subsets of the **DEMISE**
vector.

### Effect Model

This is derived from clustering against a combination of the **Domain**, **Engagement** and **Impact** models and
provides a resulting model of the effects a company has had. **Effect Flags** are created from this model.

### Responsibility Model - How Well Does a Company Respond

**Responsibility** seeks to determine how a company reacts to issues and mistakes. It also encompasses such concerns as
accountability, communication and transparency.

**Responsibility** is determined per **Effect**  by searching the **DEMISE** dataset for **Statements** in the same
latent space as the Effects after the Effect took place, and then determining how the entity reacted to the Effect. Note
that the Engagement and Motivation dimensions are also part of the DE**M**IS**E** model too. But here they are being
applied to the **Effect** not a **Statement**, we are also not calculating these fields from the statements as they have
different implications on a per statement basis as they do on an effect.

```python
class AdaptabilityDimension(str, Enum):
    """The adaptability dimension of the responsibility matrix model."""
    PREVENT = "prevent"  # Company takes action to prevent issues before they occur
    SOLVE = "solve"  # Company addresses issues after they occur
    MISDIRECT = "misdirect"  # Company redirects attention from major issues to minor solutions
    DIVERT = "divert"  # Company diverts attention with numerous minor solutions
    DENY = "deny"  # Company denies responsibility for issues
    DECEIVE = "deceive"  # Company makes positive statements without matching actions
    IGNORE = "ignore"  # Company ignores issues completely
    GASLIGHT = "gaslight"  # Company describes the problem as not being a problem


class EngagementDimension(str, Enum):
    """The engagement dimension of the responsibility matrix model."""
    ALTRUISTIC = "altruistic"  # Company does good because it's the right thing to do
    PREVENTATIVE = "preventative"  # Company seeks to prevent issues arising
    PROACTIVE = "proactive"  # Company solves issues before they become public
    RESPONSIVE = "responsive"  # Company responds to issues quickly
    REACTIVE = "reactive"  # Company responds only after pressure
    DISMISSIVE = "dismissive"  # Company dismisses importance of issues


PASSIVE = "passive"  # Company passively acknowledges issues


class MotivationDimension(str, Enum):
    """The motivation dimension of the responsibility matrix model."""
    GENUINE = "genuine"  # Company genuinely cares about addressing issues
    COMPLIANT = "compliant"  # Company acts to comply with regulations
    PRESSURED = "pressured"  # Company acts due to external pressure
    SUPERFICIAL = "superficial"  # Company acts superficially for appearance
    OPPORTUNISTIC = "opportunistic"  # Company acts to gain advantage


class ApproachDimension(str, Enum):
    """The approach dimension of the responsibility matrix model."""
    INNOVATIVE = "innovative"  # Company develops new solutions
    SYSTEMATIC = "systematic"  # Company applies systematic methods
    INCREMENTAL = "incremental"  # Company makes small, gradual changes
    OPPORTUNISTIC = "opportunistic"  # Company acts when convenient
    HAPHAZARD = "haphazard"  # Company's approach lacks coherence


class ActionDimension(str, Enum):
    """The action dimension of the responsibility matrix model."""
    MENTION = "mention"  # Company merely mentions the issue without substantial action
    DISCUSS = "discuss"  # Company discusses the issue in more detail
    ANNOUNCE = "announce"  # Company makes formal announcements about the issue
    INVESTIGATE = "investigate"  # Company commits resources to investigate the issue
    ACT = "act"  # Company takes concrete action to address the issue
```

The ***Responsibility Model*** is used to determine the ***Ethos***  and **Adaptability** scores by looking at the
responsibility Ethos & Adaptability vs impact of Effects.

# Reputation Scoring

## Harm, Engagement, Adaptability, Reliability and Transparency (HEART)

The aim of this model is to capture the essence of an entities behaviour to both rate the entity for others to see and
to allow prediction of future actions based on this personality.

**HEART** builds upon the dataset of **DEMISE** and compares related data across time and aggregated motivations and
impacts. Knowledge of these factors about an entity lead to the ability to predict future behaviours.

**H**arm - negative effects, positives ignored.
**E**ngagement - how a company engages with problems/issues/flags, calculated from the responsibility model weighted by
effect impact
**A**daptability - are they changing their ways, making improvements, calculated from the responsibility matrix weighted
by effect impact
**R**eliability/Trust - do they do what they say, have they done what they've said - calculated from promises+claims
**T**ransparency - are they trying to hide the harm they've done, includes cherry-picking and flooding

### Harm

> What harm has a company done

Harm is caculated from the **total negative effects** weighted by impact, contribution and intention. It will be
normalised - **TODO: WHAT FACTORS TO NORMALIZE BY**

### Ethos

> How engaged and what motivates the company to solve or cause problems in the ESG field. i.e. what is their ethos

This is determined via the responsibility matrix (**Engagement & Motivation**)  which is calculated on a **per effect
basis** and is used to determine if a company is trying to engage with ESG issues.

### Adaptability

> How is a company changing their ways to meet challenges in the ESG field.

The framework described by **Marshall, D et al 2023**  was used as a basis for this.

This is determined via the responsibility matrix which is calculated on a **per effect basis** and is used to determine
if a company is trying to improve itself in regards to ESG issues.

TODO: Continued re-occurance of the same flags over time will markedly reduce adaptability score.

### Reliability & Trust

> Is the company reliable and can they be trusted?

This is determined by looking at the promises/claims made by a company and whether they are provably correct.

### Transparency

> Is a company trying to obscure/hide their behaviour or are they actively reporting it.

Are actions in 3rd party reporting also in ESG reporting? Are they using cherry-picking/flooding to obscure bad actions?
Do they engage in lobbying.

 

## Process

![img](https://documents.lucid.app/documents/05ee122c-29a1-45e7-ad65-c95ac790de1a/pages/0_0?a=2012&x=-16&y=2181&w=2065&h=877&store=1&accept=image%2F*&auth=LCA%203b2b095cdded2841a79b142eb1025371ff205408d3535d7752044db92f713f5a-ts%3D1748439777)

![img](https://documents.lucid.app/documents/05ee122c-29a1-45e7-ad65-c95ac790de1a/pages/0_0?a=2012&x=-2&y=3143&w=1787&h=1644&store=1&accept=image%2F*&auth=LCA%20bc6cfe7a28d1ab58e3e1223b7733e560aaacb65c527e9d434c503114d2c207d8-ts%3D1748439777)

# Creating and Testing the Custom Embedding Model

# Baseline

A large set of baseline evaluations will be created which create synthetic data to test the responses from a frontier
model LLM. Once the model has been verified against the synthetic data and an LLM the training will start.

# Training

A large (n=100000) random sample of a large corpus of prompt/response pairs were created using an LLM with very heavy
prompting (6 calls made and about 50,000 tokens of explanation). The 6 results were combined into a single response and
a brief prompt with the text sample used in the original prompt. This corpus was then used to train Mistral Small 24B
quantized to 4bit using LoRA for fine tuning, the training run ran of two days on a single A200 (140GB VRAM) card. The
most favourable check point was taken and uploaded onto HuggingFace in a variety of formats.

When we deployed this model it became clear that even with the cheapest hosting options available that the Gemini
fine-tuned models would be even cheaper. So a new set of training data was produced in the format used by Google's
Vertex AI.

During the running of the application a small number of requests were performed against frontier models, and the results
of those requests were stored as further training data for the fine tuned models. As can be seen from the evaluations, a
high quality dataset worked almost equally as well on Gemini Flash and Gemini Flash Lite when fine-tuned. So clearly
once fine-tuned these very low cost models have enough parameters to be able to digest the language supplied into the
vector representations.

# Evaluation

For this we produced a custom evaluation harness specifically for comparing how the extraction of the DEMISE model and
the metadata compared with various different prompts and across different fine-tuned models.

# Using the Custom Embedding Model

The embeddings generated will be reverse engineered back into Python objects, these will then be used to create
sub-embeddings. The sub embeddings (such as Impact or Ethics+Impact) will be stored along with the primary embedding and
all the features as sparse key/value pairs in a Postgres database.

The embeddings can then be used for clustering, KNN etc. in a variety of latent spaces.

![image-20250116185414135](/Users/<USER>/Library/Mobile Documents/iCloud~md~obsidian/Documents/Turing/image-20250116185414135.png)

# Statement Analysis

Each statement is processed to provide metadata and features. The features are a set of key/value pairs that are turned
into a vector embedding. A statement describes an opinion, a known fact or an event or action, past present and future.

## Process

A primary page is retrieved, along with the previous and next pages. All three pages provide the **context** for the
statements.

The primary page is then split using a low-cost LLM into separate chunks with one **statement** per chunk.

Each **statement** is passed to a low-cost LLM and converted into **metadata**, the **statement**, **context** and *
*metadata** are passed to a BERT based transformer to create the feature embeddings. The result is serialized in the
database in a form that is easily queryable by embeddings sub-embeddings or individual feature.

## Statements Metadata

The metadata is information extracted from a statement that can be used for SQL like querying.

Extracted from the statement are the following:

- Statement Category - 'fact/opinion', 'event' or 'action'
- Statement Type - the type of statement this is e.g. Statement, Declaration, Supposition, Hypothesis, Assertion, Denial
  etc.
- Domain - a word describing the domain to which this statement belongs
- Subject (Named) Entity - the doer.
- Object (Named ) Entity - the done to .
- Quantities - unnamed entities such has dollars, rock etc. along with their quantities, these can only be the done to.
- Impact - the impact of the subject on the object
- Action - the action performed on the Object Entity or the
- Location - the location to which the statement relates, this may or may not be the affected entity.
- Time - the time at which an event occurs

## Flag Analysis

Flags exist in a subset of the overall **DEMISE** latent space, they use the **Domain**, **Ethics**, and **Impact** to
create an **Effect** embedding that can be used to compare ethical impactful actions. The dimensionality of the
embeddings is reduced and weights applied to make sure the latent space maps onto that of the impactful actions, which
are referred to as flags.

### Process

![img](https://documents.lucid.app/documents/05ee122c-29a1-45e7-ad65-c95ac790de1a/pages/0_0?a=2012&x=-43&y=1289&w=2664&h=809&store=1&accept=image%2F*&auth=LCA%2055192539060846f5d1ea65fe20bb329f735a8c99e820f91dcbfad65966200f30-ts%3D1748439777)

Flag analysis is performed by filtering all statements that are marked as an impactful action for a given entity. These
are clustered - the type of clustering is selectable. The clusters are then if needed sorted in proximity to the
centroid and outliers removed.

The Flag Analysis takes place over multiple steps, first we cluster by the reduced dimension vector, then each cluster's
text is analysed to see if there are any textually similar values, they are then merged. The text is analysed and if
needed split it into mulitple flags. The full set of flags are then remerged by their title and reason to make sure
there are no duplicates.

The Coherence metric was created to analyse this cluster->merge->split->merge process. It indicates the degree to which
the initial clustering managed to get the flags right and is calculated as:

​ 1 / ((number of flags merged+1) * (initial flags / number of clusters))

Higher values represent a high 'coherence' that is the minimum churn. The following are then tuned to get the maximum
coherence value and number of flags.

- Clustering mechanism (KMEANS, DBSCAN, SPECTRAL etc.)
- Clustering hyperparameters (epsilon, min_samples)
- Re-clustering similarity, for when two pieces of text were clustered differently by DEMISE but when analysed have a
  similar analysis.
- The analysis LLM and flag prompt, this effects the splitting process.
- Latent space, which parts of the DEMISE vector to use, and which to reduce the dimensions on, and which parts to add
  weighting to.
- Model Fine-tuning to improve the initial vectorisation.

### Mapping to UN SDG, Doughnut Economic and other Models

The mapping will be done by passing the key domain characteristics and a summary of the issue to an LLM along with
definitions of the specific Model. The LLM will then perform the actual mapping.

## Cherry Picking

![image (2)](./cherry.png)

# Predictive Modeling

Predictions of future actions are performed by regression analysis on the timeseries data in the combined Ethical and
Behavioural Models.

![](/Users/<USER>/Library/Mobile Documents/iCloud~md~obsidian/Documents/Turing/image-20250115185733407.png)

## Processing

Documents are cleaned and chunked.

Each chunk is split into statement + context chunks. Metadata is extracted and a vector assigned from the statement
using a fine-tuned low cost LLM. These vectors and metadata are stored along with the statement chunks.

The vectors are then decomposed into sub vectors. The additional subvectors are stored with the chunks.

## Per Entity Analysis

### Flags - Cluster By Ethics, Impact and Action

Clustering by Ethics, Impact and Action provides us with red and green 'flags'. Which identify good and bad behaviours.

The centroid of the matching statements is then used to look up from a seperate table what is the nearest descriptive
labels assigned to that space. The labels are then passed to an LLM to describe the name of the issue.

### Promises

The statements are filtered so as to only include promises. Their position in the Action and Motivation latent space is
calculated and K-Nearest Neighbours are identified with times after the promise. These statements are then passed to an
LLM to assess whether the promise was kept or broken.

### Claims

The statements are filtered so as to only include claims. Their position in the Action and Motivation latent space is
calculated and K-Nearest Neighbours are identified with before the claim. These statements are then passed to an LLM to
assess whether the promise was kept or broken.

### Cherry Picking/Flooding

The statements are clustered in the **Domain, Subject Entity, Object Entity** latent space, then patterns of many small
low impact positive actions around one or more high impact negative actions is looked for by clustering and filtering
and this is **Flooding**. Alongside flooding a pattern of a few highly repeated positive actions alongside negative
actions is identified and this is **Cherry Picking**.

## Industry Analysis

For a given set of companies, changes in the entries in the Motivation latent space across all companies reflect a
change in the pressures that affect the companies. Helping us to identify trends and challenges within an industry.

A spike in a specific motivation type, can be used to look up the statements that are related to that motivation type.
Using this we can identify possible causes of the changes in motivations.

Likewise with impact.

## Conclusion

This paper outlines a **multidimensional, consistent framework** for evaluating corporate behavior in terms of *
*Integrity** (promises), **Adaptability** (problem-solving), and **Honesty** (claims). By tracking each statement along
**Solution Orientation** and **Responsibility** axes, observers generate a comprehensive view of an organization’s
ethical posture. Repeated measurements over time can reveal whether a company is trending toward more **transparent**
problem-solving or drifting into **misleading** or **deceptive** practices.

Future research should refine the coding process, integrate it with automated text-analysis tools, and apply
longitudinal case studies across industries. The step-by-step methodology provided here should help both scholars and
practitioners systematically classify corporate communications, promoting greater accountability and transparency in
organizational behavior.

---

## References

1. Marshall, D., Rehme, J., O’Dochartaigh, A., Kelly, S., Boojihawon, R., and Chicksand, D. (2023). Reporting
   controversial issues in controversial industries. *Accounting, Auditing & Accountability Journal*, 36(9), pp.
   483-512.  https://www.emerald.com/insight/content/doi/10.1108/aaaj-07-2020-4684/full/html

---

**Acknowledgments**  The author gratefully acknowledges the various scholars and practitioners whose foundational work
on corporate ethics, sustainability, and organizational behavior shaped this framework.

# Appendix A

To be added

![[The Models]]
(c) [Year], [Your Name]. All rights reserved.*
