#!/bin/bash

# Claude Docker execution script
# This script runs Claude Code in a Docker container with proper mounting and configuration

set -ex

identifier=$1
shift

# Get the directory where this script is located
PROJECT_ROOT=$(pwd)
cd  "$(dirname $0)/.." || exit

SCRIPT_PROJECT_DIR="$(pwd)"
cd "$PROJECT_ROOT"

# Docker image name
IMAGE_NAME="claude-docker"

#Might as well just build it everytime as it's cached.
#docker build -t "$IMAGE_NAME" "$SCRIPT_PROJECT_DIR/claude/docker"

# Build the Docker image if it doesn't exist
if ! docker image inspect "$IMAGE_NAME" >/dev/null 2>&1; then
    echo "Building Claude Docker image..."
    docker build -t "$IMAGE_NAME" "$SCRIPT_PROJECT_DIR/claude/docker"
fi

# Check if Claude configuration files exist
if [ ! -f "$HOME/.claude.json" ]; then
    echo "Warning: ~/.claude.json not found. You may need to login to <PERSON> first."
fi

if [ ! -d "$HOME/.claude" ]; then
    echo "Warning: ~/.claude directory not found. You may need to setup <PERSON> first."
fi

security find-generic-password -l "Claude Code-credentials" -w > ~/.claude/.credentials.json

terminal_args="-i"
if [ -t 0 ]; then
    echo "stdin is connected to a terminal"
    terminal_args="-it"
fi

mkdir -p "$PROJECT_ROOT"/claude-docker-home
mkdir -p "$PROJECT_ROOT"/../.shared/pnpm-store
# Run the Docker container with proper mounts
docker run $terminal_args --rm \
    --name claude-code-"$identifier" \
    -v "$HOME/.claude.json:/workspace/.claude.json:ro" \
    -v "$HOME/.ssh:/home/<USER>/.ssh:ro" \
    -v "$PROJECT_ROOT/claude-docker-home:/home/<USER>" \
    -v "$HOME/.claude:/workspace/.claude:ro" \
    -v "$HOME/.mcp-auth:/home/<USER>/.mcp-auth:ro" \
    -v "$PROJECT_ROOT/repo:/workspace/$identifier/" \
    -v "$PROJECT_ROOT/test-results:/workspace/test-results/" \
    -v "$PROJECT_ROOT/linear-images:/workspace/linear-images/" \
    -v "$PROJECT_ROOT/../.shared/pnpm-store:/home/<USER>/.pnpm-store" \
    -e GITHUB_TOKEN="$GITHUB_TOKEN" \
    -w "/workspace/$identifier/" "$IMAGE_NAME" $identifier "$@"
