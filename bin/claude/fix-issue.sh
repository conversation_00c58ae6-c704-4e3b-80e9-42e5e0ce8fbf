#!/bin/bash
set -eux
cd  $(dirname $0)/../.. || exit
src_dir="$(pwd)"
cd -
"$src_dir"/bin/download-linear-images.sh EKO-$1 $LINEAR_API_KEY ~/claude_workdirs/$1/linear-images
src_dir="$(pwd)"
#Create if not exists
if [ ! -d ~/claude_workdirs/$1/repo ]; then
  mkdir -p ~/claude_workdirs/$1/repo
  git clone "**************:ekointelligence/mono-repo.git" ~/claude_workdirs/$1/repo
  cd ~/claude_workdirs/$1/repo
  git checkout -b feature/eko-$1 || true
  git branch --set-upstream-to=origin/main feature/eko-$1
  cd -
fi
#git worktree add -B feature/eko-$1 ~/claude_worktrees/$1 origin/main
cp .mcp.json ~/claude_workdirs/$1/repo/.mcp.json
mkdir -p ~/claude_workdirs/$1/repo/.claude
cp -f .claude/settings.json ~/claude_workdirs/$1/repo/.claude/
cp -f .claude/settings.json ~/claude_workdirs/$1/repo/.claude/settings.local.json
cp  apps/customer/.env.development.local ~/claude_workdirs/$1/repo/apps/customer/.env.development.local
cd  ~/claude_workdirs/$1/repo
cd ..
id=$1
shift

"$src_dir"/bin/claude-docker  $id  --model=sonnet --dangerously-skip-permissions    "

Please fix the issue EKO-$1

Please check it on Linear, any image attachments will be in /workspace/linear-images please view them.

Reminders:

- Use tailwind plugins and tailwind not custom CSS, check what plugins exist already before adding new.
- You have access to the \`gh\` command line tool for github as well as \`git\`.

Please use the following workflow:

- Plan the work before you start in detail with small incremental steps,  create a check list and  think through step by step.
- Search the web for any details you need on libraries, especially ones that change frequently, or use your Context7 MCP tool if you like.
- Update the ticket with a comment containing your plan of action using Linear MCP.
- Do the actual work, making use of the tools you have, especially run_in_db.sh for analytics db and run_in_customer_db.sh for customer db.
- You have a Linear MCP tool.
- For web apps frequently use \`tsc --noEmit\`
- Then run playwright tests \`npx playwright test --reporter=line\` at the end.
- For the customer app please add a playwright test if appropriate to reproduce the issue, this test should be in a new file called \`apps/customer/tests/issues/issue-eko-$1.spec.ts\`

- For python run the pytest tests in tests.on_commit and run \`uvx ty check\` on the changed files.
- Commit changes as you need, with a verbose comment including the Linear issue ID
- Update Linear with a report on what you have done so far.
- DO NOT create a pull request

You're doing a great job, keep at it, plan this out and ultrathink carefully step by and work your way through this methodically. Be your best self!
" < /dev/tty


# Loop while tests are failing
count=0
until npx playwright test --reporter=line apps/customer/tests/issues/issue-eko-$id.spec.ts > .last-test-run|| [ $count -eq 10 ]
do
  ((count++))
  cd ~/claude_workdirs/$1/repo
  "$src_dir"/bin/claude-docker  $id  --model=sonnet --dangerously-skip-permissions -c " The tests are failing in apps/customer/tests/issues/issue-eko-$id.spec.ts, please fix this:\n $(<.last-test-run)"
done

if (( count < 10))
then
  "$src_dir"/bin/claude-docker  $id  --model=sonnet --dangerously-skip-permissions -c "Thanks for the help. Please commit  feature/eko-$id and push all changes, then create a PR."
fi
