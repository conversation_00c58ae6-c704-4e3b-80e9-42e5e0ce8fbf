#!/bin/bash
set -eux
cd  $(dirname $0)/../.. || exit
src_dir="$(pwd)"
#Create if not exists
if [ ! -d ~/claude_workdirs/$1/repo ]; then
  mkdir -p ~/claude_workdirs/$1/repo
  git clone "**************:ekointelligence/mono-repo.git" ~/claude_workdirs/$1/repo
  cd ~/claude_workdirs/$1/repo
  git checkout -b feature/eko-$1 || true
  git branch --set-upstream-to=origin/main feature/eko-$1
  cd -
fi
#git worktree add -B feature/eko-$1 ~/claude_worktrees/$1 origin/main
cp .mcp.json ~/claude_workdirs/$1/repo/.mcp.json
mkdir -p ~/claude_workdirs/$1/repo/.claude
cp -f .claude/settings.json ~/claude_workdirs/$1/repo/.claude/
cp -f .claude/settings.json ~/claude_workdirs/$1/repo/.claude/settings.local.json
cp  apps/customer/.env.development.local ~/claude_workdirs/$1/repo/apps/customer/.env.development.local
cd  ~/claude_workdirs/$1/repo
cd ..
id=$1
shift
"$src_dir"/bin/claude-docker  $id  --model sonnet "$@"
