#!/bin/bash
set -eux
cd  $(dirname $0)/../.. || exit
src_dir="$(pwd)"
#Create if not exists
if [ ! -d ~/claude_workdirs/fix-all-tests/repo ]; then
  mkdir -p ~/claude_workdirs/fix-all-tests/repo
  git clone "**************:ekointelligence/mono-repo.git" ~/claude_workdirs/fix-all-tests/repo
  cd ~/claude_workdirs/fix-all-tests/repo
  git checkout -b feature/fix-all-tests || true
  git branch --set-upstream-to=origin/main feature/fix-all-tests
  cd -
fi
#git worktree add -B feature/eko-$1 ~/claude_worktrees/$1 origin/main
cd $src_dir
cp .mcp.json ~/claude_workdirs/fix-all-tests/repo/.mcp.json
mkdir -p ~/claude_workdirs/fix-all-tests/repo/.claude
cp -f .claude/settings.json ~/claude_workdirs/fix-all-tests/repo/.claude/
cp -f .claude/settings.json ~/claude_workdirs/fix-all-tests/repo/.claude/settings.local.json
cp  apps/customer/.env.development.local ~/claude_workdirs/fix-all-tests/repo/apps/customer/.env.development.local
cd  ~/claude_workdirs/fix-all-tests/repo
cd ..

repo_dir=~/claude_workdirs/fix-all-tests/repo
RECHECK="Thanks for the help. Please check any changed tests against guidelines in apps/customer/tests/CLAUDE.md, and ensure each test
should follow a clear sequential path from start to finish, it should not branch, it should not have try/catch,
it should not fallback it should ignore failure states. A test either passes or fails completely,
it cannot partially pass. Please also make sure that tests haven't been deleted just because they are broken.
If they have please restore them."

PROMPT="
           Tests are failing. The log file is in apps/customer/.last-test-run. The test result files are in apps/customer/test-results/.  Please fix the failing tests.

           Do not add try/catch or if/else blocks to bypass the testing, do not delete failing tests.

           Please read apps/customer/tests/CLAUDE.md for details on writing tests.

           Look at a working test such as apps/customer/tests/editor-ai-features.spec.ts or apps/customer/tests/document-templates.spec.ts and see how it works.

           Run playwright tests as follows:  \`cd apps/customer && npx playwright test --reporter=line --max-failures=1\` .

           ---
           Each test should follow a clear sequential path from start to finish, it should not branch, it should not have try/catch, it should not fallback it should not ignore failure states. A test either passes or fails.
           ---

           You're doing a great job, keep at it, plan this out and ultra think carefully step by step and work your way through this methodically.
           "

export PW_PORT=${1:-3030}
export CI=true
run_claude="$src_dir/bin/claude-docker  fix-all-tests  --model=sonnet --dangerously-skip-permissions --verbose  --output-format=stream-json --print "
# Loop while tests are failing
until (cd "$repo_dir"/apps/customer/  && npx playwright test --output=test-results/ --max-failures=1 > .last-test-run 2>&1)
do
  cd "$repo_dir"/..
  count=0
   $run_claude "$PROMPT" | "$HOME/IdeaProjects/pretty-claude-json/pretty_claude.sh"
  until (cd "$repo_dir"/apps/customer/ && npx playwright test --output=test-results/ --max-failures=1 --last-failed ) > .last-test-run 2>&1 || [ $count -eq 10 ]
  do
    cat .last-test-run
    cd "$repo_dir"/..
    $run_claude "$PROMPT"| "$HOME/IdeaProjects/pretty-claude-json/pretty_claude.sh"
    ((count++))
  done
  $run_claude "$RECHECK"| "$HOME/IdeaProjects/pretty-claude-json/pretty_claude.sh"
done
