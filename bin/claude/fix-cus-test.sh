#!/bin/bash
set -eux
cd  $(dirname $0)/../.. || exit
src_dir="$(pwd)"
#Create if not exists
if [ ! -d ~/claude_workdirs/$1/repo ]; then
  mkdir -p ~/claude_workdirs/$1/repo
  git clone "**************:ekointelligence/mono-repo.git" ~/claude_workdirs/$1/repo
  cd ~/claude_workdirs/$1/repo
  git checkout -b feature/test-$1 || true
  git branch --set-upstream-to=origin/main feature/test-$1
  cd -
fi
#git worktree add -B feature/eko-$1 ~/claude_worktrees/$1 origin/main
cd $src_dir
cp .mcp.json ~/claude_workdirs/$1/repo/.mcp.json
mkdir -p ~/claude_workdirs/$1/repo/.claude
cp -f .claude/settings.json ~/claude_workdirs/$1/repo/.claude/
cp -f .claude/settings.json ~/claude_workdirs/$1/repo/.claude/settings.local.json
cp  apps/customer/.env.development.local ~/claude_workdirs/$1/repo/apps/customer/.env.development.local
cd  ~/claude_workdirs/$1/repo
id=$1
cd ..
"$src_dir"/bin/claude-docker  test-$id  --model=sonnet --dangerously-skip-permissions  "

Please fix the test apps/customer/tests/$1.spec.ts

Firstly look at a working test such as apps/customer/tests/editor-ai.spec.ts or apps/customer/tests/document-templates.spec.ts and see how it works.

Reminders:

Use testUtils for all applicable operations. For example, login:

 let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

Or create a document:
  await testUtils.createDocumentFromTemplate()

Or wait for the editor:
  await testUtils.waitForEditor()

Please use the following workflow:

- Check the code base for what the test, tests so you can understand it.
- Add console checks:

   page.on('console', msg => {
      console.log(\`Browser console: \${msg.type()}: \${msg.text()}\`);
    });Wek

- Run playwright tests as follows:  \`cd apps/customer && npx playwright test --reporter=line tests/$1.spec.ts\` .
- If stuck get more logging with  \`DEBUG=pw:* cd apps/customer && npx playwright test --reporter=line tests/$1.spec.ts\`
- Determine if the test is broken or the code is broken.
- If the test is broken fix the test, if the code is broken fix the code.
- Timeouts are sometimes a sign of a broken test, but opening new pages and some other operations can take a time so consider increasing timeouts if needed.
- Search the web for any details you need on libraries, especially ones that change frequently, or use your Context7 MCP tool if you like.
- Do the actual work, making use of the tools you have, especially run_in_db.sh for analytics db and run_in_customer_db.sh for customer db.
- Use \`tsc --noEmit\`
- Commit changes as you need, with a verbose comment including the name of the test you are working. And then push them at the end.

You're doing a great job, keep at it, plan this out and ultrathink carefully step by and work your way through this methodically. Be your best self!
"
export PW_PORT=${2:-3333}
# Loop while tests are failing
count=0
mkdir -p ~/claude_workdirs/$1/test-results/${count}/
until (cd ~/claude_workdirs/$1/repo/apps/customer/ && npx playwright test --reporter=line --output=~/claude_workdirs/$1/test-results/${count}/ tests/"$id".spec.ts) > .last-test-run || [ $count -eq 10 ]
do
  ((count++))
  cd ~/claude_workdirs/$1
  "$src_dir"/bin/claude-docker  $id  --model=sonnet --dangerously-skip-permissions  -c "The tests are still failing in @apps/customer/tests/$id.spec.ts, please fix this. The log file is in .last-test-run. The test result files are in /workspace/test-results/${count}/ Please fix these failing tests"
done

if (( count < 10))
then
  cd  ~/claude_workdirs/$1/repo
  "$src_dir"/bin/claude-docker  $id  --model=sonnet --dangerously-skip-permissions -p -c "Thanks for the help. Please commit  feature/test-$id and push all changes, then create a PR."
fi
