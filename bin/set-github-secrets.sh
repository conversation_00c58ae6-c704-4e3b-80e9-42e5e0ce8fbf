#!/bin/bash

# Script to set GitHub repository secrets from a .env file
# Usage: ./bin/set-github-secrets.sh [env-file]
# Example: ./bin/set-github-secrets.sh .env.production

set -e  # Exit on any error

ENV_FILE=${1:-.env}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    print_error "GitHub CLI (gh) is not installed."
    print_info "Install it from: https://cli.github.com/"
    exit 1
fi

# Check if user is authenticated
if ! gh auth status &> /dev/null; then
    print_error "You are not authenticated with GitHub CLI."
    print_info "Run: gh auth login"
    exit 1
fi

# Check if .env file exists
if [[ ! -f "$ENV_FILE" ]]; then
    print_error "$ENV_FILE not found"
    print_info "Create a .env file with your secrets in KEY=VALUE format"
    print_info "Example:"
    print_info "  PAYLOAD_SECRET=your-secret-here"
    print_info "  DATABASE_URI=postgresql://user:pass@host:port/dbname"
    exit 1
fi

print_info "Reading secrets from $ENV_FILE..."
print_warning "This will set GitHub repository secrets. Make sure you're in the correct repository!"

# Get current repository info
REPO_INFO=$(gh repo view --json nameWithOwner -q .nameWithOwner 2>/dev/null || echo "unknown")
print_info "Current repository: $REPO_INFO"

# Ask for confirmation
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Cancelled."
    exit 0
fi

# Counter for secrets set
SECRET_COUNT=0

# Read the .env file and set secrets
while IFS= read -r line || [[ -n "$line" ]]; do
    # Skip empty lines and comments
    if [[ -z "$line" ]] || [[ "$line" =~ ^[[:space:]]*# ]]; then
        continue
    fi
    
    # Extract key and value
    if [[ "$line" =~ ^([^=]+)=(.*)$ ]]; then
        key="${BASH_REMATCH[1]}"
        value="${BASH_REMATCH[2]}"
        
        # Trim whitespace from key
        key=$(echo "$key" | xargs)
        
        # Remove surrounding quotes from value
        value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
        
        # Skip empty values
        if [[ -z "$value" ]]; then
            print_warning "Skipping $key (empty value)"
            continue
        fi
        
        print_info "Setting secret: $key"
        
        # Set the secret
        if echo "$value" | gh secret set "$key"; then
            print_success "Set secret: $key"
            ((SECRET_COUNT++))
        else
            print_error "Failed to set secret: $key"
        fi
    else
        print_warning "Skipping invalid line: $line"
    fi
done < "$ENV_FILE"

print_success "Done! Set $SECRET_COUNT secrets from $ENV_FILE"

# List current secrets
print_info "Current repository secrets:"
gh secret list
