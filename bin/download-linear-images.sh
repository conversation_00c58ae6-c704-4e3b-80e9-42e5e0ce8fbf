#!/bin/bash

# Linear Issue Image Downloader
# Downloads all images and attachments from a Linear issue

set -exu

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
usage() {
    echo "Usage: $0 <ISSUE_ID> <API_KEY> [OUTPUT_DIR]"
    echo ""
    echo "Arguments:"
    echo "  ISSUE_ID    Linear issue ID (e.g., ENG-123 or full UUID)"
    echo "  API_KEY     Your Linear API key"
    echo "  OUTPUT_DIR  Output directory (optional, defaults to ./linear-images-ISSUE_ID)"
    echo ""
    echo "Example:"
    echo "  $0 ENG-123 lin_api_1234567890abcdef ./downloads"
    echo ""
    echo "Note: Get your API key from Linear > Settings > Account > Security & Access"
}

# Check arguments
if [[ $# -lt 2 ]]; then
    usage
    exit 1
fi

ISSUE_ID="$1"
API_KEY="$2"
OUTPUT_DIR="$3"

# Set default output directory if not provided
if [[ -z "$OUTPUT_DIR" ]]; then
    OUTPUT_DIR="./linear-images-${ISSUE_ID}"
fi

# Validate API key format
if [[ ! "$API_KEY" =~ ^lin_api_ ]]; then
    print_warning "API key doesn't start with 'lin_api_' - this might be incorrect"
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"
print_info "Created output directory: $OUTPUT_DIR"

# Function to find issue UUID by identifier - Linear accepts shorthand IDs directly
find_issue_id() {
    local identifier="$1"
    
    print_info "Looking up issue ID for identifier: $identifier" >&2
    
    # Use direct issue query - Linear accepts shorthand identifiers like "EKO-31"
    local search_query='{"query": "query($issueId: String!) { issue(id: $issueId) { id identifier title } }", "variables": {"issueId": "'"$identifier"'"}}'
    
    local response=$(curl -s \
      -X POST \
      -H "Content-Type: application/json" \
      -H "Authorization:  $API_KEY" \
      --data "$search_query" \
      https://api.linear.app/graphql)
    
    # Check for errors
    if echo "$response" | jq -e '.errors' > /dev/null 2>&1; then
        print_error "API error while searching for issue:" >&2
        echo "$response" | jq '.errors' >&2
        return 1
    fi
    
    # Extract the UUID
    local uuid=$(echo "$response" | jq -r '.data.issue.id // empty')
    
    if [[ -n "$uuid" ]]; then
        print_success "Found issue: $(echo "$response" | jq -r '.data.issue.identifier') - $(echo "$response" | jq -r '.data.issue.title')" >&2
        echo "$uuid"
        return 0
    else
        return 1
    fi
}

# First, get the actual UUID for the issue
print_info "Resolving issue identifier: $ISSUE_ID"

if [[ "$ISSUE_ID" =~ ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$ ]]; then
    # Already a UUID
    ISSUE_UUID="$ISSUE_ID"
    print_info "Input appears to be a UUID, using directly"
else
    # Need to look up by identifier
    ISSUE_UUID=$(find_issue_id "$ISSUE_ID")
    if [[ $? -ne 0 ]] || [[ -z "$ISSUE_UUID" ]]; then
        print_error "Could not find issue with identifier: $ISSUE_ID"
        print_info "Make sure the issue exists and you have access to it"
        exit 1
    fi
fi

# GraphQL query to get full issue data using the UUID - single line JSON
GRAPHQL_QUERY='{"query": "query($issueId: String!) { issue(id: $issueId) { id identifier title description attachments { nodes { id url title subtitle } } comments { nodes { id body user { name } } } } }", "variables": {"issueId": "'"$ISSUE_UUID"'"}}'

print_info "Querying Linear API for full issue data..."

# Make GraphQL request with signed URLs (1 hour expiration)
RESPONSE=$(curl -s \
  -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization:  $API_KEY" \
  -H "public-file-urls-expire-in: 3600" \
  --data "$GRAPHQL_QUERY" \
  https://api.linear.app/graphql)

# Check if request was successful
if [[ $? -ne 0 ]]; then
    print_error "Failed to make request to Linear API"
    exit 1
fi

# Check for GraphQL errors
if echo "$RESPONSE" | jq -e '.errors' > /dev/null 2>&1; then
    print_error "GraphQL API returned errors:"
    echo "$RESPONSE" | jq '.errors'
    exit 1
fi

# Check for GraphQL errors
if echo "$RESPONSE" | jq -e '.errors' > /dev/null 2>&1; then
    print_error "GraphQL API returned errors:"
    echo "$RESPONSE" | jq '.errors'
    exit 1
fi

# Check if issue exists (this should not happen since we already validated the UUID)
if echo "$RESPONSE" | jq -e '.data.issue == null' > /dev/null 2>&1; then
    print_error "Issue not found with UUID: $ISSUE_UUID"
    exit 1
fi

# Extract issue info
ISSUE_TITLE=$(echo "$RESPONSE" | jq -r '.data.issue.title')
ISSUE_IDENTIFIER=$(echo "$RESPONSE" | jq -r '.data.issue.identifier')

print_success "Found issue: $ISSUE_IDENTIFIER - $ISSUE_TITLE"

# Create a log file
LOG_FILE="$OUTPUT_DIR/download_log.txt"
echo "Linear Issue Image Download Log" > "$LOG_FILE"
echo "Issue: $ISSUE_IDENTIFIER - $ISSUE_TITLE" >> "$LOG_FILE"
echo "Date: $(date)" >> "$LOG_FILE"
echo "----------------------------------------" >> "$LOG_FILE"

# Function to detect file extension from content-type
get_extension_from_content_type() {
    local content_type="$1"
    
    case "$content_type" in
        *"image/jpeg"*) echo ".jpg" ;;
        *"image/jpg"*) echo ".jpg" ;;
        *"image/png"*) echo ".png" ;;
        *"image/gif"*) echo ".gif" ;;
        *"image/webp"*) echo ".webp" ;;
        *"image/svg"*) echo ".svg" ;;
        *"image/bmp"*) echo ".bmp" ;;
        *"image/tiff"*) echo ".tiff" ;;
        *"application/pdf"*) echo ".pdf" ;;
        *"text/plain"*) echo ".txt" ;;
        *) echo "" ;;
    esac
}

# Function to download a file
download_file() {
    local url="$1"
    local filename="$2"
    local source="$3"
    
    print_info "Downloading: $filename (from $source)"
    
    # First, get headers to detect content type
    local headers=$(curl -L -s -I \
        -H "Authorization:  $API_KEY" \
        "$url")
    
    local content_type=$(echo "$headers" | grep -i "content-type:" | cut -d' ' -f2- | tr -d '\r\n')
    
    # Check if filename needs extension based on content type
    if [[ ! "$filename" =~ \.[a-zA-Z0-9]+$ ]]; then
        local extension=$(get_extension_from_content_type "$content_type")
        if [[ -n "$extension" ]]; then
            filename="${filename}${extension}"
            print_info "Added extension based on content-type: $extension"
        fi
    fi
    
    # Download the file
    if curl -L -s \
        -H "Authorization:  $API_KEY" \
        -o "$OUTPUT_DIR/$filename" \
        "$url"; then
        
        # Check if file was actually downloaded (not empty)
        if [[ -s "$OUTPUT_DIR/$filename" ]]; then
            print_success "Downloaded: $filename"
            echo "✓ $filename - $url ($source)" >> "$LOG_FILE"
            return 0
        else
            print_warning "Downloaded file is empty: $filename"
            rm -f "$OUTPUT_DIR/$filename"
            echo "✗ $filename - Empty file ($source)" >> "$LOG_FILE"
            return 1
        fi
    else
        print_error "Failed to download: $filename"
        echo "✗ $filename - Download failed ($source)" >> "$LOG_FILE"
        return 1
    fi
}

# Function to extract filename from URL
get_filename_from_url() {
    local url="$1"
    local prefix="$2"
    
    # Extract filename from URL, removing query parameters and fragments
    local filename=$(basename "$url" | sed 's/[?#].*//')
    
    # If filename is too short or has no extension, create a meaningful name
    if [[ ${#filename} -lt 3 ]] || [[ ! "$filename" =~ \.[a-zA-Z0-9]+$ ]]; then
        # Use a hash of the URL for uniqueness
        local url_hash=$(echo "$url" | md5sum | cut -d' ' -f1 | cut -c1-8)
        filename="${prefix}_${url_hash}"
    else
        # Use the original filename with prefix
        filename="${prefix}_${filename}"
    fi
    
    echo "$filename"
}

# Function to extract image URLs from markdown content
extract_markdown_images() {
    local content="$1"
    local prefix="$2"
    
    # Extract ![alt](url) patterns - compatible with macOS grep
    while IFS= read -r url; do
        if [[ "$url" =~ \.(jpg|jpeg|png|gif|bmp|webp|svg)(\?.*)?$ ]] || [[ "$url" =~ uploads\.linear\.app ]]; then
            local filename=$(get_filename_from_url "$url" "$prefix")
            download_file "$url" "$filename" "markdown"
        fi
    done < <(echo "$content" | grep -o '!\[[^]]*\]([^)]*)' | sed 's/.*(\([^)]*\)).*/\1/')
    
    # Extract [text](url) patterns that might be images
    while IFS= read -r url; do
        if [[ "$url" =~ \.(jpg|jpeg|png|gif|bmp|webp|svg)(\?.*)?$ ]] || [[ "$url" =~ uploads\.linear\.app ]]; then
            local filename=$(get_filename_from_url "$url" "$prefix")
            download_file "$url" "$filename" "markdown"
        fi
    done < <(echo "$content" | grep -o '\[[^]]*\]([^)]*)' | sed 's/.*(\([^)]*\)).*/\1/')
    
    # Also extract bare URLs that look like images - using grep -E for extended regex
    while IFS= read -r url; do
        local filename=$(get_filename_from_url "$url" "$prefix")
        download_file "$url" "$filename" "markdown"
    done < <(echo "$content" | grep -E -o 'https?://[^[:space:]]+\.(jpg|jpeg|png|gif|bmp|webp|svg)(\?[^[:space:]]*)?')
}

# Download direct attachments
print_info "Processing direct attachments..."
ATTACHMENT_COUNT=$(echo "$RESPONSE" | jq '.data.issue.attachments.nodes | length')

if [[ "$ATTACHMENT_COUNT" -gt 0 ]]; then
    while IFS= read -r attachment; do
        ATTACHMENT_DATA=$(echo "$attachment" | base64 --decode)
        ATTACHMENT_URL=$(echo "$ATTACHMENT_DATA" | jq -r '.url')
        ATTACHMENT_TITLE=$(echo "$ATTACHMENT_DATA" | jq -r '.title // "untitled"')
        ATTACHMENT_ID=$(echo "$ATTACHMENT_DATA" | jq -r '.id')
        
        # Create filename from title or URL
        if [[ "$ATTACHMENT_TITLE" != "null" && "$ATTACHMENT_TITLE" != "untitled" && -n "$ATTACHMENT_TITLE" ]]; then
            # Clean the title for use as filename
            clean_title="${ATTACHMENT_TITLE//[^a-zA-Z0-9._-]/_}"
            filename="attachment_${clean_title}"
        else
            filename=$(get_filename_from_url "$ATTACHMENT_URL" "attachment")
        fi
        
        download_file "$ATTACHMENT_URL" "$filename" "attachment"
    done < <(echo "$RESPONSE" | jq -r '.data.issue.attachments.nodes[] | @base64')
else
    print_info "No direct attachments found"
fi

# Extract images from issue description
print_info "Processing issue description..."
DESCRIPTION=$(echo "$RESPONSE" | jq -r '.data.issue.description // ""')
if [[ -n "$DESCRIPTION" && "$DESCRIPTION" != "null" ]]; then
    extract_markdown_images "$DESCRIPTION" "desc"
else
    print_info "No description content to process"
fi

# Extract images from comments
print_info "Processing comments..."
COMMENT_COUNT=$(echo "$RESPONSE" | jq '.data.issue.comments.nodes | length')

if [[ "$COMMENT_COUNT" -gt 0 ]]; then
    comment_index=0
    while IFS= read -r comment; do
        COMMENT_DATA=$(echo "$comment" | base64 --decode)
        COMMENT_BODY=$(echo "$COMMENT_DATA" | jq -r '.body // ""')
        COMMENT_USER=$(echo "$COMMENT_DATA" | jq -r '.user.name // "unknown"')
        
        if [[ -n "$COMMENT_BODY" && "$COMMENT_BODY" != "null" ]]; then
            print_info "Processing comment by $COMMENT_USER..."
            extract_markdown_images "$COMMENT_BODY" "comment${comment_index}"
        fi
        
        ((comment_index++)) || true
    done < <(echo "$RESPONSE" | jq -r '.data.issue.comments.nodes[] | @base64')
else
    print_info "No comments found"
fi

# Summary
print_info "Download completed!"
DOWNLOADED_FILES=$(find "$OUTPUT_DIR" -type f ! -name "download_log.txt" | wc -l)

echo "" >> "$LOG_FILE"
echo "Summary: $DOWNLOADED_FILES files downloaded" >> "$LOG_FILE"

if [[ "$DOWNLOADED_FILES" -gt 0 ]]; then
    print_success "Downloaded $DOWNLOADED_FILES files to: $OUTPUT_DIR"
    print_info "Check download_log.txt for details"
    
    # List downloaded files
    echo ""
    echo "Downloaded files:"
    find "$OUTPUT_DIR" -type f ! -name "download_log.txt" -exec basename {} \; | sort
else
    print_warning "No images found in issue: $ISSUE_ID"
fi
