-- <PERSON><PERSON>t to drop all audit triggers
DO $$
DECLARE
    trig RECORD;
BEGIN
    -- Loop through all triggers that match the audit trigger pattern
    FOR trig IN
        SELECT trigger_name, event_object_table
        FROM information_schema.triggers
        WHERE trigger_name LIKE 'audit_trigger_%'
    LOOP
        -- Generate and execute DROP TRIGGER statement
        EXECUTE format('DROP TRIGGER %I ON %I;', 
                      trig.trigger_name, 
                      trig.event_object_table);
                      
        -- Output which trigger was dropped
        RAISE NOTICE 'Dropped trigger % on table %', 
                    trig.trigger_name, 
                    trig.event_object_table;
    END LOOP;
END;
$$;

-- Verify no audit triggers remain
SELECT trigger_name, event_object_table 
FROM information_schema.triggers 
WHERE trigger_name LIKE 'audit_trigger_%';
